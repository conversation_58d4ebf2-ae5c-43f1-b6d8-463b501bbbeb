using Cysharp.Threading.Tasks;
using Google.Play.AppUpdate;
using Google.Play.Common;
using UnityEngine;

public class UpdateGame : MonoBehaviour
{
#if UNITY_ANDROID && !UNITY_EDITOR
    private AppUpdateManager appUpdateManager;

    public static event System.Action OnUpdateCancelledOrClosed;

    private void Awake()
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            appUpdateManager = new AppUpdateManager();
        }
    }

    async UniTaskVoid Start()
    {
        await CheckForUpdate();
    }

    async UniTask CheckForUpdate()
    {
        PlayAsyncOperation<AppUpdateInfo, AppUpdateErrorCode> appUpdateInfoOperation =
            appUpdateManager.GetAppUpdateInfo();

        await appUpdateInfoOperation;

        if (appUpdateInfoOperation.IsSuccessful)
        {
            var appUpdateInfoResult = appUpdateInfoOperation.GetResult();

            if (appUpdateInfoResult.UpdateAvailability == UpdateAvailability.UpdateAvailable &&
                appUpdateInfoResult.IsUpdateTypeAllowed(AppUpdateOptions.ImmediateAppUpdateOptions()))
            {
                var appUpdateOptions = AppUpdateOptions.ImmediateAppUpdateOptions();
                await appUpdateManager.StartUpdate(appUpdateInfoResult, appUpdateOptions);

                // After the update flow, check again
                var checkAgain = appUpdateManager.GetAppUpdateInfo();
                await checkAgain;
                if (checkAgain.IsSuccessful)
                {
                    var result = checkAgain.GetResult();
                    if (result.UpdateAvailability == UpdateAvailability.UpdateAvailable)
                    {
                    }
                }
            }
        }
    }
#endif
}