# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 122ms
  generate-prefab-packages
    exec-prefab 590ms
    [gap of 11ms]
  generate-prefab-packages completed in 608ms
  execute-generate-process
    exec-configure 562ms
    [gap of 80ms]
  execute-generate-process completed in 642ms
  [gap of 51ms]
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 1456ms

