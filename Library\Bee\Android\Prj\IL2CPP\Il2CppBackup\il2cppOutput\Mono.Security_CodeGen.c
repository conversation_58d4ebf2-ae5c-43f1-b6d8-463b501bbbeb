﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void Locale_GetText_m7531650AD8364720B8A7BADB48287D4E6FF9FCE8 (void);
extern void Locale_GetText_mB63D530ABD106F4BC9C2BB3AC1A38EDDFC3188A2 (void);
extern void ASN1__ctor_mA9AE2197367C1E13DBFDA67E0A383167F52CC114 (void);
extern void ASN1__ctor_mAA538F9E1BE0DE739E9747BC3BC71DC030B018AA (void);
extern void ASN1__ctor_m950BFCCF44A987ACBA12142624AA222200EE503E (void);
extern void ASN1_get_Count_mBE45E73126FAD2694E9059CAC53B7AC9A5F60833 (void);
extern void ASN1_get_Tag_m1984CF0DDF54424E61BA3650D93CBA0DCB58F232 (void);
extern void ASN1_get_Length_mC03E8FE25B4BD7B97EA3D8591E01A457F491C7FE (void);
extern void ASN1_get_Value_mA6F9BE5AC19AC060AC42673C8FD5AA864EA046B6 (void);
extern void ASN1_set_Value_mAFFA885810928715B379EAD478AA3961E8ACD589 (void);
extern void ASN1_CompareArray_mA7A05EE692B23D6D9A7C0E5B8CCC4CDECDAC0A79 (void);
extern void ASN1_CompareValue_m7397F4657555C6ACAF6622DE143C89E9E7593554 (void);
extern void ASN1_Add_m4C61487A6CCF48D5CEB0D97B248FE31F9FCD849F (void);
extern void ASN1_GetBytes_m3B7DABFDBE6BF7F9C926E4C8A16FC6BE6D1CE67B (void);
extern void ASN1_Decode_mC4CF3CB2CC1DB454AA9C720BA79520956FB1F77B (void);
extern void ASN1_DecodeTLV_mD4465394202DA7B0D37B9453CDE039233969E9DF (void);
extern void ASN1_get_Item_mF105DA24F3BE9FA3697229CF99B1602B736B647F (void);
extern void ASN1_Element_m97C1D5E39B14AC29D2F7AB0226A6E6CA2952B7C9 (void);
extern void ASN1_ToString_m4995F083B02F8FEF578ECA6EE73A257821F50A00 (void);
extern void ASN1Convert_FromInt32_mACAC096211E525F124BE0D50D90524ADCB6EA198 (void);
extern void ASN1Convert_FromOid_mBB920E2827A66620790C5E07549D74F244735C1C (void);
extern void ASN1Convert_FromUnsignedBigInteger_mFA9B30C2532C4E1909AE0CF5151544D857C941FE (void);
extern void ASN1Convert_ToInt32_m956785EB4A235575C21677C16D2F6CBE54787032 (void);
extern void ASN1Convert_ToOid_mBCE4FD3970C556190FB00A6AD409A6ABB4C627D8 (void);
extern void ASN1Convert_ToDateTime_m2D520694D39F2C86F72ECB24EF48F58B8C57AA75 (void);
extern void BitConverterLE_GetUIntBytes_mED0A55F565721091E851FD6108E128C3CBCB87F0 (void);
extern void BitConverterLE_GetULongBytes_m335C736CF3E43BF45AB110AD48E83777A03F4A26 (void);
extern void BitConverterLE_GetBytes_mEEFE00015D501FBBD32225D9C45A2C2A0673E9C7 (void);
extern void BitConverterLE_GetBytes_m1200CDE198D95D35643FBDDE226BC59D988BC44D (void);
extern void BitConverterLE_UShortFromBytes_mD0F7166ABCEED3D220DCE26EDF1F03680ED1C82A (void);
extern void BitConverterLE_UIntFromBytes_m22FF5D22A65B4C2F7D1A32BC7C28844187FCEE48 (void);
extern void BitConverterLE_ToInt32_m9883205CC9360B9937EBB27051250198255F8D2A (void);
extern void BitConverterLE_ToUInt16_m9C9006FA48C5155275790F99EC5558AB440B4088 (void);
extern void BitConverterLE_ToUInt32_m311817F60340C60EE9891E11C144DCAC2113BC8E (void);
extern void ContentInfo__ctor_m1B4B1B164D5159FE64E1346E1DE36B4E7B6F4078 (void);
extern void ContentInfo__ctor_m95EA3D16DCE96C9504DFC08940380C6983F41036 (void);
extern void ContentInfo__ctor_m7D3DD72DA482A68007DB183975E3F649EEE308CD (void);
extern void ContentInfo__ctor_mBC763695FB9DCE655DC9F64E932679635A8A803B (void);
extern void ContentInfo_get_ASN1_m3A32CA99ED50DDA79761FC779979489927D0ABE3 (void);
extern void ContentInfo_get_Content_m85ECCB3CF732981E207598428EF008D95F754F98 (void);
extern void ContentInfo_set_Content_mC885DF149B5AB25343766F00FB7749A77BEFBC36 (void);
extern void ContentInfo_get_ContentType_m4ACE1C1CD7330D35EF0C2C422CF48E3BA0FFD7D1 (void);
extern void ContentInfo_set_ContentType_mE3356914A7D44048391DCD32316D00C026415791 (void);
extern void ContentInfo_GetASN1_m52EF51C772714542A1263FCABF334391397F08AD (void);
extern void EncryptedData__ctor_m355455E414AB2547674FDB62A72BFF4EE98BDDF1 (void);
extern void EncryptedData__ctor_m5C149670A898E43CCCC28D6B30568138D4098E72 (void);
extern void EncryptedData_get_EncryptionAlgorithm_mF1F3CE667E2558EC146D7086A64C5735DEB37BF4 (void);
extern void EncryptedData_get_EncryptedContent_m28A275C350840EAD2C448DF76D9116D49000DC5C (void);
extern void SignedData__ctor_m4609283D3F9F231C0B5156FAE7C2DCF89A3AD7A6 (void);
extern void SignedData_get_Certificates_m2F2280AA69C4903FD8CAA75FAECEFC7586EF6D7E (void);
extern void SignedData_get_ContentInfo_m91409FC043E8C6FF741A922FC69BB9E98B5B7F09 (void);
extern void SignedData_set_HashName_mFDAB9AC05B7FDB00F646E3E44D4E9FF91CD0B4E7 (void);
extern void SignedData_get_SignerInfo_mAE14D45F3CE393345D41AD231A382765E551D604 (void);
extern void SignedData_OidToName_m910B0AD432C05A196504A1933374C93086027DD1 (void);
extern void SignerInfo__ctor_mD8D5B3BEC47BCB44044A801365EE363D24922FE3 (void);
extern void SignerInfo__ctor_mDB114E56EC2F9D5820B2F48A00CF14972B3BCCED (void);
extern void SignerInfo_get_IssuerName_m30347CCCF2ED4EF05618DD9AF4F71030E4A5A324 (void);
extern void SignerInfo_get_SerialNumber_m6B855067A7C4CEF6758805C18E8417729BA62BD7 (void);
extern void SignerInfo_get_AuthenticatedAttributes_m52500C3AC3793C89F0296BBEF64698F2ED20B8F4 (void);
extern void SignerInfo_get_HashName_m1FCB2FBBA2DEC8F246B2ECE65997A2972B60B424 (void);
extern void SignerInfo_set_HashName_m5C681833EF9E8380311B609BE25BE33911140E37 (void);
extern void SignerInfo_get_Signature_m6FA43681D705A53BA0857AF55624483A9AE94CA8 (void);
extern void SignerInfo_get_UnauthenticatedAttributes_m718FE21E74C9898E7B4060A5BE1264C68D3171FA (void);
extern void SignerInfo_get_Version_m0CFF6A1B831986936100F7F4A27D6386D65CFB37 (void);
extern void SafeBag__ctor_m0AE5FFE8783310FAC265D1A911F5F6FA6A1CE9BF (void);
extern void SafeBag_get_BagOID_m1218C83BAD5C56D41AFF31B58784848A27A265E6 (void);
extern void SafeBag_get_ASN1_m699A375228725122D8B9EE455013B215FBB87D05 (void);
extern void PKCS12__ctor_mC4CC29A25D40F4799AB2715A3C7FB270C74EEFDD (void);
extern void PKCS12__ctor_m77EA8CF7C0DD214E25C7C8B9580339F1E2134E31 (void);
extern void PKCS12__ctor_m2C6FBB30C343DA9D9E1753FBB72F01AD4E1F7FFC (void);
extern void PKCS12_Decode_m750703D9B730F5A8A355252F92DCD2D039C3FCDF (void);
extern void PKCS12_Finalize_m123C72042CF1A6A425005642408FF60B04D99462 (void);
extern void PKCS12_set_Password_mB7CC3D7C9B194C6CF6E82B289EB1FD98EA002646 (void);
extern void PKCS12_get_IterationCount_m2034EE21157689975AE4DC6E1F6B8628DB4539B4 (void);
extern void PKCS12_set_IterationCount_m3F155BE5145E6356037FCEB58E1811255F80A6D3 (void);
extern void PKCS12_get_Keys_mFF1C9F9DEF54D27CC472AA46DDBBE224F67DC46B (void);
extern void PKCS12_get_Certificates_m6FB30DF4377FE4FC725153AC237CBC59786E719E (void);
extern void PKCS12_get_RNG_m3C2B6A93761E38174AAB889781AB78E0A3A5B003 (void);
extern void PKCS12_Compare_mB111BD413CF9B26BBFEA519F7AFE6531BF5E2C1E (void);
extern void PKCS12_GetSymmetricAlgorithm_m10BEA3D381B4DF46D904219E1CB9297E8D7CD439 (void);
extern void PKCS12_Decrypt_mDA2EFC82AEC5616D5E0CBCF64315B9E9523FF02A (void);
extern void PKCS12_Decrypt_m9734C9729FDAD344DBE41EC3E3641F34F8F5A2B1 (void);
extern void PKCS12_Encrypt_mFF1143A709115E5239D21DB9BB2BA795AC16B504 (void);
extern void PKCS12_GetExistingParameters_m2350C6BD29DDCE4EACCCDE545B7621624FCF90F0 (void);
extern void PKCS12_AddPrivateKey_m9387CE382E87E7C38F49ECFC7D96078B7FB4463F (void);
extern void PKCS12_ReadSafeBag_m8DDA9FAD730EE4A90C18689EFB0837AEEAE492B9 (void);
extern void PKCS12_CertificateSafeBag_mC0BC2105B927BC138FB90EEDDEA95BD8A8DA024A (void);
extern void PKCS12_MAC_mBAD3C0B24FD8CA6FFE0F4A56CC29BE3E0D6F68A4 (void);
extern void PKCS12_GetBytes_mDB4325D82327A46832138F16A8CBD3AA006B3A7F (void);
extern void PKCS12_EncryptedContentInfo_m670295BC933FE6D756A18DEC50779443A1A9FB7F (void);
extern void PKCS12_AddCertificate_m11EC4FA6E1AF1BCEE6483B5601225CD6A043D183 (void);
extern void PKCS12_AddCertificate_m19F5576C596335EB6DD4E737D0CD4701884D91EF (void);
extern void PKCS12_RemoveCertificate_m65A709EDC4D5409904E14C738840252177D5E589 (void);
extern void PKCS12_RemoveCertificate_m3A6B9EF29379012C8495C7A71332C93EA4733D29 (void);
extern void PKCS12_Clone_m50D19F50BDEC33F80386C00F4BA4A409D37297C9 (void);
extern void PKCS12_get_MaximumPasswordLength_m1254971BF3342F220F85AA11F42433D1BD4E86A0 (void);
extern void PKCS12__cctor_mA93112C607BE4C95CC48AF6B6F03C866B349F6B5 (void);
extern void DeriveBytes__ctor_mE49888D87CB6AA78648DF0D8129838DC91CF891A (void);
extern void DeriveBytes_set_HashName_m8C49265C77E164C659E6B11840289B0BFF7A0AA2 (void);
extern void DeriveBytes_set_IterationCount_m0C50A49409C1B40DB50FA5123C89B2EF10C76E1D (void);
extern void DeriveBytes_set_Password_mE6A98520EA50E2A62428C9A8850D1C05B2A7FAFC (void);
extern void DeriveBytes_set_Salt_m9B502480F116A9B921146E849553EFC60DB94A7B (void);
extern void DeriveBytes_Adjust_m39BE20F4725D07DCBF711117748BE7AD654BE568 (void);
extern void DeriveBytes_Derive_m4CF08A181B9E46EDB98691BB4C6D00BD75D08EE8 (void);
extern void DeriveBytes_DeriveKey_m52FB070FC8822D470918BAE70E27A5C57CDC662A (void);
extern void DeriveBytes_DeriveIV_mDEECB1C384C67AF716AFE91FC210FC496B3334FE (void);
extern void DeriveBytes_DeriveMAC_m99726BCF64068E24CC03C4258AFF6831E235D774 (void);
extern void DeriveBytes__cctor_m6F7601E9D1537AF9EFBFB25CFD8C9F90D6FD8C25 (void);
extern void X501_ToString_mAC7577F741B34152BF7C5C4DD079CF62DB2FFF32 (void);
extern void X501_ToString_m4C3921DEC65010E4B8A225C0982C382B1ADF0399 (void);
extern void X501_AppendEntry_mE65E502CDE90C1957BFE3C9362DFCEF9B0597553 (void);
extern void X501__cctor_mF9E1961695BFF008C8CDADB7A357965E20024FFA (void);
extern void X509Crl__ctor_mE8AE92D0023939007B09AA12D066EBED204CD674 (void);
extern void X509Crl_Parse_mBFF8B04C60CD9047169F7F3CB5E9026D66B266B4 (void);
extern void X509Crl_get_Extensions_mC3F9AF029FB2AA579EA6E940EE7827BCDA2614DF (void);
extern void X509Crl_get_Hash_m4A8044B7D2C67FD6C32DAC39542CDD42B3F2C9C0 (void);
extern void X509Crl_get_IssuerName_m88AADDAC80012372BE645AA7CADE49FE090CCB68 (void);
extern void X509Crl_get_NextUpdate_mA171254E41B8FD9672F8767B1C65588F75DAF072 (void);
extern void X509Crl_Compare_mB585F4CCA3B1FAD5815FDA71976D5FC6842BD46E (void);
extern void X509Crl_GetCrlEntry_m824AEE6406406298D8740660A24F87CAD854936C (void);
extern void X509Crl_GetCrlEntry_m93093BA86976CEC62DFFDB28FFFE2353ECE35BB1 (void);
extern void X509Crl_VerifySignature_m91D8FC43DAB9C5688E85FDA94C176AF73E665389 (void);
extern void X509Crl_VerifySignature_m68859DEF40F3BB35F9DC79A025FED8981E0CE800 (void);
extern void X509Crl_VerifySignature_m4FBC30F86E1BAF545D86876CF90C9105A01D8553 (void);
extern void X509CrlEntry__ctor_m1CE31E179C47AA73774CE7C57CA24C879741EE81 (void);
extern void X509CrlEntry_get_SerialNumber_mC1CD9010A26A5983B2DDD1A459FDC15619107478 (void);
extern void X509CrlEntry_get_RevocationDate_m46DE2560B090EC69ECD7B9D819297E544C5386DA (void);
extern void X509CrlEntry_get_Extensions_m9756808EA9F465E76B8FC88417819B051538BB3B (void);
extern void X509Certificate_Parse_m52B3A2D924074F675F699C569C548DD521D5CF81 (void);
extern void X509Certificate__ctor_m4F437D7E76C6CCB0941AB5801946076C82583946 (void);
extern void X509Certificate_GetUnsignedBigInteger_mCFCA46D7C07D96A6B33AAB09A68BEA6E653E4BD3 (void);
extern void X509Certificate_get_DSA_m661EA8FA79C2108403B685495B961422BC531AA6 (void);
extern void X509Certificate_set_DSA_m35B061538E1BA0B2492CA50F46092FB3C48927B8 (void);
extern void X509Certificate_get_Extensions_m74416980E0AF90FAD180B10AF4BAE2727E3C6EA6 (void);
extern void X509Certificate_get_Hash_m116CA9E5DFB48CE5464E79C1941A0E78F20EB1F6 (void);
extern void X509Certificate_get_IssuerName_mAB4FC4E65427A67DBEA87A6182336AF6870D900A (void);
extern void X509Certificate_get_KeyAlgorithm_m7538B572B9ACFCE48CAD7B8B2F5DF0EA3299D4DF (void);
extern void X509Certificate_get_KeyAlgorithmParameters_m198AAE45BF470804D29A79EDF6CC0EFB4DF8A247 (void);
extern void X509Certificate_set_KeyAlgorithmParameters_m61198F54A91C2FDC7175E12C2E77E30A77D8E1D8 (void);
extern void X509Certificate_get_PublicKey_m5504C97766FD6A8B0AF72CC6255A09A7397C819C (void);
extern void X509Certificate_get_RSA_mB74DAA8EF4132B0917C236F0F2617D95F23005E8 (void);
extern void X509Certificate_set_RSA_m92B549DE372ABD58211BE846490719E5511D6D9C (void);
extern void X509Certificate_get_RawData_m322C38993C6E083A2FB1425C2E4D846FF905472E (void);
extern void X509Certificate_get_SerialNumber_m851B2E32EF2F46323798CBFD8450E4B8A82F264C (void);
extern void X509Certificate_get_Signature_m9B024ABD17D5320621904317E66A36BCC2B0FA46 (void);
extern void X509Certificate_get_SubjectName_m75A5BA36DC4F00694DC5F3797EA89F04C095A48D (void);
extern void X509Certificate_get_ValidFrom_m735D09A81A642542E2509EDED48399D671FE2CE9 (void);
extern void X509Certificate_get_ValidUntil_m58C8212E0E011376733C1DE57F78796483815AA7 (void);
extern void X509Certificate_get_Version_mE2DE4C0948C42ECECCA2C1B87F1C2524C2E10128 (void);
extern void X509Certificate_get_IsCurrent_mEBC52E38B22A633B0047BAA006F2446905473BDE (void);
extern void X509Certificate_WasCurrent_mA995BED12113288539D2BAB184B16AEF38844CEC (void);
extern void X509Certificate_VerifySignature_mE51967C4A00FD61035443B3E31358F40A1719AA1 (void);
extern void X509Certificate_VerifySignature_m7FCB318B8E4049EFDD072092C0EE96A552DBF417 (void);
extern void X509Certificate_VerifySignature_m2CB811C31D526CF4ECDEC02E59297A94DE8826AE (void);
extern void X509Certificate_get_IsSelfSigned_mAC256ADBF25A2572AAA95F126D3A466E1E3D00AA (void);
extern void X509Certificate_GetObjectData_mD8D5BD0C35EAB48243C4330A842755738B1CD9F3 (void);
extern void X509Certificate_PEM_mE33D5467323808200102789409F34F31DD037306 (void);
extern void X509Certificate__cctor_mD9016D839CEC21325002B8084A426A59693BB6EA (void);
extern void X509CertificateCollection__ctor_m0267958C4891BC9525E8E9A21A0CE4076205B457 (void);
extern void X509CertificateCollection_get_Item_m68CBCD6C145B7EC4E4BDF6BA208576976385A538 (void);
extern void X509CertificateCollection_Add_mBCBAC33FB2690C8D03BCB29FDD1A8D4CAF87F1D4 (void);
extern void X509CertificateCollection_AddRange_mE0BA8C454C23FDBF0247A2EE7D60D4E51B1656D3 (void);
extern void X509CertificateCollection_Contains_mEA5C9F87BCF0425AD8138CC821624B89EDDD30EE (void);
extern void X509CertificateCollection_GetEnumerator_m602E7163983BF2C8F8B5C09652D5E74771969B56 (void);
extern void X509CertificateCollection_System_Collections_IEnumerable_GetEnumerator_mEAAECD2F051D47457F594F88E7C8BF46AAFE2363 (void);
extern void X509CertificateCollection_GetHashCode_m56991888E2D575A6B43E41F227F00A22D9260A05 (void);
extern void X509CertificateCollection_IndexOf_mA6F3226C3883B9111BFE552A616B8EAFCBD8B2EF (void);
extern void X509CertificateCollection_Compare_m3569EC2D68F7500E898AC53B8A3826DCAE939B5D (void);
extern void X509CertificateEnumerator__ctor_mC58E29412F64C3ADB016F51920CD5AEBC0B4A82E (void);
extern void X509CertificateEnumerator_get_Current_mA12833D7DAE7B1E3FB58D8596D266CA30A6BCD69 (void);
extern void X509CertificateEnumerator_System_Collections_IEnumerator_get_Current_m8323BD8E1C9E4C6F2E57EDB8FA6BFCB396C03D14 (void);
extern void X509CertificateEnumerator_System_Collections_IEnumerator_MoveNext_m3619B756D91F5E808E1D9C7F2A9C5ADCB4214C80 (void);
extern void X509CertificateEnumerator_System_Collections_IEnumerator_Reset_mED5725981288EFF1B4F7F6CFEE1F9DC6A7C69F20 (void);
extern void X509CertificateEnumerator_MoveNext_mAB0C32FB96AD574439B87E0E7D2553CBD7DF37C7 (void);
extern void X509Chain__ctor_m5220D6FE9477D3D63B902475BDBFDC3CB63B3A79 (void);
extern void X509Chain_get_TrustAnchors_m988C77FD4F945D35E2D846CA169141CA56756681 (void);
extern void X509Chain_LoadCertificates_m5EA569474D1608E1292B8CF39917841C20278825 (void);
extern void X509Chain_Build_m664E7F7A48510E8F5D538A932D1D5E6EC0095C33 (void);
extern void X509Chain_Reset_m55EC23EC313B197F439649DF941228B8D799645F (void);
extern void X509Chain_IsValid_m38DBBC21B1224A7302D7A82720352CFDC0B16CB9 (void);
extern void X509Chain_FindCertificateParent_mD6853ABA9E077A8C1B9F0F67FBEE7482A3D10325 (void);
extern void X509Chain_FindCertificateRoot_mBD78F5BA569075F991BE2A4BEAA8AEC157DA450D (void);
extern void X509Chain_IsTrusted_m589BA3644A2F6BB355CF1EA7B168E1828257E5F0 (void);
extern void X509Chain_IsParent_mE63AED9211A8A4CE11B0A5305F553403234C5282 (void);
extern void X509Extension__ctor_m5B747C6BA6D46A064A6B1523D6A7A54E0EB140EB (void);
extern void X509Extension__ctor_m655F9DCB2706C0574ECF0D7CC3DC2CB91BE80FF8 (void);
extern void X509Extension_Decode_m6D6D2679F86BAC039665B67771CCA784915A6FDC (void);
extern void X509Extension_Encode_m94400983A2BD91C905232850DB45B21D535C7FC3 (void);
extern void X509Extension_get_Oid_m234B001E81AAE28787443F391F00C72D7521FDF0 (void);
extern void X509Extension_get_Critical_m5EB6ACF6B4FE3FCCF30C4EA477361DD10DF72656 (void);
extern void X509Extension_get_Value_m4852A46DF3018862BCB63F9ED94C2483DB5668BC (void);
extern void X509Extension_Equals_m310018B67B736E83554F698D4377D3850960606E (void);
extern void X509Extension_GetHashCode_mED3B0BC75A49EEF7C13AF9BE6C54855CFE2BE024 (void);
extern void X509Extension_WriteLine_m07945F4C60FA5F7A7AEFEDF6413F63BCB73A28A7 (void);
extern void X509Extension_ToString_m3F1D37C68357CBF1AD086FBE6839CC4516EA1782 (void);
extern void X509ExtensionCollection__ctor_m3D13DF7788DD26AE9A0E8F01FCDA5DC26381C7C9 (void);
extern void X509ExtensionCollection__ctor_mA36C635BC1A08C7FA3E1E43E94226F208C98E892 (void);
extern void X509ExtensionCollection_IndexOf_m199A01C1E4623E410C326AFEA184ED2D2E0CBEB6 (void);
extern void X509ExtensionCollection_System_Collections_IEnumerable_GetEnumerator_mCDF956BE91129236725221A834C635FBD093CD97 (void);
extern void X509ExtensionCollection_get_Item_mD4FA6BBB2423E92B6315410E4C1E9C467BCD93BC (void);
extern void X509Store__ctor_m8BEE6CD856A9E81B3953DAC2B491C101E669FEDF (void);
extern void X509Store_get_Certificates_mCC4F7B25DDC42CA505656FC1F9EB239925FC7422 (void);
extern void X509Store_get_Crls_m1AF25E9FC526E23F8B7266CF2465C6A6CFB8B73E (void);
extern void X509Store_Load_m6552D5345CC28C527318EC2E4517FCD1BEF62C5E (void);
extern void X509Store_LoadCertificate_m967F101849658106BFC35A16C02E94C8EC3B8B73 (void);
extern void X509Store_LoadCrl_m8021617FF1675FE822613E822B08E9F41461E28C (void);
extern void X509Store_CheckStore_mD370453EBECDAD77FAC283E9F99F5CC6BC0FE866 (void);
extern void X509Store_BuildCertificatesCollection_m32DDAF019B82B6F1BB94E8803538940502F313C6 (void);
extern void X509Store_BuildCrlsCollection_m88F1863305698A5E11DF747BCC6606EDE1DD1636 (void);
extern void X509StoreManager_get_CurrentUserPath_m3095B89B45A1567C8BFE71155629FABEFAD9CA9A (void);
extern void X509StoreManager_get_LocalMachinePath_m744CCDFEACC98BCEB7CA6CD0EC66C83C97FAEE6D (void);
extern void X509StoreManager_get_CurrentUser_mD931DB9B361AEC4B577C66405552E9E4C119FB57 (void);
extern void X509StoreManager_get_LocalMachine_m3409106FF4A09DCFADCCA71094DA2193CB543B81 (void);
extern void X509StoreManager_get_TrustedRootCertificates_m5D1C703644033D5F17F09DDCE5E4DA980AFF2D72 (void);
extern void X509Stores__ctor_m4F280AF2E389D2220F3A12601EAFB1AB89D30D18 (void);
extern void X509Stores_get_TrustedRoot_mEE0DF930C63F042A2C953BB22544517E6E338042 (void);
extern void X509Stores_Open_m698AC1CCEC5ABF81319C2C85D6760FC5DACC9371 (void);
extern void AuthorityKeyIdentifierExtension__ctor_mBB385C9A2A0B68770410545DF038A9D07F77BD50 (void);
extern void AuthorityKeyIdentifierExtension_Decode_m3AF4A819293A215392229CF0BC19F04E40790560 (void);
extern void AuthorityKeyIdentifierExtension_Encode_mBAA9D48407882B6297213AF5B5E3AAA602D67AF9 (void);
extern void AuthorityKeyIdentifierExtension_get_Identifier_m02042068ED75CB7EF4E3A491A15A6B9DCDA44EC8 (void);
extern void AuthorityKeyIdentifierExtension_ToString_m2B2DC5EB6E9A693509A2FAF2700E91C62AC340AA (void);
extern void BasicConstraintsExtension__ctor_mEBC34AF4DC736BB726ECC8C063E686A6E6707CCC (void);
extern void BasicConstraintsExtension_Decode_m9EE042A5689B98D6657E45B453A216579C6391A8 (void);
extern void BasicConstraintsExtension_Encode_m89859CE03C9F471E1ECD94F2A88246872D419AF4 (void);
extern void BasicConstraintsExtension_get_CertificateAuthority_m5BCC384C91B4A0B11CA41B7C12148457A0B56549 (void);
extern void BasicConstraintsExtension_ToString_mBAA34F73F2A1FFE3B50A367760D4C22C31725EE6 (void);
extern void ChallengeResponse__ctor_m69113B692194F152705FB0B10D5F5DE3C9E946D9 (void);
extern void ChallengeResponse__ctor_m0FF7511212760424A602D3EA720303764373341F (void);
extern void ChallengeResponse_Finalize_m613FCBBBBAAFB1C4D08D287CAD2C4EBFC1E62980 (void);
extern void ChallengeResponse_set_Password_mC4D21D00DD2094E296AA66D435EC9DFDB326F1A6 (void);
extern void ChallengeResponse_set_Challenge_m99A9D9CEAD09286DAA811D78A80ECA7B67748753 (void);
extern void ChallengeResponse_get_LM_mBF82535F35B084711977D987CBE5743D44A35A46 (void);
extern void ChallengeResponse_get_NT_m62844C0A7C8B42B02BBB1B95AD3ACB463EE71D94 (void);
extern void ChallengeResponse_Dispose_m2A0E32CF84BA5E17353C06534E90F7313D5C8F9B (void);
extern void ChallengeResponse_Dispose_m869AC64D371E202E47894DD149BAEC83326A5F44 (void);
extern void ChallengeResponse_GetResponse_m802B1B862E6D746161952D582125FC41547468EF (void);
extern void ChallengeResponse_PrepareDESKey_mD974958EF21A6ECD302202460212B1D220926A2B (void);
extern void ChallengeResponse_PasswordToKey_m459F4457FF12282E9557CE2D3F01423F00FA7501 (void);
extern void ChallengeResponse__cctor_m087640CF225A11A6F063EDCC1470B4F06FF6818B (void);
extern void ChallengeResponse2_Compute_LM_m05059CE954885BEE744F4D0F70FC8A066DDAF88F (void);
extern void ChallengeResponse2_Compute_NTLM_Password_mEC2849F4AF5C32A184B90D1E4331B8A975D2FB2E (void);
extern void ChallengeResponse2_Compute_NTLM_m26F6F261A6549A1085470E394102682209BC787C (void);
extern void ChallengeResponse2_Compute_NTLMv2_Session_m475AE185287A964A5E43268BE11AFBD6A45C1437 (void);
extern void ChallengeResponse2_Compute_NTLMv2_m2017D1D91196AA084F54B10E655F070BD32CF7C8 (void);
extern void ChallengeResponse2_Compute_mA7BABB8542156AA829E4B028D35AE06715F2B3A8 (void);
extern void ChallengeResponse2_GetResponse_m0A0633C0D3455B6DAC1DA4BF83F35045123CB8C6 (void);
extern void ChallengeResponse2_PrepareDESKey_mCDB9A6F9E86ED033D753C386C5EA04C0D5E971D1 (void);
extern void ChallengeResponse2_PasswordToKey_mCD98B16AD98DB4D93228E9F189BC5AE62A306FFF (void);
extern void ChallengeResponse2__cctor_m6E5776BD08D8279CDFCAAC17E31226FA9A800788 (void);
extern void MessageBase__ctor_m1CB51503E88E16AC1808FCEBEBE3DE8BB50DC3D8 (void);
extern void MessageBase_get_Flags_m4A5BBB1791D770EE5B78682F35F7289627B96643 (void);
extern void MessageBase_set_Flags_m4BA4AA3CA1819FFDF10A8A5ABBE9A891BB31A95E (void);
extern void MessageBase_get_Type_m9A3BCA375EA72DEE93AC6814A2D042F12CE2189B (void);
extern void MessageBase_PrepareMessage_mA2ECCEC352A8A4B4CDE8A273E2CCED50C05AE2BF (void);
extern void MessageBase_Decode_m155B9651CFCE85CD9B3858FF0A6DF89800767391 (void);
extern void MessageBase_CheckHeader_mDC37298D388D893A6328373D7DA29B3CED469929 (void);
extern void MessageBase__cctor_m4A379515E61255CD84F2C47B3BD59A904E9F8831 (void);
extern void NtlmSettings_get_DefaultAuthLevel_mB3DEF764E195BA276BBB341A3A8618299085E752 (void);
extern void NtlmSettings__cctor_m8430E34838ED4F723A656C609D8737BD4A900027 (void);
extern void Type1Message__ctor_mEB49FA35D05F01172DF3C164F97163C277AEF914 (void);
extern void Type1Message_set_Domain_mE06516CC741E4D12787911A9F1BCE92047DBA316 (void);
extern void Type1Message_set_Host_mAB5F72A7D281D31E9F9EC69B7D7518762AEE6CDA (void);
extern void Type1Message_Decode_m6B6B1FAD887619D49C8CC60ABA5F444056059741 (void);
extern void Type1Message_GetBytes_m8CAB2AE6CECDB5641EFD51A01649E75BB51DA1D1 (void);
extern void Type2Message__ctor_m7A630E10D6C79B19A67A0068102655D998172E69 (void);
extern void Type2Message_Finalize_m212EDBA6307D2A6469A1755E084959C4F416D132 (void);
extern void Type2Message_get_Nonce_mA371E0DBB0EFD643493BDE7AD6593DB3B64F11E3 (void);
extern void Type2Message_get_TargetName_m2D376951E07D21EB9CF72CECDC877DF4E4FB077D (void);
extern void Type2Message_get_TargetInfo_m26739FCB3FF9F56A86779C9A4B759FF3AFC03746 (void);
extern void Type2Message_Decode_m4DC375D0224F5F7A9C1205ED4CC51650D0F3521A (void);
extern void Type2Message_GetBytes_m7C4E5D7F49EF6535DD53262395F446ED6426F3F7 (void);
extern void Type3Message__ctor_m3A46F07DA05999471A34A64EB248160225F06768 (void);
extern void Type3Message_Finalize_m3367B5EC62552132F0F6779EC3A2590FB143D2A1 (void);
extern void Type3Message_set_Domain_mA29F199E40EDFDB8CBEC22F50925889A1F8510E0 (void);
extern void Type3Message_set_Password_m04828A9F18AE9301885B4BD3C1F7F151DE63B300 (void);
extern void Type3Message_set_Username_mE76160F96BD8EEFF971C3E04B2AD2579080E3BF8 (void);
extern void Type3Message_Decode_m8066F0309CECA25CD455B91432F3679BB59B448C (void);
extern void Type3Message_DecodeString_m44283D90E42F25A5F5ECC66AEBBFBA9306725B25 (void);
extern void Type3Message_EncodeString_m725085621A1177AD700EB63AA81FDAF9D04B2732 (void);
extern void Type3Message_GetBytes_mBC207BD47ECFF12866B4786766066A4D9D146E87 (void);
extern void Alert_get_Level_mA4A4833EBDC2FBFA4EA36634126A3E3C6EF4703C (void);
extern void Alert_get_Description_m704CDCA746F96A89493B7435D2F07920A1B7F3C5 (void);
extern void Alert__ctor_mEF460A440F5C6CBD4B90FB0CD2665A73BFCE345E (void);
extern void Alert_inferAlertLevel_mCA51E78E9C51F283831F685C1CECA4FD20087043 (void);
extern void Alert_ToString_m000BBF1E597BF854C362203244F8E2DC4EC7045B (void);
extern void ValidationResult__ctor_m4CAB4B31530A8CD62E46F8B3FAC6A36A4E615D5D (void);
extern void ValidationResult_get_Trusted_mC74B0DA857D9879C6A7428DB31DEA8A2DCD9DFF2 (void);
extern void ValidationResult_get_UserDenied_m8714FE685A3F0214EE05C59AFE8ABC71CE614E0B (void);
extern void MonoTlsConnectionInfo_get_CipherSuiteCode_m413E7FB4252CE0D6C904D722107F0A4B59C79B08 (void);
extern void MonoTlsConnectionInfo_set_CipherSuiteCode_m3BC21FD10B760939C96F6FBDB39DFEADDE50CCF5 (void);
extern void MonoTlsConnectionInfo_get_ProtocolVersion_m12ADA52C235AF895D9462B2B4820EEF8325505AF (void);
extern void MonoTlsConnectionInfo_set_ProtocolVersion_m166E8E558B961F343E719D333A892E597AB17404 (void);
extern void MonoTlsConnectionInfo_set_PeerDomainName_mDCAB6395BFE1BEF3ACEF680AEA6A3FA4DDEA2A97 (void);
extern void MonoTlsConnectionInfo_ToString_mA9F63B760E6919F545A716A7619F8786CAA7C38C (void);
extern void MonoTlsConnectionInfo__ctor_m795EBB319F254C648313E21E85ABD2A4CBE4F93C (void);
extern void MonoRemoteCertificateValidationCallback__ctor_mEBD94D46C068D03E0F3A827F0875FE424C03B22A (void);
extern void MonoRemoteCertificateValidationCallback_Invoke_m8BE32AE7FE1BE89EFB6A49559431916F785D2BD9 (void);
extern void MonoLocalCertificateSelectionCallback__ctor_mCA81824D698BD5808E501A9AC4DA99758B69D3FC (void);
extern void MonoLocalCertificateSelectionCallback_Invoke_m32D010A24184A7BEEE6191B19F04E2E8AC8C3CEE (void);
extern void MonoTlsProvider__ctor_m65BF846CE616D13609A5EFB2F42AE03A38E5CB8D (void);
extern void MonoTlsProviderFactory_GetProvider_m8684E3A1AFB043FA00DEC4BCF95F8B288C136936 (void);
extern void MonoTlsSettings_get_RemoteCertificateValidationCallback_mE07825B4A75DAE2A4BB5037D504A36311814446C (void);
extern void MonoTlsSettings_set_RemoteCertificateValidationCallback_m6CEA8A6E38C85A96C2D26613407C13DD4F965C87 (void);
extern void MonoTlsSettings_get_ClientCertificateSelectionCallback_mCFE63487D867109AD1AF856ECC8BA0996C0AA605 (void);
extern void MonoTlsSettings_set_ClientCertificateSelectionCallback_mB404DFD0C0475254CC129740A472D6D9615C56FD (void);
extern void MonoTlsSettings_get_UseServicePointManagerCallback_m11F6CF11F844ED2F6D54497CBAB18E04E6AFD754 (void);
extern void MonoTlsSettings_set_UseServicePointManagerCallback_mE3EBA2F2886B024A4ACC303EE792190A5784E18B (void);
extern void MonoTlsSettings_get_CallbackNeedsCertificateChain_m02260798D928BDA7F6D9A2356B7CE688650BF176 (void);
extern void MonoTlsSettings_get_CertificateValidationTime_mB7E248DE19CACA783A465F59859729C726DF2F9E (void);
extern void MonoTlsSettings_set_CertificateValidationTime_mE632C0A1BAB7608158B8EF05D2122B87AB6C34A6 (void);
extern void MonoTlsSettings_get_TrustAnchors_m4CA73EEBF73B4344C5334F3BC5BBFE1BC27BC7AE (void);
extern void MonoTlsSettings_set_TrustAnchors_m08A22767288F1FB5AF812D0FDE8ACCDA47116661 (void);
extern void MonoTlsSettings_get_UserSettings_m9ADCEFD59E6429580D053F5AC0AF492B59353920 (void);
extern void MonoTlsSettings_set_UserSettings_mE1E6ED54BB439AE8012FD17D7230515CAB11D55D (void);
extern void MonoTlsSettings_get_CertificateSearchPaths_mD6BE6C9347446D742B29CAB987AEB76FF845A050 (void);
extern void MonoTlsSettings_set_CertificateSearchPaths_mD7846BCF7B9292F85FEF0BE4A671779AD6C9AC32 (void);
extern void MonoTlsSettings_get_SendCloseNotify_m1D35BCF28142455EC9CDF16CA36A8DEF57B05625 (void);
extern void MonoTlsSettings_set_SendCloseNotify_m903943162225A7267CA3AD1E1E6E76EA71BCC5BF (void);
extern void MonoTlsSettings_get_ClientCertificateIssuers_m6FB58C63262D146231256A969DC2F3B60CE5C498 (void);
extern void MonoTlsSettings_set_ClientCertificateIssuers_m0866718DAECFD44FC53370D23AB4C789CE0759B8 (void);
extern void MonoTlsSettings_get_DisallowUnauthenticatedCertificateRequest_m15B7CAB13F2301D201AAD092DF2FCB49366ABDF6 (void);
extern void MonoTlsSettings_set_DisallowUnauthenticatedCertificateRequest_mCC9D044ACAD032758F91CC5A3AD1E69F8C5C7FE4 (void);
extern void MonoTlsSettings_get_EnabledProtocols_m6368286BF3E5703DB56F0EDE7B6AD2EF9C98D4C8 (void);
extern void MonoTlsSettings_set_EnabledProtocols_m41991C8F073B85156818A906815AC4AE5623E77D (void);
extern void MonoTlsSettings_get_EnabledCiphers_m7BD72B78EF53FAF51FEEBF5B3657187EC876394A (void);
extern void MonoTlsSettings_set_EnabledCiphers_mA89F7C73A29959710230593DCC689F4B9860156A (void);
extern void MonoTlsSettings__ctor_mE939325F3FC3A8950048CD299F395C8C823EA925 (void);
extern void MonoTlsSettings_get_DefaultSettings_m336A2267A1C8F1FD54496A76F319AFF3BD9A684A (void);
extern void MonoTlsSettings_CopyDefaultSettings_m4B0A3E8B7D106FA7F0D243FB2A0A4B115CD21942 (void);
extern void MonoTlsSettings_get_CertificateValidator_m2DC79DA38E7CB5E87A21E315A29F21E1D9ACF367 (void);
extern void MonoTlsSettings_CloneWithValidator_mF20535B6DE43DD45FAE2ECC6871C85C15F260D6B (void);
extern void MonoTlsSettings_Clone_mC4F9A27889ADD0B275018B32BCDA67C30865EA7D (void);
extern void MonoTlsSettings__ctor_m93F7300159A30C4B08C4A9A9B0C0CAA6B16E0D6F (void);
extern void TlsException__ctor_m343EBA01616A2B8649875A19BE4E56BBF46C6D81 (void);
extern void TlsException__ctor_m4E03D70F9BCA4C0CA737720A43ABAB696CEA6209 (void);
extern void CryptoConvert_ToHex_m1A0AD4D32CEEC47D3C60CB2E4D05A935C62F261A (void);
extern void MD2__ctor_m490777410D02CD92B398D20C6A2DAE7448A00E8B (void);
extern void MD2_Create_m63F8C14C0F090FA57982467993C7794C50E5974E (void);
extern void MD2Managed_Padding_m661705CFE5C0683685E84E1CD503326477C7138F (void);
extern void MD2Managed__ctor_m4E661935AEFB98EDCB3EEFE7D6EECAD10C807A2B (void);
extern void MD2Managed_Initialize_m578DBD3B8265ABD7FE060D4DD958EA986301698F (void);
extern void MD2Managed_HashCore_m2B4BED309AC3C80A6E8D14ADDE4BF9AC9CC266F6 (void);
extern void MD2Managed_HashFinal_mDB2FA7F5A7A8A4FEE85BD56B47801B12BCDFCAF1 (void);
extern void MD2Managed_MD2Transform_mCA2AD9EF038AC46E69F4726153B9C0DC03FC09D3 (void);
extern void MD2Managed__cctor_m93D5F30A3D7E6E3F97660073EB29207996C162F6 (void);
extern void MD4__ctor_m0E07C929BDAF3BB0CFD4EC360D22849566538CBB (void);
extern void MD4_Create_mBF86A7132FA1B49ABF8D4C91334E48FD0FCD3BE1 (void);
extern void MD4Managed__ctor_m89BE94F7DEB019FA53E6B1F46DCFB6B4CA36CD1C (void);
extern void MD4Managed_Initialize_mFC1E0B77E8CF24894ECF37C54EC3D3527E36DDFB (void);
extern void MD4Managed_HashCore_m41A5263D2F078402E912B70E7DD08B9D12085E66 (void);
extern void MD4Managed_HashFinal_m5BDA517A8D12C62F62C5EFBBCBC661DE9C198EFC (void);
extern void MD4Managed_Padding_mD7BB9A7A7F78490A4B8EC89A21ED240CB85E39F4 (void);
extern void MD4Managed_F_mD8312B8925BE2A23E1E9B1A13054A95D815F06C4 (void);
extern void MD4Managed_G_mCCB055CECFE7296853DF7DB9336A670549C8423A (void);
extern void MD4Managed_H_mA7897D3B979E6627EB9A44E6FB6C894D4B338910 (void);
extern void MD4Managed_ROL_mAEE08B95D3EA8EB7EF09EB7CC1E97CF6F3E1F31F (void);
extern void MD4Managed_FF_mA227BE16EECCB0BC298BA664D40D8703152D5AA0 (void);
extern void MD4Managed_GG_m57C28D2EE1C3AC5241943AEB683989331AFB2415 (void);
extern void MD4Managed_HH_m35ABC1BD17ADB7103B07BAE99ED070EA850FB197 (void);
extern void MD4Managed_Encode_m1E02CBE5A0E99DFF36299CCEAC13F92124422D55 (void);
extern void MD4Managed_Decode_m01D0E808B7F05B50EC6A1B146F5BE6AB909DA396 (void);
extern void MD4Managed_MD4Transform_m3BEBC5DA2F365EA60233EB3849D6B670AF44835A (void);
extern void PKCS1_Compare_m1193D66D416FE12B6F3ECF273FBB43648500250D (void);
extern void PKCS1_I2OSP_mE2B7F1991838515B88062B0291D4EE51BA983504 (void);
extern void PKCS1_OS2IP_m2908ED07FF72EA1243E2EDB4E4D276AD3A95F373 (void);
extern void PKCS1_RSAVP1_mFEBFDA793C8447C79CEAB87FD6B94488232FABD5 (void);
extern void PKCS1_Verify_v15_m00B9330B8AA9E623DF4EBFAEEE73BA062CC2BBA1 (void);
extern void PKCS1_Encode_v15_mA8D476701564761F876E7DADA3FAE6AE2458F054 (void);
extern void PKCS1_HashNameFromOid_m3BA0DDFAAF18E9B580510C2820919F71154B4290 (void);
extern void PKCS1_CreateFromOid_m6F0813F626FA58AC0DE23E144B04B2485868F706 (void);
extern void PKCS1_CreateFromName_m979F533ED6CD3CF2895D5D474A67069EB56912D7 (void);
extern void PKCS1__cctor_mA35BD1E24641E96C7CC67417DE31C90AFE4B23E6 (void);
extern void PrivateKeyInfo__ctor_m22D9C0E05AA5534D7874D9779D81BA12CB675862 (void);
extern void PrivateKeyInfo__ctor_mF8536B2938ED0774AEAFAC0D16AF8FAD636F904A (void);
extern void PrivateKeyInfo_get_Algorithm_m544D7A80C2BD7DE45C080DF09D88221A58DB9459 (void);
extern void PrivateKeyInfo_get_PrivateKey_m08A332D04C22BE19ED55E82B4D3804336502D13B (void);
extern void PrivateKeyInfo_Decode_m0418E893A9C18998D461DF559BEE133012C54B41 (void);
extern void PrivateKeyInfo_RemoveLeadingZero_mE1A64CA3FCB10714B91D673C958F8FC668FB6460 (void);
extern void PrivateKeyInfo_Normalize_m18CF37D456DAB5661BFF0F04A0DB4D39BCA79306 (void);
extern void PrivateKeyInfo_DecodeRSA_mB304590B0C8E7227CF6D1B336D67AA67276A5A24 (void);
extern void PrivateKeyInfo_Encode_m63E6B8B587EAFDC2A15D83D55F927A8DF927B0A5 (void);
extern void PrivateKeyInfo_DecodeDSA_m8F3B78E775F8D2D6F803B443CD0E97EF9A791E5A (void);
extern void PrivateKeyInfo_Encode_m62E27ACA0D0E69F1A221B06B6138D91A28D03880 (void);
extern void PrivateKeyInfo_Encode_mC9CBC8A5FE2F6CE759439BB4B3FE594AACB9D5F6 (void);
extern void EncryptedPrivateKeyInfo__ctor_mE231E160F0541E832A29C009CD95AFF578E51918 (void);
extern void EncryptedPrivateKeyInfo__ctor_m5CF5C6296CD6DA1A655FDEEF0701F4E6600E5D7F (void);
extern void EncryptedPrivateKeyInfo_get_Algorithm_mA143C07290B65E429244DBD3C98CD7E4F2FDA061 (void);
extern void EncryptedPrivateKeyInfo_get_EncryptedData_m0CE09A1279C5BD7DCFC30BD5D9D59921D7082583 (void);
extern void EncryptedPrivateKeyInfo_get_Salt_m9535A0F6F11ABF70C3DAEEBB61E437CB21042F61 (void);
extern void EncryptedPrivateKeyInfo_get_IterationCount_m0027F1C5D6D120B70924F3DFCDFAAA48C064F410 (void);
extern void EncryptedPrivateKeyInfo_Decode_m1C9F94B949283DE27932FACE345EE2C314982C85 (void);
extern void RSAManaged__ctor_m3DC50713376908C5D1B7EDE7662070DBC185597A (void);
extern void RSAManaged__ctor_mC9FD969A9C89A836300989CC0A3B20E40025D8BE (void);
extern void RSAManaged_Finalize_mFD07D39251303051B9DF8AF29AB6B4CF9FA7FDCB (void);
extern void RSAManaged_GenerateKeyPair_m3E0EB3C2ADA16EE8FE2A8AE7071333CB6795F55D (void);
extern void RSAManaged_get_KeySize_m17BA4753AA6967ADFBEBF49035158532F23DB7A3 (void);
extern void RSAManaged_get_PublicOnly_m12DE914F7BDDD2E850EDD7F34BA9CF3114C333BF (void);
extern void RSAManaged_DecryptValue_m30147A9ADDB9F2942133DB9699EA8834E2888B39 (void);
extern void RSAManaged_EncryptValue_mAED1DAF6891C6F20546DD23272193471BB495242 (void);
extern void RSAManaged_ExportParameters_mD63F6ED165F8CA27D9F2BBA4C7FDD232D6C2A53F (void);
extern void RSAManaged_ImportParameters_m585D1CB6F017A3FDA42FEFEAFD14029F8E41EBC8 (void);
extern void RSAManaged_Dispose_mE2C5CCB287A79986DFB8A3A2380E6666291C982B (void);
extern void RSAManaged_ToXmlString_m05B2E2182E8223F5242E8C0D8A0BF70750A9CB93 (void);
extern void RSAManaged_GetPaddedValue_m228CFE67E9EF6B3659E761E55D06AD18B6DFF1FD (void);
extern void KeyGeneratedEventHandler__ctor_m5B52F3B7F5A33F1A0BDC378D304EF8F681A1D8FB (void);
extern void KeyGeneratedEventHandler_Invoke_mD73F8C7BC0305037EC520C3F4AF3DE74E5481DDA (void);
extern void AuthenticodeBase__ctor_m0439483FE9A59BB7E27DE3A8D9EA1D1265BF968C (void);
extern void AuthenticodeBase_get_PEOffset_mC4BFB80BAEF28B712FD9048C85B3F20B3DC81C5B (void);
extern void AuthenticodeBase_Open_m6D97F062F4176730B6020ACAAD47ED4353B35465 (void);
extern void AuthenticodeBase_Open_mB75CCA77F829FC8F99FDFCDC639AE5ADA3B32A81 (void);
extern void AuthenticodeBase_Close_m1FA42AD55310BAF64DB00FFE6563ED06D44980E9 (void);
extern void AuthenticodeBase_ReadFirstBlock_m7F5A9F2A9C8FAA6C6685E0F3322E8D69E7324545 (void);
extern void AuthenticodeBase_ProcessFirstBlock_m3FB173D7CA309AA1119CFB86D5E4768DA1DCCC1A (void);
extern void AuthenticodeBase_GetSecurityEntry_m3690E0D6B4D14F2A8979F1BE0CCE6F7327C2E24E (void);
extern void AuthenticodeBase_GetHash_mF881D2926F2FABC3ACDA646842954F83A9434066 (void);
extern void AuthenticodeDeformatter__ctor_m2695F107A500029472C7B519B00F5F8F86918114 (void);
extern void AuthenticodeDeformatter__ctor_mE7D65775E1289E0DBB208034BE5EB40AB6469BA0 (void);
extern void AuthenticodeDeformatter_set_RawData_m0D90817618D3ED52A415A898F0037018A777D07A (void);
extern void AuthenticodeDeformatter_get_SigningCertificate_m5C675A2AB0464670DF6C557C4433871014EB52CC (void);
extern void AuthenticodeDeformatter_CheckSignature_mCC125D2B04750C3581E9C60593C4212BC54077F0 (void);
extern void AuthenticodeDeformatter_CompareIssuerSerial_m3EE0CDFEE995FAB1C96A8B358D15F1D5EB4F2413 (void);
extern void AuthenticodeDeformatter_VerifySignature_mEA4B444611069CAADC507D771CFBA1A571CD8973 (void);
extern void AuthenticodeDeformatter_VerifyCounterSignature_mCC865AF6E1CE3E6917FF161182D64EDCFE94DBCD (void);
extern void AuthenticodeDeformatter_Reset_mD15DE78C062103550A42BEE536E78F9526585B90 (void);
extern void BigInteger__ctor_m49DCBBD82981761C45EBC2350A1F16DB886E5F50 (void);
extern void BigInteger__ctor_m42B232CF6F76C15DAA3C02C3F5DE1B1F7C7BBC51 (void);
extern void BigInteger__ctor_mD26E0224E82674AFA9A6E4BF4F0674BB0B7BECBF (void);
extern void BigInteger__ctor_m8E3F4B6BB64E6F4A2FCB66402F58C192453ECB45 (void);
extern void BigInteger__ctor_m9AFFDE2505C2B712D5F89EF501A2D163B7855E02 (void);
extern void BigInteger_op_Implicit_m953448132663D58B45AF679B003D0BBC92FEBB49 (void);
extern void BigInteger_op_Implicit_mEC0736B08374B7B829F6FE6ED416186A9084EDE6 (void);
extern void BigInteger_op_Addition_m38EA8E365D315586FBFE76755898A8FF2854CD46 (void);
extern void BigInteger_op_Subtraction_m2017F0245B56CE2D6245757B43DDA997625581CA (void);
extern void BigInteger_op_Modulus_m614BB6B5B40C256436BB94EA2B8391C298260F47 (void);
extern void BigInteger_op_Modulus_m5AC5D8DD148908E30BA188BC50E6B2F0E75545CD (void);
extern void BigInteger_op_Division_m440FC499D6242819F93B60269F50546F85790C6A (void);
extern void BigInteger_op_Multiply_mEDE5A32D2745927133F814E397EA4E628A60C549 (void);
extern void BigInteger_op_LeftShift_m0450D740864BE2C62DFC1A0777498E518A2E6A73 (void);
extern void BigInteger_op_RightShift_m9ABBE9016A201283D5DF10CD67D5302510F86224 (void);
extern void BigInteger_get_Rng_m200DBFEDB3D711658BDB3796FE3B805956058A07 (void);
extern void BigInteger_GenerateRandom_m01DB8F5756F75D8B9810F25D871EC04D41069E4E (void);
extern void BigInteger_GenerateRandom_m5350D6E9C3AAE96153C46D80880F1CE43BB9DA9C (void);
extern void BigInteger_BitCount_m6FD831E1BA71E84748B08A7A5B1FFE9AB2C62551 (void);
extern void BigInteger_TestBit_m2EAAC171F87CAB5FEDA0DEC79A83A68E56762C35 (void);
extern void BigInteger_SetBit_m3E67DE35B0E691FCB886C60252CAAFC3FCB92A39 (void);
extern void BigInteger_SetBit_m60B275C4579C541B806FECB49EBBDA1579DA70BE (void);
extern void BigInteger_LowestSetBit_m6443AF5F1F7C864A65365DD731FC7DAC1495DE20 (void);
extern void BigInteger_GetBytes_mE1CA6C5DC5B8AA9014B4169BD55307040E668439 (void);
extern void BigInteger_op_Equality_m3D8FFD303A14BBC17B7423CE9EEC513700BF921F (void);
extern void BigInteger_op_Inequality_mDDDA5BA9BF336633ADBD56F97601ACF81C0133FF (void);
extern void BigInteger_op_Equality_mECF42AF9013ADE8D8274E7485C7BA23E4BFFEC60 (void);
extern void BigInteger_op_Inequality_m2AB984FD1240CAD1831070279D41DD97EB632DC7 (void);
extern void BigInteger_op_GreaterThan_m4102428737E020DEF999F9A2320F2C8962C4947C (void);
extern void BigInteger_op_LessThan_m5334E5102F45FBABAB12A790BB67FEC735A62C25 (void);
extern void BigInteger_op_GreaterThanOrEqual_m84F5223CE33D7E4B977B30D32E87AD7D5051DB4F (void);
extern void BigInteger_op_LessThanOrEqual_mFB395E4E35C3E672EB2B1D485FA9E621D769D68A (void);
extern void BigInteger_ToString_m02A324A776BF09756B1BB3B6BFF6DBD7EA98852D (void);
extern void BigInteger_ToString_m007BCE771B1D0C61761EF1F3D98B8C508EC6DF9D (void);
extern void BigInteger_Normalize_mC7C8E5FF4D3DE66DE886D541352D3A091D84DC47 (void);
extern void BigInteger_Clear_m1BFEB305789595395EEB68C5150A5331E0207323 (void);
extern void BigInteger_GetHashCode_mEAB87BB7A4882FEAF1D7D65B6713E31C0A6F07EE (void);
extern void BigInteger_ToString_m80A151A5EF5ABF1F5FF16660E78042B85241E3E9 (void);
extern void BigInteger_Equals_mEC32AE6F06C0C113291158D434ED2A92B07E6453 (void);
extern void BigInteger_ModInverse_m7E87B8BE468B504ED2BD533629D4E9089E5768E5 (void);
extern void BigInteger_ModPow_m534E775DEFD6DEDC75F71FA21492843EAA9CEC9D (void);
extern void BigInteger_GeneratePseudoPrime_m8DFE2FCECB4B1D3671A53358F702F00F359EFCCE (void);
extern void BigInteger_Incr2_mE5DCADCC1DEDD4F3E48E326940D3C926E1A37808 (void);
extern void BigInteger__cctor_mFA00C7DFC5EA828985D4858C25534222BC0F94B7 (void);
extern void ModulusRing__ctor_mA5C74C954C8ABD6F9843F41BBF197F7F00F8D00A (void);
extern void ModulusRing_BarrettReduction_mA753465B3A0DC2E9DEC32D345C24DCA1B56115D5 (void);
extern void ModulusRing_Multiply_mDBAD6831070EE1661E3B7EEC4786A515DE5C229A (void);
extern void ModulusRing_Difference_mE4B31BBB8F73710EC6AA7D3F87719672EF683AAD (void);
extern void ModulusRing_Pow_mE14DBD510D57E2A1CF1BFB2B237439A8EB08A418 (void);
extern void ModulusRing_Pow_m9F2BB905682A6999D33B598EE7A61755DC240C6D (void);
extern void Kernel_AddSameSign_m1ED361C045C7224DB71E4C049597E8FCD9B07B57 (void);
extern void Kernel_Subtract_mEA9B4A2546B4EBBEF7FAB54CD8F33F2283E08A5C (void);
extern void Kernel_MinusEq_m634A25656C7AD0F7C521B23BE30425B788030B91 (void);
extern void Kernel_PlusEq_mA092738CB4B501B664F314715C7C74FC03384AE3 (void);
extern void Kernel_Compare_mB84E41B7046C951E360FB9DA11A4BFA5A0E66611 (void);
extern void Kernel_SingleByteDivideInPlace_mD9E56BF746E318BEDECEC4280A829A63EAD28F09 (void);
extern void Kernel_DwordMod_m1B3830BC4779D5B4BEBC37E1BF320F500C20CC0E (void);
extern void Kernel_DwordDivMod_m5977404465381995E14EF36BE94500F6D18458B7 (void);
extern void Kernel_multiByteDivide_m32F2ED53F2DEC2DE630E0BDD5AA4A4BC462CD7EF (void);
extern void Kernel_LeftShift_mE40ED9013AB88A02A79C7640DA3FA1F320ED86F0 (void);
extern void Kernel_RightShift_m3EF3092A11D1C9680419B275A8349E56CC4BEC5C (void);
extern void Kernel_Multiply_mF725E33ADE4B9849626BB9F011C45E1EE7A5FBB7 (void);
extern void Kernel_MultiplyMod2p32pmod_mD9A826628E622F9BF45520DE5A6C0BD53DD2AD9E (void);
extern void Kernel_modInverse_m9AF7A04275E7EC3CB3BE97A0E49D303128551788 (void);
extern void Kernel_modInverse_m9B85AEE3C824216F039E1B5D7EFAE04F71D2A1BB (void);
extern void PrimalityTest__ctor_m73483F9E5D166F74E0340F479376C61D9280266A (void);
extern void PrimalityTest_Invoke_m7E9F9413908598A1270792B565D71288027AA552 (void);
extern void PrimalityTests_GetSPPRounds_mE93C23146823DD9BB19FF2588CACB3EA32D5E845 (void);
extern void PrimalityTests_RabinMillerTest_m69936AC3B0635D2E4145DCA9CD2A7E8F63B8CFC4 (void);
extern void PrimeGeneratorBase_get_Confidence_m8A53DA3C670504B629434C990508D4B77642B875 (void);
extern void PrimeGeneratorBase_get_PrimalityTest_m96C5E1866F96043982AF493BE7EAB5969F770E1D (void);
extern void PrimeGeneratorBase_get_TrialDivisionBounds_m706A348C994861A2B92CE9156FE20DCF7474E286 (void);
extern void PrimeGeneratorBase__ctor_mECF0CD5B964A7E6FCE4F504719164114B8A678E9 (void);
extern void SequentialSearchPrimeGeneratorBase_GenerateSearchBase_mD12A7AC0A052FA228E0F4918BBA1B2B59AD605CE (void);
extern void SequentialSearchPrimeGeneratorBase_GenerateNewPrime_m6AC834873702FE49B85FB261931CA31BC239FFCD (void);
extern void SequentialSearchPrimeGeneratorBase_GenerateNewPrime_m6599A58FA9EBD14FEB9D18073419FF8341365B8B (void);
extern void SequentialSearchPrimeGeneratorBase_IsPrimeAcceptable_m2682AEC2B91FC89D1EB0799BFE5DA4C0F7F8C1D0 (void);
extern void SequentialSearchPrimeGeneratorBase__ctor_mFE1A43FA14390E871ED0C9025B57B84D9A7AA754 (void);
extern void U3CPrivateImplementationDetailsU3E_ComputeStringHash_mC7DC26EF4301846E2947FBD7916A16E88C887055 (void);
static Il2CppMethodPointer s_methodPointers[513] = 
{
	Locale_GetText_m7531650AD8364720B8A7BADB48287D4E6FF9FCE8,
	Locale_GetText_mB63D530ABD106F4BC9C2BB3AC1A38EDDFC3188A2,
	ASN1__ctor_mA9AE2197367C1E13DBFDA67E0A383167F52CC114,
	ASN1__ctor_mAA538F9E1BE0DE739E9747BC3BC71DC030B018AA,
	ASN1__ctor_m950BFCCF44A987ACBA12142624AA222200EE503E,
	ASN1_get_Count_mBE45E73126FAD2694E9059CAC53B7AC9A5F60833,
	ASN1_get_Tag_m1984CF0DDF54424E61BA3650D93CBA0DCB58F232,
	ASN1_get_Length_mC03E8FE25B4BD7B97EA3D8591E01A457F491C7FE,
	ASN1_get_Value_mA6F9BE5AC19AC060AC42673C8FD5AA864EA046B6,
	ASN1_set_Value_mAFFA885810928715B379EAD478AA3961E8ACD589,
	ASN1_CompareArray_mA7A05EE692B23D6D9A7C0E5B8CCC4CDECDAC0A79,
	ASN1_CompareValue_m7397F4657555C6ACAF6622DE143C89E9E7593554,
	ASN1_Add_m4C61487A6CCF48D5CEB0D97B248FE31F9FCD849F,
	ASN1_GetBytes_m3B7DABFDBE6BF7F9C926E4C8A16FC6BE6D1CE67B,
	ASN1_Decode_mC4CF3CB2CC1DB454AA9C720BA79520956FB1F77B,
	ASN1_DecodeTLV_mD4465394202DA7B0D37B9453CDE039233969E9DF,
	ASN1_get_Item_mF105DA24F3BE9FA3697229CF99B1602B736B647F,
	ASN1_Element_m97C1D5E39B14AC29D2F7AB0226A6E6CA2952B7C9,
	ASN1_ToString_m4995F083B02F8FEF578ECA6EE73A257821F50A00,
	ASN1Convert_FromInt32_mACAC096211E525F124BE0D50D90524ADCB6EA198,
	ASN1Convert_FromOid_mBB920E2827A66620790C5E07549D74F244735C1C,
	ASN1Convert_FromUnsignedBigInteger_mFA9B30C2532C4E1909AE0CF5151544D857C941FE,
	ASN1Convert_ToInt32_m956785EB4A235575C21677C16D2F6CBE54787032,
	ASN1Convert_ToOid_mBCE4FD3970C556190FB00A6AD409A6ABB4C627D8,
	ASN1Convert_ToDateTime_m2D520694D39F2C86F72ECB24EF48F58B8C57AA75,
	BitConverterLE_GetUIntBytes_mED0A55F565721091E851FD6108E128C3CBCB87F0,
	BitConverterLE_GetULongBytes_m335C736CF3E43BF45AB110AD48E83777A03F4A26,
	BitConverterLE_GetBytes_mEEFE00015D501FBBD32225D9C45A2C2A0673E9C7,
	BitConverterLE_GetBytes_m1200CDE198D95D35643FBDDE226BC59D988BC44D,
	BitConverterLE_UShortFromBytes_mD0F7166ABCEED3D220DCE26EDF1F03680ED1C82A,
	BitConverterLE_UIntFromBytes_m22FF5D22A65B4C2F7D1A32BC7C28844187FCEE48,
	BitConverterLE_ToInt32_m9883205CC9360B9937EBB27051250198255F8D2A,
	BitConverterLE_ToUInt16_m9C9006FA48C5155275790F99EC5558AB440B4088,
	BitConverterLE_ToUInt32_m311817F60340C60EE9891E11C144DCAC2113BC8E,
	ContentInfo__ctor_m1B4B1B164D5159FE64E1346E1DE36B4E7B6F4078,
	ContentInfo__ctor_m95EA3D16DCE96C9504DFC08940380C6983F41036,
	ContentInfo__ctor_m7D3DD72DA482A68007DB183975E3F649EEE308CD,
	ContentInfo__ctor_mBC763695FB9DCE655DC9F64E932679635A8A803B,
	ContentInfo_get_ASN1_m3A32CA99ED50DDA79761FC779979489927D0ABE3,
	ContentInfo_get_Content_m85ECCB3CF732981E207598428EF008D95F754F98,
	ContentInfo_set_Content_mC885DF149B5AB25343766F00FB7749A77BEFBC36,
	ContentInfo_get_ContentType_m4ACE1C1CD7330D35EF0C2C422CF48E3BA0FFD7D1,
	ContentInfo_set_ContentType_mE3356914A7D44048391DCD32316D00C026415791,
	ContentInfo_GetASN1_m52EF51C772714542A1263FCABF334391397F08AD,
	EncryptedData__ctor_m355455E414AB2547674FDB62A72BFF4EE98BDDF1,
	EncryptedData__ctor_m5C149670A898E43CCCC28D6B30568138D4098E72,
	EncryptedData_get_EncryptionAlgorithm_mF1F3CE667E2558EC146D7086A64C5735DEB37BF4,
	EncryptedData_get_EncryptedContent_m28A275C350840EAD2C448DF76D9116D49000DC5C,
	SignedData__ctor_m4609283D3F9F231C0B5156FAE7C2DCF89A3AD7A6,
	SignedData_get_Certificates_m2F2280AA69C4903FD8CAA75FAECEFC7586EF6D7E,
	SignedData_get_ContentInfo_m91409FC043E8C6FF741A922FC69BB9E98B5B7F09,
	SignedData_set_HashName_mFDAB9AC05B7FDB00F646E3E44D4E9FF91CD0B4E7,
	SignedData_get_SignerInfo_mAE14D45F3CE393345D41AD231A382765E551D604,
	SignedData_OidToName_m910B0AD432C05A196504A1933374C93086027DD1,
	SignerInfo__ctor_mD8D5B3BEC47BCB44044A801365EE363D24922FE3,
	SignerInfo__ctor_mDB114E56EC2F9D5820B2F48A00CF14972B3BCCED,
	SignerInfo_get_IssuerName_m30347CCCF2ED4EF05618DD9AF4F71030E4A5A324,
	SignerInfo_get_SerialNumber_m6B855067A7C4CEF6758805C18E8417729BA62BD7,
	SignerInfo_get_AuthenticatedAttributes_m52500C3AC3793C89F0296BBEF64698F2ED20B8F4,
	SignerInfo_get_HashName_m1FCB2FBBA2DEC8F246B2ECE65997A2972B60B424,
	SignerInfo_set_HashName_m5C681833EF9E8380311B609BE25BE33911140E37,
	SignerInfo_get_Signature_m6FA43681D705A53BA0857AF55624483A9AE94CA8,
	SignerInfo_get_UnauthenticatedAttributes_m718FE21E74C9898E7B4060A5BE1264C68D3171FA,
	SignerInfo_get_Version_m0CFF6A1B831986936100F7F4A27D6386D65CFB37,
	SafeBag__ctor_m0AE5FFE8783310FAC265D1A911F5F6FA6A1CE9BF,
	SafeBag_get_BagOID_m1218C83BAD5C56D41AFF31B58784848A27A265E6,
	SafeBag_get_ASN1_m699A375228725122D8B9EE455013B215FBB87D05,
	PKCS12__ctor_mC4CC29A25D40F4799AB2715A3C7FB270C74EEFDD,
	PKCS12__ctor_m77EA8CF7C0DD214E25C7C8B9580339F1E2134E31,
	PKCS12__ctor_m2C6FBB30C343DA9D9E1753FBB72F01AD4E1F7FFC,
	PKCS12_Decode_m750703D9B730F5A8A355252F92DCD2D039C3FCDF,
	PKCS12_Finalize_m123C72042CF1A6A425005642408FF60B04D99462,
	PKCS12_set_Password_mB7CC3D7C9B194C6CF6E82B289EB1FD98EA002646,
	PKCS12_get_IterationCount_m2034EE21157689975AE4DC6E1F6B8628DB4539B4,
	PKCS12_set_IterationCount_m3F155BE5145E6356037FCEB58E1811255F80A6D3,
	PKCS12_get_Keys_mFF1C9F9DEF54D27CC472AA46DDBBE224F67DC46B,
	PKCS12_get_Certificates_m6FB30DF4377FE4FC725153AC237CBC59786E719E,
	PKCS12_get_RNG_m3C2B6A93761E38174AAB889781AB78E0A3A5B003,
	PKCS12_Compare_mB111BD413CF9B26BBFEA519F7AFE6531BF5E2C1E,
	PKCS12_GetSymmetricAlgorithm_m10BEA3D381B4DF46D904219E1CB9297E8D7CD439,
	PKCS12_Decrypt_mDA2EFC82AEC5616D5E0CBCF64315B9E9523FF02A,
	PKCS12_Decrypt_m9734C9729FDAD344DBE41EC3E3641F34F8F5A2B1,
	PKCS12_Encrypt_mFF1143A709115E5239D21DB9BB2BA795AC16B504,
	PKCS12_GetExistingParameters_m2350C6BD29DDCE4EACCCDE545B7621624FCF90F0,
	PKCS12_AddPrivateKey_m9387CE382E87E7C38F49ECFC7D96078B7FB4463F,
	PKCS12_ReadSafeBag_m8DDA9FAD730EE4A90C18689EFB0837AEEAE492B9,
	PKCS12_CertificateSafeBag_mC0BC2105B927BC138FB90EEDDEA95BD8A8DA024A,
	PKCS12_MAC_mBAD3C0B24FD8CA6FFE0F4A56CC29BE3E0D6F68A4,
	PKCS12_GetBytes_mDB4325D82327A46832138F16A8CBD3AA006B3A7F,
	PKCS12_EncryptedContentInfo_m670295BC933FE6D756A18DEC50779443A1A9FB7F,
	PKCS12_AddCertificate_m11EC4FA6E1AF1BCEE6483B5601225CD6A043D183,
	PKCS12_AddCertificate_m19F5576C596335EB6DD4E737D0CD4701884D91EF,
	PKCS12_RemoveCertificate_m65A709EDC4D5409904E14C738840252177D5E589,
	PKCS12_RemoveCertificate_m3A6B9EF29379012C8495C7A71332C93EA4733D29,
	PKCS12_Clone_m50D19F50BDEC33F80386C00F4BA4A409D37297C9,
	PKCS12_get_MaximumPasswordLength_m1254971BF3342F220F85AA11F42433D1BD4E86A0,
	PKCS12__cctor_mA93112C607BE4C95CC48AF6B6F03C866B349F6B5,
	DeriveBytes__ctor_mE49888D87CB6AA78648DF0D8129838DC91CF891A,
	DeriveBytes_set_HashName_m8C49265C77E164C659E6B11840289B0BFF7A0AA2,
	DeriveBytes_set_IterationCount_m0C50A49409C1B40DB50FA5123C89B2EF10C76E1D,
	DeriveBytes_set_Password_mE6A98520EA50E2A62428C9A8850D1C05B2A7FAFC,
	DeriveBytes_set_Salt_m9B502480F116A9B921146E849553EFC60DB94A7B,
	DeriveBytes_Adjust_m39BE20F4725D07DCBF711117748BE7AD654BE568,
	DeriveBytes_Derive_m4CF08A181B9E46EDB98691BB4C6D00BD75D08EE8,
	DeriveBytes_DeriveKey_m52FB070FC8822D470918BAE70E27A5C57CDC662A,
	DeriveBytes_DeriveIV_mDEECB1C384C67AF716AFE91FC210FC496B3334FE,
	DeriveBytes_DeriveMAC_m99726BCF64068E24CC03C4258AFF6831E235D774,
	DeriveBytes__cctor_m6F7601E9D1537AF9EFBFB25CFD8C9F90D6FD8C25,
	X501_ToString_mAC7577F741B34152BF7C5C4DD079CF62DB2FFF32,
	X501_ToString_m4C3921DEC65010E4B8A225C0982C382B1ADF0399,
	X501_AppendEntry_mE65E502CDE90C1957BFE3C9362DFCEF9B0597553,
	X501__cctor_mF9E1961695BFF008C8CDADB7A357965E20024FFA,
	X509Crl__ctor_mE8AE92D0023939007B09AA12D066EBED204CD674,
	X509Crl_Parse_mBFF8B04C60CD9047169F7F3CB5E9026D66B266B4,
	X509Crl_get_Extensions_mC3F9AF029FB2AA579EA6E940EE7827BCDA2614DF,
	X509Crl_get_Hash_m4A8044B7D2C67FD6C32DAC39542CDD42B3F2C9C0,
	X509Crl_get_IssuerName_m88AADDAC80012372BE645AA7CADE49FE090CCB68,
	X509Crl_get_NextUpdate_mA171254E41B8FD9672F8767B1C65588F75DAF072,
	X509Crl_Compare_mB585F4CCA3B1FAD5815FDA71976D5FC6842BD46E,
	X509Crl_GetCrlEntry_m824AEE6406406298D8740660A24F87CAD854936C,
	X509Crl_GetCrlEntry_m93093BA86976CEC62DFFDB28FFFE2353ECE35BB1,
	X509Crl_VerifySignature_m91D8FC43DAB9C5688E85FDA94C176AF73E665389,
	X509Crl_VerifySignature_m68859DEF40F3BB35F9DC79A025FED8981E0CE800,
	X509Crl_VerifySignature_m4FBC30F86E1BAF545D86876CF90C9105A01D8553,
	X509CrlEntry__ctor_m1CE31E179C47AA73774CE7C57CA24C879741EE81,
	X509CrlEntry_get_SerialNumber_mC1CD9010A26A5983B2DDD1A459FDC15619107478,
	X509CrlEntry_get_RevocationDate_m46DE2560B090EC69ECD7B9D819297E544C5386DA,
	X509CrlEntry_get_Extensions_m9756808EA9F465E76B8FC88417819B051538BB3B,
	X509Certificate_Parse_m52B3A2D924074F675F699C569C548DD521D5CF81,
	X509Certificate__ctor_m4F437D7E76C6CCB0941AB5801946076C82583946,
	X509Certificate_GetUnsignedBigInteger_mCFCA46D7C07D96A6B33AAB09A68BEA6E653E4BD3,
	X509Certificate_get_DSA_m661EA8FA79C2108403B685495B961422BC531AA6,
	X509Certificate_set_DSA_m35B061538E1BA0B2492CA50F46092FB3C48927B8,
	X509Certificate_get_Extensions_m74416980E0AF90FAD180B10AF4BAE2727E3C6EA6,
	X509Certificate_get_Hash_m116CA9E5DFB48CE5464E79C1941A0E78F20EB1F6,
	X509Certificate_get_IssuerName_mAB4FC4E65427A67DBEA87A6182336AF6870D900A,
	X509Certificate_get_KeyAlgorithm_m7538B572B9ACFCE48CAD7B8B2F5DF0EA3299D4DF,
	X509Certificate_get_KeyAlgorithmParameters_m198AAE45BF470804D29A79EDF6CC0EFB4DF8A247,
	X509Certificate_set_KeyAlgorithmParameters_m61198F54A91C2FDC7175E12C2E77E30A77D8E1D8,
	X509Certificate_get_PublicKey_m5504C97766FD6A8B0AF72CC6255A09A7397C819C,
	X509Certificate_get_RSA_mB74DAA8EF4132B0917C236F0F2617D95F23005E8,
	X509Certificate_set_RSA_m92B549DE372ABD58211BE846490719E5511D6D9C,
	X509Certificate_get_RawData_m322C38993C6E083A2FB1425C2E4D846FF905472E,
	X509Certificate_get_SerialNumber_m851B2E32EF2F46323798CBFD8450E4B8A82F264C,
	X509Certificate_get_Signature_m9B024ABD17D5320621904317E66A36BCC2B0FA46,
	X509Certificate_get_SubjectName_m75A5BA36DC4F00694DC5F3797EA89F04C095A48D,
	X509Certificate_get_ValidFrom_m735D09A81A642542E2509EDED48399D671FE2CE9,
	X509Certificate_get_ValidUntil_m58C8212E0E011376733C1DE57F78796483815AA7,
	X509Certificate_get_Version_mE2DE4C0948C42ECECCA2C1B87F1C2524C2E10128,
	X509Certificate_get_IsCurrent_mEBC52E38B22A633B0047BAA006F2446905473BDE,
	X509Certificate_WasCurrent_mA995BED12113288539D2BAB184B16AEF38844CEC,
	X509Certificate_VerifySignature_mE51967C4A00FD61035443B3E31358F40A1719AA1,
	X509Certificate_VerifySignature_m7FCB318B8E4049EFDD072092C0EE96A552DBF417,
	X509Certificate_VerifySignature_m2CB811C31D526CF4ECDEC02E59297A94DE8826AE,
	X509Certificate_get_IsSelfSigned_mAC256ADBF25A2572AAA95F126D3A466E1E3D00AA,
	X509Certificate_GetObjectData_mD8D5BD0C35EAB48243C4330A842755738B1CD9F3,
	X509Certificate_PEM_mE33D5467323808200102789409F34F31DD037306,
	X509Certificate__cctor_mD9016D839CEC21325002B8084A426A59693BB6EA,
	X509CertificateCollection__ctor_m0267958C4891BC9525E8E9A21A0CE4076205B457,
	X509CertificateCollection_get_Item_m68CBCD6C145B7EC4E4BDF6BA208576976385A538,
	X509CertificateCollection_Add_mBCBAC33FB2690C8D03BCB29FDD1A8D4CAF87F1D4,
	X509CertificateCollection_AddRange_mE0BA8C454C23FDBF0247A2EE7D60D4E51B1656D3,
	X509CertificateCollection_Contains_mEA5C9F87BCF0425AD8138CC821624B89EDDD30EE,
	X509CertificateCollection_GetEnumerator_m602E7163983BF2C8F8B5C09652D5E74771969B56,
	X509CertificateCollection_System_Collections_IEnumerable_GetEnumerator_mEAAECD2F051D47457F594F88E7C8BF46AAFE2363,
	X509CertificateCollection_GetHashCode_m56991888E2D575A6B43E41F227F00A22D9260A05,
	X509CertificateCollection_IndexOf_mA6F3226C3883B9111BFE552A616B8EAFCBD8B2EF,
	X509CertificateCollection_Compare_m3569EC2D68F7500E898AC53B8A3826DCAE939B5D,
	X509CertificateEnumerator__ctor_mC58E29412F64C3ADB016F51920CD5AEBC0B4A82E,
	X509CertificateEnumerator_get_Current_mA12833D7DAE7B1E3FB58D8596D266CA30A6BCD69,
	X509CertificateEnumerator_System_Collections_IEnumerator_get_Current_m8323BD8E1C9E4C6F2E57EDB8FA6BFCB396C03D14,
	X509CertificateEnumerator_System_Collections_IEnumerator_MoveNext_m3619B756D91F5E808E1D9C7F2A9C5ADCB4214C80,
	X509CertificateEnumerator_System_Collections_IEnumerator_Reset_mED5725981288EFF1B4F7F6CFEE1F9DC6A7C69F20,
	X509CertificateEnumerator_MoveNext_mAB0C32FB96AD574439B87E0E7D2553CBD7DF37C7,
	X509Chain__ctor_m5220D6FE9477D3D63B902475BDBFDC3CB63B3A79,
	X509Chain_get_TrustAnchors_m988C77FD4F945D35E2D846CA169141CA56756681,
	X509Chain_LoadCertificates_m5EA569474D1608E1292B8CF39917841C20278825,
	X509Chain_Build_m664E7F7A48510E8F5D538A932D1D5E6EC0095C33,
	X509Chain_Reset_m55EC23EC313B197F439649DF941228B8D799645F,
	X509Chain_IsValid_m38DBBC21B1224A7302D7A82720352CFDC0B16CB9,
	X509Chain_FindCertificateParent_mD6853ABA9E077A8C1B9F0F67FBEE7482A3D10325,
	X509Chain_FindCertificateRoot_mBD78F5BA569075F991BE2A4BEAA8AEC157DA450D,
	X509Chain_IsTrusted_m589BA3644A2F6BB355CF1EA7B168E1828257E5F0,
	X509Chain_IsParent_mE63AED9211A8A4CE11B0A5305F553403234C5282,
	X509Extension__ctor_m5B747C6BA6D46A064A6B1523D6A7A54E0EB140EB,
	X509Extension__ctor_m655F9DCB2706C0574ECF0D7CC3DC2CB91BE80FF8,
	X509Extension_Decode_m6D6D2679F86BAC039665B67771CCA784915A6FDC,
	X509Extension_Encode_m94400983A2BD91C905232850DB45B21D535C7FC3,
	X509Extension_get_Oid_m234B001E81AAE28787443F391F00C72D7521FDF0,
	X509Extension_get_Critical_m5EB6ACF6B4FE3FCCF30C4EA477361DD10DF72656,
	X509Extension_get_Value_m4852A46DF3018862BCB63F9ED94C2483DB5668BC,
	X509Extension_Equals_m310018B67B736E83554F698D4377D3850960606E,
	X509Extension_GetHashCode_mED3B0BC75A49EEF7C13AF9BE6C54855CFE2BE024,
	X509Extension_WriteLine_m07945F4C60FA5F7A7AEFEDF6413F63BCB73A28A7,
	X509Extension_ToString_m3F1D37C68357CBF1AD086FBE6839CC4516EA1782,
	X509ExtensionCollection__ctor_m3D13DF7788DD26AE9A0E8F01FCDA5DC26381C7C9,
	X509ExtensionCollection__ctor_mA36C635BC1A08C7FA3E1E43E94226F208C98E892,
	X509ExtensionCollection_IndexOf_m199A01C1E4623E410C326AFEA184ED2D2E0CBEB6,
	X509ExtensionCollection_System_Collections_IEnumerable_GetEnumerator_mCDF956BE91129236725221A834C635FBD093CD97,
	X509ExtensionCollection_get_Item_mD4FA6BBB2423E92B6315410E4C1E9C467BCD93BC,
	X509Store__ctor_m8BEE6CD856A9E81B3953DAC2B491C101E669FEDF,
	X509Store_get_Certificates_mCC4F7B25DDC42CA505656FC1F9EB239925FC7422,
	X509Store_get_Crls_m1AF25E9FC526E23F8B7266CF2465C6A6CFB8B73E,
	X509Store_Load_m6552D5345CC28C527318EC2E4517FCD1BEF62C5E,
	X509Store_LoadCertificate_m967F101849658106BFC35A16C02E94C8EC3B8B73,
	X509Store_LoadCrl_m8021617FF1675FE822613E822B08E9F41461E28C,
	X509Store_CheckStore_mD370453EBECDAD77FAC283E9F99F5CC6BC0FE866,
	X509Store_BuildCertificatesCollection_m32DDAF019B82B6F1BB94E8803538940502F313C6,
	X509Store_BuildCrlsCollection_m88F1863305698A5E11DF747BCC6606EDE1DD1636,
	X509StoreManager_get_CurrentUserPath_m3095B89B45A1567C8BFE71155629FABEFAD9CA9A,
	X509StoreManager_get_LocalMachinePath_m744CCDFEACC98BCEB7CA6CD0EC66C83C97FAEE6D,
	X509StoreManager_get_CurrentUser_mD931DB9B361AEC4B577C66405552E9E4C119FB57,
	X509StoreManager_get_LocalMachine_m3409106FF4A09DCFADCCA71094DA2193CB543B81,
	X509StoreManager_get_TrustedRootCertificates_m5D1C703644033D5F17F09DDCE5E4DA980AFF2D72,
	X509Stores__ctor_m4F280AF2E389D2220F3A12601EAFB1AB89D30D18,
	X509Stores_get_TrustedRoot_mEE0DF930C63F042A2C953BB22544517E6E338042,
	X509Stores_Open_m698AC1CCEC5ABF81319C2C85D6760FC5DACC9371,
	AuthorityKeyIdentifierExtension__ctor_mBB385C9A2A0B68770410545DF038A9D07F77BD50,
	AuthorityKeyIdentifierExtension_Decode_m3AF4A819293A215392229CF0BC19F04E40790560,
	AuthorityKeyIdentifierExtension_Encode_mBAA9D48407882B6297213AF5B5E3AAA602D67AF9,
	AuthorityKeyIdentifierExtension_get_Identifier_m02042068ED75CB7EF4E3A491A15A6B9DCDA44EC8,
	AuthorityKeyIdentifierExtension_ToString_m2B2DC5EB6E9A693509A2FAF2700E91C62AC340AA,
	BasicConstraintsExtension__ctor_mEBC34AF4DC736BB726ECC8C063E686A6E6707CCC,
	BasicConstraintsExtension_Decode_m9EE042A5689B98D6657E45B453A216579C6391A8,
	BasicConstraintsExtension_Encode_m89859CE03C9F471E1ECD94F2A88246872D419AF4,
	BasicConstraintsExtension_get_CertificateAuthority_m5BCC384C91B4A0B11CA41B7C12148457A0B56549,
	BasicConstraintsExtension_ToString_mBAA34F73F2A1FFE3B50A367760D4C22C31725EE6,
	ChallengeResponse__ctor_m69113B692194F152705FB0B10D5F5DE3C9E946D9,
	ChallengeResponse__ctor_m0FF7511212760424A602D3EA720303764373341F,
	ChallengeResponse_Finalize_m613FCBBBBAAFB1C4D08D287CAD2C4EBFC1E62980,
	ChallengeResponse_set_Password_mC4D21D00DD2094E296AA66D435EC9DFDB326F1A6,
	ChallengeResponse_set_Challenge_m99A9D9CEAD09286DAA811D78A80ECA7B67748753,
	ChallengeResponse_get_LM_mBF82535F35B084711977D987CBE5743D44A35A46,
	ChallengeResponse_get_NT_m62844C0A7C8B42B02BBB1B95AD3ACB463EE71D94,
	ChallengeResponse_Dispose_m2A0E32CF84BA5E17353C06534E90F7313D5C8F9B,
	ChallengeResponse_Dispose_m869AC64D371E202E47894DD149BAEC83326A5F44,
	ChallengeResponse_GetResponse_m802B1B862E6D746161952D582125FC41547468EF,
	ChallengeResponse_PrepareDESKey_mD974958EF21A6ECD302202460212B1D220926A2B,
	ChallengeResponse_PasswordToKey_m459F4457FF12282E9557CE2D3F01423F00FA7501,
	ChallengeResponse__cctor_m087640CF225A11A6F063EDCC1470B4F06FF6818B,
	ChallengeResponse2_Compute_LM_m05059CE954885BEE744F4D0F70FC8A066DDAF88F,
	ChallengeResponse2_Compute_NTLM_Password_mEC2849F4AF5C32A184B90D1E4331B8A975D2FB2E,
	ChallengeResponse2_Compute_NTLM_m26F6F261A6549A1085470E394102682209BC787C,
	ChallengeResponse2_Compute_NTLMv2_Session_m475AE185287A964A5E43268BE11AFBD6A45C1437,
	ChallengeResponse2_Compute_NTLMv2_m2017D1D91196AA084F54B10E655F070BD32CF7C8,
	ChallengeResponse2_Compute_mA7BABB8542156AA829E4B028D35AE06715F2B3A8,
	ChallengeResponse2_GetResponse_m0A0633C0D3455B6DAC1DA4BF83F35045123CB8C6,
	ChallengeResponse2_PrepareDESKey_mCDB9A6F9E86ED033D753C386C5EA04C0D5E971D1,
	ChallengeResponse2_PasswordToKey_mCD98B16AD98DB4D93228E9F189BC5AE62A306FFF,
	ChallengeResponse2__cctor_m6E5776BD08D8279CDFCAAC17E31226FA9A800788,
	MessageBase__ctor_m1CB51503E88E16AC1808FCEBEBE3DE8BB50DC3D8,
	MessageBase_get_Flags_m4A5BBB1791D770EE5B78682F35F7289627B96643,
	MessageBase_set_Flags_m4BA4AA3CA1819FFDF10A8A5ABBE9A891BB31A95E,
	MessageBase_get_Type_m9A3BCA375EA72DEE93AC6814A2D042F12CE2189B,
	MessageBase_PrepareMessage_mA2ECCEC352A8A4B4CDE8A273E2CCED50C05AE2BF,
	MessageBase_Decode_m155B9651CFCE85CD9B3858FF0A6DF89800767391,
	MessageBase_CheckHeader_mDC37298D388D893A6328373D7DA29B3CED469929,
	NULL,
	MessageBase__cctor_m4A379515E61255CD84F2C47B3BD59A904E9F8831,
	NtlmSettings_get_DefaultAuthLevel_mB3DEF764E195BA276BBB341A3A8618299085E752,
	NtlmSettings__cctor_m8430E34838ED4F723A656C609D8737BD4A900027,
	Type1Message__ctor_mEB49FA35D05F01172DF3C164F97163C277AEF914,
	Type1Message_set_Domain_mE06516CC741E4D12787911A9F1BCE92047DBA316,
	Type1Message_set_Host_mAB5F72A7D281D31E9F9EC69B7D7518762AEE6CDA,
	Type1Message_Decode_m6B6B1FAD887619D49C8CC60ABA5F444056059741,
	Type1Message_GetBytes_m8CAB2AE6CECDB5641EFD51A01649E75BB51DA1D1,
	Type2Message__ctor_m7A630E10D6C79B19A67A0068102655D998172E69,
	Type2Message_Finalize_m212EDBA6307D2A6469A1755E084959C4F416D132,
	Type2Message_get_Nonce_mA371E0DBB0EFD643493BDE7AD6593DB3B64F11E3,
	Type2Message_get_TargetName_m2D376951E07D21EB9CF72CECDC877DF4E4FB077D,
	Type2Message_get_TargetInfo_m26739FCB3FF9F56A86779C9A4B759FF3AFC03746,
	Type2Message_Decode_m4DC375D0224F5F7A9C1205ED4CC51650D0F3521A,
	Type2Message_GetBytes_m7C4E5D7F49EF6535DD53262395F446ED6426F3F7,
	Type3Message__ctor_m3A46F07DA05999471A34A64EB248160225F06768,
	Type3Message_Finalize_m3367B5EC62552132F0F6779EC3A2590FB143D2A1,
	Type3Message_set_Domain_mA29F199E40EDFDB8CBEC22F50925889A1F8510E0,
	Type3Message_set_Password_m04828A9F18AE9301885B4BD3C1F7F151DE63B300,
	Type3Message_set_Username_mE76160F96BD8EEFF971C3E04B2AD2579080E3BF8,
	Type3Message_Decode_m8066F0309CECA25CD455B91432F3679BB59B448C,
	Type3Message_DecodeString_m44283D90E42F25A5F5ECC66AEBBFBA9306725B25,
	Type3Message_EncodeString_m725085621A1177AD700EB63AA81FDAF9D04B2732,
	Type3Message_GetBytes_mBC207BD47ECFF12866B4786766066A4D9D146E87,
	Alert_get_Level_mA4A4833EBDC2FBFA4EA36634126A3E3C6EF4703C,
	Alert_get_Description_m704CDCA746F96A89493B7435D2F07920A1B7F3C5,
	Alert__ctor_mEF460A440F5C6CBD4B90FB0CD2665A73BFCE345E,
	Alert_inferAlertLevel_mCA51E78E9C51F283831F685C1CECA4FD20087043,
	Alert_ToString_m000BBF1E597BF854C362203244F8E2DC4EC7045B,
	ValidationResult__ctor_m4CAB4B31530A8CD62E46F8B3FAC6A36A4E615D5D,
	ValidationResult_get_Trusted_mC74B0DA857D9879C6A7428DB31DEA8A2DCD9DFF2,
	ValidationResult_get_UserDenied_m8714FE685A3F0214EE05C59AFE8ABC71CE614E0B,
	MonoTlsConnectionInfo_get_CipherSuiteCode_m413E7FB4252CE0D6C904D722107F0A4B59C79B08,
	MonoTlsConnectionInfo_set_CipherSuiteCode_m3BC21FD10B760939C96F6FBDB39DFEADDE50CCF5,
	MonoTlsConnectionInfo_get_ProtocolVersion_m12ADA52C235AF895D9462B2B4820EEF8325505AF,
	MonoTlsConnectionInfo_set_ProtocolVersion_m166E8E558B961F343E719D333A892E597AB17404,
	MonoTlsConnectionInfo_set_PeerDomainName_mDCAB6395BFE1BEF3ACEF680AEA6A3FA4DDEA2A97,
	MonoTlsConnectionInfo_ToString_mA9F63B760E6919F545A716A7619F8786CAA7C38C,
	MonoTlsConnectionInfo__ctor_m795EBB319F254C648313E21E85ABD2A4CBE4F93C,
	MonoRemoteCertificateValidationCallback__ctor_mEBD94D46C068D03E0F3A827F0875FE424C03B22A,
	MonoRemoteCertificateValidationCallback_Invoke_m8BE32AE7FE1BE89EFB6A49559431916F785D2BD9,
	MonoLocalCertificateSelectionCallback__ctor_mCA81824D698BD5808E501A9AC4DA99758B69D3FC,
	MonoLocalCertificateSelectionCallback_Invoke_m32D010A24184A7BEEE6191B19F04E2E8AC8C3CEE,
	MonoTlsProvider__ctor_m65BF846CE616D13609A5EFB2F42AE03A38E5CB8D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	MonoTlsProviderFactory_GetProvider_m8684E3A1AFB043FA00DEC4BCF95F8B288C136936,
	MonoTlsSettings_get_RemoteCertificateValidationCallback_mE07825B4A75DAE2A4BB5037D504A36311814446C,
	MonoTlsSettings_set_RemoteCertificateValidationCallback_m6CEA8A6E38C85A96C2D26613407C13DD4F965C87,
	MonoTlsSettings_get_ClientCertificateSelectionCallback_mCFE63487D867109AD1AF856ECC8BA0996C0AA605,
	MonoTlsSettings_set_ClientCertificateSelectionCallback_mB404DFD0C0475254CC129740A472D6D9615C56FD,
	MonoTlsSettings_get_UseServicePointManagerCallback_m11F6CF11F844ED2F6D54497CBAB18E04E6AFD754,
	MonoTlsSettings_set_UseServicePointManagerCallback_mE3EBA2F2886B024A4ACC303EE792190A5784E18B,
	MonoTlsSettings_get_CallbackNeedsCertificateChain_m02260798D928BDA7F6D9A2356B7CE688650BF176,
	MonoTlsSettings_get_CertificateValidationTime_mB7E248DE19CACA783A465F59859729C726DF2F9E,
	MonoTlsSettings_set_CertificateValidationTime_mE632C0A1BAB7608158B8EF05D2122B87AB6C34A6,
	MonoTlsSettings_get_TrustAnchors_m4CA73EEBF73B4344C5334F3BC5BBFE1BC27BC7AE,
	MonoTlsSettings_set_TrustAnchors_m08A22767288F1FB5AF812D0FDE8ACCDA47116661,
	MonoTlsSettings_get_UserSettings_m9ADCEFD59E6429580D053F5AC0AF492B59353920,
	MonoTlsSettings_set_UserSettings_mE1E6ED54BB439AE8012FD17D7230515CAB11D55D,
	MonoTlsSettings_get_CertificateSearchPaths_mD6BE6C9347446D742B29CAB987AEB76FF845A050,
	MonoTlsSettings_set_CertificateSearchPaths_mD7846BCF7B9292F85FEF0BE4A671779AD6C9AC32,
	MonoTlsSettings_get_SendCloseNotify_m1D35BCF28142455EC9CDF16CA36A8DEF57B05625,
	MonoTlsSettings_set_SendCloseNotify_m903943162225A7267CA3AD1E1E6E76EA71BCC5BF,
	MonoTlsSettings_get_ClientCertificateIssuers_m6FB58C63262D146231256A969DC2F3B60CE5C498,
	MonoTlsSettings_set_ClientCertificateIssuers_m0866718DAECFD44FC53370D23AB4C789CE0759B8,
	MonoTlsSettings_get_DisallowUnauthenticatedCertificateRequest_m15B7CAB13F2301D201AAD092DF2FCB49366ABDF6,
	MonoTlsSettings_set_DisallowUnauthenticatedCertificateRequest_mCC9D044ACAD032758F91CC5A3AD1E69F8C5C7FE4,
	MonoTlsSettings_get_EnabledProtocols_m6368286BF3E5703DB56F0EDE7B6AD2EF9C98D4C8,
	MonoTlsSettings_set_EnabledProtocols_m41991C8F073B85156818A906815AC4AE5623E77D,
	MonoTlsSettings_get_EnabledCiphers_m7BD72B78EF53FAF51FEEBF5B3657187EC876394A,
	MonoTlsSettings_set_EnabledCiphers_mA89F7C73A29959710230593DCC689F4B9860156A,
	MonoTlsSettings__ctor_mE939325F3FC3A8950048CD299F395C8C823EA925,
	MonoTlsSettings_get_DefaultSettings_m336A2267A1C8F1FD54496A76F319AFF3BD9A684A,
	MonoTlsSettings_CopyDefaultSettings_m4B0A3E8B7D106FA7F0D243FB2A0A4B115CD21942,
	MonoTlsSettings_get_CertificateValidator_m2DC79DA38E7CB5E87A21E315A29F21E1D9ACF367,
	MonoTlsSettings_CloneWithValidator_mF20535B6DE43DD45FAE2ECC6871C85C15F260D6B,
	MonoTlsSettings_Clone_mC4F9A27889ADD0B275018B32BCDA67C30865EA7D,
	MonoTlsSettings__ctor_m93F7300159A30C4B08C4A9A9B0C0CAA6B16E0D6F,
	TlsException__ctor_m343EBA01616A2B8649875A19BE4E56BBF46C6D81,
	TlsException__ctor_m4E03D70F9BCA4C0CA737720A43ABAB696CEA6209,
	CryptoConvert_ToHex_m1A0AD4D32CEEC47D3C60CB2E4D05A935C62F261A,
	MD2__ctor_m490777410D02CD92B398D20C6A2DAE7448A00E8B,
	MD2_Create_m63F8C14C0F090FA57982467993C7794C50E5974E,
	MD2Managed_Padding_m661705CFE5C0683685E84E1CD503326477C7138F,
	MD2Managed__ctor_m4E661935AEFB98EDCB3EEFE7D6EECAD10C807A2B,
	MD2Managed_Initialize_m578DBD3B8265ABD7FE060D4DD958EA986301698F,
	MD2Managed_HashCore_m2B4BED309AC3C80A6E8D14ADDE4BF9AC9CC266F6,
	MD2Managed_HashFinal_mDB2FA7F5A7A8A4FEE85BD56B47801B12BCDFCAF1,
	MD2Managed_MD2Transform_mCA2AD9EF038AC46E69F4726153B9C0DC03FC09D3,
	MD2Managed__cctor_m93D5F30A3D7E6E3F97660073EB29207996C162F6,
	MD4__ctor_m0E07C929BDAF3BB0CFD4EC360D22849566538CBB,
	MD4_Create_mBF86A7132FA1B49ABF8D4C91334E48FD0FCD3BE1,
	MD4Managed__ctor_m89BE94F7DEB019FA53E6B1F46DCFB6B4CA36CD1C,
	MD4Managed_Initialize_mFC1E0B77E8CF24894ECF37C54EC3D3527E36DDFB,
	MD4Managed_HashCore_m41A5263D2F078402E912B70E7DD08B9D12085E66,
	MD4Managed_HashFinal_m5BDA517A8D12C62F62C5EFBBCBC661DE9C198EFC,
	MD4Managed_Padding_mD7BB9A7A7F78490A4B8EC89A21ED240CB85E39F4,
	MD4Managed_F_mD8312B8925BE2A23E1E9B1A13054A95D815F06C4,
	MD4Managed_G_mCCB055CECFE7296853DF7DB9336A670549C8423A,
	MD4Managed_H_mA7897D3B979E6627EB9A44E6FB6C894D4B338910,
	MD4Managed_ROL_mAEE08B95D3EA8EB7EF09EB7CC1E97CF6F3E1F31F,
	MD4Managed_FF_mA227BE16EECCB0BC298BA664D40D8703152D5AA0,
	MD4Managed_GG_m57C28D2EE1C3AC5241943AEB683989331AFB2415,
	MD4Managed_HH_m35ABC1BD17ADB7103B07BAE99ED070EA850FB197,
	MD4Managed_Encode_m1E02CBE5A0E99DFF36299CCEAC13F92124422D55,
	MD4Managed_Decode_m01D0E808B7F05B50EC6A1B146F5BE6AB909DA396,
	MD4Managed_MD4Transform_m3BEBC5DA2F365EA60233EB3849D6B670AF44835A,
	PKCS1_Compare_m1193D66D416FE12B6F3ECF273FBB43648500250D,
	PKCS1_I2OSP_mE2B7F1991838515B88062B0291D4EE51BA983504,
	PKCS1_OS2IP_m2908ED07FF72EA1243E2EDB4E4D276AD3A95F373,
	PKCS1_RSAVP1_mFEBFDA793C8447C79CEAB87FD6B94488232FABD5,
	PKCS1_Verify_v15_m00B9330B8AA9E623DF4EBFAEEE73BA062CC2BBA1,
	PKCS1_Encode_v15_mA8D476701564761F876E7DADA3FAE6AE2458F054,
	PKCS1_HashNameFromOid_m3BA0DDFAAF18E9B580510C2820919F71154B4290,
	PKCS1_CreateFromOid_m6F0813F626FA58AC0DE23E144B04B2485868F706,
	PKCS1_CreateFromName_m979F533ED6CD3CF2895D5D474A67069EB56912D7,
	PKCS1__cctor_mA35BD1E24641E96C7CC67417DE31C90AFE4B23E6,
	PrivateKeyInfo__ctor_m22D9C0E05AA5534D7874D9779D81BA12CB675862,
	PrivateKeyInfo__ctor_mF8536B2938ED0774AEAFAC0D16AF8FAD636F904A,
	PrivateKeyInfo_get_Algorithm_m544D7A80C2BD7DE45C080DF09D88221A58DB9459,
	PrivateKeyInfo_get_PrivateKey_m08A332D04C22BE19ED55E82B4D3804336502D13B,
	PrivateKeyInfo_Decode_m0418E893A9C18998D461DF559BEE133012C54B41,
	PrivateKeyInfo_RemoveLeadingZero_mE1A64CA3FCB10714B91D673C958F8FC668FB6460,
	PrivateKeyInfo_Normalize_m18CF37D456DAB5661BFF0F04A0DB4D39BCA79306,
	PrivateKeyInfo_DecodeRSA_mB304590B0C8E7227CF6D1B336D67AA67276A5A24,
	PrivateKeyInfo_Encode_m63E6B8B587EAFDC2A15D83D55F927A8DF927B0A5,
	PrivateKeyInfo_DecodeDSA_m8F3B78E775F8D2D6F803B443CD0E97EF9A791E5A,
	PrivateKeyInfo_Encode_m62E27ACA0D0E69F1A221B06B6138D91A28D03880,
	PrivateKeyInfo_Encode_mC9CBC8A5FE2F6CE759439BB4B3FE594AACB9D5F6,
	EncryptedPrivateKeyInfo__ctor_mE231E160F0541E832A29C009CD95AFF578E51918,
	EncryptedPrivateKeyInfo__ctor_m5CF5C6296CD6DA1A655FDEEF0701F4E6600E5D7F,
	EncryptedPrivateKeyInfo_get_Algorithm_mA143C07290B65E429244DBD3C98CD7E4F2FDA061,
	EncryptedPrivateKeyInfo_get_EncryptedData_m0CE09A1279C5BD7DCFC30BD5D9D59921D7082583,
	EncryptedPrivateKeyInfo_get_Salt_m9535A0F6F11ABF70C3DAEEBB61E437CB21042F61,
	EncryptedPrivateKeyInfo_get_IterationCount_m0027F1C5D6D120B70924F3DFCDFAAA48C064F410,
	EncryptedPrivateKeyInfo_Decode_m1C9F94B949283DE27932FACE345EE2C314982C85,
	RSAManaged__ctor_m3DC50713376908C5D1B7EDE7662070DBC185597A,
	RSAManaged__ctor_mC9FD969A9C89A836300989CC0A3B20E40025D8BE,
	RSAManaged_Finalize_mFD07D39251303051B9DF8AF29AB6B4CF9FA7FDCB,
	RSAManaged_GenerateKeyPair_m3E0EB3C2ADA16EE8FE2A8AE7071333CB6795F55D,
	RSAManaged_get_KeySize_m17BA4753AA6967ADFBEBF49035158532F23DB7A3,
	RSAManaged_get_PublicOnly_m12DE914F7BDDD2E850EDD7F34BA9CF3114C333BF,
	RSAManaged_DecryptValue_m30147A9ADDB9F2942133DB9699EA8834E2888B39,
	RSAManaged_EncryptValue_mAED1DAF6891C6F20546DD23272193471BB495242,
	RSAManaged_ExportParameters_mD63F6ED165F8CA27D9F2BBA4C7FDD232D6C2A53F,
	RSAManaged_ImportParameters_m585D1CB6F017A3FDA42FEFEAFD14029F8E41EBC8,
	RSAManaged_Dispose_mE2C5CCB287A79986DFB8A3A2380E6666291C982B,
	RSAManaged_ToXmlString_m05B2E2182E8223F5242E8C0D8A0BF70750A9CB93,
	RSAManaged_GetPaddedValue_m228CFE67E9EF6B3659E761E55D06AD18B6DFF1FD,
	KeyGeneratedEventHandler__ctor_m5B52F3B7F5A33F1A0BDC378D304EF8F681A1D8FB,
	KeyGeneratedEventHandler_Invoke_mD73F8C7BC0305037EC520C3F4AF3DE74E5481DDA,
	AuthenticodeBase__ctor_m0439483FE9A59BB7E27DE3A8D9EA1D1265BF968C,
	AuthenticodeBase_get_PEOffset_mC4BFB80BAEF28B712FD9048C85B3F20B3DC81C5B,
	AuthenticodeBase_Open_m6D97F062F4176730B6020ACAAD47ED4353B35465,
	AuthenticodeBase_Open_mB75CCA77F829FC8F99FDFCDC639AE5ADA3B32A81,
	AuthenticodeBase_Close_m1FA42AD55310BAF64DB00FFE6563ED06D44980E9,
	AuthenticodeBase_ReadFirstBlock_m7F5A9F2A9C8FAA6C6685E0F3322E8D69E7324545,
	AuthenticodeBase_ProcessFirstBlock_m3FB173D7CA309AA1119CFB86D5E4768DA1DCCC1A,
	AuthenticodeBase_GetSecurityEntry_m3690E0D6B4D14F2A8979F1BE0CCE6F7327C2E24E,
	AuthenticodeBase_GetHash_mF881D2926F2FABC3ACDA646842954F83A9434066,
	AuthenticodeDeformatter__ctor_m2695F107A500029472C7B519B00F5F8F86918114,
	AuthenticodeDeformatter__ctor_mE7D65775E1289E0DBB208034BE5EB40AB6469BA0,
	AuthenticodeDeformatter_set_RawData_m0D90817618D3ED52A415A898F0037018A777D07A,
	AuthenticodeDeformatter_get_SigningCertificate_m5C675A2AB0464670DF6C557C4433871014EB52CC,
	AuthenticodeDeformatter_CheckSignature_mCC125D2B04750C3581E9C60593C4212BC54077F0,
	AuthenticodeDeformatter_CompareIssuerSerial_m3EE0CDFEE995FAB1C96A8B358D15F1D5EB4F2413,
	AuthenticodeDeformatter_VerifySignature_mEA4B444611069CAADC507D771CFBA1A571CD8973,
	AuthenticodeDeformatter_VerifyCounterSignature_mCC865AF6E1CE3E6917FF161182D64EDCFE94DBCD,
	AuthenticodeDeformatter_Reset_mD15DE78C062103550A42BEE536E78F9526585B90,
	BigInteger__ctor_m49DCBBD82981761C45EBC2350A1F16DB886E5F50,
	BigInteger__ctor_m42B232CF6F76C15DAA3C02C3F5DE1B1F7C7BBC51,
	BigInteger__ctor_mD26E0224E82674AFA9A6E4BF4F0674BB0B7BECBF,
	BigInteger__ctor_m8E3F4B6BB64E6F4A2FCB66402F58C192453ECB45,
	BigInteger__ctor_m9AFFDE2505C2B712D5F89EF501A2D163B7855E02,
	BigInteger_op_Implicit_m953448132663D58B45AF679B003D0BBC92FEBB49,
	BigInteger_op_Implicit_mEC0736B08374B7B829F6FE6ED416186A9084EDE6,
	BigInteger_op_Addition_m38EA8E365D315586FBFE76755898A8FF2854CD46,
	BigInteger_op_Subtraction_m2017F0245B56CE2D6245757B43DDA997625581CA,
	BigInteger_op_Modulus_m614BB6B5B40C256436BB94EA2B8391C298260F47,
	BigInteger_op_Modulus_m5AC5D8DD148908E30BA188BC50E6B2F0E75545CD,
	BigInteger_op_Division_m440FC499D6242819F93B60269F50546F85790C6A,
	BigInteger_op_Multiply_mEDE5A32D2745927133F814E397EA4E628A60C549,
	BigInteger_op_LeftShift_m0450D740864BE2C62DFC1A0777498E518A2E6A73,
	BigInteger_op_RightShift_m9ABBE9016A201283D5DF10CD67D5302510F86224,
	BigInteger_get_Rng_m200DBFEDB3D711658BDB3796FE3B805956058A07,
	BigInteger_GenerateRandom_m01DB8F5756F75D8B9810F25D871EC04D41069E4E,
	BigInteger_GenerateRandom_m5350D6E9C3AAE96153C46D80880F1CE43BB9DA9C,
	BigInteger_BitCount_m6FD831E1BA71E84748B08A7A5B1FFE9AB2C62551,
	BigInteger_TestBit_m2EAAC171F87CAB5FEDA0DEC79A83A68E56762C35,
	BigInteger_SetBit_m3E67DE35B0E691FCB886C60252CAAFC3FCB92A39,
	BigInteger_SetBit_m60B275C4579C541B806FECB49EBBDA1579DA70BE,
	BigInteger_LowestSetBit_m6443AF5F1F7C864A65365DD731FC7DAC1495DE20,
	BigInteger_GetBytes_mE1CA6C5DC5B8AA9014B4169BD55307040E668439,
	BigInteger_op_Equality_m3D8FFD303A14BBC17B7423CE9EEC513700BF921F,
	BigInteger_op_Inequality_mDDDA5BA9BF336633ADBD56F97601ACF81C0133FF,
	BigInteger_op_Equality_mECF42AF9013ADE8D8274E7485C7BA23E4BFFEC60,
	BigInteger_op_Inequality_m2AB984FD1240CAD1831070279D41DD97EB632DC7,
	BigInteger_op_GreaterThan_m4102428737E020DEF999F9A2320F2C8962C4947C,
	BigInteger_op_LessThan_m5334E5102F45FBABAB12A790BB67FEC735A62C25,
	BigInteger_op_GreaterThanOrEqual_m84F5223CE33D7E4B977B30D32E87AD7D5051DB4F,
	BigInteger_op_LessThanOrEqual_mFB395E4E35C3E672EB2B1D485FA9E621D769D68A,
	BigInteger_ToString_m02A324A776BF09756B1BB3B6BFF6DBD7EA98852D,
	BigInteger_ToString_m007BCE771B1D0C61761EF1F3D98B8C508EC6DF9D,
	BigInteger_Normalize_mC7C8E5FF4D3DE66DE886D541352D3A091D84DC47,
	BigInteger_Clear_m1BFEB305789595395EEB68C5150A5331E0207323,
	BigInteger_GetHashCode_mEAB87BB7A4882FEAF1D7D65B6713E31C0A6F07EE,
	BigInteger_ToString_m80A151A5EF5ABF1F5FF16660E78042B85241E3E9,
	BigInteger_Equals_mEC32AE6F06C0C113291158D434ED2A92B07E6453,
	BigInteger_ModInverse_m7E87B8BE468B504ED2BD533629D4E9089E5768E5,
	BigInteger_ModPow_m534E775DEFD6DEDC75F71FA21492843EAA9CEC9D,
	BigInteger_GeneratePseudoPrime_m8DFE2FCECB4B1D3671A53358F702F00F359EFCCE,
	BigInteger_Incr2_mE5DCADCC1DEDD4F3E48E326940D3C926E1A37808,
	BigInteger__cctor_mFA00C7DFC5EA828985D4858C25534222BC0F94B7,
	ModulusRing__ctor_mA5C74C954C8ABD6F9843F41BBF197F7F00F8D00A,
	ModulusRing_BarrettReduction_mA753465B3A0DC2E9DEC32D345C24DCA1B56115D5,
	ModulusRing_Multiply_mDBAD6831070EE1661E3B7EEC4786A515DE5C229A,
	ModulusRing_Difference_mE4B31BBB8F73710EC6AA7D3F87719672EF683AAD,
	ModulusRing_Pow_mE14DBD510D57E2A1CF1BFB2B237439A8EB08A418,
	ModulusRing_Pow_m9F2BB905682A6999D33B598EE7A61755DC240C6D,
	Kernel_AddSameSign_m1ED361C045C7224DB71E4C049597E8FCD9B07B57,
	Kernel_Subtract_mEA9B4A2546B4EBBEF7FAB54CD8F33F2283E08A5C,
	Kernel_MinusEq_m634A25656C7AD0F7C521B23BE30425B788030B91,
	Kernel_PlusEq_mA092738CB4B501B664F314715C7C74FC03384AE3,
	Kernel_Compare_mB84E41B7046C951E360FB9DA11A4BFA5A0E66611,
	Kernel_SingleByteDivideInPlace_mD9E56BF746E318BEDECEC4280A829A63EAD28F09,
	Kernel_DwordMod_m1B3830BC4779D5B4BEBC37E1BF320F500C20CC0E,
	Kernel_DwordDivMod_m5977404465381995E14EF36BE94500F6D18458B7,
	Kernel_multiByteDivide_m32F2ED53F2DEC2DE630E0BDD5AA4A4BC462CD7EF,
	Kernel_LeftShift_mE40ED9013AB88A02A79C7640DA3FA1F320ED86F0,
	Kernel_RightShift_m3EF3092A11D1C9680419B275A8349E56CC4BEC5C,
	Kernel_Multiply_mF725E33ADE4B9849626BB9F011C45E1EE7A5FBB7,
	Kernel_MultiplyMod2p32pmod_mD9A826628E622F9BF45520DE5A6C0BD53DD2AD9E,
	Kernel_modInverse_m9AF7A04275E7EC3CB3BE97A0E49D303128551788,
	Kernel_modInverse_m9B85AEE3C824216F039E1B5D7EFAE04F71D2A1BB,
	PrimalityTest__ctor_m73483F9E5D166F74E0340F479376C61D9280266A,
	PrimalityTest_Invoke_m7E9F9413908598A1270792B565D71288027AA552,
	PrimalityTests_GetSPPRounds_mE93C23146823DD9BB19FF2588CACB3EA32D5E845,
	PrimalityTests_RabinMillerTest_m69936AC3B0635D2E4145DCA9CD2A7E8F63B8CFC4,
	PrimeGeneratorBase_get_Confidence_m8A53DA3C670504B629434C990508D4B77642B875,
	PrimeGeneratorBase_get_PrimalityTest_m96C5E1866F96043982AF493BE7EAB5969F770E1D,
	PrimeGeneratorBase_get_TrialDivisionBounds_m706A348C994861A2B92CE9156FE20DCF7474E286,
	NULL,
	PrimeGeneratorBase__ctor_mECF0CD5B964A7E6FCE4F504719164114B8A678E9,
	SequentialSearchPrimeGeneratorBase_GenerateSearchBase_mD12A7AC0A052FA228E0F4918BBA1B2B59AD605CE,
	SequentialSearchPrimeGeneratorBase_GenerateNewPrime_m6AC834873702FE49B85FB261931CA31BC239FFCD,
	SequentialSearchPrimeGeneratorBase_GenerateNewPrime_m6599A58FA9EBD14FEB9D18073419FF8341365B8B,
	SequentialSearchPrimeGeneratorBase_IsPrimeAcceptable_m2682AEC2B91FC89D1EB0799BFE5DA4C0F7F8C1D0,
	SequentialSearchPrimeGeneratorBase__ctor_mFE1A43FA14390E871ED0C9025B57B84D9A7AA754,
	U3CPrivateImplementationDetailsU3E_ComputeStringHash_mC7DC26EF4301846E2947FBD7916A16E88C887055,
};
static const int32_t s_InvokerIndices[513] = 
{
	32090,
	28070,
	15757,
	6695,
	15968,
	20694,
	20550,
	20694,
	20761,
	15968,
	5018,
	11681,
	13820,
	20761,
	3690,
	1227,
	13811,
	6073,
	20761,
	32085,
	32090,
	32090,
	31787,
	32090,
	31630,
	32063,
	32063,
	32085,
	32086,
	26441,
	26441,
	27854,
	28365,
	28384,
	21016,
	15968,
	15968,
	15968,
	20761,
	20761,
	15968,
	20761,
	15968,
	20761,
	21016,
	15968,
	20761,
	20761,
	15968,
	20761,
	20761,
	15968,
	20761,
	13820,
	21016,
	15968,
	20761,
	20761,
	20761,
	20761,
	15968,
	20761,
	20761,
	20550,
	7995,
	20761,
	20761,
	21016,
	15968,
	7995,
	15968,
	21016,
	15968,
	20694,
	15903,
	20761,
	20761,
	20761,
	5018,
	3324,
	2276,
	13820,
	2276,
	12274,
	15968,
	15968,
	6094,
	2276,
	20761,
	6094,
	15968,
	7995,
	15968,
	7995,
	20761,
	34138,
	34252,
	21016,
	15968,
	15903,
	15968,
	15968,
	3726,
	6091,
	13811,
	13811,
	13811,
	34252,
	32090,
	24313,
	26562,
	34252,
	15968,
	15968,
	20761,
	20761,
	20761,
	20582,
	5018,
	13820,
	13820,
	11681,
	11681,
	11681,
	15968,
	20761,
	20582,
	20761,
	15968,
	15968,
	13820,
	20761,
	15968,
	20761,
	20761,
	20761,
	20761,
	20761,
	15968,
	20761,
	20761,
	15968,
	20761,
	20761,
	20761,
	20761,
	20582,
	20582,
	20694,
	20550,
	11523,
	11681,
	11681,
	11681,
	20550,
	8014,
	28070,
	34252,
	21016,
	13811,
	13212,
	15968,
	11681,
	20761,
	20761,
	20694,
	13212,
	5018,
	15968,
	20761,
	20761,
	20550,
	21016,
	20550,
	21016,
	20761,
	15968,
	11681,
	21016,
	11681,
	13820,
	13820,
	11681,
	5018,
	15968,
	15968,
	21016,
	21016,
	20761,
	20550,
	20761,
	11681,
	20694,
	3722,
	20761,
	21016,
	15968,
	13212,
	20761,
	13820,
	3695,
	20761,
	20761,
	13820,
	13820,
	13820,
	5010,
	13820,
	13820,
	34156,
	34156,
	34156,
	34156,
	34156,
	7968,
	20761,
	6087,
	15968,
	21016,
	21016,
	20761,
	20761,
	15968,
	21016,
	21016,
	20550,
	20761,
	21016,
	7995,
	21016,
	15968,
	15968,
	20761,
	20761,
	21016,
	15757,
	13820,
	6091,
	6091,
	34252,
	28070,
	32090,
	28070,
	24642,
	24337,
	21886,
	28070,
	28064,
	28064,
	34252,
	15903,
	20694,
	15903,
	20694,
	13811,
	15968,
	11681,
	-1,
	34252,
	34138,
	34252,
	21016,
	15968,
	15968,
	15968,
	20761,
	15968,
	21016,
	20761,
	20761,
	20761,
	15968,
	20761,
	15968,
	21016,
	15968,
	15968,
	15968,
	15968,
	3315,
	13820,
	20761,
	20550,
	20550,
	15757,
	21016,
	20761,
	2514,
	20550,
	20550,
	20983,
	16178,
	20694,
	15903,
	15968,
	20761,
	21016,
	7988,
	1479,
	7988,
	2280,
	21016,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	34156,
	20761,
	15968,
	20761,
	15968,
	18925,
	15022,
	20550,
	18926,
	15025,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20550,
	15757,
	20761,
	15968,
	20550,
	15757,
	18946,
	15046,
	20761,
	15968,
	21016,
	34156,
	34156,
	20761,
	13820,
	20761,
	15968,
	7995,
	6695,
	32090,
	21016,
	34156,
	13811,
	21016,
	21016,
	3722,
	20761,
	2841,
	34252,
	21016,
	34156,
	21016,
	21016,
	3722,
	20761,
	13811,
	3419,
	3419,
	3419,
	6221,
	660,
	660,
	660,
	7995,
	3758,
	3758,
	27508,
	28064,
	32090,
	28070,
	22251,
	25680,
	28056,
	32090,
	32090,
	34252,
	21016,
	15968,
	20761,
	20761,
	15968,
	32090,
	28064,
	32090,
	32090,
	28059,
	32090,
	32090,
	21016,
	15968,
	20761,
	20761,
	20761,
	20694,
	15968,
	21016,
	15903,
	21016,
	21016,
	20694,
	20550,
	13820,
	13820,
	13918,
	16014,
	15757,
	13795,
	6091,
	7988,
	7995,
	21016,
	20694,
	15968,
	15968,
	21016,
	21016,
	20694,
	20761,
	13820,
	21016,
	15968,
	15968,
	20761,
	20550,
	3102,
	3102,
	5018,
	21016,
	7636,
	15968,
	8045,
	15968,
	16179,
	32111,
	32085,
	28070,
	28070,
	28387,
	28070,
	28070,
	28070,
	28064,
	28064,
	34156,
	28038,
	32085,
	20694,
	11619,
	16179,
	8155,
	20694,
	20761,
	27514,
	27514,
	27508,
	27508,
	27508,
	27508,
	27508,
	27508,
	13838,
	6115,
	21016,
	21016,
	20694,
	20761,
	11681,
	13820,
	6094,
	32085,
	21016,
	34252,
	15968,
	15968,
	6094,
	6094,
	6094,
	6115,
	28070,
	28070,
	29176,
	29176,
	27855,
	28387,
	28387,
	28078,
	28070,
	28064,
	28064,
	21754,
	21646,
	28387,
	28070,
	7988,
	5016,
	27854,
	27504,
	20694,
	20761,
	20694,
	-1,
	21016,
	6076,
	13811,
	6076,
	5018,
	21016,
	32439,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Mono_Security_CodeGenModule;
const Il2CppCodeGenModule g_Mono_Security_CodeGenModule = 
{
	"Mono.Security.dll",
	513,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
