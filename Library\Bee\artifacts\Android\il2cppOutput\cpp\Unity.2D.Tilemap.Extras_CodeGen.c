﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mF1559E8AAA9EC05EB865BBD8310E647B60419315 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m358685E22263F4E473610EB1BFC46D0442CF85B0 (void);
extern void HexagonalRuleTile_get_m_RotationAngle_mDD58A85556B3EE5B5BF67CD6774BC9995241DE0C (void);
extern void HexagonalRuleTile_TilemapPositionToWorldPosition_mC00CEB616152AC2903B4D09CD6B56244BA942DB0 (void);
extern void HexagonalRuleTile_WorldPositionToTilemapPosition_mD227E09487435419F76756F879839C289462F776 (void);
extern void HexagonalRuleTile_GetOffsetPosition_m9418CC09843754C9D33810727CAB52FC89ADB867 (void);
extern void HexagonalRuleTile_GetOffsetPositionReverse_mB92B718ABA28C5B3CFDDAABF5B25290E248F2834 (void);
extern void HexagonalRuleTile_GetRotatedPosition_m46DF5AF7EDB64C4E2D4886BA02D24649F763273D (void);
extern void HexagonalRuleTile_GetMirroredPosition_mF6D2411014AD1E645837C87F0B813FE17CAE9A78 (void);
extern void HexagonalRuleTile__ctor_mD091F912A02CEC20B9D34A14BB4C005E6C2C2C16 (void);
extern void HexagonalRuleTile__cctor_m0FE1FE30B69EEB3DFF7F30DCF8AEF83ED74586E0 (void);
extern void IsometricRuleTile__ctor_m016CC4482ED629E80FAEAE08B90A342573AF9900 (void);
extern void RuleTile_get_m_NeighborType_mD22089281EE41453168127DB5472F852939476EE (void);
extern void RuleTile_get_m_RotationAngle_mC4E7FA2B9537A51B8C2D51E6287622DF5301531D (void);
extern void RuleTile_get_m_RotationCount_m2CBDA8836FB4D88BA1A8C3060C68F6D95EEFBCBF (void);
extern void RuleTile_get_neighborPositions_mE48C37907633A307D34ABDE27BDBB455F0FEB2EA (void);
extern void RuleTile_UpdateNeighborPositions_mF1E0734CD82B0DDACEC74E399C7897282ECED6B2 (void);
extern void RuleTile_StartUp_mE42191406441174CEDC669772F1E5DE11654F066 (void);
extern void RuleTile_GetTileData_m808E08D6AFC5893D1A517DACFF61B3DAA9F2F431 (void);
extern void RuleTile_GetPerlinValue_m0AF7168A4C778257F23E60B1784A584436C5CFAA (void);
extern void RuleTile_IsTilemapUsedTilesChange_mFF2088F8B51C955A524BF269E6DA56B88C947688 (void);
extern void RuleTile_CachingTilemapNeighborPositions_mE26EBD1F5569810CB62C7FBCE4B373E151025371 (void);
extern void RuleTile_NeedRelease_mD59755B112E58C50BA4BEA8D5673DA41BF01964C (void);
extern void RuleTile_ReleaseDestroyedTilemapCacheData_mFCB311A08075FB57B6EE2072DCD239CB3F72F323 (void);
extern void RuleTile_GetTileAnimationData_mBDC09525FD6F454EF1F12154AB0E70EE9A23B8C4 (void);
extern void RuleTile_RefreshTile_mE2CE05AA63E86494EB701710E59BAC3E59BD383E (void);
extern void RuleTile_RuleMatches_mC46AD557F2C85668BB5C48CE5D58904B48AEA96B (void);
extern void RuleTile_ApplyRandomTransform_m1451FBC0BF9FB1C8B7CF73987D05913F6E519919 (void);
extern void RuleTile_GetCustomFields_m20E5229812AC3DBD1A2765F27163928A7194C10C (void);
extern void RuleTile_RuleMatch_mDBA6EDCC409125D42CBE7B4F63470131D7CD8EF1 (void);
extern void RuleTile_RuleMatches_mA8FAAD266B6A044BE463CAC16FD74982E7051C92 (void);
extern void RuleTile_RuleMatches_m8766CC8F8EE91C0DA0CEB8D8DE482DFFECBE5D3A (void);
extern void RuleTile_GetRotatedPosition_m03F5F7263C0A1A372439CDD02A54B34602FB45B0 (void);
extern void RuleTile_GetMirroredPosition_mAFA0B2F9DD1DB590BDCFED89F93ABD2E334DF266 (void);
extern void RuleTile_GetOffsetPosition_m14D02BC751910D45D16665AF74FD347F9343F8AF (void);
extern void RuleTile_GetOffsetPositionReverse_m617F0F4CC3D6F5EAD0F2D0AA9710026F1A5ED2BF (void);
extern void RuleTile__ctor_m3548BB5D248C38FDC759A225E4D48ECDCDD247DE (void);
extern void RuleTile__cctor_m9A29A13993DC4AA524187C2015D4D19AFC78F83F (void);
extern void TilingRuleOutput__ctor_m01155B9B604B5322723E9ACCC2A2ED47A0EBB990 (void);
extern void Neighbor__ctor_m8D6045542EF5AB2870BD8B905688D59B082B7257 (void);
extern void TilingRule_Clone_m783F847A0C6A23C9C67292C9F342CDE2E59E8E04 (void);
extern void TilingRule_GetNeighbors_m47FDA38A6F313862E10471132DE32D06B0D3AD79 (void);
extern void TilingRule_ApplyNeighbors_mB8BEB69614D404E4A14DC322B14EFC2E19216DCF (void);
extern void TilingRule_GetBounds_mB47D6F463F0B4E5FB66AFCB37704556860C0EE16 (void);
extern void TilingRule__ctor_m1873560C84F01E67080E925344CA1A504BCF22EA (void);
extern void DontOverride__ctor_mFF94FEE9E232640F1AAC28BC4F8C252F516789DB (void);
extern void U3CU3Ec__cctor_mC047430393DA663C6B82327A1E157E40383920BC (void);
extern void U3CU3Ec__ctor_m78922446FE7BCC59AE6DCC5B59FDE30424E13F16 (void);
extern void U3CU3Ec_U3CGetCustomFieldsU3Eb__27_0_mB606F85896AA35419AA9B9D86A18449F490E7B7E (void);
extern void U3CU3Ec_U3CGetCustomFieldsU3Eb__27_1_mFEDABFC378E706B7B3DB01D4FA028A5EA1809BC1 (void);
extern void U3CU3Ec_U3CGetCustomFieldsU3Eb__27_2_m58DF262A5C7C2C7131FED522B02174DBC4B2841B (void);
extern void U3CU3Ec__DisplayClass27_0__ctor_m5F733F0DD63CFCD6F3B7FE49B4A30634AFF3E98C (void);
extern void U3CU3Ec__DisplayClass27_0_U3CGetCustomFieldsU3Eb__3_m7650E2C633F6281405F126D781765E939FAB10BB (void);
extern void GridInformation_get_PositionProperties_m793F7FB3FA660B9E0B7BE4B7EA53D7799DA823B3 (void);
extern void GridInformation_Reset_m211B34303B6C1271227F3F8CABAAB77F3B58A1E6 (void);
extern void GridInformation_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mD0EC6C20408203AD81A8E5ACA56065DBC37E6B55 (void);
extern void GridInformation_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m084CD3BB6D503172BBC2C12412AB479256918E68 (void);
extern void GridInformation_SetPositionProperty_mC1BF51F7911CA27B9083E0E683FA2E9B4A7F12E0 (void);
extern void GridInformation_SetPositionProperty_mFB8458F1FFDCA731836962EC730E2EDFF20D852B (void);
extern void GridInformation_SetPositionProperty_m9D1A8EF6A592141B5B17DA04CAB28F6ADD9FD0F6 (void);
extern void GridInformation_SetPositionProperty_mE7467B3537A2575000779153F7884CB683E220DF (void);
extern void GridInformation_SetPositionProperty_m08BE473643FD07837AE6AC3A1CFEA48176105B45 (void);
extern void GridInformation_SetPositionProperty_mBE2D8029E4CD5E061852D21E08BC8000530589F1 (void);
extern void GridInformation_SetPositionProperty_mFDB93F193695B4727C8A4F27A4C1405624E3415D (void);
extern void GridInformation_GetPositionProperty_mD281CD98AE570B33EC6D84FAB9676E9AAE81671A (void);
extern void GridInformation_GetPositionProperty_mC94CDE87945AA9F78B7CB698554F581DCBB306BE (void);
extern void GridInformation_GetPositionProperty_mF59B2D16494EA51D7A965628F4A844CE8E811A91 (void);
extern void GridInformation_GetPositionProperty_mF3A3512DBCB9EA2C5E309B56AD57DCBB80DA4B6A (void);
extern void GridInformation_GetPositionProperty_mBCE407A8B4243785CD45D9E7A08864DD7E1B6609 (void);
extern void GridInformation_ErasePositionProperty_mBE1D65A52B97212AF5D9592CA7F0AE47204E35E0 (void);
extern void GridInformation_GetAllPositions_mE64C529198C90D397BBFC86B2FE2FF5F3A95BC48 (void);
extern void GridInformation__ctor_mE7087D203FB7A8197EDC80619D868757DCBA123D (void);
extern void GridInformationKey_Equals_m1A88DEDEB215E2615ECCA8358503046C3E9C14BD (void);
extern void GridInformationKey_GetHashCode_mE9B43A8313A3909C6D4A7812A0E550D148F89B60 (void);
extern void U3CU3Ec__cctor_m0C23339AB84EE1A39FDF1EC078144044A28DDDD6 (void);
extern void U3CU3Ec__ctor_m5A3E4B8F6DC5756A2C7394CD0622DE38A2D47339 (void);
extern void U3CU3Ec_U3CGetAllPositionsU3Eb__33_1_m492821B6907D952BDBDA649230048228A2E5A9A9 (void);
extern void U3CU3Ec__DisplayClass33_0__ctor_m95DE943B2460DAD44CD99D89569AD772119E9839 (void);
extern void U3CU3Ec__DisplayClass33_0_U3CGetAllPositionsU3Eb__0_mBB0E1E43D1401E23F0FF4636E44FAB03CFD1D732 (void);
extern void AnimatedTile_GetTileData_mDF1FBA9B2703D58D14A7A5C9BB3E7A0084551A31 (void);
extern void AnimatedTile_GetTileAnimationData_m2ED843E2BB544C7A4693274E40F18D49F1DAD208 (void);
extern void AnimatedTile__ctor_m0FFE5D803298F6F2DB47D7078704B600B507DD9B (void);
extern void AutoTile_get_random_mB7FE2B369A74E6E7FBC086F4C2B11AD7F6EEE7DE (void);
extern void AutoTile_set_random_m6E87E23DE7E4A380807D3F3FB593C695EA404925 (void);
extern void AutoTile_RefreshTile_m633991B2FCB099B9BED9122457831D679910A35D (void);
extern void AutoTile_GetTileData_mA75CFD2EADCA688C0F47333A6FEFA093B68A3256 (void);
extern void AutoTile_AddSprite_m7B8EDFDE7C8DB0A95B1C3EA96CA4915D85E825A2 (void);
extern void AutoTile_RemoveSprite_mD3AE77E3C91E8EE95A18B947A2E107D59B909FB6 (void);
extern void AutoTile_Validate_m4348836EFB7ABB8573E2672391B67EF77BF55B87 (void);
extern void AutoTile_Convert2x2Mask_m797F0E4B1A6C5B190F3CA548BDE46CEC78A65D7F (void);
extern void AutoTile_Convert3x3Mask_m81E1664BC44C78978559D5E635DF830E86B32F92 (void);
extern void AutoTile__ctor_mAAFFD14E614BC804456936470805D9B29A942123 (void);
extern void AutoTile__cctor_mA9BA3841BF97C8E91401A4B179C62B7F61552A26 (void);
extern void AutoTileData__ctor_m5F5279A133F95B701A0A8936991A4EBA1BD36FDD (void);
extern void AutoTileDictionary__ctor_mF988EF792657ACA37FE6338013964EBB08BDD1E3 (void);
extern void AdvancedRuleOverrideTile_get_Item_m05B0AF56DA599E88005FF4295D0631DD96FF5595 (void);
extern void AdvancedRuleOverrideTile_set_Item_m21F916C8C049CD66C6CAFC76769943B3431523E0 (void);
extern void AdvancedRuleOverrideTile_ApplyOverrides_mA769A012EE7BD9A499CE9A275B597F508BBAACF3 (void);
extern void AdvancedRuleOverrideTile_GetOverrides_m584A8A124B6C35A05901DD2A3044E321397766AD (void);
extern void AdvancedRuleOverrideTile_Override_m3F6DB465C1D18A720ABC7E11700048188FA99ED6 (void);
extern void AdvancedRuleOverrideTile__ctor_m75215A3904DE672166A506441977D90848FE6517 (void);
extern void U3CU3Ec__DisplayClass8_0__ctor_m63A5D4045DCFFB4EC2721931140B5FC77A973665 (void);
extern void U3CU3Ec__DisplayClass8_0_U3CGetOverridesU3Eb__0_m179ED197E2500F45F420DF7811361B198B77E13F (void);
extern void RuleOverrideTile_get_Item_mC8C6D1F175AAF6DAEC7FC18799C41B00307A4523 (void);
extern void RuleOverrideTile_set_Item_mFDDB260640E074198E00BF0D0C5208B69F703B4D (void);
extern void RuleOverrideTile_get_Item_m4D0492BF2F534CCAF9BBBC989F3CC6D615D8B023 (void);
extern void RuleOverrideTile_set_Item_m64697A0B0F6E2F1AF05E86F53B0E9DF57D2E982B (void);
extern void RuleOverrideTile_OnEnable_m6968BB87F2A7566938B02826009D5FD00AE1C1A3 (void);
extern void RuleOverrideTile_CreateInstanceTile_mAB9FF64AA0B2284F0223B956003FC8C24F9FB6C4 (void);
extern void RuleOverrideTile_ApplyOverrides_m428D10F52D1ACD212B6F2B81A2DC0F0A208F3F55 (void);
extern void RuleOverrideTile_ApplyOverrides_m4956A834DC2F5EEB378858946C6157FA22558E16 (void);
extern void RuleOverrideTile_GetOverrides_m6E3BE85FCEB7CC276F2B7F39108FD740D534D222 (void);
extern void RuleOverrideTile_GetOverrides_mB5FEC5D6754963D2A76EEB3E870435F601A54BBA (void);
extern void RuleOverrideTile_Override_m5263016808B37E86D5DE876717DE095E82FC95CE (void);
extern void RuleOverrideTile_PrepareOverride_m83EA5D66B3719D7F91BA19EE0329DB39BF63E9A5 (void);
extern void RuleOverrideTile_GetTileAnimationData_m27B91334C6419B9D0A37A7B5C6C161BA6613B7B9 (void);
extern void RuleOverrideTile_GetTileData_m5DD0C44804BD2A3C937B3378BDDA55706064EF15 (void);
extern void RuleOverrideTile_RefreshTile_m0428D4368A4B1162D168F70D0B2E5C77FD8046AF (void);
extern void RuleOverrideTile_StartUp_mC3A631F743E51E88D80A3F78D87980610DF006A2 (void);
extern void RuleOverrideTile__ctor_m9B92C4457F8B15369F4122A8710CF5BFB659BC07 (void);
extern void TileSpritePair__ctor_m53A284CB5FFE2B5FD8F65165EF3531EAB3D07ACB (void);
extern void TileGameObjectPair__ctor_m2482E5EAB45873446C42E9B6135DF92023D33EDB (void);
extern void U3CU3Ec__cctor_mEAF65E7028A5D7D313482C67959FF72BB8B503D1 (void);
extern void U3CU3Ec__ctor_mA10F77D280B3221DACF66A9EA14811AECB6EDA2B (void);
extern void U3CU3Ec_U3CPrepareOverrideU3Eb__17_0_m90D01A3393C83FA4CBDEE714EC7052695208B905 (void);
extern void U3CU3Ec__DisplayClass17_0__ctor_mEA67FDD77BD544CD6B4DF009D9C1FC4939288B3F (void);
extern void U3CU3Ec__DisplayClass17_0_U3CPrepareOverrideU3Eb__1_mBFFB8A33C7A45A0DB6BD3747B5C78B00A8836A1D (void);
extern void U3CU3Ec__DisplayClass6_0__ctor_m87070FE73CA888514A6C6B551E464F721C9AA3D3 (void);
extern void U3CU3Ec__DisplayClass6_0_U3Cset_ItemU3Eb__0_m3820D5766899DB664C1A479F7CD6122573AFE79A (void);
extern void U3CU3Ec__DisplayClass9_0__ctor_m4195D9E8B74E1C571D15C2B41E0835A535E7C00B (void);
extern void U3CU3Ec__DisplayClass9_0_U3Cset_ItemU3Eb__0_m11B25BB0893BE61F2F9E028EF7BF86A64EB334FF (void);
static Il2CppMethodPointer s_methodPointers[142] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mF1559E8AAA9EC05EB865BBD8310E647B60419315,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m358685E22263F4E473610EB1BFC46D0442CF85B0,
	NULL,
	NULL,
	HexagonalRuleTile_get_m_RotationAngle_mDD58A85556B3EE5B5BF67CD6774BC9995241DE0C,
	HexagonalRuleTile_TilemapPositionToWorldPosition_mC00CEB616152AC2903B4D09CD6B56244BA942DB0,
	HexagonalRuleTile_WorldPositionToTilemapPosition_mD227E09487435419F76756F879839C289462F776,
	HexagonalRuleTile_GetOffsetPosition_m9418CC09843754C9D33810727CAB52FC89ADB867,
	HexagonalRuleTile_GetOffsetPositionReverse_mB92B718ABA28C5B3CFDDAABF5B25290E248F2834,
	HexagonalRuleTile_GetRotatedPosition_m46DF5AF7EDB64C4E2D4886BA02D24649F763273D,
	HexagonalRuleTile_GetMirroredPosition_mF6D2411014AD1E645837C87F0B813FE17CAE9A78,
	HexagonalRuleTile__ctor_mD091F912A02CEC20B9D34A14BB4C005E6C2C2C16,
	HexagonalRuleTile__cctor_m0FE1FE30B69EEB3DFF7F30DCF8AEF83ED74586E0,
	NULL,
	NULL,
	IsometricRuleTile__ctor_m016CC4482ED629E80FAEAE08B90A342573AF9900,
	NULL,
	NULL,
	RuleTile_get_m_NeighborType_mD22089281EE41453168127DB5472F852939476EE,
	RuleTile_get_m_RotationAngle_mC4E7FA2B9537A51B8C2D51E6287622DF5301531D,
	RuleTile_get_m_RotationCount_m2CBDA8836FB4D88BA1A8C3060C68F6D95EEFBCBF,
	RuleTile_get_neighborPositions_mE48C37907633A307D34ABDE27BDBB455F0FEB2EA,
	RuleTile_UpdateNeighborPositions_mF1E0734CD82B0DDACEC74E399C7897282ECED6B2,
	RuleTile_StartUp_mE42191406441174CEDC669772F1E5DE11654F066,
	RuleTile_GetTileData_m808E08D6AFC5893D1A517DACFF61B3DAA9F2F431,
	RuleTile_GetPerlinValue_m0AF7168A4C778257F23E60B1784A584436C5CFAA,
	RuleTile_IsTilemapUsedTilesChange_mFF2088F8B51C955A524BF269E6DA56B88C947688,
	RuleTile_CachingTilemapNeighborPositions_mE26EBD1F5569810CB62C7FBCE4B373E151025371,
	RuleTile_NeedRelease_mD59755B112E58C50BA4BEA8D5673DA41BF01964C,
	RuleTile_ReleaseDestroyedTilemapCacheData_mFCB311A08075FB57B6EE2072DCD239CB3F72F323,
	RuleTile_GetTileAnimationData_mBDC09525FD6F454EF1F12154AB0E70EE9A23B8C4,
	RuleTile_RefreshTile_mE2CE05AA63E86494EB701710E59BAC3E59BD383E,
	RuleTile_RuleMatches_mC46AD557F2C85668BB5C48CE5D58904B48AEA96B,
	RuleTile_ApplyRandomTransform_m1451FBC0BF9FB1C8B7CF73987D05913F6E519919,
	RuleTile_GetCustomFields_m20E5229812AC3DBD1A2765F27163928A7194C10C,
	RuleTile_RuleMatch_mDBA6EDCC409125D42CBE7B4F63470131D7CD8EF1,
	RuleTile_RuleMatches_mA8FAAD266B6A044BE463CAC16FD74982E7051C92,
	RuleTile_RuleMatches_m8766CC8F8EE91C0DA0CEB8D8DE482DFFECBE5D3A,
	RuleTile_GetRotatedPosition_m03F5F7263C0A1A372439CDD02A54B34602FB45B0,
	RuleTile_GetMirroredPosition_mAFA0B2F9DD1DB590BDCFED89F93ABD2E334DF266,
	RuleTile_GetOffsetPosition_m14D02BC751910D45D16665AF74FD347F9343F8AF,
	RuleTile_GetOffsetPositionReverse_m617F0F4CC3D6F5EAD0F2D0AA9710026F1A5ED2BF,
	RuleTile__ctor_m3548BB5D248C38FDC759A225E4D48ECDCDD247DE,
	RuleTile__cctor_m9A29A13993DC4AA524187C2015D4D19AFC78F83F,
	TilingRuleOutput__ctor_m01155B9B604B5322723E9ACCC2A2ED47A0EBB990,
	Neighbor__ctor_m8D6045542EF5AB2870BD8B905688D59B082B7257,
	TilingRule_Clone_m783F847A0C6A23C9C67292C9F342CDE2E59E8E04,
	TilingRule_GetNeighbors_m47FDA38A6F313862E10471132DE32D06B0D3AD79,
	TilingRule_ApplyNeighbors_mB8BEB69614D404E4A14DC322B14EFC2E19216DCF,
	TilingRule_GetBounds_mB47D6F463F0B4E5FB66AFCB37704556860C0EE16,
	TilingRule__ctor_m1873560C84F01E67080E925344CA1A504BCF22EA,
	DontOverride__ctor_mFF94FEE9E232640F1AAC28BC4F8C252F516789DB,
	U3CU3Ec__cctor_mC047430393DA663C6B82327A1E157E40383920BC,
	U3CU3Ec__ctor_m78922446FE7BCC59AE6DCC5B59FDE30424E13F16,
	U3CU3Ec_U3CGetCustomFieldsU3Eb__27_0_mB606F85896AA35419AA9B9D86A18449F490E7B7E,
	U3CU3Ec_U3CGetCustomFieldsU3Eb__27_1_mFEDABFC378E706B7B3DB01D4FA028A5EA1809BC1,
	U3CU3Ec_U3CGetCustomFieldsU3Eb__27_2_m58DF262A5C7C2C7131FED522B02174DBC4B2841B,
	U3CU3Ec__DisplayClass27_0__ctor_m5F733F0DD63CFCD6F3B7FE49B4A30634AFF3E98C,
	U3CU3Ec__DisplayClass27_0_U3CGetCustomFieldsU3Eb__3_m7650E2C633F6281405F126D781765E939FAB10BB,
	GridInformation_get_PositionProperties_m793F7FB3FA660B9E0B7BE4B7EA53D7799DA823B3,
	GridInformation_Reset_m211B34303B6C1271227F3F8CABAAB77F3B58A1E6,
	GridInformation_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mD0EC6C20408203AD81A8E5ACA56065DBC37E6B55,
	GridInformation_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m084CD3BB6D503172BBC2C12412AB479256918E68,
	NULL,
	GridInformation_SetPositionProperty_mC1BF51F7911CA27B9083E0E683FA2E9B4A7F12E0,
	GridInformation_SetPositionProperty_mFB8458F1FFDCA731836962EC730E2EDFF20D852B,
	GridInformation_SetPositionProperty_m9D1A8EF6A592141B5B17DA04CAB28F6ADD9FD0F6,
	GridInformation_SetPositionProperty_mE7467B3537A2575000779153F7884CB683E220DF,
	GridInformation_SetPositionProperty_m08BE473643FD07837AE6AC3A1CFEA48176105B45,
	GridInformation_SetPositionProperty_mBE2D8029E4CD5E061852D21E08BC8000530589F1,
	GridInformation_SetPositionProperty_mFDB93F193695B4727C8A4F27A4C1405624E3415D,
	NULL,
	GridInformation_GetPositionProperty_mD281CD98AE570B33EC6D84FAB9676E9AAE81671A,
	GridInformation_GetPositionProperty_mC94CDE87945AA9F78B7CB698554F581DCBB306BE,
	GridInformation_GetPositionProperty_mF59B2D16494EA51D7A965628F4A844CE8E811A91,
	GridInformation_GetPositionProperty_mF3A3512DBCB9EA2C5E309B56AD57DCBB80DA4B6A,
	GridInformation_GetPositionProperty_mBCE407A8B4243785CD45D9E7A08864DD7E1B6609,
	GridInformation_ErasePositionProperty_mBE1D65A52B97212AF5D9592CA7F0AE47204E35E0,
	GridInformation_GetAllPositions_mE64C529198C90D397BBFC86B2FE2FF5F3A95BC48,
	GridInformation__ctor_mE7087D203FB7A8197EDC80619D868757DCBA123D,
	GridInformationKey_Equals_m1A88DEDEB215E2615ECCA8358503046C3E9C14BD,
	GridInformationKey_GetHashCode_mE9B43A8313A3909C6D4A7812A0E550D148F89B60,
	U3CU3Ec__cctor_m0C23339AB84EE1A39FDF1EC078144044A28DDDD6,
	U3CU3Ec__ctor_m5A3E4B8F6DC5756A2C7394CD0622DE38A2D47339,
	U3CU3Ec_U3CGetAllPositionsU3Eb__33_1_m492821B6907D952BDBDA649230048228A2E5A9A9,
	U3CU3Ec__DisplayClass33_0__ctor_m95DE943B2460DAD44CD99D89569AD772119E9839,
	U3CU3Ec__DisplayClass33_0_U3CGetAllPositionsU3Eb__0_mBB0E1E43D1401E23F0FF4636E44FAB03CFD1D732,
	AnimatedTile_GetTileData_mDF1FBA9B2703D58D14A7A5C9BB3E7A0084551A31,
	AnimatedTile_GetTileAnimationData_m2ED843E2BB544C7A4693274E40F18D49F1DAD208,
	AnimatedTile__ctor_m0FFE5D803298F6F2DB47D7078704B600B507DD9B,
	AutoTile_get_random_mB7FE2B369A74E6E7FBC086F4C2B11AD7F6EEE7DE,
	AutoTile_set_random_m6E87E23DE7E4A380807D3F3FB593C695EA404925,
	AutoTile_RefreshTile_m633991B2FCB099B9BED9122457831D679910A35D,
	AutoTile_GetTileData_mA75CFD2EADCA688C0F47333A6FEFA093B68A3256,
	AutoTile_AddSprite_m7B8EDFDE7C8DB0A95B1C3EA96CA4915D85E825A2,
	AutoTile_RemoveSprite_mD3AE77E3C91E8EE95A18B947A2E107D59B909FB6,
	AutoTile_Validate_m4348836EFB7ABB8573E2672391B67EF77BF55B87,
	AutoTile_Convert2x2Mask_m797F0E4B1A6C5B190F3CA548BDE46CEC78A65D7F,
	AutoTile_Convert3x3Mask_m81E1664BC44C78978559D5E635DF830E86B32F92,
	AutoTile__ctor_mAAFFD14E614BC804456936470805D9B29A942123,
	AutoTile__cctor_mA9BA3841BF97C8E91401A4B179C62B7F61552A26,
	NULL,
	NULL,
	NULL,
	AutoTileData__ctor_m5F5279A133F95B701A0A8936991A4EBA1BD36FDD,
	AutoTileDictionary__ctor_mF988EF792657ACA37FE6338013964EBB08BDD1E3,
	AdvancedRuleOverrideTile_get_Item_m05B0AF56DA599E88005FF4295D0631DD96FF5595,
	AdvancedRuleOverrideTile_set_Item_m21F916C8C049CD66C6CAFC76769943B3431523E0,
	AdvancedRuleOverrideTile_ApplyOverrides_mA769A012EE7BD9A499CE9A275B597F508BBAACF3,
	AdvancedRuleOverrideTile_GetOverrides_m584A8A124B6C35A05901DD2A3044E321397766AD,
	AdvancedRuleOverrideTile_Override_m3F6DB465C1D18A720ABC7E11700048188FA99ED6,
	AdvancedRuleOverrideTile__ctor_m75215A3904DE672166A506441977D90848FE6517,
	U3CU3Ec__DisplayClass8_0__ctor_m63A5D4045DCFFB4EC2721931140B5FC77A973665,
	U3CU3Ec__DisplayClass8_0_U3CGetOverridesU3Eb__0_m179ED197E2500F45F420DF7811361B198B77E13F,
	RuleOverrideTile_get_Item_mC8C6D1F175AAF6DAEC7FC18799C41B00307A4523,
	RuleOverrideTile_set_Item_mFDDB260640E074198E00BF0D0C5208B69F703B4D,
	RuleOverrideTile_get_Item_m4D0492BF2F534CCAF9BBBC989F3CC6D615D8B023,
	RuleOverrideTile_set_Item_m64697A0B0F6E2F1AF05E86F53B0E9DF57D2E982B,
	RuleOverrideTile_OnEnable_m6968BB87F2A7566938B02826009D5FD00AE1C1A3,
	RuleOverrideTile_CreateInstanceTile_mAB9FF64AA0B2284F0223B956003FC8C24F9FB6C4,
	RuleOverrideTile_ApplyOverrides_m428D10F52D1ACD212B6F2B81A2DC0F0A208F3F55,
	RuleOverrideTile_ApplyOverrides_m4956A834DC2F5EEB378858946C6157FA22558E16,
	RuleOverrideTile_GetOverrides_m6E3BE85FCEB7CC276F2B7F39108FD740D534D222,
	RuleOverrideTile_GetOverrides_mB5FEC5D6754963D2A76EEB3E870435F601A54BBA,
	RuleOverrideTile_Override_m5263016808B37E86D5DE876717DE095E82FC95CE,
	RuleOverrideTile_PrepareOverride_m83EA5D66B3719D7F91BA19EE0329DB39BF63E9A5,
	RuleOverrideTile_GetTileAnimationData_m27B91334C6419B9D0A37A7B5C6C161BA6613B7B9,
	RuleOverrideTile_GetTileData_m5DD0C44804BD2A3C937B3378BDDA55706064EF15,
	RuleOverrideTile_RefreshTile_m0428D4368A4B1162D168F70D0B2E5C77FD8046AF,
	RuleOverrideTile_StartUp_mC3A631F743E51E88D80A3F78D87980610DF006A2,
	RuleOverrideTile__ctor_m9B92C4457F8B15369F4122A8710CF5BFB659BC07,
	TileSpritePair__ctor_m53A284CB5FFE2B5FD8F65165EF3531EAB3D07ACB,
	TileGameObjectPair__ctor_m2482E5EAB45873446C42E9B6135DF92023D33EDB,
	U3CU3Ec__cctor_mEAF65E7028A5D7D313482C67959FF72BB8B503D1,
	U3CU3Ec__ctor_mA10F77D280B3221DACF66A9EA14811AECB6EDA2B,
	U3CU3Ec_U3CPrepareOverrideU3Eb__17_0_m90D01A3393C83FA4CBDEE714EC7052695208B905,
	U3CU3Ec__DisplayClass17_0__ctor_mEA67FDD77BD544CD6B4DF009D9C1FC4939288B3F,
	U3CU3Ec__DisplayClass17_0_U3CPrepareOverrideU3Eb__1_mBFFB8A33C7A45A0DB6BD3747B5C78B00A8836A1D,
	U3CU3Ec__DisplayClass6_0__ctor_m87070FE73CA888514A6C6B551E464F721C9AA3D3,
	U3CU3Ec__DisplayClass6_0_U3Cset_ItemU3Eb__0_m3820D5766899DB664C1A479F7CD6122573AFE79A,
	U3CU3Ec__DisplayClass9_0__ctor_m4195D9E8B74E1C571D15C2B41E0835A535E7C00B,
	U3CU3Ec__DisplayClass9_0_U3Cset_ItemU3Eb__0_m11B25BB0893BE61F2F9E028EF7BF86A64EB334FF,
};
extern void GridInformationKey_Equals_m1A88DEDEB215E2615ECCA8358503046C3E9C14BD_AdjustorThunk (void);
extern void GridInformationKey_GetHashCode_mE9B43A8313A3909C6D4A7812A0E550D148F89B60_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[2] = 
{
	{ 0x06000051, GridInformationKey_Equals_m1A88DEDEB215E2615ECCA8358503046C3E9C14BD_AdjustorThunk },
	{ 0x06000052, GridInformationKey_GetHashCode_mE9B43A8313A3909C6D4A7812A0E550D148F89B60_AdjustorThunk },
};
static const int32_t s_InvokerIndices[142] = 
{
	34302,
	21016,
	-1,
	-1,
	20694,
	32702,
	32708,
	6260,
	6260,
	6259,
	3441,
	21016,
	34252,
	-1,
	-1,
	21016,
	-1,
	-1,
	20761,
	20694,
	20694,
	20761,
	21016,
	3168,
	3899,
	25767,
	27499,
	29987,
	34103,
	34252,
	3163,
	8204,
	1482,
	2228,
	13795,
	4941,
	844,
	843,
	6259,
	3441,
	6260,
	6260,
	21016,
	34252,
	21016,
	21016,
	20761,
	20761,
	15968,
	20548,
	21016,
	21016,
	34252,
	21016,
	11681,
	11681,
	11681,
	21016,
	11681,
	20761,
	21016,
	21016,
	21016,
	-1,
	3167,
	3168,
	3169,
	3166,
	3168,
	3165,
	1513,
	-1,
	3256,
	3357,
	3397,
	3196,
	3191,
	5189,
	13820,
	21016,
	12042,
	20694,
	34252,
	21016,
	14299,
	21016,
	12042,
	3899,
	3163,
	21016,
	20550,
	15757,
	8204,
	3899,
	3774,
	8045,
	21016,
	14223,
	14223,
	21016,
	34252,
	-1,
	-1,
	-1,
	21016,
	21016,
	13820,
	7995,
	15968,
	7957,
	21016,
	21016,
	21016,
	10561,
	13820,
	7995,
	13820,
	7995,
	21016,
	21016,
	15968,
	15968,
	7957,
	7957,
	21016,
	21016,
	3163,
	3899,
	8204,
	3168,
	21016,
	21016,
	21016,
	34252,
	21016,
	13820,
	21016,
	13820,
	21016,
	11681,
	21016,
	11681,
};
static const Il2CppTokenRangePair s_rgctxIndices[5] = 
{
	{ 0x02000004, { 0, 1 } },
	{ 0x02000006, { 1, 1 } },
	{ 0x02000008, { 2, 1 } },
	{ 0x0200001A, { 4, 29 } },
	{ 0x06000048, { 3, 1 } },
};
extern const uint32_t g_rgctx_T_t4108675C13CEA6208135D513229D6174CE90106C;
extern const uint32_t g_rgctx_T_t33DCF53C3C83299A1900947B49FC8C6C64F94E85;
extern const uint32_t g_rgctx_T_t074C0444F25119C8A04D66CE02781835438B99C3;
extern const uint32_t g_rgctx_T_t4D910725EDAE3A87DD7F181927B6227528996ED8;
extern const uint32_t g_rgctx_Dictionary_2_Clear_mB18E7F48DF8B4CDB54EF2A87D0D07B949C026399;
extern const uint32_t g_rgctx_SerializedDictionary_2_t23AD56FDC6C1F70B38F8FA85F6CC66A333510C4E;
extern const uint32_t g_rgctx_List_1_t1C21BC1308565CA774E9AB6C9789B2D26AE7E430;
extern const uint32_t g_rgctx_List_1_get_Item_mEFDB45C9524367A3EF76BBC21736AC91762AE737;
extern const uint32_t g_rgctx_TKey_t843DB3480BB870494586AD7EBE198FF754339D35;
extern const uint32_t g_rgctx_List_1_t84E3E67A1AC58216A4389976785653468B000D9B;
extern const uint32_t g_rgctx_List_1_get_Item_m2DF0C9E402C27170904D5C110024C843DEC328F1;
extern const uint32_t g_rgctx_TValue_tBBFF8DAFDD1E30B351B8AC0F88AF6F920E45AFFD;
extern const uint32_t g_rgctx_Dictionary_2_set_Item_mCA9BBFACA879BF97F97317A7FB8FE8FB4805F01A;
extern const uint32_t g_rgctx_List_1_get_Count_mF11CD86B705E1C71DA96E789D8B155C3DCA79F33;
extern const uint32_t g_rgctx_List_1_get_Count_m05D425BEC1FD0AD929CC39E20413E47B280BA0FA;
extern const uint32_t g_rgctx_List_1_Clear_m4990E3A5E1F8DB5D7919AC51D406D2DF22523B17;
extern const uint32_t g_rgctx_List_1_Clear_mFC7466950AD82CCC666515A5FA1DFECB52C61A37;
extern const uint32_t g_rgctx_Dictionary_2_GetEnumerator_mDC30AF7C8544313C488EF1112ECD15496B50184B;
extern const uint32_t g_rgctx_Enumerator_t879602F09DFAC05DDC15FF8EE43BAE0C4409503A;
extern const uint32_t g_rgctx_Enumerator_get_Current_mDE5AC4DC60196CA28E2CADDFFA910746620E864A;
extern const uint32_t g_rgctx_Enumerator_t879602F09DFAC05DDC15FF8EE43BAE0C4409503A;
extern const uint32_t g_rgctx_KeyValuePair_2_t3B9C6BE21A5033D849BD36B616446EEBB0FBD755;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Key_m3CB89413520F99A3DCC2889CD75DB051C9A12326;
extern const uint32_t g_rgctx_KeyValuePair_2_t3B9C6BE21A5033D849BD36B616446EEBB0FBD755;
extern const uint32_t g_rgctx_List_1_Add_mD7E0B95CA4E9EFBBA4CF7C82BE0BBCC744E1199A;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Value_mEF0B1DEE2916DFA6DAB9D6AE9F519D5D3605DA6F;
extern const uint32_t g_rgctx_List_1_Add_m8BCA2DBBDC856A20C10AD15523475EE889EF741D;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m2448DD2437DF04ECE300A76CE9F0DEFAAA6E24DC;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_t879602F09DFAC05DDC15FF8EE43BAE0C4409503A_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_List_1__ctor_mC82106CDD5BAFE0340ECDEEB75C7746C472B17A9;
extern const uint32_t g_rgctx_List_1__ctor_mEA21EE72D9D2D7476BFCB2C5E25FB0E92BB96E90;
extern const uint32_t g_rgctx_Dictionary_2__ctor_mA12CDFE643390575C0B2D3611554087BC687DBE8;
extern const uint32_t g_rgctx_Dictionary_2_tD2B86C64EA5D40DEE1FD44AF4043F59FB9253674;
static const Il2CppRGCTXDefinition s_rgctxValues[33] = 
{
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t4108675C13CEA6208135D513229D6174CE90106C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t33DCF53C3C83299A1900947B49FC8C6C64F94E85 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t074C0444F25119C8A04D66CE02781835438B99C3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4D910725EDAE3A87DD7F181927B6227528996ED8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Clear_mB18E7F48DF8B4CDB54EF2A87D0D07B949C026399 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SerializedDictionary_2_t23AD56FDC6C1F70B38F8FA85F6CC66A333510C4E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t1C21BC1308565CA774E9AB6C9789B2D26AE7E430 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mEFDB45C9524367A3EF76BBC21736AC91762AE737 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_t843DB3480BB870494586AD7EBE198FF754339D35 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t84E3E67A1AC58216A4389976785653468B000D9B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m2DF0C9E402C27170904D5C110024C843DEC328F1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_tBBFF8DAFDD1E30B351B8AC0F88AF6F920E45AFFD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_set_Item_mCA9BBFACA879BF97F97317A7FB8FE8FB4805F01A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mF11CD86B705E1C71DA96E789D8B155C3DCA79F33 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m05D425BEC1FD0AD929CC39E20413E47B280BA0FA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m4990E3A5E1F8DB5D7919AC51D406D2DF22523B17 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_mFC7466950AD82CCC666515A5FA1DFECB52C61A37 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_GetEnumerator_mDC30AF7C8544313C488EF1112ECD15496B50184B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t879602F09DFAC05DDC15FF8EE43BAE0C4409503A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_mDE5AC4DC60196CA28E2CADDFFA910746620E864A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t879602F09DFAC05DDC15FF8EE43BAE0C4409503A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_t3B9C6BE21A5033D849BD36B616446EEBB0FBD755 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Key_m3CB89413520F99A3DCC2889CD75DB051C9A12326 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_t3B9C6BE21A5033D849BD36B616446EEBB0FBD755 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_mD7E0B95CA4E9EFBBA4CF7C82BE0BBCC744E1199A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Value_mEF0B1DEE2916DFA6DAB9D6AE9F519D5D3605DA6F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m8BCA2DBBDC856A20C10AD15523475EE889EF741D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m2448DD2437DF04ECE300A76CE9F0DEFAAA6E24DC },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_t879602F09DFAC05DDC15FF8EE43BAE0C4409503A_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_mC82106CDD5BAFE0340ECDEEB75C7746C472B17A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_mEA21EE72D9D2D7476BFCB2C5E25FB0E92BB96E90 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_mA12CDFE643390575C0B2D3611554087BC687DBE8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_tD2B86C64EA5D40DEE1FD44AF4043F59FB9253674 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_2D_Tilemap_Extras_CodeGenModule;
const Il2CppCodeGenModule g_Unity_2D_Tilemap_Extras_CodeGenModule = 
{
	"Unity.2D.Tilemap.Extras.dll",
	142,
	s_methodPointers,
	2,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	5,
	s_rgctxIndices,
	33,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
