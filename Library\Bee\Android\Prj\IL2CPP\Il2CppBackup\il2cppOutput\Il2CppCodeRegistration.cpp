﻿#include "pch-cpp.hpp"








IL2CPP_EXTERN_C const Il2CppMethodPointer g_ReversePInvokeWrapperPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_Il2CppGenericMethodPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_Il2CppGenericAdjustorThunks[];
IL2CPP_EXTERN_C const InvokerMethod g_Il2CppInvokerPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_UnresolvedVirtualMethodPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_UnresolvedInstanceMethodPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_UnresolvedStaticMethodPointers[];
IL2CPP_EXTERN_C Il2CppInteropData g_Il2CppInteropData[];
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_CFXRDemo_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_CFXRRuntime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_CryptoLibrary_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_EventFramework_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Google_Play_AppUpdate_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Google_Play_Common_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Google_Play_Core_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Google_Play_Games_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_KinoBloom_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Mono_Security_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Newtonsoft_Json_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_OwnMatch3_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_PrimeTween_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Configuration_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Core_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Data_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Drawing_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Numerics_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Runtime_CompilerServices_Unsafe_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Runtime_Serialization_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Xml_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Xml_Linq_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UniTask_Addressables_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UniTask_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UniTask_DOTween_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UniTask_Linq_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UniTask_TextMeshPro_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AndroidJNIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AnimationModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AssetBundleModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AudioModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_CoreModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_DirectorModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_GameCenterModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_GridModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_HierarchyCoreModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_IMGUIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_InputForUIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_InputLegacyModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_InputModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_JSONSerializeModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_ParticleSystemModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_Physics2DModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_PropertiesModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_SharedInternalsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_SpriteMaskModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_SpriteShapeModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TerrainModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TextCoreFontEngineModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TextCoreTextEngineModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TextRenderingModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TilemapModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UIElementsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UI_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityWebRequestAssetBundleModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityWebRequestModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityWebRequestTextureModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_VFXModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_2D_Animation_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_2D_Common_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_2D_IK_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_2D_SpriteShape_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_2D_Tilemap_Extras_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_AppUI_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_AppUI_InternalAPIBridge_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Burst_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Burst_Unsafe_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Collections_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Collections_LowLevel_ILSupport_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_InputSystem_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_InputSystem_ForUI_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_InternalAPIEngineBridge_001_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_LevelPlay_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Mathematics_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_RenderPipeline_Universal_ShaderLibrary_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_RenderPipelines_Core_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_RenderPipelines_Core_Runtime_Shared_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_RenderPipelines_GPUDriven_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_RenderPipelines_Universal_2D_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_RenderPipelines_Universal_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_TextMeshPro_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Timeline_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_VisualEffectGraph_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_ZString_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g___Generated_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_mscorlib_CodeGenModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule* g_CodeGenModules[];
const Il2CppCodeGenModule* g_CodeGenModules[92] = 
{
	(&g_AssemblyU2DCSharp_CodeGenModule),
	(&g_CFXRDemo_CodeGenModule),
	(&g_CFXRRuntime_CodeGenModule),
	(&g_CryptoLibrary_CodeGenModule),
	(&g_EventFramework_CodeGenModule),
	(&g_Google_Play_AppUpdate_CodeGenModule),
	(&g_Google_Play_Common_CodeGenModule),
	(&g_Google_Play_Core_CodeGenModule),
	(&g_Google_Play_Games_CodeGenModule),
	(&g_KinoBloom_Runtime_CodeGenModule),
	(&g_Mono_Security_CodeGenModule),
	(&g_Newtonsoft_Json_CodeGenModule),
	(&g_OwnMatch3_CodeGenModule),
	(&g_PrimeTween_Runtime_CodeGenModule),
	(&g_System_CodeGenModule),
	(&g_System_Configuration_CodeGenModule),
	(&g_System_Core_CodeGenModule),
	(&g_System_Data_CodeGenModule),
	(&g_System_Drawing_CodeGenModule),
	(&g_System_Numerics_CodeGenModule),
	(&g_System_Runtime_CompilerServices_Unsafe_CodeGenModule),
	(&g_System_Runtime_Serialization_CodeGenModule),
	(&g_System_Xml_CodeGenModule),
	(&g_System_Xml_Linq_CodeGenModule),
	(&g_UniTask_Addressables_CodeGenModule),
	(&g_UniTask_CodeGenModule),
	(&g_UniTask_DOTween_CodeGenModule),
	(&g_UniTask_Linq_CodeGenModule),
	(&g_UniTask_TextMeshPro_CodeGenModule),
	(&g_UnityEngine_AIModule_CodeGenModule),
	(&g_UnityEngine_AndroidJNIModule_CodeGenModule),
	(&g_UnityEngine_AnimationModule_CodeGenModule),
	(&g_UnityEngine_AssetBundleModule_CodeGenModule),
	(&g_UnityEngine_AudioModule_CodeGenModule),
	(&g_UnityEngine_CodeGenModule),
	(&g_UnityEngine_CoreModule_CodeGenModule),
	(&g_UnityEngine_DirectorModule_CodeGenModule),
	(&g_UnityEngine_GameCenterModule_CodeGenModule),
	(&g_UnityEngine_GridModule_CodeGenModule),
	(&g_UnityEngine_HierarchyCoreModule_CodeGenModule),
	(&g_UnityEngine_IMGUIModule_CodeGenModule),
	(&g_UnityEngine_InputForUIModule_CodeGenModule),
	(&g_UnityEngine_InputLegacyModule_CodeGenModule),
	(&g_UnityEngine_InputModule_CodeGenModule),
	(&g_UnityEngine_JSONSerializeModule_CodeGenModule),
	(&g_UnityEngine_ParticleSystemModule_CodeGenModule),
	(&g_UnityEngine_Physics2DModule_CodeGenModule),
	(&g_UnityEngine_PhysicsModule_CodeGenModule),
	(&g_UnityEngine_PropertiesModule_CodeGenModule),
	(&g_UnityEngine_SharedInternalsModule_CodeGenModule),
	(&g_UnityEngine_SpriteMaskModule_CodeGenModule),
	(&g_UnityEngine_SpriteShapeModule_CodeGenModule),
	(&g_UnityEngine_TerrainModule_CodeGenModule),
	(&g_UnityEngine_TextCoreFontEngineModule_CodeGenModule),
	(&g_UnityEngine_TextCoreTextEngineModule_CodeGenModule),
	(&g_UnityEngine_TextRenderingModule_CodeGenModule),
	(&g_UnityEngine_TilemapModule_CodeGenModule),
	(&g_UnityEngine_UIElementsModule_CodeGenModule),
	(&g_UnityEngine_UIModule_CodeGenModule),
	(&g_UnityEngine_UI_CodeGenModule),
	(&g_UnityEngine_UnityWebRequestAssetBundleModule_CodeGenModule),
	(&g_UnityEngine_UnityWebRequestModule_CodeGenModule),
	(&g_UnityEngine_UnityWebRequestTextureModule_CodeGenModule),
	(&g_UnityEngine_VFXModule_CodeGenModule),
	(&g_Unity_2D_Animation_Runtime_CodeGenModule),
	(&g_Unity_2D_Common_Runtime_CodeGenModule),
	(&g_Unity_2D_IK_Runtime_CodeGenModule),
	(&g_Unity_2D_SpriteShape_Runtime_CodeGenModule),
	(&g_Unity_2D_Tilemap_Extras_CodeGenModule),
	(&g_Unity_AppUI_CodeGenModule),
	(&g_Unity_AppUI_InternalAPIBridge_CodeGenModule),
	(&g_Unity_Burst_CodeGenModule),
	(&g_Unity_Burst_Unsafe_CodeGenModule),
	(&g_Unity_Collections_CodeGenModule),
	(&g_Unity_Collections_LowLevel_ILSupport_CodeGenModule),
	(&g_Unity_InputSystem_CodeGenModule),
	(&g_Unity_InputSystem_ForUI_CodeGenModule),
	(&g_Unity_InternalAPIEngineBridge_001_CodeGenModule),
	(&g_Unity_LevelPlay_CodeGenModule),
	(&g_Unity_Mathematics_CodeGenModule),
	(&g_Unity_RenderPipeline_Universal_ShaderLibrary_CodeGenModule),
	(&g_Unity_RenderPipelines_Core_Runtime_CodeGenModule),
	(&g_Unity_RenderPipelines_Core_Runtime_Shared_CodeGenModule),
	(&g_Unity_RenderPipelines_GPUDriven_Runtime_CodeGenModule),
	(&g_Unity_RenderPipelines_Universal_2D_Runtime_CodeGenModule),
	(&g_Unity_RenderPipelines_Universal_Runtime_CodeGenModule),
	(&g_Unity_TextMeshPro_CodeGenModule),
	(&g_Unity_Timeline_CodeGenModule),
	(&g_Unity_VisualEffectGraph_Runtime_CodeGenModule),
	(&g_ZString_CodeGenModule),
	(&g___Generated_CodeGenModule),
	(&g_mscorlib_CodeGenModule),
};
IL2CPP_EXTERN_C const Il2CppCodeRegistration g_CodeRegistration;
const Il2CppCodeRegistration g_CodeRegistration = 
{
	52,
	g_ReversePInvokeWrapperPointers,
	176769,
	g_Il2CppGenericMethodPointers,
	g_Il2CppGenericAdjustorThunks,
	34324,
	g_Il2CppInvokerPointers,
	4983,
	g_UnresolvedVirtualMethodPointers,
	g_UnresolvedInstanceMethodPointers,
	g_UnresolvedStaticMethodPointers,
	1061,
	g_Il2CppInteropData,
	0,
	NULL,
	92,
	g_CodeGenModules,
};
IL2CPP_EXTERN_C_CONST Il2CppMetadataRegistration g_MetadataRegistration;
static const Il2CppCodeGenOptions s_Il2CppCodeGenOptions = 
{
	true,
	7,
	1,
};
void s_Il2CppCodegenRegistration()
{
	il2cpp_codegen_register (&g_CodeRegistration, &g_MetadataRegistration, &s_Il2CppCodeGenOptions);
}
#if RUNTIME_IL2CPP
typedef void (*CodegenRegistrationFunction)();
CodegenRegistrationFunction g_CodegenRegistration = s_Il2CppCodegenRegistration;
#endif
