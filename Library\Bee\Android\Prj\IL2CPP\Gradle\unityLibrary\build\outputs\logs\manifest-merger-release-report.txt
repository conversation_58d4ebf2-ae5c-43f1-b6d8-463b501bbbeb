-- Merging decision tree log ---
manifest
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:2:1-32:12
INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:2:1-32:12
	package
		INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:2:70-116
	xmlns:android
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:2:11-69
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:3:3-77
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:3:20-74
uses-permission#android.permission.VIBRATE
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:3-64
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:20-61
uses-permission#android.permission.INTERNET
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:5:3-65
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:5:20-62
uses-feature#0x00030000
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:6:3-52
	android:glEsVersion
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:6:17-49
uses-feature#android.hardware.touchscreen
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:3-88
	android:required
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:61-85
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:17-60
uses-feature#android.hardware.touchscreen.multitouch
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:8:3-99
	android:required
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:8:72-96
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:8:17-71
uses-feature#android.hardware.touchscreen.multitouch.distinct
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:3-108
	android:required
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:81-105
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:17-80
application
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:3-31:17
	android:extractNativeLibs
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:88-120
	android:allowBackup
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:16-43
	android:enableOnBackInvokedCallback
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:44-87
	android:usesCleartextTraffic
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:121-157
meta-data#unity.splash-mode
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:11:5-69
	android:value
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:11:49-66
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:11:16-48
meta-data#unity.splash-enable
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:5-74
	android:value
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:51-71
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:16-50
meta-data#unity.launch-fullscreen
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:5-78
	android:value
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:55-75
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:16-54
meta-data#unity.render-outside-safearea
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:5-84
	android:value
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:61-81
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:16-60
meta-data#notch.config
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:5-81
	android:value
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:44-78
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:16-43
meta-data#unity.auto-report-fully-drawn
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:16:5-84
	android:value
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:16:61-81
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:16:16-60
meta-data#unity.auto-set-game-state
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:5-80
	android:value
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:57-77
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:16-56
meta-data#unity.strip-engine-code
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:5-78
	android:value
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:55-75
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:16-54
activity#com.unity3d.player.appui.AppUIGameActivity
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:5-30:16
	android:screenOrientation
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:381-421
	android:launchMode
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:257-288
	android:hardwareAccelerated
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:221-256
	android:exported
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:197-220
	android:resizeableActivity
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:347-380
	android:configChanges
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:15-196
	android:theme
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:422-471
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:289-346
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:20:7-23:23
category#android.intent.category.LAUNCHER
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:21:9-69
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:21:19-66
action#android.intent.action.MAIN
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:22:9-61
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:22:17-58
meta-data#unityplayer.UnityActivity
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:24:7-82
	android:value
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:24:59-79
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:24:18-58
meta-data#android.app.lib_name
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:25:7-77
	android:value
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:25:54-74
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:25:18-53
meta-data#WindowManagerPreference:FreeformWindowSize
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:26:7-130
	android:value
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:26:76-127
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:26:18-75
meta-data#WindowManagerPreference:FreeformWindowOrientation
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:27:7-144
	android:value
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:27:83-141
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:27:18-82
meta-data#notch_support
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:28:7-70
	android:value
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:28:47-67
	android:name
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:28:18-46
layout
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:29:7-68
	android:minWidth
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:29:41-65
	android:minHeight
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:29:15-40
uses-sdk
INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
