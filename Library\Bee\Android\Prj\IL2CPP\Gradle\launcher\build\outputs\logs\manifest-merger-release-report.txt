-- Merging decision tree log ---
manifest
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
MERGED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-90:12
MERGED from [com.google.android.gms:play-services-ads:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ca8816a700ee06964b9a3920f6eaae18\transformed\jetified-play-services-ads-24.4.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\253844010a2b1f920fcaa65c5e03756e\transformed\jetified-play-services-ads-identifier-18.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b7c22ec466b3bd4a6dbbe533f7578a0a\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:2:1-35:12
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:2:1-21:12
MERGED from [com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:2:1-20:12
MERGED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:2:1-65:12
MERGED from [com.unity3d.ads-mediation:admob-adapter:4.3.52] C:\Users\<USER>\.gradle\caches\8.9\transforms\1153453d859bae5914eb88a52748824f\transformed\jetified-admob-adapter-4.3.52\AndroidManifest.xml:2:1-7:12
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:2:1-48:12
MERGED from [com.unity3d.ads-mediation:unityads-adapter:4.3.57] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8cedf25c0a31d0d98ed30b050a78fe7\transformed\jetified-unityads-adapter-4.3.57\AndroidManifest.xml:2:1-7:12
MERGED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-23:12
MERGED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:1-30:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\aa6aef0b366c6464950ea2f6ac03ac66\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d2b4e023067e1255755d74798b3b5ddb\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5fb357593bf2e41df09357350faade82\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:17:1-115:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\526c9a8073d6a3fd9aecabb2b559555a\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.9\transforms\eaff1915eeff1f3e684b5c651654ff65\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8eaa61affd9a6cec455eb87616037b2a\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:2:1-33:12
MERGED from [com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:2:1-16:12
MERGED from [com.google.android.gms:play-services-cronet:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\30fbf7abc4070fa1e2ce7bca99307007\transformed\jetified-play-services-cronet-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-drive:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2abc264b9df41e4a10b5ed3f548577d9\transformed\jetified-play-services-drive-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07b9ad58ba10f280dfbacdca436e4963\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\792437666b45f157dc6abd83a2eb64ab\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c560451490566ef852eaf9b877f3a4\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\71a3fb37eaddc4bb53c47db89bff0249\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\81fa9ac55da116227df27a834392e904\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\d45fed3a22ef80b0d59961fd3893403c\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\022132af6bac98e8948adfeadca2b88c\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\640b75ad0d55ed1c3508a4ec4216dab9\transformed\jetified-activity-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\54fcecaa48ceedf986a351f17809f27b\transformed\jetified-activity-ktx-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b59caebd5b0eeb398bb0b51203a06c00\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d484ffba6ed6b8298693d91d630519be\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f58bcd8e1991acfe241385b620619f9\transformed\work-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\98ff949cc7717bd5044f8351b350755c\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\995e12a1c562df2f75eb88c63e97c974\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a38a42141934d1efd5bd748762aad605\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2e921cd16d63f4e0d0a788ce4333e813\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2c39ddc44823b33db91a8a467a82992b\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0acb1fec1e7ba4af001e41f13611abe5\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\854426de3cc1265ebe6fde1d66be3ffb\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\efd0bac167285b3cd2e13a5f789c95d8\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d7fe1bbac8c4910b21f08d7fc9f6ca4c\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\6fd0cc9e4918b4728af8224fec77caf4\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f208bdbdbf7b72a4088cabd7ffa3dac0\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2cdac65b0b77d9940fbb90ec6e9c1374\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7760ce22f26dd47c54282c0fa70fc5e3\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30fba741f8e98cb753260dbf350d5148\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ba0144e1eaeacaff4f5b3a717a34c7be\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\10ee40a6142a5042899ff73fe04c5d8b\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a0bca1faee0ed97922b59827b4112c1\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.games:games-activity:3.0.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\5652c66f07446d89a89a7ab2b3eed394\transformed\jetified-games-activity-3.0.5\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\37cbb73f3bb006c5cbb9064d51f4a190\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5914cd0d1f7ce5b2d77c09c643cca61b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7ac29f21cf70d1ce5819be934c2bde1\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b9f85c476eaf85301d79092f7795a901\transformed\jetified-adquality-sdk-7.25.2\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6abf8626c442ae157d9fa8be702afb2c\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f415a8e3d9fc654f2084f27bfdaf48\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\575019187b8792bebba099b4b4f9269a\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f61726133436fb716beadfcb15fd5237\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\87fb528cbef6670d9c9afd9273e1e601\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7724c59453a1403f9df229226c692987\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b846f2f00b2fb8998e8048ba693200c5\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.9\transforms\697f6d6b7002f85d4072d8dadcebb248\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:15:1-22:12
	package
		INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:111-157
	android:versionCode
		INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:52-110
	android:installLocation
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:11-51
application
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:3-126
INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:3-126
MERGED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:5-88:19
MERGED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:5-88:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\253844010a2b1f920fcaa65c5e03756e\transformed\jetified-play-services-ads-identifier-18.1.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\253844010a2b1f920fcaa65c5e03756e\transformed\jetified-play-services-ads-identifier-18.1.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b7c22ec466b3bd4a6dbbe533f7578a0a\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b7c22ec466b3bd4a6dbbe533f7578a0a\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:17:5-33:19
MERGED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:17:5-33:19
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:8:5-18:19
MERGED from [com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:8:5-18:19
MERGED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:20:5-63:19
MERGED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:20:5-63:19
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:11:5-46:19
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:11:5-46:19
MERGED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-21:19
MERGED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-21:19
MERGED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:5-28:19
MERGED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:5-28:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\aa6aef0b366c6464950ea2f6ac03ac66\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\aa6aef0b366c6464950ea2f6ac03ac66\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8eaa61affd9a6cec455eb87616037b2a\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8eaa61affd9a6cec455eb87616037b2a\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:12:5-31:19
MERGED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:12:5-31:19
MERGED from [com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:9:5-14:19
MERGED from [com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:9:5-14:19
MERGED from [com.google.android.gms:play-services-cronet:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\30fbf7abc4070fa1e2ce7bca99307007\transformed\jetified-play-services-cronet-18.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-cronet:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\30fbf7abc4070fa1e2ce7bca99307007\transformed\jetified-play-services-cronet-18.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-drive:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2abc264b9df41e4a10b5ed3f548577d9\transformed\jetified-play-services-drive-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-drive:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2abc264b9df41e4a10b5ed3f548577d9\transformed\jetified-play-services-drive-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07b9ad58ba10f280dfbacdca436e4963\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07b9ad58ba10f280dfbacdca436e4963\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\71a3fb37eaddc4bb53c47db89bff0249\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\71a3fb37eaddc4bb53c47db89bff0249\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\d45fed3a22ef80b0d59961fd3893403c\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\d45fed3a22ef80b0d59961fd3893403c\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d7fe1bbac8c4910b21f08d7fc9f6ca4c\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d7fe1bbac8c4910b21f08d7fc9f6ca4c\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\37cbb73f3bb006c5cbb9064d51f4a190\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\37cbb73f3bb006c5cbb9064d51f4a190\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5914cd0d1f7ce5b2d77c09c643cca61b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5914cd0d1f7ce5b2d77c09c643cca61b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
	android:extractNativeLibs
		INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		REJECTED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-41
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:label
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:48-80
	android:roundIcon
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:81-123
	android:allowBackup
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:9-36
	android:icon
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:16-47
	android:enableOnBackInvokedCallback
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:9-52
	android:usesCleartextTraffic
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:9-45
supports-screens
ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:3-163
	android:largeScreens
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:78-105
	android:smallScreens
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:21-48
	android:normalScreens
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:49-77
	android:xlargeScreens
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:106-134
	android:anyDensity
		ADDED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:135-160
uses-sdk
INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
MERGED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-ads:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ca8816a700ee06964b9a3920f6eaae18\transformed\jetified-play-services-ads-24.4.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ca8816a700ee06964b9a3920f6eaae18\transformed\jetified-play-services-ads-24.4.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads-identifier:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\253844010a2b1f920fcaa65c5e03756e\transformed\jetified-play-services-ads-identifier-18.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\253844010a2b1f920fcaa65c5e03756e\transformed\jetified-play-services-ads-identifier-18.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b7c22ec466b3bd4a6dbbe533f7578a0a\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b7c22ec466b3bd4a6dbbe533f7578a0a\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:6:5-44
MERGED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:6:5-44
MERGED from [com.unity3d.ads-mediation:admob-adapter:4.3.52] C:\Users\<USER>\.gradle\caches\8.9\transforms\1153453d859bae5914eb88a52748824f\transformed\jetified-admob-adapter-4.3.52\AndroidManifest.xml:5:5-44
MERGED from [com.unity3d.ads-mediation:admob-adapter:4.3.52] C:\Users\<USER>\.gradle\caches\8.9\transforms\1153453d859bae5914eb88a52748824f\transformed\jetified-admob-adapter-4.3.52\AndroidManifest.xml:5:5-44
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:5:5-44
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:5:5-44
MERGED from [com.unity3d.ads-mediation:unityads-adapter:4.3.57] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8cedf25c0a31d0d98ed30b050a78fe7\transformed\jetified-unityads-adapter-4.3.57\AndroidManifest.xml:5:5-44
MERGED from [com.unity3d.ads-mediation:unityads-adapter:4.3.57] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8cedf25c0a31d0d98ed30b050a78fe7\transformed\jetified-unityads-adapter-4.3.57\AndroidManifest.xml:5:5-44
MERGED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-44
MERGED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\aa6aef0b366c6464950ea2f6ac03ac66\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\aa6aef0b366c6464950ea2f6ac03ac66\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d2b4e023067e1255755d74798b3b5ddb\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d2b4e023067e1255755d74798b3b5ddb\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5fb357593bf2e41df09357350faade82\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5fb357593bf2e41df09357350faade82\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\526c9a8073d6a3fd9aecabb2b559555a\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\526c9a8073d6a3fd9aecabb2b559555a\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.9\transforms\eaff1915eeff1f3e684b5c651654ff65\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.9\transforms\eaff1915eeff1f3e684b5c651654ff65\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8eaa61affd9a6cec455eb87616037b2a\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8eaa61affd9a6cec455eb87616037b2a\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cronet:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\30fbf7abc4070fa1e2ce7bca99307007\transformed\jetified-play-services-cronet-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cronet:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\30fbf7abc4070fa1e2ce7bca99307007\transformed\jetified-play-services-cronet-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-drive:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2abc264b9df41e4a10b5ed3f548577d9\transformed\jetified-play-services-drive-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-drive:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2abc264b9df41e4a10b5ed3f548577d9\transformed\jetified-play-services-drive-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07b9ad58ba10f280dfbacdca436e4963\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07b9ad58ba10f280dfbacdca436e4963\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\792437666b45f157dc6abd83a2eb64ab\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\792437666b45f157dc6abd83a2eb64ab\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c560451490566ef852eaf9b877f3a4\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c560451490566ef852eaf9b877f3a4\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\71a3fb37eaddc4bb53c47db89bff0249\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\71a3fb37eaddc4bb53c47db89bff0249\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\81fa9ac55da116227df27a834392e904\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\81fa9ac55da116227df27a834392e904\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\d45fed3a22ef80b0d59961fd3893403c\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\d45fed3a22ef80b0d59961fd3893403c\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\022132af6bac98e8948adfeadca2b88c\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\022132af6bac98e8948adfeadca2b88c\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\640b75ad0d55ed1c3508a4ec4216dab9\transformed\jetified-activity-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\640b75ad0d55ed1c3508a4ec4216dab9\transformed\jetified-activity-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\54fcecaa48ceedf986a351f17809f27b\transformed\jetified-activity-ktx-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\54fcecaa48ceedf986a351f17809f27b\transformed\jetified-activity-ktx-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b59caebd5b0eeb398bb0b51203a06c00\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b59caebd5b0eeb398bb0b51203a06c00\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d484ffba6ed6b8298693d91d630519be\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d484ffba6ed6b8298693d91d630519be\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f58bcd8e1991acfe241385b620619f9\transformed\work-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f58bcd8e1991acfe241385b620619f9\transformed\work-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\98ff949cc7717bd5044f8351b350755c\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\98ff949cc7717bd5044f8351b350755c\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\995e12a1c562df2f75eb88c63e97c974\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\995e12a1c562df2f75eb88c63e97c974\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a38a42141934d1efd5bd748762aad605\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a38a42141934d1efd5bd748762aad605\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2e921cd16d63f4e0d0a788ce4333e813\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2e921cd16d63f4e0d0a788ce4333e813\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2c39ddc44823b33db91a8a467a82992b\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2c39ddc44823b33db91a8a467a82992b\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0acb1fec1e7ba4af001e41f13611abe5\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0acb1fec1e7ba4af001e41f13611abe5\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\854426de3cc1265ebe6fde1d66be3ffb\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\854426de3cc1265ebe6fde1d66be3ffb\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\efd0bac167285b3cd2e13a5f789c95d8\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\efd0bac167285b3cd2e13a5f789c95d8\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d7fe1bbac8c4910b21f08d7fc9f6ca4c\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d7fe1bbac8c4910b21f08d7fc9f6ca4c\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\6fd0cc9e4918b4728af8224fec77caf4\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\6fd0cc9e4918b4728af8224fec77caf4\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f208bdbdbf7b72a4088cabd7ffa3dac0\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f208bdbdbf7b72a4088cabd7ffa3dac0\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2cdac65b0b77d9940fbb90ec6e9c1374\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2cdac65b0b77d9940fbb90ec6e9c1374\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7760ce22f26dd47c54282c0fa70fc5e3\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7760ce22f26dd47c54282c0fa70fc5e3\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30fba741f8e98cb753260dbf350d5148\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30fba741f8e98cb753260dbf350d5148\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ba0144e1eaeacaff4f5b3a717a34c7be\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ba0144e1eaeacaff4f5b3a717a34c7be\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\10ee40a6142a5042899ff73fe04c5d8b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\10ee40a6142a5042899ff73fe04c5d8b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a0bca1faee0ed97922b59827b4112c1\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a0bca1faee0ed97922b59827b4112c1\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.games:games-activity:3.0.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\5652c66f07446d89a89a7ab2b3eed394\transformed\jetified-games-activity-3.0.5\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.games:games-activity:3.0.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\5652c66f07446d89a89a7ab2b3eed394\transformed\jetified-games-activity-3.0.5\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\37cbb73f3bb006c5cbb9064d51f4a190\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\37cbb73f3bb006c5cbb9064d51f4a190\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5914cd0d1f7ce5b2d77c09c643cca61b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5914cd0d1f7ce5b2d77c09c643cca61b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7ac29f21cf70d1ce5819be934c2bde1\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7ac29f21cf70d1ce5819be934c2bde1\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b9f85c476eaf85301d79092f7795a901\transformed\jetified-adquality-sdk-7.25.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b9f85c476eaf85301d79092f7795a901\transformed\jetified-adquality-sdk-7.25.2\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6abf8626c442ae157d9fa8be702afb2c\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6abf8626c442ae157d9fa8be702afb2c\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f415a8e3d9fc654f2084f27bfdaf48\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f415a8e3d9fc654f2084f27bfdaf48\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\575019187b8792bebba099b4b4f9269a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\575019187b8792bebba099b4b4f9269a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f61726133436fb716beadfcb15fd5237\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f61726133436fb716beadfcb15fd5237\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\87fb528cbef6670d9c9afd9273e1e601\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\87fb528cbef6670d9c9afd9273e1e601\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7724c59453a1403f9df229226c692987\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7724c59453a1403f9df229226c692987\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b846f2f00b2fb8998e8048ba693200c5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b846f2f00b2fb8998e8048ba693200c5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.9\transforms\697f6d6b7002f85d4072d8dadcebb248\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:18:5-74
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.9\transforms\697f6d6b7002f85d4072d8dadcebb248\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:18:5-74
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ca8816a700ee06964b9a3920f6eaae18\transformed\jetified-play-services-ads-24.4.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\253844010a2b1f920fcaa65c5e03756e\transformed\jetified-play-services-ads-identifier-18.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\253844010a2b1f920fcaa65c5e03756e\transformed\jetified-play-services-ads-identifier-18.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:16:5-79
MERGED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:16:5-79
MERGED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-79
MERGED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\792437666b45f157dc6abd83a2eb64ab\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\792437666b45f157dc6abd83a2eb64ab\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-76
uses-permission#android.permission.VIBRATE
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-66
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:22-63
uses-permission#android.permission.INTERNET
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-67
MERGED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:15:5-67
MERGED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:15:5-67
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:7:5-67
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:7:5-67
MERGED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-67
MERGED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\792437666b45f157dc6abd83a2eb64ab\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\792437666b45f157dc6abd83a2eb64ab\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b9f85c476eaf85301d79092f7795a901\transformed\jetified-adquality-sdk-7.25.2\AndroidManifest.xml:12:5-67
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b9f85c476eaf85301d79092f7795a901\transformed\jetified-adquality-sdk-7.25.2\AndroidManifest.xml:12:5-67
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.9\transforms\697f6d6b7002f85d4072d8dadcebb248\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:19:5-66
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.9\transforms\697f6d6b7002f85d4072d8dadcebb248\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:19:5-66
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:22-64
uses-feature#0x00030000
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-54
	android:glEsVersion
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:19-51
uses-feature#android.hardware.touchscreen
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:5-15:36
	android:required
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-33
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:9-52
uses-feature#android.hardware.touchscreen.multitouch
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:5-18:36
	android:required
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-33
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:9-63
uses-feature#android.hardware.touchscreen.multitouch.distinct
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:5-21:36
	android:required
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-33
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:9-72
meta-data#unity.splash-mode
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:9-30:33
	android:value
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-30
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-45
meta-data#unity.splash-enable
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:9-33:36
	android:value
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:13-33
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:32:13-47
meta-data#unity.launch-fullscreen
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:9-36:36
	android:value
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-33
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-51
meta-data#unity.render-outside-safearea
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:9-39:36
	android:value
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:13-33
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:13-57
meta-data#notch.config
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:9-42:50
	android:value
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:13-47
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:13-40
meta-data#unity.auto-report-fully-drawn
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:9-45:36
	android:value
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-33
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-57
meta-data#unity.auto-set-game-state
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:46:9-48:36
	android:value
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:48:13-33
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:47:13-53
meta-data#unity.strip-engine-code
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:49:9-51:36
	android:value
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:51:13-33
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:50:13-51
activity#com.unity3d.player.appui.AppUIGameActivity
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:53:9-87:20
	android:screenOrientation
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:60:13-53
	android:launchMode
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:58:13-44
	android:hardwareAccelerated
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:57:13-48
	android:exported
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:56:13-36
	android:resizeableActivity
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:59:13-46
	android:configChanges
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:55:13-194
	android:theme
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:61:13-62
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:54:13-70
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:62:13-66:29
category#android.intent.category.LAUNCHER
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:63:17-77
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:63:27-74
action#android.intent.action.MAIN
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:65:17-69
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:65:25-66
meta-data#unityplayer.UnityActivity
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:68:13-70:40
	android:value
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:70:17-37
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:69:17-57
meta-data#android.app.lib_name
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:71:13-73:40
	android:value
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:73:17-37
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:72:17-52
meta-data#WindowManagerPreference:FreeformWindowSize
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:74:13-76:71
	android:value
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:76:17-68
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:75:17-74
meta-data#WindowManagerPreference:FreeformWindowOrientation
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:77:13-79:78
	android:value
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:79:17-75
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:78:17-81
meta-data#notch_support
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:80:13-82:40
	android:value
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:82:17-37
	android:name
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:81:17-45
layout
ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:84:13-86:44
	android:minWidth
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:86:17-41
	android:minHeight
		ADDED from [:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:85:17-42
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:13:5-77
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:13:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:15:5-87
	android:name
		ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:15:22-84
service#com.google.android.play.core.assetpacks.AssetPackExtractionService
ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:20:9-27:19
	android:enabled
		ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:22:13-36
	android:exported
		ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:23:13-36
	android:name
		ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:21:13-94
meta-data#com.google.android.play.core.assetpacks.versionCode
ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:24:13-26:41
	android:value
		ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:26:17-38
	android:name
		ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:25:17-83
service#com.google.android.play.core.assetpacks.ExtractionForegroundService
ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:28:9-32:56
	android:enabled
		ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:30:13-36
	android:exported
		ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:31:13-37
	android:foregroundServiceType
		ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:32:13-53
	android:name
		ADDED from [com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:29:13-95
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
activity#com.google.games.bridge.GenericResolutionActivity
ADDED from [com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:9:9-17:20
	android:label
		ADDED from [com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:12:13-54
	android:exported
		ADDED from [com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:11:13-37
	android:theme
		ADDED from [com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:13:13-83
	android:name
		ADDED from [com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:10:13-77
meta-data#instantapps.clients.allowed
ADDED from [com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:14:13-16:40
	android:value
		ADDED from [com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:16:17-37
	android:name
		ADDED from [com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:15:17-59
queries
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:8:5-12:15
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:7:5-10:15
MERGED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:7:5-10:15
intent#action:name:com.attribution.REFERRAL_PROVIDER
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:9:9-11:18
action#com.attribution.REFERRAL_PROVIDER
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:10:13-72
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:10:21-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:14:5-79
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:8:5-79
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:8:5-79
MERGED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-79
MERGED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\792437666b45f157dc6abd83a2eb64ab\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\792437666b45f157dc6abd83a2eb64ab\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b9f85c476eaf85301d79092f7795a901\transformed\jetified-adquality-sdk-7.25.2\AndroidManifest.xml:11:5-79
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b9f85c476eaf85301d79092f7795a901\transformed\jetified-adquality-sdk-7.25.2\AndroidManifest.xml:11:5-79
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.9\transforms\697f6d6b7002f85d4072d8dadcebb248\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:20:5-78
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.9\transforms\697f6d6b7002f85d4072d8dadcebb248\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:20:5-78
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:14:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:17:5-83
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:29:5-83
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:17:22-80
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:18:5-88
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:9:5-88
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:9:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:28:5-88
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:18:22-85
activity#com.unity3d.services.ads.adunit.AdUnitActivity
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:21:9-26:74
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:25:13-47
	android:exported
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:24:13-37
	android:configChanges
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:23:13-170
	android:theme
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:26:13-71
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:22:13-74
activity#com.unity3d.services.ads.adunit.AdUnitTransparentActivity
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:27:9-32:86
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:31:13-47
	android:exported
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:30:13-37
	android:configChanges
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:29:13-170
	android:theme
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:32:13-83
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:28:13-85
activity#com.unity3d.services.ads.adunit.AdUnitTransparentSoftwareActivity
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:33:9-38:86
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:37:13-48
	android:exported
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:36:13-37
	android:configChanges
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:35:13-170
	android:theme
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:38:13-83
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:34:13-93
activity#com.unity3d.services.ads.adunit.AdUnitSoftwareActivity
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:39:9-44:74
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:43:13-48
	android:exported
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:42:13-37
	android:configChanges
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:41:13-170
	android:theme
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:44:13-71
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:40:13-82
activity#com.unity3d.ads.adplayer.FullScreenWebViewDisplay
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:45:9-50:74
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:49:13-47
	android:exported
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:48:13-37
	android:configChanges
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:47:13-170
	android:theme
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:50:13-71
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:46:13-77
provider#androidx.startup.InitializationProvider
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:52:9-62:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d7fe1bbac8c4910b21f08d7fc9f6ca4c\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d7fe1bbac8c4910b21f08d7fc9f6ca4c\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5914cd0d1f7ce5b2d77c09c643cca61b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5914cd0d1f7ce5b2d77c09c643cca61b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:55:13-31
	android:authorities
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:54:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:53:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:56:13-58:52
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d7fe1bbac8c4910b21f08d7fc9f6ca4c\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d7fe1bbac8c4910b21f08d7fc9f6ca4c\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:58:17-49
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:57:17-78
meta-data#com.unity3d.services.core.configuration.AdsSdkInitializer
ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:59:13-61:52
	android:value
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:61:17-49
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:60:17-89
activity#com.ironsource.sdk.controller.ControllerActivity
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:12:9-16:63
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:15:13-47
	android:configChanges
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:14:13-59
	android:theme
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:16:13-60
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:13:13-76
activity#com.ironsource.sdk.controller.InterstitialActivity
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:17:9-21:75
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:20:13-47
	android:configChanges
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:19:13-59
	android:theme
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:21:13-72
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:18:13-78
activity#com.ironsource.sdk.controller.OpenUrlActivity
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:22:9-26:75
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:25:13-47
	android:configChanges
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:24:13-59
	android:theme
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:26:13-72
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:23:13-73
activity#com.ironsource.mediationsdk.testSuite.TestSuiteActivity
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:27:9-36:20
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:31:13-47
	android:exported
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:30:13-37
	android:configChanges
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:29:13-59
	android:theme
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:32:13-60
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:28:13-83
meta-data#android.webkit.WebView.EnableSafeBrowsing
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:33:13-35:40
	android:value
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:35:17-37
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:34:17-73
provider#com.ironsource.lifecycle.IronsourceLifecycleProvider
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:38:9-41:40
	android:authorities
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:40:13-79
	android:exported
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:39:13-80
provider#com.ironsource.lifecycle.LevelPlayActivityLifecycleProvider
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:42:9-45:40
	android:authorities
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:44:13-86
	android:exported
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:45:13-37
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:43:13-87
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-20:70
	android:value
		ADDED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-67
	android:name
		ADDED from [:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-69
meta-data#com.google.android.gms.games.APP_ID
ADDED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:9-18:48
	android:value
		ADDED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-45
	android:name
		ADDED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-63
meta-data#com.google.android.gms.games.unityVersion
ADDED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-23:42
	android:value
		ADDED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-39
	android:name
		ADDED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-69
activity#com.google.games.bridge.NativeBridgeActivity
ADDED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:9-27:86
	android:theme
		ADDED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-83
	android:name
		ADDED from [:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:13-72
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:27:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:27:22-79
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:31:9-65
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:38:9-44:18
action#android.intent.action.VIEW
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:39:21-62
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:41:23-71
data
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:43:13-44
	android:scheme
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:43:19-41
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:99:13-82
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:107:9-109:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:109:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:108:13-79
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:110:9-112:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:112:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:111:13-83
package#com.google.android.gms
ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:8:9-58
	android:name
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:8:18-55
package#com.android.vending
ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:9:9-55
	android:name
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:9:18-52
provider#com.google.android.gms.games.provider.PlayGamesInitProvider
ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:13:9-17:38
	android:authorities
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:15:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:16:13-37
	android:initOrder
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:17:13-35
	android:name
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:14:13-87
activity#com.google.android.gms.games.internal.v2.resolution.GamesResolutionActivity
ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:19:9-22:62
	android:exported
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:22:13-59
	android:name
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:20:13-103
activity#com.google.android.gms.games.internal.v2.appshortcuts.PlayGamesAppShortcutsActivity
ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:23:9-26:62
	android:exported
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:26:13-59
	android:name
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:24:13-111
meta-data#com.google.android.gms.games.version
ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:28:9-30:62
	android:value
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:30:13-59
	android:name
		ADDED from [com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:29:13-64
service#com.google.android.gms.nearby.exposurenotification.WakeUpService
ADDED from [com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:10:9-13:105
	android:exported
		ADDED from [com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:12:13-36
	android:permission
		ADDED from [com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:13:13-102
	android:name
		ADDED from [com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:11:13-92
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\792437666b45f157dc6abd83a2eb64ab\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\792437666b45f157dc6abd83a2eb64ab\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\d45fed3a22ef80b0d59961fd3893403c\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\d45fed3a22ef80b0d59961fd3893403c\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\d45fed3a22ef80b0d59961fd3893403c\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.PhantomTeam.PufflandAdventure.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.PhantomTeam.PufflandAdventure.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
