{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1753365097182184, "dur": 483985, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097183126, "dur": 41938, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097239936, "dur": 383653, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097240497, "dur": 321512, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097241030, "dur": 46182, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097287583, "dur": 6315, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097295565, "dur": 58759, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097354924, "dur": 198983, "ph": "X", "name": "SetupIl2CppBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097355822, "dur": 36116, "ph": "X", "name": "SetupIl2Cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097422508, "dur": 124351, "ph": "X", "name": "SetupSpecificConfigImpl libil2cpp", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1753365097430378, "dur": 76402, "ph": "X", "name": "SetupSpecificConfigImpl il2cpp", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1753365097431544, "dur": 44278, "ph": "X", "name": "SetupSpecificConfigImpl bdwgc", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1753365097475862, "dur": 2221, "ph": "X", "name": "SetupSpecificConfigImpl zlib", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1753365097554374, "dur": 7633, "ph": "X", "name": "SetupCopyDataIl2cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097630583, "dur": 765, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097631349, "dur": 34791, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097632497, "dur": 27320, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097672491, "dur": 2114, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753365097672069, "dur": 2738, "ph": "X", "name": "Write chrome-trace events", "args": {} },
