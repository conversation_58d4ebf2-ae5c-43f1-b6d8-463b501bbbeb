{"totalSettings": 29, "totalSettingsOnPlayer": 13, "settings": [{"type": "UnityEngine.Rendering.Universal.URPShaderStrippingSetting, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": false}, {"type": "UnityEngine.Rendering.Universal.UniversalRenderPipelineRuntimeShaders, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": true, "strippersDefined": false}, {"type": "UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorMaterials, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": false}, {"type": "UnityEngine.Rendering.Universal.RenderGraphSettings, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": true, "strippersDefined": false}, {"type": "UnityEngine.Rendering.Universal.URPDefaultVolumeProfileSettings, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": true, "strippersDefined": false}, {"type": "UnityEngine.Rendering.Universal.UniversalRendererResources, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": true}, {"type": "UnityEngine.Rendering.Universal.UniversalRenderPipelineRuntimeXRResources, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": true, "strippersDefined": false}, {"type": "UnityEngine.Rendering.Universal.Renderer2DResources, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": true, "strippersDefined": true}, {"type": "UnityEngine.Rendering.Universal.UniversalRenderPipelineRuntimeTextures, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": true, "strippersDefined": false}, {"type": "UnityEngine.Rendering.Universal.UniversalRenderPipelineDebugShaders, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": true, "strippersDefined": false}, {"type": "UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorShaders, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": false}, {"type": "UnityEngine.Rendering.GPUResidentDrawerResources, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": true}, {"type": "UnityEngine.Rendering.RenderGraphGlobalSettings, Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": true, "strippersDefined": false}, {"type": "UnityEngine.Rendering.ProbeVolumeRuntimeResources, Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": true}, {"type": "UnityEngine.Rendering.IncludeAdditionalRPAssets, Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": false}, {"type": "UnityEngine.Rendering.ProbeVolumeBakingResources, Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": true}, {"type": "UnityEngine.Rendering.RenderGraphModule.Util.RenderGraphUtilsResources, Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": true, "strippersDefined": true}, {"type": "UnityEngine.Rendering.ProbeVolumeDebugResources, Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": true}, {"type": "UnityEngine.Rendering.STP+RuntimeResources, Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": true}, {"type": "UnityEngine.Rendering.ProbeVolumeGlobalSettings, Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": true}, {"type": "UnityEngine.Rendering.ShaderStrippingSetting, Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": true, "strippersDefined": false}, {"type": "UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": false}, {"type": "UnityEngine.Rendering.Universal.PostProcessData+TextureResources, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": false}, {"type": "UnityEngine.Rendering.Universal.ScreenSpaceAmbientOcclusionPersistentResources, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": true}, {"type": "UnityEngine.Rendering.Universal.PostProcessData+ShaderResources, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": false}, {"type": "UnityEngine.Rendering.Universal.ScreenSpaceAmbientOcclusionDynamicResources, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": false, "strippersDefined": true}, {"type": "UnityEngine.Rendering.VrsRenderPipelineRuntimeResources, Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": true, "strippersDefined": false}, {"type": "UnityEngine.Rendering.LightmapSamplingSettings, Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": true, "strippersDefined": false}, {"type": "UnityEngine.Rendering.RenderingDebuggerRuntimeResources, Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "isAvailableInPlayerBuild": true, "strippersDefined": true}]}