﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void SR_Format_mFA381AB984D00222E1CACA0EE8F4C53E8C99D34B (void);
extern void BigInteger__ctor_mFEDEDA4A6AFC2BA534FEEF4EB3431B9C5AC3F9D9 (void);
extern void BigInteger__ctor_mE9288D5C617F6BF5B8E44F8B73D9198F30B90D84 (void);
extern void BigInteger__ctor_mA87DC2182BB2F6AA0209BD98E81C30BCE5B8BBAA (void);
extern void BigInteger__ctor_m4685E4C69B49F9470E056A761DCEB8DB9FA7D01C (void);
extern void BigInteger__ctor_m9F274FB1B4EC1E507374A65D16F8D1A6D23D54AC (void);
extern void BigInteger__ctor_m48BEDD707B2B28BDB94A838395590DFFE775015A (void);
extern void BigInteger__ctor_m9544C18A3217F10163645D4A62264DE37CA49821 (void);
extern void BigInteger__ctor_m25FA3F3C832BC621CD91ABFAC3535A2F4EE7B371 (void);
extern void BigInteger__ctor_mF6B2AC2AD4056AB6903913C56A92F565EB08BAFD (void);
extern void BigInteger__ctor_mB5E6337A77FC9888762DD29AAF54D2BBDD828D03 (void);
extern void BigInteger__ctor_m14A0AC47ABF1AF6DE7B653CED685D7AD11B54BE6 (void);
extern void BigInteger_get_Zero_m034F723A07EC664776C5541E2FE1300C02171201 (void);
extern void BigInteger_get_MinusOne_m3FF0F44F8C3D6DFB66C00B61017E42E555585465 (void);
extern void BigInteger_get_IsZero_m54B3F16D56CB9E59DBEC6929CF61A7FBE3592DC4 (void);
extern void BigInteger_Parse_m2B82A0CB4AF02EFCD6175561813E9F0B8DA08334 (void);
extern void BigInteger_Parse_m9B099A3E92013755D8645DDB1D977A63BB692D73 (void);
extern void BigInteger_GetHashCode_m2CB01C462C09EB23616010EB24E0A87A4D35D783 (void);
extern void BigInteger_Equals_mC711A33C4466DC2C053785E0A8A34B49A197EA1C (void);
extern void BigInteger_Equals_m6FA62389611EBC75026561E5E3509E7839846361 (void);
extern void BigInteger_Equals_m36D3C46B170C61FBA8D78C5D4D52B6627DCB2314 (void);
extern void BigInteger_CompareTo_m8E77C4EDC6840B293E163EAC20CAA3644282F923 (void);
extern void BigInteger_CompareTo_m1B9ADF53CC7255B4DE59A270AA1EA854E25E7296 (void);
extern void BigInteger_CompareTo_mFF2E7BDB82C698B3A3BFD93FD723629F9606B33E (void);
extern void BigInteger_ToByteArray_mC501D315AF605FB0EF172628E9D4C95D2F015EB3 (void);
extern void BigInteger_ToByteArray_m3133379D53710B317BD4963D510EFFFD60D101E4 (void);
extern void BigInteger_TryWriteBytes_m4B86F875CA1C01790FE20D1A689DCA41D22326C0 (void);
extern void BigInteger_TryWriteOrCountBytes_mB22B053CE50BA54DF99F85EEAE05CA7ABDEF6441 (void);
extern void BigInteger_TryGetBytes_m015641ED36F51F9CA2D92665048EB5E0ED3C6DCD (void);
extern void BigInteger_ToString_m6AFB0DE9CD953DA8B015C31B3CC1FEF86D98A306 (void);
extern void BigInteger_ToString_mD17ED938094AEF4030E39A2F95C7C7834C6F70BB (void);
extern void BigInteger_ToString_mF67077A813661D27640565FC41346D65A155B3F6 (void);
extern void BigInteger_Add_m7544497BCCB4A4612A2CFC9F272FFA6E22F740D8 (void);
extern void BigInteger_op_Subtraction_m91D2E4A4810792046B697F0500B8797F7645FF14 (void);
extern void BigInteger_Subtract_mA96B4B988F08F5DCADABE0B27BA23D970EBFF88D (void);
extern void BigInteger_op_Implicit_m9B2DA118DD0522DFA6983787CC9C09DACDB5469B (void);
extern void BigInteger_op_Implicit_m2AC4E39750D414F220B167E8A628DE4E0932709A (void);
extern void BigInteger_op_Implicit_m9A5BFDA84D812086448392C31CA7AAD2BB167335 (void);
extern void BigInteger_op_Implicit_mE47ECF71B693CBA3538A95F12133334F08BE31F8 (void);
extern void BigInteger_op_Implicit_m0E4A1C7B1F24EB10AE57608F2EBA5B127006D850 (void);
extern void BigInteger_op_Implicit_m8C540D5135B0D715002A12DC41B1824623BAD8E4 (void);
extern void BigInteger_op_Implicit_mB409693E4A4DD397B321CE56E748831FD0B67A06 (void);
extern void BigInteger_op_Implicit_m82583327AA7F7D65A79CDCEF93F19C2398974548 (void);
extern void BigInteger_op_Explicit_mD787D48C59BD782640D92B79382C7F83022DAD04 (void);
extern void BigInteger_op_Explicit_m80CEDBE704B6E92747EA2A3B434A00265F5CF8BE (void);
extern void BigInteger_op_Explicit_m14FC0BE25F104B46C8F125CF70F8AF25A27FEF89 (void);
extern void BigInteger_op_Explicit_mD44A55A97D5D1F9BC616462A3D64ED652D86EFDF (void);
extern void BigInteger_op_Explicit_m381F80B7F65BFEC7F2597DCDB24622F7F32E6848 (void);
extern void BigInteger_op_Explicit_mB338BD78CA1D4642E264645861B15EFA025F1ED8 (void);
extern void BigInteger_op_Explicit_mFADFEBD0E21A83E1522FF45D3B41F46F546F2264 (void);
extern void BigInteger_op_Explicit_mC90AC1989E67C94D9146E1E19794016BA20AD101 (void);
extern void BigInteger_op_Explicit_m8EC30B39ADC92FEFEC38CC8D49938F4D6E9D6BD8 (void);
extern void BigInteger_op_Explicit_mC8FE8ED9D93EB044FEE2792B100C9CB9005F1EB3 (void);
extern void BigInteger_op_Explicit_m1F21788C5AD515DC2A1FCE89B189F87449CE7835 (void);
extern void BigInteger_op_LeftShift_m9C9DD1A22775C31A01650FEB59A91D4A58B719B3 (void);
extern void BigInteger_op_RightShift_mE0645B6555F95B4838A8F471584DCA026848F7AB (void);
extern void BigInteger_op_UnaryNegation_m2C40453E25F1D2E3E5D892D92CE633C8E177C1C3 (void);
extern void BigInteger_op_Addition_m895A3ED96D07E1A6E4AD97483EDC256107A31B12 (void);
extern void BigInteger_op_Multiply_m2AF82FA0B4750F56C6A5522A54FBFF2C84919EFE (void);
extern void BigInteger_op_Division_mEABFC30E3B59FF3FF53F8CEB65F70D13CD2E64D5 (void);
extern void BigInteger_op_Modulus_m3224170883C73BCEC10BC5ACB8ED7C979E43A8CE (void);
extern void BigInteger_op_LessThanOrEqual_m0217024560C6B2E56EBE0A62A028BCF9D5610A4E (void);
extern void BigInteger_op_Inequality_mF231CB46B3043DDB6A2F96D77816F1719AE9EA9D (void);
extern void BigInteger_op_LessThan_mD76A0CAA6AB2F6A0C712889429122ECA85261ABD (void);
extern void BigInteger_op_LessThanOrEqual_m775786F38F607284E58833A6E9D77F7E946ACB12 (void);
extern void BigInteger_op_Equality_mC3E68AE3326F7FAC3441A5618537BFB24CD15248 (void);
extern void BigInteger_op_Inequality_mB9EBC12ED2739B3BE3F327ABB232B1A31344F199 (void);
extern void BigInteger_op_LessThan_mD2622E31DA9CEAB50A6FBFD9F687EBB0D7B7FA81 (void);
extern void BigInteger_op_LessThanOrEqual_mEE3D1E36DC2B592F51309DEB828A781AD63D7367 (void);
extern void BigInteger_GetPartsForBitManipulation_m679473F863B7A311A59B4B58F26F1D30ADFEEADF (void);
extern void BigInteger_GetDiffLength_m86F9E98613660CB092EA24BC931C98B60E802902 (void);
extern void BigInteger__cctor_mEE4D16FA07B1AD31F26502B9C54DB72DB4D1C626 (void);
extern void BigIntegerCalculator_Add_m6B363515A5150645D0CAD0DDF4B04348B95C29C3 (void);
extern void BigIntegerCalculator_Add_mE49BAD7CDF073CF2D2A2DEABCF11AEE5CC239298 (void);
extern void BigIntegerCalculator_Add_m3CB52FDB28E32D96E1400C839ED8E7F51726A814 (void);
extern void BigIntegerCalculator_AddSelf_mDCFCD397060ED753B4BDAA0CEB13F0AEC7DD5F3C (void);
extern void BigIntegerCalculator_Subtract_mB336D562EA742D2092AFD220202FC15D9A2791A7 (void);
extern void BigIntegerCalculator_Subtract_m8BFB5D1D8B089DA2BA5AC0EB0A45D98A6F6AC681 (void);
extern void BigIntegerCalculator_Subtract_mAD5DABE3E13103E8ADCBDA013BC874F17839AC45 (void);
extern void BigIntegerCalculator_Compare_mD61B527BE1032F19B97519458C7AEAD60CBB8774 (void);
extern void BigIntegerCalculator_Divide_mD16CAF67955449A813B06DE1B977DF607AB0B64A (void);
extern void BigIntegerCalculator_Remainder_m7D45BD3AA527773069AB92174E0931230134346C (void);
extern void BigIntegerCalculator_Divide_mDEE98A394A0224CB7BE16B0E791E2E296D342FF9 (void);
extern void BigIntegerCalculator_Remainder_m6BFFAC7F7D41CC7B1DF3F579503FC8F042ACDA12 (void);
extern void BigIntegerCalculator_Divide_m02AF0ED21ED26277D6DBCACC53ABEE6F1AF4C5E8 (void);
extern void BigIntegerCalculator_AddDivisor_mF69A2BD15D3599354D77A9DBDBAC10CD71A2EE12 (void);
extern void BigIntegerCalculator_SubtractDivisor_m7090028697CB3CB82674313B928C94DD05E8502B (void);
extern void BigIntegerCalculator_DivideGuessTooBig_mC37EEE251D5A1A589F955E2A41DF6C4AE9059B17 (void);
extern void BigIntegerCalculator_CreateCopy_m6BB371F98504377F4783A367A156A967C3697206 (void);
extern void BigIntegerCalculator_LeadingZeros_m706C3F50A08A9E22533D617C46EBD62F28389BE3 (void);
extern void BigIntegerCalculator_Square_mACD0642A382CD4190157C54F060EFE9CB018B752 (void);
extern void BigIntegerCalculator_Square_m2584DA98522B28EA71D2C5B1F060D6636DD839B0 (void);
extern void BigIntegerCalculator_Multiply_mCA84C473CE355EE31D0247BB752A4E822B3F1073 (void);
extern void BigIntegerCalculator_Multiply_m5440E8027D99BCC80110F5F1946D0204449F3F3A (void);
extern void BigIntegerCalculator_Multiply_m71523151C65A265EACB4221E700FF2C5D3096DAF (void);
extern void BigIntegerCalculator_SubtractCore_m25B6AB78D49CC8E351CD04D9F8500611CF587B2E (void);
extern void BigIntegerCalculator__cctor_mB0C09E685F08931052AF8D95CEDCCFD3D914D03B (void);
extern void BigNumber_TryValidateParseStyleInteger_m8BAA6C818C5B29BF922A9B9B64137D98305E0FC3 (void);
extern void BigNumber_TryParseBigInteger_m20B86D8F2A7A726E9E4749997BE1AF8400EDB74F (void);
extern void BigNumber_ParseBigInteger_m8F3EE6622D0AA876261B5AFA2C3D9A1DCA0EC2CD (void);
extern void BigNumber_ParseBigInteger_mEBFCA034195BBBDB2FD46A41DA2CB00C9B67E6DB (void);
extern void BigNumber_HexNumberToBigInteger_m13BC6482D5FE8837F635D93BF48DC7D81B334E19 (void);
extern void BigNumber_NumberToBigInteger_m352492F43EB4817F94BB5FDC6D7002B4FDD302B0 (void);
extern void BigNumber_ParseFormatSpecifier_m642DCFB18345FAC78777645E487EE8279BA17073 (void);
extern void BigNumber_FormatBigIntegerToHex_m5BD805D186861A2FDE1CB6D7C8BF730B78B251BE (void);
extern void BigNumber_FormatBigInteger_m1C6793BBD747BBC3E729A18FDAF8A5C814C80DC4 (void);
extern void BigNumber_FormatBigInteger_m626A9168DC2BBB348E31E47313E83D88263747AA (void);
extern void BigNumberBuffer_Create_mCB1426735F95CA7747B2D5C418B778EA4441E8D8 (void);
extern void NumericsHelpers_GetDoubleParts_mEDC6A393466D91364322BC21F0DAB1056B4B8BDE (void);
extern void NumericsHelpers_GetDoubleFromParts_m7AD1EBE9AF03E541D8773F1501109954A7ED8044 (void);
extern void NumericsHelpers_DangerousMakeTwosComplement_m4B6EED2DF0E8C7B5171CDC7BBE5A7A3D68C79D4B (void);
extern void NumericsHelpers_MakeUlong_mAB879C53817E4E9BE9C649F09EEE0CB11DE53514 (void);
extern void NumericsHelpers_Abs_m82DD45E6FEBF5DAB9533B31E5BC62DC5EF37568A (void);
extern void NumericsHelpers_CombineHash_m16177FC379833624A7C14834FA38ADE527A53CCE (void);
extern void NumericsHelpers_CombineHash_m676E72BC5EC287D4C9602A8D876D24E2CBFDC776 (void);
extern void NumericsHelpers_CbitHighZero_m72B63F48B91DEB05E71577AF422AD373CE72CAC2 (void);
extern void NumericsHelpers_CbitHighZero_mEF438EF7844C19E38A5ABE2B950ECB5994D03B5F (void);
extern void FormatProvider_FormatBigInteger_m1FBB2719E6A285F3F9EE6A4EE4B95AB6C50C0BAB (void);
extern void FormatProvider_TryStringToBigInteger_m4A4E85DF102A9188498A7D5E2074B9DE43960F6C (void);
extern void Number_IsWhite_m37BBB954A36F00B5A274536FFCA964128F9D867F (void);
extern void Number_MatchChars_mEB4DE6AF58FF0B189D2BFA242BC200B7A7F0401C (void);
extern void Number_MatchChars_m47E62446DBCE6EBEBA662E65BF5BECC772EA5BBF (void);
extern void Number_ParseNumber_m401874631147B19C641F35E43E1FC1D23038CBCE (void);
extern void Number_TrailingZeros_mA4C4FD1634CDCC9915CB7412DD7E2E5B0D2718D7 (void);
extern void Number_TryStringToNumber_mA39F96566BD17881E186DD87981C8228FA9AF11E (void);
extern void Number_Int32ToDecChars_mA94E1FFBC0C831A23C6A974FC11018B9E6F9ED4E (void);
extern void Number_ParseFormatSpecifier_mC2A7C10F8899ED9BA94E9D9EFE6FDDCADE68618A (void);
extern void Number_NumberToString_mB02B6AFBEEF66C19BB094F00189CC8E15A16AD18 (void);
extern void Number_FormatCurrency_m097DB55A0D1FC114CC86AF8F08F56A8AFEDC93DD (void);
extern void Number_wcslen_mCD526D9E32ECC29B992889CBDBC18EFF2F3F7CC4 (void);
extern void Number_FormatFixed_m0AE79A769FD61DE736216A34F723B8D7D793C1BB (void);
extern void Number_FormatNumber_m24CDBE74E5644DDE85C931202384C04F91951EA6 (void);
extern void Number_FormatScientific_m2F27814915B4A407DE4F3692B2EECE8AD267C358 (void);
extern void Number_FormatExponent_m2194D98B7488C2DE4AB59E0AFECDCF4D258412F1 (void);
extern void Number_FormatGeneral_m68D4F0A31B064E3FDF311EFF410D774C0D3BAF0A (void);
extern void Number_FormatPercent_m765FF9BE8896DA80FDBC469B9EB40732C521B85D (void);
extern void Number_RoundNumber_m4CF4E60F6BB2595CAF7D220275299620A954CAA0 (void);
extern void Number_FindSection_mC2D1C69F848ACAB296ADB63DD0D87CF39C446849 (void);
extern void Number_NumberToStringFormat_mA407C99BE332392E17203E2A9BDC5544DDF89090 (void);
extern void Number__cctor_m2D3E19E23CA70D2ABBB814BA5694680EE8AFE430 (void);
extern void NumberBuffer_get_digits_mB2D6183F180088A57C57ED5FBA7BDD4CDF4A369A (void);
extern void ValueStringBuilder__ctor_m0660F060D846CA37202B1BEEE35D26DAC2B6AFF6 (void);
extern void ValueStringBuilder_get_Length_m5D0F5925DA1601B18CF1ADC62D8750F955DC3F6B (void);
extern void ValueStringBuilder_ToString_mAB4C26796468880783F57E543C5102DE83C10BCE (void);
extern void ValueStringBuilder_TryCopyTo_m1ADDDEC065D0CCAB6A61D871D7272522B95F801E (void);
extern void ValueStringBuilder_Insert_m658B685FEAD8D7A9935D2720FAAAB05382942E2C (void);
extern void ValueStringBuilder_Append_mBB79BFE6EAB412D689B7D6675A6E0BC3F6FCDFCC (void);
extern void ValueStringBuilder_Append_m4F9C03D9B78FD7AE877AAC57178D2F84AD2956CF (void);
extern void ValueStringBuilder_AppendSlow_mF1E32E44AE0CD50A28EE3E945C8CCE40FB184526 (void);
extern void ValueStringBuilder_Append_m4E46E62A9444CE58033DDB6EC5D9AE7CF02B48B0 (void);
extern void ValueStringBuilder_Append_m58580EDC69E4BCFEFFE0A266FE36684AC660BBD6 (void);
extern void ValueStringBuilder_AppendSpan_m0D80091AA43B5BD4944DCD4D8729310FEAF11382 (void);
extern void ValueStringBuilder_GrowAndAppend_mDB5F96AAA8A9CAD064B96D8A182D84C76BF24F46 (void);
extern void ValueStringBuilder_Grow_m8107401166703C9CB129685FA6F78F26615FC6A9 (void);
extern void ValueStringBuilder_Dispose_m3BC81A03C95916FF7171ADB0CF6F16E2366A1392 (void);
static Il2CppMethodPointer s_methodPointers[155] = 
{
	SR_Format_mFA381AB984D00222E1CACA0EE8F4C53E8C99D34B,
	BigInteger__ctor_mFEDEDA4A6AFC2BA534FEEF4EB3431B9C5AC3F9D9,
	BigInteger__ctor_mE9288D5C617F6BF5B8E44F8B73D9198F30B90D84,
	BigInteger__ctor_mA87DC2182BB2F6AA0209BD98E81C30BCE5B8BBAA,
	BigInteger__ctor_m4685E4C69B49F9470E056A761DCEB8DB9FA7D01C,
	BigInteger__ctor_m9F274FB1B4EC1E507374A65D16F8D1A6D23D54AC,
	BigInteger__ctor_m48BEDD707B2B28BDB94A838395590DFFE775015A,
	BigInteger__ctor_m9544C18A3217F10163645D4A62264DE37CA49821,
	BigInteger__ctor_m25FA3F3C832BC621CD91ABFAC3535A2F4EE7B371,
	BigInteger__ctor_mF6B2AC2AD4056AB6903913C56A92F565EB08BAFD,
	BigInteger__ctor_mB5E6337A77FC9888762DD29AAF54D2BBDD828D03,
	BigInteger__ctor_m14A0AC47ABF1AF6DE7B653CED685D7AD11B54BE6,
	BigInteger_get_Zero_m034F723A07EC664776C5541E2FE1300C02171201,
	BigInteger_get_MinusOne_m3FF0F44F8C3D6DFB66C00B61017E42E555585465,
	BigInteger_get_IsZero_m54B3F16D56CB9E59DBEC6929CF61A7FBE3592DC4,
	BigInteger_Parse_m2B82A0CB4AF02EFCD6175561813E9F0B8DA08334,
	BigInteger_Parse_m9B099A3E92013755D8645DDB1D977A63BB692D73,
	BigInteger_GetHashCode_m2CB01C462C09EB23616010EB24E0A87A4D35D783,
	BigInteger_Equals_mC711A33C4466DC2C053785E0A8A34B49A197EA1C,
	BigInteger_Equals_m6FA62389611EBC75026561E5E3509E7839846361,
	BigInteger_Equals_m36D3C46B170C61FBA8D78C5D4D52B6627DCB2314,
	BigInteger_CompareTo_m8E77C4EDC6840B293E163EAC20CAA3644282F923,
	BigInteger_CompareTo_m1B9ADF53CC7255B4DE59A270AA1EA854E25E7296,
	BigInteger_CompareTo_mFF2E7BDB82C698B3A3BFD93FD723629F9606B33E,
	BigInteger_ToByteArray_mC501D315AF605FB0EF172628E9D4C95D2F015EB3,
	BigInteger_ToByteArray_m3133379D53710B317BD4963D510EFFFD60D101E4,
	BigInteger_TryWriteBytes_m4B86F875CA1C01790FE20D1A689DCA41D22326C0,
	BigInteger_TryWriteOrCountBytes_mB22B053CE50BA54DF99F85EEAE05CA7ABDEF6441,
	BigInteger_TryGetBytes_m015641ED36F51F9CA2D92665048EB5E0ED3C6DCD,
	BigInteger_ToString_m6AFB0DE9CD953DA8B015C31B3CC1FEF86D98A306,
	BigInteger_ToString_mD17ED938094AEF4030E39A2F95C7C7834C6F70BB,
	BigInteger_ToString_mF67077A813661D27640565FC41346D65A155B3F6,
	BigInteger_Add_m7544497BCCB4A4612A2CFC9F272FFA6E22F740D8,
	BigInteger_op_Subtraction_m91D2E4A4810792046B697F0500B8797F7645FF14,
	BigInteger_Subtract_mA96B4B988F08F5DCADABE0B27BA23D970EBFF88D,
	BigInteger_op_Implicit_m9B2DA118DD0522DFA6983787CC9C09DACDB5469B,
	BigInteger_op_Implicit_m2AC4E39750D414F220B167E8A628DE4E0932709A,
	BigInteger_op_Implicit_m9A5BFDA84D812086448392C31CA7AAD2BB167335,
	BigInteger_op_Implicit_mE47ECF71B693CBA3538A95F12133334F08BE31F8,
	BigInteger_op_Implicit_m0E4A1C7B1F24EB10AE57608F2EBA5B127006D850,
	BigInteger_op_Implicit_m8C540D5135B0D715002A12DC41B1824623BAD8E4,
	BigInteger_op_Implicit_mB409693E4A4DD397B321CE56E748831FD0B67A06,
	BigInteger_op_Implicit_m82583327AA7F7D65A79CDCEF93F19C2398974548,
	BigInteger_op_Explicit_mD787D48C59BD782640D92B79382C7F83022DAD04,
	BigInteger_op_Explicit_m80CEDBE704B6E92747EA2A3B434A00265F5CF8BE,
	BigInteger_op_Explicit_m14FC0BE25F104B46C8F125CF70F8AF25A27FEF89,
	BigInteger_op_Explicit_mD44A55A97D5D1F9BC616462A3D64ED652D86EFDF,
	BigInteger_op_Explicit_m381F80B7F65BFEC7F2597DCDB24622F7F32E6848,
	BigInteger_op_Explicit_mB338BD78CA1D4642E264645861B15EFA025F1ED8,
	BigInteger_op_Explicit_mFADFEBD0E21A83E1522FF45D3B41F46F546F2264,
	BigInteger_op_Explicit_mC90AC1989E67C94D9146E1E19794016BA20AD101,
	BigInteger_op_Explicit_m8EC30B39ADC92FEFEC38CC8D49938F4D6E9D6BD8,
	BigInteger_op_Explicit_mC8FE8ED9D93EB044FEE2792B100C9CB9005F1EB3,
	BigInteger_op_Explicit_m1F21788C5AD515DC2A1FCE89B189F87449CE7835,
	BigInteger_op_LeftShift_m9C9DD1A22775C31A01650FEB59A91D4A58B719B3,
	BigInteger_op_RightShift_mE0645B6555F95B4838A8F471584DCA026848F7AB,
	BigInteger_op_UnaryNegation_m2C40453E25F1D2E3E5D892D92CE633C8E177C1C3,
	BigInteger_op_Addition_m895A3ED96D07E1A6E4AD97483EDC256107A31B12,
	BigInteger_op_Multiply_m2AF82FA0B4750F56C6A5522A54FBFF2C84919EFE,
	BigInteger_op_Division_mEABFC30E3B59FF3FF53F8CEB65F70D13CD2E64D5,
	BigInteger_op_Modulus_m3224170883C73BCEC10BC5ACB8ED7C979E43A8CE,
	BigInteger_op_LessThanOrEqual_m0217024560C6B2E56EBE0A62A028BCF9D5610A4E,
	BigInteger_op_Inequality_mF231CB46B3043DDB6A2F96D77816F1719AE9EA9D,
	BigInteger_op_LessThan_mD76A0CAA6AB2F6A0C712889429122ECA85261ABD,
	BigInteger_op_LessThanOrEqual_m775786F38F607284E58833A6E9D77F7E946ACB12,
	BigInteger_op_Equality_mC3E68AE3326F7FAC3441A5618537BFB24CD15248,
	BigInteger_op_Inequality_mB9EBC12ED2739B3BE3F327ABB232B1A31344F199,
	BigInteger_op_LessThan_mD2622E31DA9CEAB50A6FBFD9F687EBB0D7B7FA81,
	BigInteger_op_LessThanOrEqual_mEE3D1E36DC2B592F51309DEB828A781AD63D7367,
	BigInteger_GetPartsForBitManipulation_m679473F863B7A311A59B4B58F26F1D30ADFEEADF,
	BigInteger_GetDiffLength_m86F9E98613660CB092EA24BC931C98B60E802902,
	BigInteger__cctor_mEE4D16FA07B1AD31F26502B9C54DB72DB4D1C626,
	BigIntegerCalculator_Add_m6B363515A5150645D0CAD0DDF4B04348B95C29C3,
	BigIntegerCalculator_Add_mE49BAD7CDF073CF2D2A2DEABCF11AEE5CC239298,
	BigIntegerCalculator_Add_m3CB52FDB28E32D96E1400C839ED8E7F51726A814,
	BigIntegerCalculator_AddSelf_mDCFCD397060ED753B4BDAA0CEB13F0AEC7DD5F3C,
	BigIntegerCalculator_Subtract_mB336D562EA742D2092AFD220202FC15D9A2791A7,
	BigIntegerCalculator_Subtract_m8BFB5D1D8B089DA2BA5AC0EB0A45D98A6F6AC681,
	BigIntegerCalculator_Subtract_mAD5DABE3E13103E8ADCBDA013BC874F17839AC45,
	BigIntegerCalculator_Compare_mD61B527BE1032F19B97519458C7AEAD60CBB8774,
	BigIntegerCalculator_Divide_mD16CAF67955449A813B06DE1B977DF607AB0B64A,
	BigIntegerCalculator_Remainder_m7D45BD3AA527773069AB92174E0931230134346C,
	BigIntegerCalculator_Divide_mDEE98A394A0224CB7BE16B0E791E2E296D342FF9,
	BigIntegerCalculator_Remainder_m6BFFAC7F7D41CC7B1DF3F579503FC8F042ACDA12,
	BigIntegerCalculator_Divide_m02AF0ED21ED26277D6DBCACC53ABEE6F1AF4C5E8,
	BigIntegerCalculator_AddDivisor_mF69A2BD15D3599354D77A9DBDBAC10CD71A2EE12,
	BigIntegerCalculator_SubtractDivisor_m7090028697CB3CB82674313B928C94DD05E8502B,
	BigIntegerCalculator_DivideGuessTooBig_mC37EEE251D5A1A589F955E2A41DF6C4AE9059B17,
	BigIntegerCalculator_CreateCopy_m6BB371F98504377F4783A367A156A967C3697206,
	BigIntegerCalculator_LeadingZeros_m706C3F50A08A9E22533D617C46EBD62F28389BE3,
	BigIntegerCalculator_Square_mACD0642A382CD4190157C54F060EFE9CB018B752,
	BigIntegerCalculator_Square_m2584DA98522B28EA71D2C5B1F060D6636DD839B0,
	BigIntegerCalculator_Multiply_mCA84C473CE355EE31D0247BB752A4E822B3F1073,
	BigIntegerCalculator_Multiply_m5440E8027D99BCC80110F5F1946D0204449F3F3A,
	BigIntegerCalculator_Multiply_m71523151C65A265EACB4221E700FF2C5D3096DAF,
	BigIntegerCalculator_SubtractCore_m25B6AB78D49CC8E351CD04D9F8500611CF587B2E,
	BigIntegerCalculator__cctor_mB0C09E685F08931052AF8D95CEDCCFD3D914D03B,
	BigNumber_TryValidateParseStyleInteger_m8BAA6C818C5B29BF922A9B9B64137D98305E0FC3,
	BigNumber_TryParseBigInteger_m20B86D8F2A7A726E9E4749997BE1AF8400EDB74F,
	BigNumber_ParseBigInteger_m8F3EE6622D0AA876261B5AFA2C3D9A1DCA0EC2CD,
	BigNumber_ParseBigInteger_mEBFCA034195BBBDB2FD46A41DA2CB00C9B67E6DB,
	BigNumber_HexNumberToBigInteger_m13BC6482D5FE8837F635D93BF48DC7D81B334E19,
	BigNumber_NumberToBigInteger_m352492F43EB4817F94BB5FDC6D7002B4FDD302B0,
	BigNumber_ParseFormatSpecifier_m642DCFB18345FAC78777645E487EE8279BA17073,
	BigNumber_FormatBigIntegerToHex_m5BD805D186861A2FDE1CB6D7C8BF730B78B251BE,
	BigNumber_FormatBigInteger_m1C6793BBD747BBC3E729A18FDAF8A5C814C80DC4,
	BigNumber_FormatBigInteger_m626A9168DC2BBB348E31E47313E83D88263747AA,
	BigNumberBuffer_Create_mCB1426735F95CA7747B2D5C418B778EA4441E8D8,
	NumericsHelpers_GetDoubleParts_mEDC6A393466D91364322BC21F0DAB1056B4B8BDE,
	NumericsHelpers_GetDoubleFromParts_m7AD1EBE9AF03E541D8773F1501109954A7ED8044,
	NumericsHelpers_DangerousMakeTwosComplement_m4B6EED2DF0E8C7B5171CDC7BBE5A7A3D68C79D4B,
	NumericsHelpers_MakeUlong_mAB879C53817E4E9BE9C649F09EEE0CB11DE53514,
	NumericsHelpers_Abs_m82DD45E6FEBF5DAB9533B31E5BC62DC5EF37568A,
	NumericsHelpers_CombineHash_m16177FC379833624A7C14834FA38ADE527A53CCE,
	NumericsHelpers_CombineHash_m676E72BC5EC287D4C9602A8D876D24E2CBFDC776,
	NumericsHelpers_CbitHighZero_m72B63F48B91DEB05E71577AF422AD373CE72CAC2,
	NumericsHelpers_CbitHighZero_mEF438EF7844C19E38A5ABE2B950ECB5994D03B5F,
	FormatProvider_FormatBigInteger_m1FBB2719E6A285F3F9EE6A4EE4B95AB6C50C0BAB,
	FormatProvider_TryStringToBigInteger_m4A4E85DF102A9188498A7D5E2074B9DE43960F6C,
	Number_IsWhite_m37BBB954A36F00B5A274536FFCA964128F9D867F,
	Number_MatchChars_mEB4DE6AF58FF0B189D2BFA242BC200B7A7F0401C,
	Number_MatchChars_m47E62446DBCE6EBEBA662E65BF5BECC772EA5BBF,
	Number_ParseNumber_m401874631147B19C641F35E43E1FC1D23038CBCE,
	Number_TrailingZeros_mA4C4FD1634CDCC9915CB7412DD7E2E5B0D2718D7,
	Number_TryStringToNumber_mA39F96566BD17881E186DD87981C8228FA9AF11E,
	Number_Int32ToDecChars_mA94E1FFBC0C831A23C6A974FC11018B9E6F9ED4E,
	Number_ParseFormatSpecifier_mC2A7C10F8899ED9BA94E9D9EFE6FDDCADE68618A,
	Number_NumberToString_mB02B6AFBEEF66C19BB094F00189CC8E15A16AD18,
	Number_FormatCurrency_m097DB55A0D1FC114CC86AF8F08F56A8AFEDC93DD,
	Number_wcslen_mCD526D9E32ECC29B992889CBDBC18EFF2F3F7CC4,
	Number_FormatFixed_m0AE79A769FD61DE736216A34F723B8D7D793C1BB,
	Number_FormatNumber_m24CDBE74E5644DDE85C931202384C04F91951EA6,
	Number_FormatScientific_m2F27814915B4A407DE4F3692B2EECE8AD267C358,
	Number_FormatExponent_m2194D98B7488C2DE4AB59E0AFECDCF4D258412F1,
	Number_FormatGeneral_m68D4F0A31B064E3FDF311EFF410D774C0D3BAF0A,
	Number_FormatPercent_m765FF9BE8896DA80FDBC469B9EB40732C521B85D,
	Number_RoundNumber_m4CF4E60F6BB2595CAF7D220275299620A954CAA0,
	Number_FindSection_mC2D1C69F848ACAB296ADB63DD0D87CF39C446849,
	Number_NumberToStringFormat_mA407C99BE332392E17203E2A9BDC5544DDF89090,
	Number__cctor_m2D3E19E23CA70D2ABBB814BA5694680EE8AFE430,
	NumberBuffer_get_digits_mB2D6183F180088A57C57ED5FBA7BDD4CDF4A369A,
	ValueStringBuilder__ctor_m0660F060D846CA37202B1BEEE35D26DAC2B6AFF6,
	ValueStringBuilder_get_Length_m5D0F5925DA1601B18CF1ADC62D8750F955DC3F6B,
	ValueStringBuilder_ToString_mAB4C26796468880783F57E543C5102DE83C10BCE,
	ValueStringBuilder_TryCopyTo_m1ADDDEC065D0CCAB6A61D871D7272522B95F801E,
	ValueStringBuilder_Insert_m658B685FEAD8D7A9935D2720FAAAB05382942E2C,
	ValueStringBuilder_Append_mBB79BFE6EAB412D689B7D6675A6E0BC3F6FCDFCC,
	ValueStringBuilder_Append_m4F9C03D9B78FD7AE877AAC57178D2F84AD2956CF,
	ValueStringBuilder_AppendSlow_mF1E32E44AE0CD50A28EE3E945C8CCE40FB184526,
	ValueStringBuilder_Append_m4E46E62A9444CE58033DDB6EC5D9AE7CF02B48B0,
	ValueStringBuilder_Append_m58580EDC69E4BCFEFFE0A266FE36684AC660BBD6,
	ValueStringBuilder_AppendSpan_m0D80091AA43B5BD4944DCD4D8729310FEAF11382,
	ValueStringBuilder_GrowAndAppend_mDB5F96AAA8A9CAD064B96D8A182D84C76BF24F46,
	ValueStringBuilder_Grow_m8107401166703C9CB129685FA6F78F26615FC6A9,
	ValueStringBuilder_Dispose_m3BC81A03C95916FF7171ADB0CF6F16E2366A1392,
};
extern void BigInteger__ctor_mFEDEDA4A6AFC2BA534FEEF4EB3431B9C5AC3F9D9_AdjustorThunk (void);
extern void BigInteger__ctor_mE9288D5C617F6BF5B8E44F8B73D9198F30B90D84_AdjustorThunk (void);
extern void BigInteger__ctor_mA87DC2182BB2F6AA0209BD98E81C30BCE5B8BBAA_AdjustorThunk (void);
extern void BigInteger__ctor_m4685E4C69B49F9470E056A761DCEB8DB9FA7D01C_AdjustorThunk (void);
extern void BigInteger__ctor_m9F274FB1B4EC1E507374A65D16F8D1A6D23D54AC_AdjustorThunk (void);
extern void BigInteger__ctor_m48BEDD707B2B28BDB94A838395590DFFE775015A_AdjustorThunk (void);
extern void BigInteger__ctor_m9544C18A3217F10163645D4A62264DE37CA49821_AdjustorThunk (void);
extern void BigInteger__ctor_m25FA3F3C832BC621CD91ABFAC3535A2F4EE7B371_AdjustorThunk (void);
extern void BigInteger__ctor_mF6B2AC2AD4056AB6903913C56A92F565EB08BAFD_AdjustorThunk (void);
extern void BigInteger__ctor_mB5E6337A77FC9888762DD29AAF54D2BBDD828D03_AdjustorThunk (void);
extern void BigInteger__ctor_m14A0AC47ABF1AF6DE7B653CED685D7AD11B54BE6_AdjustorThunk (void);
extern void BigInteger_get_IsZero_m54B3F16D56CB9E59DBEC6929CF61A7FBE3592DC4_AdjustorThunk (void);
extern void BigInteger_GetHashCode_m2CB01C462C09EB23616010EB24E0A87A4D35D783_AdjustorThunk (void);
extern void BigInteger_Equals_mC711A33C4466DC2C053785E0A8A34B49A197EA1C_AdjustorThunk (void);
extern void BigInteger_Equals_m6FA62389611EBC75026561E5E3509E7839846361_AdjustorThunk (void);
extern void BigInteger_Equals_m36D3C46B170C61FBA8D78C5D4D52B6627DCB2314_AdjustorThunk (void);
extern void BigInteger_CompareTo_m8E77C4EDC6840B293E163EAC20CAA3644282F923_AdjustorThunk (void);
extern void BigInteger_CompareTo_m1B9ADF53CC7255B4DE59A270AA1EA854E25E7296_AdjustorThunk (void);
extern void BigInteger_CompareTo_mFF2E7BDB82C698B3A3BFD93FD723629F9606B33E_AdjustorThunk (void);
extern void BigInteger_ToByteArray_mC501D315AF605FB0EF172628E9D4C95D2F015EB3_AdjustorThunk (void);
extern void BigInteger_ToByteArray_m3133379D53710B317BD4963D510EFFFD60D101E4_AdjustorThunk (void);
extern void BigInteger_TryWriteBytes_m4B86F875CA1C01790FE20D1A689DCA41D22326C0_AdjustorThunk (void);
extern void BigInteger_TryWriteOrCountBytes_mB22B053CE50BA54DF99F85EEAE05CA7ABDEF6441_AdjustorThunk (void);
extern void BigInteger_TryGetBytes_m015641ED36F51F9CA2D92665048EB5E0ED3C6DCD_AdjustorThunk (void);
extern void BigInteger_ToString_m6AFB0DE9CD953DA8B015C31B3CC1FEF86D98A306_AdjustorThunk (void);
extern void BigInteger_ToString_mD17ED938094AEF4030E39A2F95C7C7834C6F70BB_AdjustorThunk (void);
extern void BigInteger_ToString_mF67077A813661D27640565FC41346D65A155B3F6_AdjustorThunk (void);
extern void NumberBuffer_get_digits_mB2D6183F180088A57C57ED5FBA7BDD4CDF4A369A_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[28] = 
{
	{ 0x06000002, BigInteger__ctor_mFEDEDA4A6AFC2BA534FEEF4EB3431B9C5AC3F9D9_AdjustorThunk },
	{ 0x06000003, BigInteger__ctor_mE9288D5C617F6BF5B8E44F8B73D9198F30B90D84_AdjustorThunk },
	{ 0x06000004, BigInteger__ctor_mA87DC2182BB2F6AA0209BD98E81C30BCE5B8BBAA_AdjustorThunk },
	{ 0x06000005, BigInteger__ctor_m4685E4C69B49F9470E056A761DCEB8DB9FA7D01C_AdjustorThunk },
	{ 0x06000006, BigInteger__ctor_m9F274FB1B4EC1E507374A65D16F8D1A6D23D54AC_AdjustorThunk },
	{ 0x06000007, BigInteger__ctor_m48BEDD707B2B28BDB94A838395590DFFE775015A_AdjustorThunk },
	{ 0x06000008, BigInteger__ctor_m9544C18A3217F10163645D4A62264DE37CA49821_AdjustorThunk },
	{ 0x06000009, BigInteger__ctor_m25FA3F3C832BC621CD91ABFAC3535A2F4EE7B371_AdjustorThunk },
	{ 0x0600000A, BigInteger__ctor_mF6B2AC2AD4056AB6903913C56A92F565EB08BAFD_AdjustorThunk },
	{ 0x0600000B, BigInteger__ctor_mB5E6337A77FC9888762DD29AAF54D2BBDD828D03_AdjustorThunk },
	{ 0x0600000C, BigInteger__ctor_m14A0AC47ABF1AF6DE7B653CED685D7AD11B54BE6_AdjustorThunk },
	{ 0x0600000F, BigInteger_get_IsZero_m54B3F16D56CB9E59DBEC6929CF61A7FBE3592DC4_AdjustorThunk },
	{ 0x06000012, BigInteger_GetHashCode_m2CB01C462C09EB23616010EB24E0A87A4D35D783_AdjustorThunk },
	{ 0x06000013, BigInteger_Equals_mC711A33C4466DC2C053785E0A8A34B49A197EA1C_AdjustorThunk },
	{ 0x06000014, BigInteger_Equals_m6FA62389611EBC75026561E5E3509E7839846361_AdjustorThunk },
	{ 0x06000015, BigInteger_Equals_m36D3C46B170C61FBA8D78C5D4D52B6627DCB2314_AdjustorThunk },
	{ 0x06000016, BigInteger_CompareTo_m8E77C4EDC6840B293E163EAC20CAA3644282F923_AdjustorThunk },
	{ 0x06000017, BigInteger_CompareTo_m1B9ADF53CC7255B4DE59A270AA1EA854E25E7296_AdjustorThunk },
	{ 0x06000018, BigInteger_CompareTo_mFF2E7BDB82C698B3A3BFD93FD723629F9606B33E_AdjustorThunk },
	{ 0x06000019, BigInteger_ToByteArray_mC501D315AF605FB0EF172628E9D4C95D2F015EB3_AdjustorThunk },
	{ 0x0600001A, BigInteger_ToByteArray_m3133379D53710B317BD4963D510EFFFD60D101E4_AdjustorThunk },
	{ 0x0600001B, BigInteger_TryWriteBytes_m4B86F875CA1C01790FE20D1A689DCA41D22326C0_AdjustorThunk },
	{ 0x0600001C, BigInteger_TryWriteOrCountBytes_mB22B053CE50BA54DF99F85EEAE05CA7ABDEF6441_AdjustorThunk },
	{ 0x0600001D, BigInteger_TryGetBytes_m015641ED36F51F9CA2D92665048EB5E0ED3C6DCD_AdjustorThunk },
	{ 0x0600001E, BigInteger_ToString_m6AFB0DE9CD953DA8B015C31B3CC1FEF86D98A306_AdjustorThunk },
	{ 0x0600001F, BigInteger_ToString_mD17ED938094AEF4030E39A2F95C7C7834C6F70BB_AdjustorThunk },
	{ 0x06000020, BigInteger_ToString_mF67077A813661D27640565FC41346D65A155B3F6_AdjustorThunk },
	{ 0x0600008D, NumberBuffer_get_digits_mB2D6183F180088A57C57ED5FBA7BDD4CDF4A369A_AdjustorThunk },
};
static const int32_t s_InvokerIndices[155] = 
{
	28070,
	15903,
	16179,
	15904,
	16180,
	16071,
	15804,
	15798,
	15968,
	3507,
	7461,
	7968,
	34098,
	34098,
	20550,
	26940,
	25243,
	20694,
	11681,
	11620,
	11480,
	13158,
	13048,
	13212,
	20761,
	6056,
	1393,
	1393,
	1090,
	20761,
	13820,
	6094,
	23634,
	26938,
	23634,
	31526,
	31531,
	31527,
	31532,
	31528,
	31533,
	31529,
	31534,
	31552,
	32204,
	31742,
	32410,
	31770,
	32430,
	31815,
	32516,
	32251,
	31664,
	31639,
	26939,
	26939,
	31525,
	26938,
	26938,
	26938,
	26938,
	27411,
	27411,
	27412,
	27412,
	27412,
	27412,
	27479,
	27479,
	25261,
	25526,
	34252,
	28078,
	28070,
	22072,
	24477,
	28078,
	28070,
	22072,
	27855,
	28078,
	28387,
	28070,
	28070,
	22072,
	24425,
	22866,
	22268,
	32090,
	31797,
	32090,
	24477,
	28078,
	28070,
	22072,
	22072,
	34252,
	27473,
	23648,
	25243,
	25242,
	27387,
	27387,
	28358,
	21695,
	25627,
	21694,
	34260,
	23284,
	25389,
	32764,
	28415,
	32436,
	28397,
	27836,
	31797,
	31798,
	21724,
	21782,
	31578,
	25164,
	25163,
	21785,
	27180,
	21928,
	24469,
	28358,
	22070,
	23251,
	31768,
	21723,
	23251,
	22062,
	22077,
	21857,
	23251,
	28916,
	27785,
	24457,
	34252,
	20521,
	15118,
	20694,
	20761,
	4669,
	3653,
	16178,
	15968,
	15968,
	8150,
	6517,
	9373,
	16178,
	15903,
	21016,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_System_Numerics_CodeGenModule;
const Il2CppCodeGenModule g_System_Numerics_CodeGenModule = 
{
	"System.Numerics.dll",
	155,
	s_methodPointers,
	28,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
