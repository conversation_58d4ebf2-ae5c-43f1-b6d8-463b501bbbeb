{ "pid": 107136, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON>ink<PERSON>" } },
{ "pid": 107136, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 107136, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 107136, "tid": 12884901888, "ts": 1753368364901426, "dur": 10719, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Blocking GC"} },
{ "pid": 107136, "tid": 12884901888, "ts": 1753368366006278, "dur": 189100, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 107136, "tid": 12884901888, "ts": 1753368370425331, "dur": 447209, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Background GC"} },
{ "pid": 107136, "tid": 1, "ts": 1753368372106433, "dur": 2920, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 107136, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 107136, "tid": 8589934592, "ts": 1753368366007429, "dur": 132875, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 107136, "tid": 1, "ts": 1753368372109357, "dur": 33, "ph": "X", "name": "GC - Gen1", "args": {} },
{ "pid": 107136, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 107136, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 107136, "tid": 1, "ts": 1753368364502821, "dur": 7591348, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 107136, "tid": 1, "ts": 1753368364504615, "dur": 61168, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368364509714, "dur": 31735, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368364565786, "dur": 7810, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368364597920, "dur": 38506, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368364639119, "dur": 109964, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368364749146, "dur": 23655, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368364772825, "dur": 38303, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368364811139, "dur": 150865, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368364962019, "dur": 4022, "ph": "X", "name": "ResolveForEngineModuleStrippingEnabledStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368364966052, "dur": 43954, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365010013, "dur": 1256, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365011273, "dur": 2339, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365013616, "dur": 375, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365014002, "dur": 15424, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365029435, "dur": 329, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365029765, "dur": 173, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365029939, "dur": 69, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365030009, "dur": 235, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365030245, "dur": 148, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365030394, "dur": 53, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365030449, "dur": 493, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365030943, "dur": 311, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365031256, "dur": 354, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365031624, "dur": 9285, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365040922, "dur": 6298, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365047225, "dur": 291, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365047519, "dur": 50, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365047569, "dur": 1861, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365049435, "dur": 390, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365049827, "dur": 128, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365049956, "dur": 88, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365050045, "dur": 57, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365050102, "dur": 846, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365050951, "dur": 245, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365051198, "dur": 3681, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365054886, "dur": 266, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365055156, "dur": 3017, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365058178, "dur": 209, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365058389, "dur": 4054, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365062449, "dur": 346, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365062799, "dur": 554, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365063357, "dur": 235, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365063596, "dur": 552, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365064150, "dur": 132, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365064283, "dur": 3683, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365067969, "dur": 4168, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365072143, "dur": 1263, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365073411, "dur": 3955, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365077370, "dur": 914, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365078286, "dur": 2856, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365081146, "dur": 1733, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365082883, "dur": 566, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365083452, "dur": 2328, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365085784, "dur": 1028, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365086826, "dur": 1801, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365088636, "dur": 2359, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365091003, "dur": 916, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365091925, "dur": 4573, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365096503, "dur": 149, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365096654, "dur": 158, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365096813, "dur": 2094, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365098910, "dur": 1860, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365100774, "dur": 965, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365101749, "dur": 22445, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365124200, "dur": 4698, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365128900, "dur": 2597, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365131508, "dur": 143984, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365275515, "dur": 471, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365275996, "dur": 684, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365276691, "dur": 293, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365276993, "dur": 876, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365277877, "dur": 605, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365278489, "dur": 4836, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365283331, "dur": 583, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365283917, "dur": 12, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365283930, "dur": 57, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365283989, "dur": 79, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365284068, "dur": 377, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365284445, "dur": 44, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365284490, "dur": 43, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365284534, "dur": 129, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365284664, "dur": 33, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365284697, "dur": 32, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365284730, "dur": 15, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365284746, "dur": 450, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365285197, "dur": 17, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365285214, "dur": 6, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365285220, "dur": 7, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365285228, "dur": 2, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365285230, "dur": 2, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365285232, "dur": 480, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365285713, "dur": 2, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365285724, "dur": 94499, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365380247, "dur": 14548, "ph": "X", "name": "EngineStrippingAnnotationStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365394810, "dur": 165908, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365560739, "dur": 323, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365561073, "dur": 108654, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365669741, "dur": 20858, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365690619, "dur": 11158, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365701794, "dur": 15799, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365717621, "dur": 18649, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368365736288, "dur": 3892694, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368369628996, "dur": 906, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368369629909, "dur": 5427, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368369635345, "dur": 99853, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368369735209, "dur": 5243, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368369740464, "dur": 4706, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368369745181, "dur": 2428, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368369747636, "dur": 3725, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368369751368, "dur": 3088, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368369754462, "dur": 163, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368369754629, "dur": 416, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368369755050, "dur": 441, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368369755495, "dur": 2276762, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368372032277, "dur": 28267, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368372060558, "dur": 309, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368372064138, "dur": 29756, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368372094173, "dur": 2239, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368372109392, "dur": 188, "ph": "X", "name": "", "args": {} },
{ "pid": 107136, "tid": 1, "ts": 1753368372105515, "dur": 5900, "ph": "X", "name": "Write chrome-trace events", "args": {} },
