-target:library
-out:"Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.ref.dll"
-define:UNITY_6000_2_0
-define:UNITY_6000_2
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:UNITY_6000_2_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_UNITY_CONSENT
-define:ENABLE_UNITY_CLOUD_IDENTIFIERS
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_ACCESSIBILITY
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_ANDROID
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLOUD_SERVICES_ENGINE_DIAGNOSTICS
-define:ENABLE_FIREBASE_IDENTIFIERS
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:PLATFORM_EXTENDS_VULKAN_PIPELINE_CACHE
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:PLATFORM_HAS_ADDITIONAL_API_CHECKS
-define:PLATFORM_HAS_GRAPHICS_JOBS_SUPPORT_CHECK_OVERRIDE
-define:PLATFORM_IMPLEMENTS_INSIGHTS_ANR
-define:ENABLE_ANDROID_ADVERTISING_IDS
-define:PLATFORM_HAS_BUGGY_MSAA_RESOLVE
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:PRIME_TWEEN_SAFETY_CHECKS
-define:LEVELPLAY_DEPENDENCIES_INSTALLED
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/Plugins/System.Runtime.CompilerServices.Unsafe.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.ClothModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.IdentifiersModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.InsightsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConsentModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d78732e851eb/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/CFXRDemo.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/CFXREditor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/CFXRRuntime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/CryptoLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/EventFramework.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Google.Play.AppUpdate.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Google.Play.Common.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Google.Play.Core.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Google.Play.Games.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Google.Play.Games.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/KinoBloom.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/OwnMatch3.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/PPv2URPConverters.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/PrimeTween.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/PrimeTween.Installer.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/PrimeTween.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/PsdPlugin.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/ToonyColorsPro.Demo.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UniTask.Addressables.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UniTask.DOTween.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UniTask.Linq.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UniTask.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UniTask.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Animation.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Animation.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Aseprite.Common.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Aseprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Common.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Common.Path.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Common.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Enhancers.Editor.AIBridge.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Enhancers.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.IK.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.IK.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.PixelPerfect.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.PixelPerfect.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Psdimporter.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.AIIntegration.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.SpriteShape.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.SpriteShape.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Tilemap.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Tilemap.Extras.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Tilemap.Extras.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Updater.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.AppUI.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.AppUI.MVVM.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.AppUI.Navigation.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.AppUI.Navigation.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.AppUI.Redux.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.AppUI.Redux.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.AppUI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.AppUI.Undo.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Bindings.OpenImageIO.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InferenceEngine.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InferenceEngine.iOSBLAS.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InferenceEngine.MacBLAS.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InferenceEngine.ONNX.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InferenceEngine.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InternalAPIEditorBridge.001.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InternalAPIEngineBridge.001.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.LevelPlay.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Recorder.Base.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Recorder.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Recorder.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Shaders.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Searcher.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Services.Core.Analytics.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Services.Core.Components.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Services.Core.Environments.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Services.Core.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Sysroot.Linux_x86_64.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.SysrootPackage.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Toolchain.Win-x86_64-Linux-x86_64.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualEffectGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualEffectGraph.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/ZString.ref.dll"
-analyzer:"F:/Unity Installs/6000.2.0b9/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"F:/Unity Installs/6000.2.0b9/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"F:/Unity Installs/6000.2.0b9/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
-analyzer:"Library/PackageCache/com.unity.dt.app-ui@5d6dc8bfd8d9/Runtime/MVVM/SourceGenerators/netstandard2.0/MVVMSourceGenerators.dll"
-analyzer:"Library/PackageCache/com.unity.dt.app-ui@5d6dc8bfd8d9/Runtime/SourceGenerators/netstandard2.0/EnumToLowerCase.dll"
"Assets/GemHunterMatch/Scripts/Authoring/GemPlacerTile.cs"
"Assets/GemHunterMatch/Scripts/Authoring/GemSpawner.cs"
"Assets/GemHunterMatch/Scripts/Authoring/ObstaclePlacer.cs"
"Assets/GemHunterMatch/Scripts/Board.cs"
"Assets/GemHunterMatch/Scripts/BoardCell.cs"
"Assets/GemHunterMatch/Scripts/BonusGem/BonusGem.cs"
"Assets/GemHunterMatch/Scripts/BonusGem/ColorClean.cs"
"Assets/GemHunterMatch/Scripts/BonusGem/JumpingFish.cs"
"Assets/GemHunterMatch/Scripts/BonusGem/LargeBomb.cs"
"Assets/GemHunterMatch/Scripts/BonusGem/LineRocket.cs"
"Assets/GemHunterMatch/Scripts/BonusGem/SmallBomb.cs"
"Assets/GemHunterMatch/Scripts/BonusItem/BonusGemBonusItem.cs"
"Assets/GemHunterMatch/Scripts/BonusItem/BonusItem.cs"
"Assets/GemHunterMatch/Scripts/Crate.cs"
"Assets/GemHunterMatch/Scripts/FpsMeter.cs"
"Assets/GemHunterMatch/Scripts/FrozenIce.cs"
"Assets/GemHunterMatch/Scripts/GameManager.cs"
"Assets/GemHunterMatch/Scripts/GameSettings.cs"
"Assets/GemHunterMatch/Scripts/Gem.cs"
"Assets/GemHunterMatch/Scripts/InitLoader.cs"
"Assets/GemHunterMatch/Scripts/IObstacle.cs"
"Assets/GemHunterMatch/Scripts/LevelData.cs"
"Assets/GemHunterMatch/Scripts/LevelList.cs"
"Assets/GemHunterMatch/Scripts/LightManager.cs"
"Assets/GemHunterMatch/Scripts/Match.cs"
"Assets/GemHunterMatch/Scripts/Obstacle.cs"
"Assets/GemHunterMatch/Scripts/ShopItem/ShopItemBonusItem.cs"
"Assets/GemHunterMatch/Scripts/ShopItem/ShopItemCoin.cs"
"Assets/GemHunterMatch/Scripts/ShopItem/ShopItemLive.cs"
"Assets/GemHunterMatch/Scripts/TieBlocker.cs"
"Assets/GemHunterMatch/Scripts/UI/MainMenu.cs"
"Assets/GemHunterMatch/Scripts/UI/UIHandler.cs"
"Assets/GemHunterMatch/Scripts/VFXPoolSystem.cs"
"Assets/GPGSIds.cs"
"Assets/Samples/Ads Mediation/8.10.0/Unity LevelPlay Sample/Scripts/AdConfig.cs"
"Assets/Samples/Ads Mediation/8.10.0/Unity LevelPlay Sample/Scripts/LevelPlaySample.cs"
"Assets/Scripts/ZStringExample.cs"
"Assets/Scripts/ZStringGameExamples.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"