﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mB406F47B02EFEDD0B33695C96E42106DCDEC78ED (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m117472C31C1E6F72847C92723BAAD1600DBB38CC (void);
extern void CFXR_Effect_ResetState_m610E43E011438C9B8496202AC20150A8A296C1FF (void);
extern void CFXR_Effect_Awake_mB25E65B9616B1020B47D93C27D43E4EC230DA465 (void);
extern void CFXR_Effect_OnEnable_mADA162F093C9E4F496E703369D9A80C1FC5394F7 (void);
extern void CFXR_Effect_OnDisable_m2CF4DC60ED0CCC367C03B7BC2FB083C49A8D3317 (void);
extern void CFXR_Effect_Update_m035CC14FF344F0061BDB30C7D3A51E8BFA926CE3 (void);
extern void CFXR_Effect_Animate_mB58DB9057AF39423B6C97F6107FE8C687927818B (void);
extern void CFXR_Effect_FadeOut_mE504B347809886CCD0A95EC517B054C6AB087995 (void);
extern void CFXR_Effect__ctor_m31E304B0066DFD0B76A44742CC7CF0C8F717EB04 (void);
extern void CameraShake_OnPreRenderCamera_Static_URP_m9C2F7CB89DE806D52BF12940541918633827E240 (void);
extern void CameraShake_OnPostRenderCamera_Static_URP_m143D6E666E9F0D4BC152ABFC8D55203677C83295 (void);
extern void CameraShake_OnPreRenderCamera_Static_mC9E5F1E8CA0B6BD6D5615FFAB1152EC48BA83440 (void);
extern void CameraShake_OnPostRenderCamera_Static_m1AA90602BD727079E5B1AF45EACE37DED2AB0D9F (void);
extern void CameraShake_RegisterStaticCallback_mA4FF11E804E94B596E40AA7C062EE75B0EBD65B5 (void);
extern void CameraShake_UnregisterStaticCallback_mF45CBD97F3B98CAF0F07D166D06B1822335033C9 (void);
extern void CameraShake_onPreRenderCamera_m3CE20B6B1B0FE1BB20B7400FFCC8983A1CE84D47 (void);
extern void CameraShake_onPostRenderCamera_m19E320117525982CB489A3B509ACD475AE6B2715 (void);
extern void CameraShake_fetchCameras_mBCB8270C08E9C224B438E93F9FEBD94A08C6D85B (void);
extern void CameraShake_StartShake_mB917662A46460659A807D99F141FC9B9C0A02CD4 (void);
extern void CameraShake_StopShake_mC67CD54979ACABCC6CB318B8B3AADD539C4E3EDE (void);
extern void CameraShake_animate_m434261E45F0AC0F48E12BA641E135292B5045FC1 (void);
extern void CameraShake__ctor_mD923E68C7BA3FB7317E4B7CC4131217FA23EC056 (void);
extern void CameraShake__cctor_m98166064D4A798C278C3A75C031244A566C138DE (void);
extern void AnimatedLight_animate_mD33AEB88B3937D4F4D75CFA9723BA1DDC2955426 (void);
extern void AnimatedLight_animateFadeOut_mDC5D169D4A8B5DE4928D27CEBE6DE07EB55406E2 (void);
extern void AnimatedLight_reset_m43CAFEA18F765285CC5BD53D9D836FDD46BCAB71 (void);
extern void AnimatedLight__ctor_mD72E4057AC1B447DE5EF7069CAA2C0443E75115A (void);
extern void AnimatedLight__cctor_mD7264F3E5BA090B43096D89FCC42DBF52CC533F7 (void);
extern void CFXR_EmissionBySurface__ctor_m11DD798A78F524B8CCB32522BFBC803CEAD5CA32 (void);
extern void CFXR_ParticleText_Awake_m352451D17C999E2DF55171F975F09B34FA865D8D (void);
extern void CFXR_ParticleText_InitializeFirstParticle_m5D1660A01B1C1CAC24DF269570DFEB7974B9204D (void);
extern void CFXR_ParticleText_UpdateText_mD2C7936B4CC8542E63286C45474607CA6DC581D3 (void);
extern void CFXR_ParticleText__ctor_m4C8DF5AE03CA264427D8F117EAECF1814583F741 (void);
extern void CFXR_ParticleTextFontAsset_OnValidate_m703B8054F6D76E393BAFA0A8A0309057ED59A800 (void);
extern void CFXR_ParticleTextFontAsset_IsValid_m1B2A4563CCB6AAB0F0FAC4E93F6B1738AF726DCF (void);
extern void CFXR_ParticleTextFontAsset__ctor_m6921F09A711893F0A6A8E2BB1AB7798F8B361B48 (void);
extern void Kerning__ctor_mE4EF1B5BAFA9083398C45EF5535154487362644E (void);
static Il2CppMethodPointer s_methodPointers[38] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mB406F47B02EFEDD0B33695C96E42106DCDEC78ED,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m117472C31C1E6F72847C92723BAAD1600DBB38CC,
	CFXR_Effect_ResetState_m610E43E011438C9B8496202AC20150A8A296C1FF,
	CFXR_Effect_Awake_mB25E65B9616B1020B47D93C27D43E4EC230DA465,
	CFXR_Effect_OnEnable_mADA162F093C9E4F496E703369D9A80C1FC5394F7,
	CFXR_Effect_OnDisable_m2CF4DC60ED0CCC367C03B7BC2FB083C49A8D3317,
	CFXR_Effect_Update_m035CC14FF344F0061BDB30C7D3A51E8BFA926CE3,
	CFXR_Effect_Animate_mB58DB9057AF39423B6C97F6107FE8C687927818B,
	CFXR_Effect_FadeOut_mE504B347809886CCD0A95EC517B054C6AB087995,
	CFXR_Effect__ctor_m31E304B0066DFD0B76A44742CC7CF0C8F717EB04,
	CameraShake_OnPreRenderCamera_Static_URP_m9C2F7CB89DE806D52BF12940541918633827E240,
	CameraShake_OnPostRenderCamera_Static_URP_m143D6E666E9F0D4BC152ABFC8D55203677C83295,
	CameraShake_OnPreRenderCamera_Static_mC9E5F1E8CA0B6BD6D5615FFAB1152EC48BA83440,
	CameraShake_OnPostRenderCamera_Static_m1AA90602BD727079E5B1AF45EACE37DED2AB0D9F,
	CameraShake_RegisterStaticCallback_mA4FF11E804E94B596E40AA7C062EE75B0EBD65B5,
	CameraShake_UnregisterStaticCallback_mF45CBD97F3B98CAF0F07D166D06B1822335033C9,
	CameraShake_onPreRenderCamera_m3CE20B6B1B0FE1BB20B7400FFCC8983A1CE84D47,
	CameraShake_onPostRenderCamera_m19E320117525982CB489A3B509ACD475AE6B2715,
	CameraShake_fetchCameras_mBCB8270C08E9C224B438E93F9FEBD94A08C6D85B,
	CameraShake_StartShake_mB917662A46460659A807D99F141FC9B9C0A02CD4,
	CameraShake_StopShake_mC67CD54979ACABCC6CB318B8B3AADD539C4E3EDE,
	CameraShake_animate_m434261E45F0AC0F48E12BA641E135292B5045FC1,
	CameraShake__ctor_mD923E68C7BA3FB7317E4B7CC4131217FA23EC056,
	CameraShake__cctor_m98166064D4A798C278C3A75C031244A566C138DE,
	AnimatedLight_animate_mD33AEB88B3937D4F4D75CFA9723BA1DDC2955426,
	AnimatedLight_animateFadeOut_mDC5D169D4A8B5DE4928D27CEBE6DE07EB55406E2,
	AnimatedLight_reset_m43CAFEA18F765285CC5BD53D9D836FDD46BCAB71,
	AnimatedLight__ctor_mD72E4057AC1B447DE5EF7069CAA2C0443E75115A,
	AnimatedLight__cctor_mD7264F3E5BA090B43096D89FCC42DBF52CC533F7,
	CFXR_EmissionBySurface__ctor_m11DD798A78F524B8CCB32522BFBC803CEAD5CA32,
	CFXR_ParticleText_Awake_m352451D17C999E2DF55171F975F09B34FA865D8D,
	CFXR_ParticleText_InitializeFirstParticle_m5D1660A01B1C1CAC24DF269570DFEB7974B9204D,
	CFXR_ParticleText_UpdateText_mD2C7936B4CC8542E63286C45474607CA6DC581D3,
	CFXR_ParticleText__ctor_m4C8DF5AE03CA264427D8F117EAECF1814583F741,
	CFXR_ParticleTextFontAsset_OnValidate_m703B8054F6D76E393BAFA0A8A0309057ED59A800,
	CFXR_ParticleTextFontAsset_IsValid_m1B2A4563CCB6AAB0F0FAC4E93F6B1738AF726DCF,
	CFXR_ParticleTextFontAsset__ctor_m6921F09A711893F0A6A8E2BB1AB7798F8B361B48,
	Kerning__ctor_mE4EF1B5BAFA9083398C45EF5535154487362644E,
};
static const int32_t s_InvokerIndices[38] = 
{
	34283,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	16071,
	16071,
	21016,
	29211,
	29211,
	32764,
	32764,
	32764,
	32764,
	15968,
	15968,
	21016,
	21016,
	21016,
	16071,
	21016,
	34252,
	16071,
	16071,
	21016,
	21016,
	34252,
	21016,
	21016,
	21016,
	699,
	21016,
	21016,
	20550,
	21016,
	21016,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_CFXRRuntime_CodeGenModule;
const Il2CppCodeGenModule g_CFXRRuntime_CodeGenModule = 
{
	"CFXRRuntime.dll",
	38,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
