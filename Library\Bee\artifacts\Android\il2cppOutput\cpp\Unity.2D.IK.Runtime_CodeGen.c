﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m5A4017436345C2EC90E8CAFA7C2BDAC85E39C3C4 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m51E866A9BBFCA976314F4A1161DFF4BAF330ECCD (void);
extern void CCDSolver2D_get_iterations_m1E17FF09F7A6B418F56D7B870AE93C9DBE48FA0B (void);
extern void CCDSolver2D_set_iterations_m030418A0D4E9D442BC2EB6EDA20A19425FFB95FD (void);
extern void CCDSolver2D_get_tolerance_m890790808D9B1AA18542FCE6573CFF2B1E1F6DFC (void);
extern void CCDSolver2D_set_tolerance_m100EDA67C0E37DFD4785935C438B6397E3D8F54A (void);
extern void CCDSolver2D_get_velocity_mECFBB20BD0622109E68E794289B1230BBB9908C3 (void);
extern void CCDSolver2D_set_velocity_mCEBA801B175F8891B2C4B1EC13C2C0007BD8F632 (void);
extern void CCDSolver2D_GetChainCount_m819754BE2021EE7DA2355E4677EC770765513785 (void);
extern void CCDSolver2D_GetChain_m5308AE3D809CE037A8AED06C262CC39ACE2D0C86 (void);
extern void CCDSolver2D_DoPrepare_mC4080EE45A50590F35F17C53C18E5A4220E4765E (void);
extern void CCDSolver2D_DoUpdateIK_m544ED8A118737D62D2353985F2C28D372CCC7822 (void);
extern void CCDSolver2D__ctor_mD5B87177AFC6772683005DEB18286140956F1D59 (void);
extern void AlwaysUpdateCullingStrategy_AreBonesVisible_m7F1CDE8BC538D2CEA56FCB35605A1DB8E8D8A563 (void);
extern void AlwaysUpdateCullingStrategy__ctor_mB449196207D728D0C524C27250CF2C0625F43B3B (void);
extern void BaseCullingStrategy_get_enabled_mA930FCA2CD1D12DDE5B5EE102A1806B9D46780C1 (void);
extern void BaseCullingStrategy_AddRequestingObject_mAFAB4A2F7F17BC075B74D95190BBB2CC6F138931 (void);
extern void BaseCullingStrategy_RemoveRequestingObject_m2ABEC8ACB0009A349BDCA4835F1856EC57BFC721 (void);
extern void BaseCullingStrategy_Initialize_mB306445F840AE7D0638654A820FB236C60FAE6B9 (void);
extern void BaseCullingStrategy_Update_m72823CD2AA20792AE1BAA850778A02DD562981DB (void);
extern void BaseCullingStrategy_Disable_m11463A92CEAA48C3B27B53847B3488CC42DC38A8 (void);
extern void BaseCullingStrategy_OnInitialize_m1937CB4A40534348444DD61CF9281BA8AB492928 (void);
extern void BaseCullingStrategy_OnUpdate_m8C53B72EC399B6F8924982E5B773B5BE07E584C5 (void);
extern void BaseCullingStrategy_OnDisable_m0CFB1BD03C607580402B93DB0200B2DD4C2DC677 (void);
extern void BaseCullingStrategy__ctor_m695AC24F681BC1AF74389F53E44C5A5E76CFA258 (void);
extern void CullingManager_get_instance_mB0DC9AACA07D582BAF8B86EF82B668F26C07D393 (void);
extern void CullingManager_CreateNewManager_mE29BDFA71680A9BD37C5171C8823408559913BA9 (void);
extern void CullingManager_Initialize_mEBFE555ECFFBD37789BE0031BCE22C4C7E2A088D (void);
extern void CullingManager_Update_mCB8524695A7611F3E09EB513AEB5313909B17012 (void);
extern void CullingManager_OnUpdate_m623F5C96450C1F8985395B4C218FE8CA09CD6B24 (void);
extern void CullingManager_AddCullingStrategy_mBABB6F99B46A20641BB9041823F432277CAFBA90 (void);
extern void CullingManager_RemoveCullingStrategy_m22A443BA5952A5F72B3ED3AD4CF9CF2A7C8E73CB (void);
extern void CullingManager__ctor_mDEE4B70A89D83C4A84EED7C05A24197AFE0BABAF (void);
extern void SpriteSkinVisibilityCullingStrategy_AreBonesVisible_m5765B7FF04FFE8AA1266029047CC958597F16A8C (void);
extern void SpriteSkinVisibilityCullingStrategy_OnInitialize_m463EEAE435B6250C97D126A3AB3B4EF00F2C3822 (void);
extern void SpriteSkinVisibilityCullingStrategy_OnDisable_m67088EC634F1F9283FE37A7FD8F570C5D79EEB51 (void);
extern void SpriteSkinVisibilityCullingStrategy_AddListeners_mDBF352A354A8AB986844B3E50AABEB293CC1F180 (void);
extern void SpriteSkinVisibilityCullingStrategy_RemoveListeners_m23C91277C4B5A4F49CBCD5ACA46142096159D3D6 (void);
extern void SpriteSkinVisibilityCullingStrategy_OnUpdate_m47E33E743DC85B1FA9D06DAEEDDDF2E19B152DFB (void);
extern void SpriteSkinVisibilityCullingStrategy_OnBoneTransformChanged_m59B72673F4ED646BDF7A2B8369644CDF5A643E3E (void);
extern void SpriteSkinVisibilityCullingStrategy_IsSpriteSkinRegistered_m3CE657C52E9CFE3EA948924A44C2774C174574F1 (void);
extern void SpriteSkinVisibilityCullingStrategy_UnregisterSpriteSkin_mB05FECCA537DAD27099984CDC888CCA871ABAAB8 (void);
extern void SpriteSkinVisibilityCullingStrategy_UpdateSpriteSkinVisibility_mBAA480E0A6F033B69D3980E6AA0D87ED945D16FD (void);
extern void SpriteSkinVisibilityCullingStrategy_RegisterSpriteSkinBonesMapping_mBCE0A2C59AD3128E83871882F1089A674C504331 (void);
extern void SpriteSkinVisibilityCullingStrategy_UnregisterSpriteSkinBonesMapping_mF666E97ACF04E4B7050207A142FAFDA85F052700 (void);
extern void SpriteSkinVisibilityCullingStrategy_RecalculateVisibility_mF457D32AD9D2AED69F90C02252891674E5F3F929 (void);
extern void SpriteSkinVisibilityCullingStrategy__ctor_mF1C3018683C3214FD290C177137AB7CD47520651 (void);
extern void SpriteSkinRegistry__ctor_mC5CA62396757951C78049541AC30DDC84E17D094 (void);
extern void FabrikSolver2D_get_iterations_m90663B612E79A541557CE5FE2332F576AACA889C (void);
extern void FabrikSolver2D_set_iterations_m984F18D5E2696A6C1D51B2CC453F34D39B4DA416 (void);
extern void FabrikSolver2D_get_tolerance_mAC394B13B2C18EF782FC07D4971667096BBEF675 (void);
extern void FabrikSolver2D_set_tolerance_mDD412DFAC39FD69D0D32C9576A3704C34EA4D322 (void);
extern void FabrikSolver2D_GetChainCount_m02301D46EEF4D935609E76CFEB556AEE8B540D62 (void);
extern void FabrikSolver2D_GetChain_m724AA4022DF67B7B842955F14DC610212C39E1BB (void);
extern void FabrikSolver2D_DoPrepare_mBE509EA5F61FC2C30E8264FAD33B852B2F77FCCF (void);
extern void FabrikSolver2D_DoUpdateIK_m6A95A98395B536BDA7790A7155CB2D7D38E53B44 (void);
extern void FabrikSolver2D__ctor_m02EB90147ABB1334EBF75902F51316509AEB6D23 (void);
extern void IKChain2D_get_effector_mCAE3D75C8A9A83E0B52E3D192F544EEF304A3349 (void);
extern void IKChain2D_set_effector_m4853F9D65FB3CAEDF9E52330465806616CB9A4AF (void);
extern void IKChain2D_get_target_m4B66C9ED4DB241B94D5120A01C9EB113DF99E1DA (void);
extern void IKChain2D_set_target_mC24E589B0ADCFC1E8DA3A77CF9C756A448D3C7A2 (void);
extern void IKChain2D_get_transforms_mEE348AE625BB43491CFB19ED4679B3B0064E8E89 (void);
extern void IKChain2D_get_rootTransform_mF620719296A45DCF6382F0903FE1461F38506578 (void);
extern void IKChain2D_get_lastTransform_m704ABE7559E9E1643772AA03D264D291E9440D8E (void);
extern void IKChain2D_get_transformCount_mA6F36958842F7D39613538068BF1D4C92AEB9CCF (void);
extern void IKChain2D_set_transformCount_m484A72F3191AAFF658455A2D2777EAFDA97CBD44 (void);
extern void IKChain2D_get_isValid_mD439569F71CB18F11F64AF75EACCB09CC2023F33 (void);
extern void IKChain2D_get_lengths_mA44EAAB5BD8B78D0C72B99B3AC12EA03BD359C89 (void);
extern void IKChain2D_Validate_mA2D1811455C28394F19BBC94BBC4D8EAF73185FA (void);
extern void IKChain2D_Initialize_m347513A510836079357CAC3A798729310DB0546A (void);
extern void IKChain2D_PrepareLengths_mAEDD1BFEBA015976F6C87FEC0AD9A2D79AF38D3C (void);
extern void IKChain2D_RestoreDefaultPose_m5D63FE742E347673FBFF70A8B1AF3E60FF951493 (void);
extern void IKChain2D_StoreLocalRotations_m6C181DEC69C49E7589660C22B7ECC1A490B4911A (void);
extern void IKChain2D_BlendFkToIk_m968201CF448901C816A19B2899AF83E9E5F3012F (void);
extern void IKChain2D__ctor_m4AC20B7E0E8FC4C3C00AFF0F1E93B99A2A092E2E (void);
extern void IKManager2D_GetCullingStrategy_mBBB8238A760AE7198F93D9312EBB1012142E132D (void);
extern void IKManager2D_get_weight_mFC9A080720A36B037B7E31111D13B7845970E554 (void);
extern void IKManager2D_set_weight_mCD79C1938214D3EAB19C8367C84CF3C11C473B49 (void);
extern void IKManager2D_get_solvers_m361C286167338C9C04BDC16AD3FF54882DCAE94C (void);
extern void IKManager2D_get_alwaysUpdate_m771D8CDA15049BB5AAD7C3C801BF7782B83B7534 (void);
extern void IKManager2D_set_alwaysUpdate_mCD42F3BA1FE187D9FF0443982421E2DC54B17EC2 (void);
extern void IKManager2D_OnEnable_m61D7860580F7EB991432ED47CFD131E086544155 (void);
extern void IKManager2D_OnDisable_mE32D68EC9A96C3EEAE26107163BD8AE1CA11A6EB (void);
extern void IKManager2D_ToggleCulling_mB0F95D211B6E7A021BB00BBDC96D22507F1BE048 (void);
extern void IKManager2D_OnValidate_mDB6E3F3E03DD0A7CCB17C9629723AD6462B825DE (void);
extern void IKManager2D_Reset_mE1EAE9B993BBEF06C418120F78BBA9EC998E83C1 (void);
extern void IKManager2D_FindChildSolvers_m8E0CF7AD71106967FA756B5AE4C2F0A92C5A4B8B (void);
extern void IKManager2D_AddSolver_m0FBE44EBA4DC9C069A19C2BDA732ADDBA44F2063 (void);
extern void IKManager2D_RemoveSolver_mB9E5526E3389A759C3429115D846EA5116009E64 (void);
extern void IKManager2D_UpdateManager_m474509A4B77A3BFDF4C66DB038B40E5203D09FB6 (void);
extern void IKManager2D_CacheSolversTransformIds_m27F6FC0949955ECE00DD87578A3A3981445C4BE6 (void);
extern void IKManager2D_OnPreviewUpdate_m8B9F24D7982B404444DDF0CF098BBED0A11AB417 (void);
extern void IKManager2D_IsInGUIUpdateLoop_m723DA62F63E411C0C92254E570900B3DE172EECB (void);
extern void IKManager2D_LateUpdate_mF7BBD7A2C8ECFF61A610AA8D0E291DF675C347FA (void);
extern void IKManager2D_OnEditorDataValidate_mDDE9733B6B90CFA774FC2A849C4310837C216EDF (void);
extern void IKManager2D_AddSolverEditorData_m1105D40D7671D7D384D84C3634D8F321FBEB27A5 (void);
extern void IKManager2D_RemoveSolverEditorData_m5DE5FD9D359F9F2E826B27EB6E74C01A101CC880 (void);
extern void IKManager2D__ctor_m6ADB2C521DEFD000797B1768386017E048EF53BD (void);
extern void IKUtility_IsDescendentOf_mD70200C678615BF2F1B94EDDA142FD83C06C5FB8 (void);
extern void IKUtility_GetAncestorCount_mB61DA10227213D2CA386F3C800BA1E753BAD624A (void);
extern void IKUtility_GetMaxChainCount_m39639147731465F07D07B240F6D0BE640BC38E30 (void);
extern void IKUtility__ctor_m41F81E51030E07C6024CF29BD34DD8D250ECA26A (void);
extern void LimbSolver2D_get_flip_m20F3882B6DAC5F42BB88C2C4C0ECE416C5348F9E (void);
extern void LimbSolver2D_set_flip_m922024951EDD05AF72FC0651DA97C169478037B5 (void);
extern void LimbSolver2D_DoInitialize_m77E1C08E2F81C79847F9FC27B93E4C907881FC76 (void);
extern void LimbSolver2D_GetChainCount_m74CF9E27D9938A5E0C4C2AA8B99523B098580C61 (void);
extern void LimbSolver2D_GetChain_m89E10EE1BE00A0D4A1D470DFF8EA4ED00F7717EC (void);
extern void LimbSolver2D_DoPrepare_m735FBCDD40AEA3CA799FC672473B1145EE104F0D (void);
extern void LimbSolver2D_DoUpdateIK_mBA886C5297FD723D8717D211175F4CF67CB57FA6 (void);
extern void LimbSolver2D__ctor_m6FAFF5E98C917879B7D1BADBE7AA2C4B73D3F54F (void);
extern void Solver2D_get_chainCount_m3F9C78D5B01B340350F48C637F885907C16FAD37 (void);
extern void Solver2D_get_constrainRotation_m791ECAF8523C2A5B2FA59219AAE71C19B2B607E5 (void);
extern void Solver2D_set_constrainRotation_m33256C24D4413AB3CF4CEEC8F5ACAD23B2FFF547 (void);
extern void Solver2D_get_solveFromDefaultPose_m6361C425AD236457B74864CD9334EEAA6D3339B1 (void);
extern void Solver2D_set_solveFromDefaultPose_m595C2A96327441742C491A6051348660D650488B (void);
extern void Solver2D_get_isValid_m080E3EDDC785DABCE5E0DC3AF9A307CB03D67EF4 (void);
extern void Solver2D_get_allChainsHaveTargets_mC11413F7B65A47F330E5C62B301115D32590F61A (void);
extern void Solver2D_get_weight_m2D76514C508A42B5C3CDE7A36A1CF7D1D393EB0A (void);
extern void Solver2D_set_weight_mA499B016460AE7E4FF976A843DCA404C88C69305 (void);
extern void Solver2D_OnValidate_mE9FB9A90C20151D44959302596327E5305BD2106 (void);
extern void Solver2D_Validate_m5F9513A3CAB3FED1D4B28D0B7D11F7B1EA8AF48C (void);
extern void Solver2D_HasTargets_m5AD395C999CA2E3DC30CD4B437772EE927B55839 (void);
extern void Solver2D_Initialize_mF37B0EFC4204F5C391BFD012A6B3066389345E9F (void);
extern void Solver2D_Prepare_m83A160176B9800C5A8A8412B335B00771354C89F (void);
extern void Solver2D_PrepareEffectorPositions_m0FD173D7475493FDA7FB31F17A97DB54A3C1B1F1 (void);
extern void Solver2D_UpdateIK_mBBB85290F06E02EEF83695DA67B13D35D552921C (void);
extern void Solver2D_UpdateIK_mDF4E234C6F57AD0690079AD14F26E56FC5E1680B (void);
extern void Solver2D_StoreLocalRotations_m10F4D8E21D57009153195CD41F6F367751A89FEF (void);
extern void Solver2D_BlendFkToIk_m5776E9F1597732ABED1701AD0D15EC0CA5A2ED22 (void);
extern void Solver2D_DoValidate_mE9875877F6ED0F8A808AF2E54F8633825B4E120A (void);
extern void Solver2D_DoInitialize_m5D859891BC734191D0A21613DCFB5EA3AEFEC6AF (void);
extern void Solver2D_DoPrepare_m732480459E9562385968EE8797AA221C4BE04F29 (void);
extern void Solver2D_GetPlaneRootTransform_m92F0C66E2EBDC723DC192FF1A759AB0641727725 (void);
extern void Solver2D_GetPointOnSolverPlane_m481ABFDA5820377833E37A6938D85AFCC078A078 (void);
extern void Solver2D_GetWorldPositionFromSolverPlanePoint_m93CF7969520D2738C4FBD3D40339CF7DEE4651CB (void);
extern void Solver2D_OnPreviewUpdate_m06492F5BB88D3DA7A2FCD8219157D956AACAB3C5 (void);
extern void Solver2D__ctor_mF40BCF0D802785A1FC2B36EF567DE599E6B47F07 (void);
extern void Solver2DMenuAttribute_get_menuPath_mDABAE56590BBFF3CE010290383A37845F4694FCD (void);
extern void Solver2DMenuAttribute__ctor_m96D6726CD6817F16338DC2FF52E939FCECAC3130 (void);
extern void CCD2D_Solve_m090C8AF93EC4680C2DD815B05F5221372929FE29 (void);
extern void CCD2D_DoIteration_m4C8B2C2F53579BF6E8A0D859FCD29FEE3DCAD7B0 (void);
extern void CCD2D_RotatePositionFrom_mD3CA4A2270443D5999926BE2BE72073B74667109 (void);
extern void FABRIKChain2D_get_first_mBABAF6C89D0033779E9CDB310A5252B16AD2D8F6 (void);
extern void FABRIKChain2D_get_last_m376020BA929990CDBC649643F52DEEB50C9860B8 (void);
extern void FABRIK2D_Solve_m383D53AB0F553990D02B881E6A1BFD06825F28D9 (void);
extern void FABRIK2D_SolveChain_mF2C8A7024BA94A0691D37D0237366047D80650D2 (void);
extern void FABRIK2D_ValidateChain_m7ABE72C3A0304C39252DA147722AAFBC2E6C238C (void);
extern void FABRIK2D_SolveForwardsChain_m0FFFE9ACE872DBD9F00BEACBC286001692883953 (void);
extern void FABRIK2D_SolveBackwardsChain_mFEDF7BF7418B1AD8C994A07D3B9194C8EE651564 (void);
extern void FABRIK2D_Forward_m5FA025890A864A53A45347229A23C087EEE26057 (void);
extern void FABRIK2D_Backward_m6C14042151D2F0BC7978FF15ECC60DFE37852772 (void);
extern void FABRIK2D_ValidateJoint_m70727F6E8B94E1FC05E91EEDB7D4251DC6F98A96 (void);
extern void Limb_Solve_mF32CB2433C3102CA47F98FD51E10844623987C4A (void);
static Il2CppMethodPointer s_methodPointers[158] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m5A4017436345C2EC90E8CAFA7C2BDAC85E39C3C4,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m51E866A9BBFCA976314F4A1161DFF4BAF330ECCD,
	CCDSolver2D_get_iterations_m1E17FF09F7A6B418F56D7B870AE93C9DBE48FA0B,
	CCDSolver2D_set_iterations_m030418A0D4E9D442BC2EB6EDA20A19425FFB95FD,
	CCDSolver2D_get_tolerance_m890790808D9B1AA18542FCE6573CFF2B1E1F6DFC,
	CCDSolver2D_set_tolerance_m100EDA67C0E37DFD4785935C438B6397E3D8F54A,
	CCDSolver2D_get_velocity_mECFBB20BD0622109E68E794289B1230BBB9908C3,
	CCDSolver2D_set_velocity_mCEBA801B175F8891B2C4B1EC13C2C0007BD8F632,
	CCDSolver2D_GetChainCount_m819754BE2021EE7DA2355E4677EC770765513785,
	CCDSolver2D_GetChain_m5308AE3D809CE037A8AED06C262CC39ACE2D0C86,
	CCDSolver2D_DoPrepare_mC4080EE45A50590F35F17C53C18E5A4220E4765E,
	CCDSolver2D_DoUpdateIK_m544ED8A118737D62D2353985F2C28D372CCC7822,
	CCDSolver2D__ctor_mD5B87177AFC6772683005DEB18286140956F1D59,
	AlwaysUpdateCullingStrategy_AreBonesVisible_m7F1CDE8BC538D2CEA56FCB35605A1DB8E8D8A563,
	AlwaysUpdateCullingStrategy__ctor_mB449196207D728D0C524C27250CF2C0625F43B3B,
	BaseCullingStrategy_get_enabled_mA930FCA2CD1D12DDE5B5EE102A1806B9D46780C1,
	NULL,
	BaseCullingStrategy_AddRequestingObject_mAFAB4A2F7F17BC075B74D95190BBB2CC6F138931,
	BaseCullingStrategy_RemoveRequestingObject_m2ABEC8ACB0009A349BDCA4835F1856EC57BFC721,
	BaseCullingStrategy_Initialize_mB306445F840AE7D0638654A820FB236C60FAE6B9,
	BaseCullingStrategy_Update_m72823CD2AA20792AE1BAA850778A02DD562981DB,
	BaseCullingStrategy_Disable_m11463A92CEAA48C3B27B53847B3488CC42DC38A8,
	BaseCullingStrategy_OnInitialize_m1937CB4A40534348444DD61CF9281BA8AB492928,
	BaseCullingStrategy_OnUpdate_m8C53B72EC399B6F8924982E5B773B5BE07E584C5,
	BaseCullingStrategy_OnDisable_m0CFB1BD03C607580402B93DB0200B2DD4C2DC677,
	BaseCullingStrategy__ctor_m695AC24F681BC1AF74389F53E44C5A5E76CFA258,
	CullingManager_get_instance_mB0DC9AACA07D582BAF8B86EF82B668F26C07D393,
	CullingManager_CreateNewManager_mE29BDFA71680A9BD37C5171C8823408559913BA9,
	CullingManager_Initialize_mEBFE555ECFFBD37789BE0031BCE22C4C7E2A088D,
	CullingManager_Update_mCB8524695A7611F3E09EB513AEB5313909B17012,
	CullingManager_OnUpdate_m623F5C96450C1F8985395B4C218FE8CA09CD6B24,
	CullingManager_AddCullingStrategy_mBABB6F99B46A20641BB9041823F432277CAFBA90,
	CullingManager_RemoveCullingStrategy_m22A443BA5952A5F72B3ED3AD4CF9CF2A7C8E73CB,
	NULL,
	CullingManager__ctor_mDEE4B70A89D83C4A84EED7C05A24197AFE0BABAF,
	SpriteSkinVisibilityCullingStrategy_AreBonesVisible_m5765B7FF04FFE8AA1266029047CC958597F16A8C,
	SpriteSkinVisibilityCullingStrategy_OnInitialize_m463EEAE435B6250C97D126A3AB3B4EF00F2C3822,
	SpriteSkinVisibilityCullingStrategy_OnDisable_m67088EC634F1F9283FE37A7FD8F570C5D79EEB51,
	SpriteSkinVisibilityCullingStrategy_AddListeners_mDBF352A354A8AB986844B3E50AABEB293CC1F180,
	SpriteSkinVisibilityCullingStrategy_RemoveListeners_m23C91277C4B5A4F49CBCD5ACA46142096159D3D6,
	SpriteSkinVisibilityCullingStrategy_OnUpdate_m47E33E743DC85B1FA9D06DAEEDDDF2E19B152DFB,
	SpriteSkinVisibilityCullingStrategy_OnBoneTransformChanged_m59B72673F4ED646BDF7A2B8369644CDF5A643E3E,
	SpriteSkinVisibilityCullingStrategy_IsSpriteSkinRegistered_m3CE657C52E9CFE3EA948924A44C2774C174574F1,
	SpriteSkinVisibilityCullingStrategy_UnregisterSpriteSkin_mB05FECCA537DAD27099984CDC888CCA871ABAAB8,
	SpriteSkinVisibilityCullingStrategy_UpdateSpriteSkinVisibility_mBAA480E0A6F033B69D3980E6AA0D87ED945D16FD,
	SpriteSkinVisibilityCullingStrategy_RegisterSpriteSkinBonesMapping_mBCE0A2C59AD3128E83871882F1089A674C504331,
	SpriteSkinVisibilityCullingStrategy_UnregisterSpriteSkinBonesMapping_mF666E97ACF04E4B7050207A142FAFDA85F052700,
	SpriteSkinVisibilityCullingStrategy_RecalculateVisibility_mF457D32AD9D2AED69F90C02252891674E5F3F929,
	SpriteSkinVisibilityCullingStrategy__ctor_mF1C3018683C3214FD290C177137AB7CD47520651,
	SpriteSkinRegistry__ctor_mC5CA62396757951C78049541AC30DDC84E17D094,
	FabrikSolver2D_get_iterations_m90663B612E79A541557CE5FE2332F576AACA889C,
	FabrikSolver2D_set_iterations_m984F18D5E2696A6C1D51B2CC453F34D39B4DA416,
	FabrikSolver2D_get_tolerance_mAC394B13B2C18EF782FC07D4971667096BBEF675,
	FabrikSolver2D_set_tolerance_mDD412DFAC39FD69D0D32C9576A3704C34EA4D322,
	FabrikSolver2D_GetChainCount_m02301D46EEF4D935609E76CFEB556AEE8B540D62,
	FabrikSolver2D_GetChain_m724AA4022DF67B7B842955F14DC610212C39E1BB,
	FabrikSolver2D_DoPrepare_mBE509EA5F61FC2C30E8264FAD33B852B2F77FCCF,
	FabrikSolver2D_DoUpdateIK_m6A95A98395B536BDA7790A7155CB2D7D38E53B44,
	FabrikSolver2D__ctor_m02EB90147ABB1334EBF75902F51316509AEB6D23,
	IKChain2D_get_effector_mCAE3D75C8A9A83E0B52E3D192F544EEF304A3349,
	IKChain2D_set_effector_m4853F9D65FB3CAEDF9E52330465806616CB9A4AF,
	IKChain2D_get_target_m4B66C9ED4DB241B94D5120A01C9EB113DF99E1DA,
	IKChain2D_set_target_mC24E589B0ADCFC1E8DA3A77CF9C756A448D3C7A2,
	IKChain2D_get_transforms_mEE348AE625BB43491CFB19ED4679B3B0064E8E89,
	IKChain2D_get_rootTransform_mF620719296A45DCF6382F0903FE1461F38506578,
	IKChain2D_get_lastTransform_m704ABE7559E9E1643772AA03D264D291E9440D8E,
	IKChain2D_get_transformCount_mA6F36958842F7D39613538068BF1D4C92AEB9CCF,
	IKChain2D_set_transformCount_m484A72F3191AAFF658455A2D2777EAFDA97CBD44,
	IKChain2D_get_isValid_mD439569F71CB18F11F64AF75EACCB09CC2023F33,
	IKChain2D_get_lengths_mA44EAAB5BD8B78D0C72B99B3AC12EA03BD359C89,
	IKChain2D_Validate_mA2D1811455C28394F19BBC94BBC4D8EAF73185FA,
	IKChain2D_Initialize_m347513A510836079357CAC3A798729310DB0546A,
	IKChain2D_PrepareLengths_mAEDD1BFEBA015976F6C87FEC0AD9A2D79AF38D3C,
	IKChain2D_RestoreDefaultPose_m5D63FE742E347673FBFF70A8B1AF3E60FF951493,
	IKChain2D_StoreLocalRotations_m6C181DEC69C49E7589660C22B7ECC1A490B4911A,
	IKChain2D_BlendFkToIk_m968201CF448901C816A19B2899AF83E9E5F3012F,
	IKChain2D__ctor_m4AC20B7E0E8FC4C3C00AFF0F1E93B99A2A092E2E,
	IKManager2D_GetCullingStrategy_mBBB8238A760AE7198F93D9312EBB1012142E132D,
	IKManager2D_get_weight_mFC9A080720A36B037B7E31111D13B7845970E554,
	IKManager2D_set_weight_mCD79C1938214D3EAB19C8367C84CF3C11C473B49,
	IKManager2D_get_solvers_m361C286167338C9C04BDC16AD3FF54882DCAE94C,
	IKManager2D_get_alwaysUpdate_m771D8CDA15049BB5AAD7C3C801BF7782B83B7534,
	IKManager2D_set_alwaysUpdate_mCD42F3BA1FE187D9FF0443982421E2DC54B17EC2,
	IKManager2D_OnEnable_m61D7860580F7EB991432ED47CFD131E086544155,
	IKManager2D_OnDisable_mE32D68EC9A96C3EEAE26107163BD8AE1CA11A6EB,
	IKManager2D_ToggleCulling_mB0F95D211B6E7A021BB00BBDC96D22507F1BE048,
	IKManager2D_OnValidate_mDB6E3F3E03DD0A7CCB17C9629723AD6462B825DE,
	IKManager2D_Reset_mE1EAE9B993BBEF06C418120F78BBA9EC998E83C1,
	IKManager2D_FindChildSolvers_m8E0CF7AD71106967FA756B5AE4C2F0A92C5A4B8B,
	IKManager2D_AddSolver_m0FBE44EBA4DC9C069A19C2BDA732ADDBA44F2063,
	IKManager2D_RemoveSolver_mB9E5526E3389A759C3429115D846EA5116009E64,
	IKManager2D_UpdateManager_m474509A4B77A3BFDF4C66DB038B40E5203D09FB6,
	IKManager2D_CacheSolversTransformIds_m27F6FC0949955ECE00DD87578A3A3981445C4BE6,
	IKManager2D_OnPreviewUpdate_m8B9F24D7982B404444DDF0CF098BBED0A11AB417,
	IKManager2D_IsInGUIUpdateLoop_m723DA62F63E411C0C92254E570900B3DE172EECB,
	IKManager2D_LateUpdate_mF7BBD7A2C8ECFF61A610AA8D0E291DF675C347FA,
	IKManager2D_OnEditorDataValidate_mDDE9733B6B90CFA774FC2A849C4310837C216EDF,
	IKManager2D_AddSolverEditorData_m1105D40D7671D7D384D84C3634D8F321FBEB27A5,
	IKManager2D_RemoveSolverEditorData_m5DE5FD9D359F9F2E826B27EB6E74C01A101CC880,
	IKManager2D__ctor_m6ADB2C521DEFD000797B1768386017E048EF53BD,
	IKUtility_IsDescendentOf_mD70200C678615BF2F1B94EDDA142FD83C06C5FB8,
	IKUtility_GetAncestorCount_mB61DA10227213D2CA386F3C800BA1E753BAD624A,
	IKUtility_GetMaxChainCount_m39639147731465F07D07B240F6D0BE640BC38E30,
	IKUtility__ctor_m41F81E51030E07C6024CF29BD34DD8D250ECA26A,
	LimbSolver2D_get_flip_m20F3882B6DAC5F42BB88C2C4C0ECE416C5348F9E,
	LimbSolver2D_set_flip_m922024951EDD05AF72FC0651DA97C169478037B5,
	LimbSolver2D_DoInitialize_m77E1C08E2F81C79847F9FC27B93E4C907881FC76,
	LimbSolver2D_GetChainCount_m74CF9E27D9938A5E0C4C2AA8B99523B098580C61,
	LimbSolver2D_GetChain_m89E10EE1BE00A0D4A1D470DFF8EA4ED00F7717EC,
	LimbSolver2D_DoPrepare_m735FBCDD40AEA3CA799FC672473B1145EE104F0D,
	LimbSolver2D_DoUpdateIK_mBA886C5297FD723D8717D211175F4CF67CB57FA6,
	LimbSolver2D__ctor_m6FAFF5E98C917879B7D1BADBE7AA2C4B73D3F54F,
	Solver2D_get_chainCount_m3F9C78D5B01B340350F48C637F885907C16FAD37,
	Solver2D_get_constrainRotation_m791ECAF8523C2A5B2FA59219AAE71C19B2B607E5,
	Solver2D_set_constrainRotation_m33256C24D4413AB3CF4CEEC8F5ACAD23B2FFF547,
	Solver2D_get_solveFromDefaultPose_m6361C425AD236457B74864CD9334EEAA6D3339B1,
	Solver2D_set_solveFromDefaultPose_m595C2A96327441742C491A6051348660D650488B,
	Solver2D_get_isValid_m080E3EDDC785DABCE5E0DC3AF9A307CB03D67EF4,
	Solver2D_get_allChainsHaveTargets_mC11413F7B65A47F330E5C62B301115D32590F61A,
	Solver2D_get_weight_m2D76514C508A42B5C3CDE7A36A1CF7D1D393EB0A,
	Solver2D_set_weight_mA499B016460AE7E4FF976A843DCA404C88C69305,
	Solver2D_OnValidate_mE9FB9A90C20151D44959302596327E5305BD2106,
	Solver2D_Validate_m5F9513A3CAB3FED1D4B28D0B7D11F7B1EA8AF48C,
	Solver2D_HasTargets_m5AD395C999CA2E3DC30CD4B437772EE927B55839,
	Solver2D_Initialize_mF37B0EFC4204F5C391BFD012A6B3066389345E9F,
	Solver2D_Prepare_m83A160176B9800C5A8A8412B335B00771354C89F,
	Solver2D_PrepareEffectorPositions_m0FD173D7475493FDA7FB31F17A97DB54A3C1B1F1,
	Solver2D_UpdateIK_mBBB85290F06E02EEF83695DA67B13D35D552921C,
	Solver2D_UpdateIK_mDF4E234C6F57AD0690079AD14F26E56FC5E1680B,
	Solver2D_StoreLocalRotations_m10F4D8E21D57009153195CD41F6F367751A89FEF,
	Solver2D_BlendFkToIk_m5776E9F1597732ABED1701AD0D15EC0CA5A2ED22,
	NULL,
	NULL,
	NULL,
	Solver2D_DoValidate_mE9875877F6ED0F8A808AF2E54F8633825B4E120A,
	Solver2D_DoInitialize_m5D859891BC734191D0A21613DCFB5EA3AEFEC6AF,
	Solver2D_DoPrepare_m732480459E9562385968EE8797AA221C4BE04F29,
	Solver2D_GetPlaneRootTransform_m92F0C66E2EBDC723DC192FF1A759AB0641727725,
	Solver2D_GetPointOnSolverPlane_m481ABFDA5820377833E37A6938D85AFCC078A078,
	Solver2D_GetWorldPositionFromSolverPlanePoint_m93CF7969520D2738C4FBD3D40339CF7DEE4651CB,
	Solver2D_OnPreviewUpdate_m06492F5BB88D3DA7A2FCD8219157D956AACAB3C5,
	Solver2D__ctor_mF40BCF0D802785A1FC2B36EF567DE599E6B47F07,
	Solver2DMenuAttribute_get_menuPath_mDABAE56590BBFF3CE010290383A37845F4694FCD,
	Solver2DMenuAttribute__ctor_m96D6726CD6817F16338DC2FF52E939FCECAC3130,
	CCD2D_Solve_m090C8AF93EC4680C2DD815B05F5221372929FE29,
	CCD2D_DoIteration_m4C8B2C2F53579BF6E8A0D859FCD29FEE3DCAD7B0,
	CCD2D_RotatePositionFrom_mD3CA4A2270443D5999926BE2BE72073B74667109,
	FABRIKChain2D_get_first_mBABAF6C89D0033779E9CDB310A5252B16AD2D8F6,
	FABRIKChain2D_get_last_m376020BA929990CDBC649643F52DEEB50C9860B8,
	FABRIK2D_Solve_m383D53AB0F553990D02B881E6A1BFD06825F28D9,
	FABRIK2D_SolveChain_mF2C8A7024BA94A0691D37D0237366047D80650D2,
	FABRIK2D_ValidateChain_m7ABE72C3A0304C39252DA147722AAFBC2E6C238C,
	FABRIK2D_SolveForwardsChain_m0FFFE9ACE872DBD9F00BEACBC286001692883953,
	FABRIK2D_SolveBackwardsChain_mFEDF7BF7418B1AD8C994A07D3B9194C8EE651564,
	FABRIK2D_Forward_m5FA025890A864A53A45347229A23C087EEE26057,
	FABRIK2D_Backward_m6C14042151D2F0BC7978FF15ECC60DFE37852772,
	FABRIK2D_ValidateJoint_m70727F6E8B94E1FC05E91EEDB7D4251DC6F98A96,
	Limb_Solve_mF32CB2433C3102CA47F98FD51E10844623987C4A,
};
extern void FABRIKChain2D_get_first_mBABAF6C89D0033779E9CDB310A5252B16AD2D8F6_AdjustorThunk (void);
extern void FABRIKChain2D_get_last_m376020BA929990CDBC649643F52DEEB50C9860B8_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[2] = 
{
	{ 0x06000094, FABRIKChain2D_get_first_mBABAF6C89D0033779E9CDB310A5252B16AD2D8F6_AdjustorThunk },
	{ 0x06000095, FABRIKChain2D_get_last_m376020BA929990CDBC649643F52DEEB50C9860B8_AdjustorThunk },
};
static const int32_t s_InvokerIndices[158] = 
{
	34300,
	21016,
	20694,
	15903,
	20873,
	16071,
	20873,
	16071,
	20694,
	13811,
	21016,
	15968,
	21016,
	11681,
	21016,
	20550,
	-1,
	15968,
	15968,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	34156,
	34156,
	21016,
	21016,
	21016,
	15968,
	15968,
	-1,
	21016,
	11681,
	21016,
	21016,
	21016,
	21016,
	21016,
	15968,
	11681,
	15968,
	15968,
	13820,
	15968,
	15968,
	21016,
	7968,
	20694,
	15903,
	20873,
	16071,
	20694,
	13811,
	21016,
	15968,
	21016,
	20761,
	15968,
	20761,
	15968,
	20761,
	20761,
	20761,
	20694,
	15903,
	20550,
	20761,
	20550,
	21016,
	21016,
	15757,
	21016,
	8113,
	21016,
	20761,
	20873,
	16071,
	20761,
	20550,
	15757,
	21016,
	21016,
	15757,
	21016,
	21016,
	21016,
	15968,
	15968,
	21016,
	21016,
	21016,
	34103,
	21016,
	21016,
	21016,
	15968,
	21016,
	27508,
	31787,
	31787,
	21016,
	20550,
	15757,
	21016,
	20694,
	13811,
	21016,
	15968,
	21016,
	20694,
	20550,
	15757,
	20550,
	15757,
	20550,
	20550,
	20873,
	16071,
	21016,
	20550,
	20550,
	21016,
	21016,
	21016,
	16071,
	8013,
	21016,
	16071,
	-1,
	-1,
	-1,
	20550,
	21016,
	21016,
	20761,
	14279,
	14278,
	21016,
	21016,
	20761,
	15968,
	21954,
	23560,
	25869,
	21002,
	21002,
	22269,
	27473,
	31561,
	28945,
	27473,
	26616,
	26616,
	22884,
	23779,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x06000022, { 0, 2 } },
};
extern const uint32_t g_rgctx_T_tBE8A8A2D92F0404E7DF0DB3162D9F3B789829CAC;
extern const uint32_t g_rgctx_T_tBE8A8A2D92F0404E7DF0DB3162D9F3B789829CAC;
static const Il2CppRGCTXDefinition s_rgctxValues[2] = 
{
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tBE8A8A2D92F0404E7DF0DB3162D9F3B789829CAC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tBE8A8A2D92F0404E7DF0DB3162D9F3B789829CAC },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_2D_IK_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_Unity_2D_IK_Runtime_CodeGenModule = 
{
	"Unity.2D.IK.Runtime.dll",
	158,
	s_methodPointers,
	2,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	2,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
