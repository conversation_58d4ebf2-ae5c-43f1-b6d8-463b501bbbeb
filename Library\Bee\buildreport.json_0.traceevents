{ "pid": 147316, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 147316, "tid": 1, "ts": 1753368347856210, "dur": 2193, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 147316, "tid": 1, "ts": 1753368347858408, "dur": 65693, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 147316, "tid": 1, "ts": 1753368347924103, "dur": 1741, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 147316, "tid": 2144, "ts": 1753368347941575, "dur": 30, "ph": "X", "name": "", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347856169, "dur": 43825, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347899996, "dur": 41130, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347900006, "dur": 42, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347900050, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347900052, "dur": 1008, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347901068, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347901070, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347901127, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347901133, "dur": 4925, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347906127, "dur": 47, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347906191, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347906290, "dur": 3, "ph": "X", "name": "ProcessMessages 757", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347906295, "dur": 27, "ph": "X", "name": "ReadAsync 757", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347906326, "dur": 455, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347906806, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347906841, "dur": 20142, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347926992, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347926995, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347927019, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 147316, "tid": 60129542144, "ts": 1753368347927022, "dur": 14093, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 147316, "tid": 2144, "ts": 1753368347941607, "dur": 30, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 147316, "tid": 55834574848, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 147316, "tid": 55834574848, "ts": 1753368347856106, "dur": 69761, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 147316, "tid": 55834574848, "ts": 1753368347925869, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 147316, "tid": 55834574848, "ts": 1753368347925870, "dur": 61, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 147316, "tid": 2144, "ts": 1753368347941639, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 147316, "tid": 51539607552, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 147316, "tid": 51539607552, "ts": 1753368347854044, "dur": 87118, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 147316, "tid": 51539607552, "ts": 1753368347854134, "dur": 1927, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 147316, "tid": 51539607552, "ts": 1753368347941167, "dur": 10, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 147316, "tid": 51539607552, "ts": 1753368347941179, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 147316, "tid": 2144, "ts": 1753368347941644, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1753368347900882, "dur":5132, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753368347906028, "dur":616, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753368347906679, "dur":377, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753368347907086, "dur":85, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753368347907173, "dur":20639, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753368347927813, "dur":128, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753368347928062, "dur":8549, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1753368347907257, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753368347907341, "dur":487, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1753368347907861, "dur":19943, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753368347907272, "dur":437, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753368347907709, "dur":20094, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753368347907266, "dur":436, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753368347907703, "dur":20124, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753368347907297, "dur":353, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753368347907651, "dur":20182, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753368347907378, "dur":307, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753368347907686, "dur":20148, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753368347907481, "dur":177, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753368347907659, "dur":20167, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753368347907536, "dur":19546, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753368347927650, "dur":127, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"F:/Unity Installs/6000.2.0b9/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":7, "ts":1753368347927083, "dur":700, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753368347907674, "dur":20132, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753368347941464, "dur":263, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 147316, "tid": 2144, "ts": 1753368347942086, "dur": 3117, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 147316, "tid": 2144, "ts": 1753368347945273, "dur": 4098, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 147316, "tid": 2144, "ts": 1753368347941570, "dur": 7844, "ph": "X", "name": "Write chrome-trace events", "args": {} },
