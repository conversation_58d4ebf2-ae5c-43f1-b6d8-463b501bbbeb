﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void SR_Format_m9977F23B838724F9DA04223E0762B7AEA3AA91FC (void);
extern void HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8 (void);
extern void HashHelpers__cctor_mA1680F8D1B4E1C62B6660FF8F4CB5852ED393F99 (void);
extern void KnownColorTable_EnsureColorTable_mDA6E15087C13F589AE3A2038B16F2D7DBAF3FA83 (void);
extern void KnownColorTable_InitColorTable_mC6E537EF5340F0DE473FD4D8A168D0631C38CD10 (void);
extern void KnownColorTable_EnsureColorNameTable_m7D1BDA884F19A4F1AFEDA4B4CF3C59F20F612303 (void);
extern void KnownColorTable_InitColorNameTable_mB77CC6E7FACE77180DAA58884E6EAF11865C3D83 (void);
extern void KnownColorTable_KnownColorToArgb_m0979478F1E425ECEA80341F48B9C24AA50B36E97 (void);
extern void KnownColorTable_KnownColorToName_mEFAED2FFD4EFF6A60FA921AE11C3894880DD63BE (void);
extern void KnownColorTable_UpdateSystemColors_mE162F51D4EB7E8842CFC3B1678801F3E3DB5DAD0 (void);
extern void Color__ctor_m19020CC887E9C4F7C63526E49D6895683FA56970 (void);
extern void Color_get_R_mA69925F53F4EF7AC1A3B4A94B9F4ABEA72662951 (void);
extern void Color_get_G_m8BBF290336C0D938F2546AB296B002C9995B3BAA (void);
extern void Color_get_B_mC668C85173CDC211AC0E2FEB3E6C36E9C57DCC61 (void);
extern void Color_get_A_m20373E97C1E01A1B27CD551E4FB1CB62DD381230 (void);
extern void Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198 (void);
extern void Color_get_Name_m82B674D6BA69AE386BB798DD78DB116307F3B876 (void);
extern void Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574 (void);
extern void Color_CheckByte_mAEC90FAB7CD6D1AB42685B47E3EDDCDD13D2FBE7 (void);
extern void Color_MakeArgb_mCFECEF91CF3C17C3992AAF04A0216E01954E3E2E (void);
extern void Color_FromArgb_m6044885AA1B22440D932301C32CA28CC70FA937C (void);
extern void Color_FromArgb_m7855D60AB3843525C91FB2174D10238B44F2320C (void);
extern void Color_ToArgb_m1782C7D79C856B95679C601C8A2AB40C8B90AFFC (void);
extern void Color_ToString_m5C7D9DB036E127D264B17FA528580B7BD052513F (void);
extern void Color_op_Equality_m5BD270427D7DA9AF8F2C004ACA2F43F734F2EE57 (void);
extern void Color_Equals_m0AECC84D788C0CB1B1FB44C945705AD7BAFF603A (void);
extern void Color_Equals_mCC06EC6FD579073BFC35481FA94461A70DA7B7BA (void);
extern void Color_GetHashCode_m87358396DFDAE1F14332D3AC10A83ED1060FC9BB (void);
extern void Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A (void);
extern void Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3 (void);
extern void Point_op_Equality_m16DF80982C9BD6458336F4101DFEBFC2C958216D (void);
extern void Point_Equals_mC52C70909BD95B4192039FC19157003D4DB22C97 (void);
extern void Point_Equals_m3818E37548CAC5B822230E60BB7C45EF569E98E4 (void);
extern void Point_GetHashCode_mDCA1B4C2AF30051D731F2B500019F35239D6E951 (void);
extern void Point_ToString_mA8731A1FEDC9F643E304C052B3F4285F6ABC56FF (void);
extern void PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B (void);
extern void PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3 (void);
extern void PointF_op_Equality_m0BD00717287326DD803FA0C9A74083A5A0114371 (void);
extern void PointF_Equals_mF1B0AEFEFECC0044933D67DCC12851F941F98E1D (void);
extern void PointF_Equals_m0EC0248A2D9522AB35E4C32DD4DE06ABAD946CE1 (void);
extern void PointF_GetHashCode_m7128D4D9228862A2B1C6862A8E8A7971A2AFD27E (void);
extern void PointF_ToString_m6D64DB31243686C6547C230BA5221BC3BC9E4EC2 (void);
extern void Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D (void);
extern void Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239 (void);
extern void Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52 (void);
extern void Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2 (void);
extern void Rectangle_Equals_m5B125B9AE09AE711F2218BB7BF9BFE97F054A250 (void);
extern void Rectangle_Equals_mC876A9A25614D79581F23875D17F80D1C78910FD (void);
extern void Rectangle_op_Equality_m502490E5E78505CE41D65FF3B66339074464B0B2 (void);
extern void Rectangle_GetHashCode_m3B1DDBEEFEF70E4EEB5145960DED4C1A4574D574 (void);
extern void Rectangle_ToString_m20252CB5611CF6E9F46C82F7D8068FF76AB6C508 (void);
extern void RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98 (void);
extern void RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF (void);
extern void RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D (void);
extern void RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D (void);
extern void RectangleF_Equals_m5D6DE25E3F05FB749F4D089AD8904D7FC4F0A975 (void);
extern void RectangleF_Equals_mF6803124E9CD077B02C2E8FABF907A9D186BE7BA (void);
extern void RectangleF_op_Equality_m5269F1C1E5549CA2991FE8173BBAD6462935C990 (void);
extern void RectangleF_GetHashCode_m53718FAB968C4281E4D40ABF6AF94796B345AAF8 (void);
extern void RectangleF_ToString_m3E02FDFF099EACBADDE0A2F447656C2F283757FB (void);
extern void Size_op_Equality_m1D2F397A87FCA47D38E8F5C15B947F849DD8C52A (void);
extern void Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB (void);
extern void Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961 (void);
extern void Size_Equals_m93DF80FE836459123FE02A03242CFE243564512E (void);
extern void Size_Equals_mB5785092C65D4CEFD76E302B468F50AE78C10C06 (void);
extern void Size_GetHashCode_m7DA961F97C5B8F66002E60938979DA777058E424 (void);
extern void Size_ToString_m9A0FC3B49D251B96F12A244DC5C70C341FFFE2F2 (void);
extern void SizeF_op_Equality_m125E91D7B2C63D963EDCA9A79B056897CFC436AD (void);
extern void SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3 (void);
extern void SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2 (void);
extern void SizeF_Equals_m8349658750C243186A64E059DE6602CDD809EDEE (void);
extern void SizeF_Equals_m42EA81684D6B4D93EDFC4CEB6FFA6ACE9F87F6B8 (void);
extern void SizeF_GetHashCode_m73CD624CAC3855F1EE921AA02CAA389AEAA931CD (void);
extern void SizeF_ToString_m888741B5D1636CE8E9BF5AD6D542F57CA73B5835 (void);
static Il2CppMethodPointer s_methodPointers[74] = 
{
	SR_Format_m9977F23B838724F9DA04223E0762B7AEA3AA91FC,
	HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8,
	HashHelpers__cctor_mA1680F8D1B4E1C62B6660FF8F4CB5852ED393F99,
	KnownColorTable_EnsureColorTable_mDA6E15087C13F589AE3A2038B16F2D7DBAF3FA83,
	KnownColorTable_InitColorTable_mC6E537EF5340F0DE473FD4D8A168D0631C38CD10,
	KnownColorTable_EnsureColorNameTable_m7D1BDA884F19A4F1AFEDA4B4CF3C59F20F612303,
	KnownColorTable_InitColorNameTable_mB77CC6E7FACE77180DAA58884E6EAF11865C3D83,
	KnownColorTable_KnownColorToArgb_m0979478F1E425ECEA80341F48B9C24AA50B36E97,
	KnownColorTable_KnownColorToName_mEFAED2FFD4EFF6A60FA921AE11C3894880DD63BE,
	KnownColorTable_UpdateSystemColors_mE162F51D4EB7E8842CFC3B1678801F3E3DB5DAD0,
	Color__ctor_m19020CC887E9C4F7C63526E49D6895683FA56970,
	Color_get_R_mA69925F53F4EF7AC1A3B4A94B9F4ABEA72662951,
	Color_get_G_m8BBF290336C0D938F2546AB296B002C9995B3BAA,
	Color_get_B_mC668C85173CDC211AC0E2FEB3E6C36E9C57DCC61,
	Color_get_A_m20373E97C1E01A1B27CD551E4FB1CB62DD381230,
	Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198,
	Color_get_Name_m82B674D6BA69AE386BB798DD78DB116307F3B876,
	Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574,
	Color_CheckByte_mAEC90FAB7CD6D1AB42685B47E3EDDCDD13D2FBE7,
	Color_MakeArgb_mCFECEF91CF3C17C3992AAF04A0216E01954E3E2E,
	Color_FromArgb_m6044885AA1B22440D932301C32CA28CC70FA937C,
	Color_FromArgb_m7855D60AB3843525C91FB2174D10238B44F2320C,
	Color_ToArgb_m1782C7D79C856B95679C601C8A2AB40C8B90AFFC,
	Color_ToString_m5C7D9DB036E127D264B17FA528580B7BD052513F,
	Color_op_Equality_m5BD270427D7DA9AF8F2C004ACA2F43F734F2EE57,
	Color_Equals_m0AECC84D788C0CB1B1FB44C945705AD7BAFF603A,
	Color_Equals_mCC06EC6FD579073BFC35481FA94461A70DA7B7BA,
	Color_GetHashCode_m87358396DFDAE1F14332D3AC10A83ED1060FC9BB,
	Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A,
	Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3,
	Point_op_Equality_m16DF80982C9BD6458336F4101DFEBFC2C958216D,
	Point_Equals_mC52C70909BD95B4192039FC19157003D4DB22C97,
	Point_Equals_m3818E37548CAC5B822230E60BB7C45EF569E98E4,
	Point_GetHashCode_mDCA1B4C2AF30051D731F2B500019F35239D6E951,
	Point_ToString_mA8731A1FEDC9F643E304C052B3F4285F6ABC56FF,
	PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B,
	PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3,
	PointF_op_Equality_m0BD00717287326DD803FA0C9A74083A5A0114371,
	PointF_Equals_mF1B0AEFEFECC0044933D67DCC12851F941F98E1D,
	PointF_Equals_m0EC0248A2D9522AB35E4C32DD4DE06ABAD946CE1,
	PointF_GetHashCode_m7128D4D9228862A2B1C6862A8E8A7971A2AFD27E,
	PointF_ToString_m6D64DB31243686C6547C230BA5221BC3BC9E4EC2,
	Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D,
	Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239,
	Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52,
	Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2,
	Rectangle_Equals_m5B125B9AE09AE711F2218BB7BF9BFE97F054A250,
	Rectangle_Equals_mC876A9A25614D79581F23875D17F80D1C78910FD,
	Rectangle_op_Equality_m502490E5E78505CE41D65FF3B66339074464B0B2,
	Rectangle_GetHashCode_m3B1DDBEEFEF70E4EEB5145960DED4C1A4574D574,
	Rectangle_ToString_m20252CB5611CF6E9F46C82F7D8068FF76AB6C508,
	RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98,
	RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF,
	RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D,
	RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D,
	RectangleF_Equals_m5D6DE25E3F05FB749F4D089AD8904D7FC4F0A975,
	RectangleF_Equals_mF6803124E9CD077B02C2E8FABF907A9D186BE7BA,
	RectangleF_op_Equality_m5269F1C1E5549CA2991FE8173BBAD6462935C990,
	RectangleF_GetHashCode_m53718FAB968C4281E4D40ABF6AF94796B345AAF8,
	RectangleF_ToString_m3E02FDFF099EACBADDE0A2F447656C2F283757FB,
	Size_op_Equality_m1D2F397A87FCA47D38E8F5C15B947F849DD8C52A,
	Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB,
	Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961,
	Size_Equals_m93DF80FE836459123FE02A03242CFE243564512E,
	Size_Equals_mB5785092C65D4CEFD76E302B468F50AE78C10C06,
	Size_GetHashCode_m7DA961F97C5B8F66002E60938979DA777058E424,
	Size_ToString_m9A0FC3B49D251B96F12A244DC5C70C341FFFE2F2,
	SizeF_op_Equality_m125E91D7B2C63D963EDCA9A79B056897CFC436AD,
	SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3,
	SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2,
	SizeF_Equals_m8349658750C243186A64E059DE6602CDD809EDEE,
	SizeF_Equals_m42EA81684D6B4D93EDFC4CEB6FFA6ACE9F87F6B8,
	SizeF_GetHashCode_m73CD624CAC3855F1EE921AA02CAA389AEAA931CD,
	SizeF_ToString_m888741B5D1636CE8E9BF5AD6D542F57CA73B5835,
};
extern void Color__ctor_m19020CC887E9C4F7C63526E49D6895683FA56970_AdjustorThunk (void);
extern void Color_get_R_mA69925F53F4EF7AC1A3B4A94B9F4ABEA72662951_AdjustorThunk (void);
extern void Color_get_G_m8BBF290336C0D938F2546AB296B002C9995B3BAA_AdjustorThunk (void);
extern void Color_get_B_mC668C85173CDC211AC0E2FEB3E6C36E9C57DCC61_AdjustorThunk (void);
extern void Color_get_A_m20373E97C1E01A1B27CD551E4FB1CB62DD381230_AdjustorThunk (void);
extern void Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198_AdjustorThunk (void);
extern void Color_get_Name_m82B674D6BA69AE386BB798DD78DB116307F3B876_AdjustorThunk (void);
extern void Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574_AdjustorThunk (void);
extern void Color_ToArgb_m1782C7D79C856B95679C601C8A2AB40C8B90AFFC_AdjustorThunk (void);
extern void Color_ToString_m5C7D9DB036E127D264B17FA528580B7BD052513F_AdjustorThunk (void);
extern void Color_Equals_m0AECC84D788C0CB1B1FB44C945705AD7BAFF603A_AdjustorThunk (void);
extern void Color_Equals_mCC06EC6FD579073BFC35481FA94461A70DA7B7BA_AdjustorThunk (void);
extern void Color_GetHashCode_m87358396DFDAE1F14332D3AC10A83ED1060FC9BB_AdjustorThunk (void);
extern void Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_AdjustorThunk (void);
extern void Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_AdjustorThunk (void);
extern void Point_Equals_mC52C70909BD95B4192039FC19157003D4DB22C97_AdjustorThunk (void);
extern void Point_Equals_m3818E37548CAC5B822230E60BB7C45EF569E98E4_AdjustorThunk (void);
extern void Point_GetHashCode_mDCA1B4C2AF30051D731F2B500019F35239D6E951_AdjustorThunk (void);
extern void Point_ToString_mA8731A1FEDC9F643E304C052B3F4285F6ABC56FF_AdjustorThunk (void);
extern void PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_AdjustorThunk (void);
extern void PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_AdjustorThunk (void);
extern void PointF_Equals_mF1B0AEFEFECC0044933D67DCC12851F941F98E1D_AdjustorThunk (void);
extern void PointF_Equals_m0EC0248A2D9522AB35E4C32DD4DE06ABAD946CE1_AdjustorThunk (void);
extern void PointF_GetHashCode_m7128D4D9228862A2B1C6862A8E8A7971A2AFD27E_AdjustorThunk (void);
extern void PointF_ToString_m6D64DB31243686C6547C230BA5221BC3BC9E4EC2_AdjustorThunk (void);
extern void Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_AdjustorThunk (void);
extern void Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_AdjustorThunk (void);
extern void Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_AdjustorThunk (void);
extern void Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_AdjustorThunk (void);
extern void Rectangle_Equals_m5B125B9AE09AE711F2218BB7BF9BFE97F054A250_AdjustorThunk (void);
extern void Rectangle_Equals_mC876A9A25614D79581F23875D17F80D1C78910FD_AdjustorThunk (void);
extern void Rectangle_GetHashCode_m3B1DDBEEFEF70E4EEB5145960DED4C1A4574D574_AdjustorThunk (void);
extern void Rectangle_ToString_m20252CB5611CF6E9F46C82F7D8068FF76AB6C508_AdjustorThunk (void);
extern void RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_AdjustorThunk (void);
extern void RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_AdjustorThunk (void);
extern void RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_AdjustorThunk (void);
extern void RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_AdjustorThunk (void);
extern void RectangleF_Equals_m5D6DE25E3F05FB749F4D089AD8904D7FC4F0A975_AdjustorThunk (void);
extern void RectangleF_Equals_mF6803124E9CD077B02C2E8FABF907A9D186BE7BA_AdjustorThunk (void);
extern void RectangleF_GetHashCode_m53718FAB968C4281E4D40ABF6AF94796B345AAF8_AdjustorThunk (void);
extern void RectangleF_ToString_m3E02FDFF099EACBADDE0A2F447656C2F283757FB_AdjustorThunk (void);
extern void Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_AdjustorThunk (void);
extern void Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_AdjustorThunk (void);
extern void Size_Equals_m93DF80FE836459123FE02A03242CFE243564512E_AdjustorThunk (void);
extern void Size_Equals_mB5785092C65D4CEFD76E302B468F50AE78C10C06_AdjustorThunk (void);
extern void Size_GetHashCode_m7DA961F97C5B8F66002E60938979DA777058E424_AdjustorThunk (void);
extern void Size_ToString_m9A0FC3B49D251B96F12A244DC5C70C341FFFE2F2_AdjustorThunk (void);
extern void SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_AdjustorThunk (void);
extern void SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_AdjustorThunk (void);
extern void SizeF_Equals_m8349658750C243186A64E059DE6602CDD809EDEE_AdjustorThunk (void);
extern void SizeF_Equals_m42EA81684D6B4D93EDFC4CEB6FFA6ACE9F87F6B8_AdjustorThunk (void);
extern void SizeF_GetHashCode_m73CD624CAC3855F1EE921AA02CAA389AEAA931CD_AdjustorThunk (void);
extern void SizeF_ToString_m888741B5D1636CE8E9BF5AD6D542F57CA73B5835_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[53] = 
{
	{ 0x0600000B, Color__ctor_m19020CC887E9C4F7C63526E49D6895683FA56970_AdjustorThunk },
	{ 0x0600000C, Color_get_R_mA69925F53F4EF7AC1A3B4A94B9F4ABEA72662951_AdjustorThunk },
	{ 0x0600000D, Color_get_G_m8BBF290336C0D938F2546AB296B002C9995B3BAA_AdjustorThunk },
	{ 0x0600000E, Color_get_B_mC668C85173CDC211AC0E2FEB3E6C36E9C57DCC61_AdjustorThunk },
	{ 0x0600000F, Color_get_A_m20373E97C1E01A1B27CD551E4FB1CB62DD381230_AdjustorThunk },
	{ 0x06000010, Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198_AdjustorThunk },
	{ 0x06000011, Color_get_Name_m82B674D6BA69AE386BB798DD78DB116307F3B876_AdjustorThunk },
	{ 0x06000012, Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574_AdjustorThunk },
	{ 0x06000017, Color_ToArgb_m1782C7D79C856B95679C601C8A2AB40C8B90AFFC_AdjustorThunk },
	{ 0x06000018, Color_ToString_m5C7D9DB036E127D264B17FA528580B7BD052513F_AdjustorThunk },
	{ 0x0600001A, Color_Equals_m0AECC84D788C0CB1B1FB44C945705AD7BAFF603A_AdjustorThunk },
	{ 0x0600001B, Color_Equals_mCC06EC6FD579073BFC35481FA94461A70DA7B7BA_AdjustorThunk },
	{ 0x0600001C, Color_GetHashCode_m87358396DFDAE1F14332D3AC10A83ED1060FC9BB_AdjustorThunk },
	{ 0x0600001D, Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_AdjustorThunk },
	{ 0x0600001E, Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_AdjustorThunk },
	{ 0x06000020, Point_Equals_mC52C70909BD95B4192039FC19157003D4DB22C97_AdjustorThunk },
	{ 0x06000021, Point_Equals_m3818E37548CAC5B822230E60BB7C45EF569E98E4_AdjustorThunk },
	{ 0x06000022, Point_GetHashCode_mDCA1B4C2AF30051D731F2B500019F35239D6E951_AdjustorThunk },
	{ 0x06000023, Point_ToString_mA8731A1FEDC9F643E304C052B3F4285F6ABC56FF_AdjustorThunk },
	{ 0x06000024, PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_AdjustorThunk },
	{ 0x06000025, PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_AdjustorThunk },
	{ 0x06000027, PointF_Equals_mF1B0AEFEFECC0044933D67DCC12851F941F98E1D_AdjustorThunk },
	{ 0x06000028, PointF_Equals_m0EC0248A2D9522AB35E4C32DD4DE06ABAD946CE1_AdjustorThunk },
	{ 0x06000029, PointF_GetHashCode_m7128D4D9228862A2B1C6862A8E8A7971A2AFD27E_AdjustorThunk },
	{ 0x0600002A, PointF_ToString_m6D64DB31243686C6547C230BA5221BC3BC9E4EC2_AdjustorThunk },
	{ 0x0600002B, Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_AdjustorThunk },
	{ 0x0600002C, Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_AdjustorThunk },
	{ 0x0600002D, Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_AdjustorThunk },
	{ 0x0600002E, Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_AdjustorThunk },
	{ 0x0600002F, Rectangle_Equals_m5B125B9AE09AE711F2218BB7BF9BFE97F054A250_AdjustorThunk },
	{ 0x06000030, Rectangle_Equals_mC876A9A25614D79581F23875D17F80D1C78910FD_AdjustorThunk },
	{ 0x06000032, Rectangle_GetHashCode_m3B1DDBEEFEF70E4EEB5145960DED4C1A4574D574_AdjustorThunk },
	{ 0x06000033, Rectangle_ToString_m20252CB5611CF6E9F46C82F7D8068FF76AB6C508_AdjustorThunk },
	{ 0x06000034, RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_AdjustorThunk },
	{ 0x06000035, RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_AdjustorThunk },
	{ 0x06000036, RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_AdjustorThunk },
	{ 0x06000037, RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_AdjustorThunk },
	{ 0x06000038, RectangleF_Equals_m5D6DE25E3F05FB749F4D089AD8904D7FC4F0A975_AdjustorThunk },
	{ 0x06000039, RectangleF_Equals_mF6803124E9CD077B02C2E8FABF907A9D186BE7BA_AdjustorThunk },
	{ 0x0600003B, RectangleF_GetHashCode_m53718FAB968C4281E4D40ABF6AF94796B345AAF8_AdjustorThunk },
	{ 0x0600003C, RectangleF_ToString_m3E02FDFF099EACBADDE0A2F447656C2F283757FB_AdjustorThunk },
	{ 0x0600003E, Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_AdjustorThunk },
	{ 0x0600003F, Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_AdjustorThunk },
	{ 0x06000040, Size_Equals_m93DF80FE836459123FE02A03242CFE243564512E_AdjustorThunk },
	{ 0x06000041, Size_Equals_mB5785092C65D4CEFD76E302B468F50AE78C10C06_AdjustorThunk },
	{ 0x06000042, Size_GetHashCode_m7DA961F97C5B8F66002E60938979DA777058E424_AdjustorThunk },
	{ 0x06000043, Size_ToString_m9A0FC3B49D251B96F12A244DC5C70C341FFFE2F2_AdjustorThunk },
	{ 0x06000045, SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_AdjustorThunk },
	{ 0x06000046, SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_AdjustorThunk },
	{ 0x06000047, SizeF_Equals_m8349658750C243186A64E059DE6602CDD809EDEE_AdjustorThunk },
	{ 0x06000048, SizeF_Equals_m42EA81684D6B4D93EDFC4CEB6FFA6ACE9F87F6B8_AdjustorThunk },
	{ 0x06000049, SizeF_GetHashCode_m73CD624CAC3855F1EE921AA02CAA389AEAA931CD_AdjustorThunk },
	{ 0x0600004A, SizeF_ToString_m888741B5D1636CE8E9BF5AD6D542F57CA73B5835_AdjustorThunk },
};
static const int32_t s_InvokerIndices[74] = 
{
	28070,
	27836,
	34252,
	34252,
	34252,
	34252,
	34252,
	31782,
	32085,
	32764,
	2587,
	20550,
	20550,
	20550,
	20550,
	20550,
	20761,
	20695,
	28951,
	24195,
	31609,
	23789,
	20694,
	20761,
	27426,
	11681,
	11495,
	20694,
	20694,
	20694,
	27526,
	11681,
	11713,
	20694,
	20761,
	20873,
	20873,
	27527,
	11681,
	11714,
	20694,
	20761,
	20694,
	20694,
	20694,
	20694,
	11681,
	11737,
	27538,
	20694,
	20761,
	20873,
	20873,
	20873,
	20873,
	11681,
	11738,
	27539,
	20694,
	20761,
	27559,
	20694,
	20694,
	11681,
	11794,
	20694,
	20761,
	27560,
	20873,
	20873,
	11681,
	11795,
	20694,
	20761,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_System_Drawing_CodeGenModule;
const Il2CppCodeGenModule g_System_Drawing_CodeGenModule = 
{
	"System.Drawing.dll",
	74,
	s_methodPointers,
	53,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
