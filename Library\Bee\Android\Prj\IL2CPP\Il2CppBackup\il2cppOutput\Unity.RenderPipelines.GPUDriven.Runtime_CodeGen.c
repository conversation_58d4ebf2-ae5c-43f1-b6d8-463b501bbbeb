﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* GPUResidentDrawerBurst_ClassifyMaterials_m9FEDC6820FD183791F7BD2B682CFFFBBF9DC9F40_RuntimeMethod_var;
extern const RuntimeMethod* GPUResidentDrawerBurst_FindUnsupportedRenderers_m927B5E54923278315B6256EA8178A34AA566B4BD_RuntimeMethod_var;
extern const RuntimeMethod* GPUResidentDrawerBurst_GetMaterialsWithChangedPackedMaterial_mC2D4AB98974823074A7E8543ED7A3D5BF7DB26FA_RuntimeMethod_var;
extern const RuntimeMethod* InstanceCullerBurst_SetupCullingJobInput_m48DA62BB1FCE439FD65DE65952C97249ECB28C56_RuntimeMethod_var;
extern const RuntimeMethod* InstanceCullingBatcherBurst_CreateDrawBatches_m607108DE9C1C56A9A8AE1C377CA5C64D155497E3_RuntimeMethod_var;
extern const RuntimeMethod* InstanceCullingBatcherBurst_RemoveDrawInstanceIndices_mCD31103C406E421E8E420A624BAE66CE0B58F140_RuntimeMethod_var;
extern const RuntimeMethod* InstanceDataSystemBurst_FreeInstances_m40A2076A7C0DE278135AD4A33911F95F2A94E630_RuntimeMethod_var;
extern const RuntimeMethod* InstanceDataSystemBurst_FreeRendererGroupInstances_mE5B774F1873565C1564044629ADC0EC240EC6547_RuntimeMethod_var;
extern const RuntimeMethod* InstanceDataSystemBurst_ReallocateInstances_mF18C9347288DA7A9C194DFB92AC6A014D24975D9_RuntimeMethod_var;
extern const RuntimeMethod* LODGroupDataPoolBurst_AllocateOrGetLODGroupDataInstances_mBD22A32D0C093047BA4165B0D6E9A38EE73F96F5_RuntimeMethod_var;
extern const RuntimeMethod* LODGroupDataPoolBurst_FreeLODGroupData_mA6615B0E58416F4443ED0744A145DCB275902C79_RuntimeMethod_var;



extern void EmbeddedAttribute__ctor_mC74BAF246B6DBDC3E01CA6C01E9F99BEBFB85292 (void);
extern void IsUnmanagedAttribute__ctor_mECABE2CB94F3EB55D9EBAB15A7E05DF541B049E3 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m1EDC75FA86A9139DD4A9DB0DAEFD85A1B786E080 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mBD0A412FCC9806186BDA6EE066831333F28A3414 (void);
extern void AABB_get_min_m209EF3ECD01E859258E1BDE8780CA4C25276DF70 (void);
extern void AABB_get_max_mCD29B3A2C2DEA60BBF50091D8E3C938511D82B08 (void);
extern void AABB_ToString_mB0C529C757F63FD0F78316C90D2B86206159B678 (void);
extern void AABB_RotateExtents_mE4E40331E25B5F3310112548831B83F096C98E06 (void);
extern void AABB_Transform_m1B125B36873D7F03DCBB8817DE20E81FD17C149C (void);
extern void AABBExtensions_ToAABB_m367412CA9980E495609B7267A2CC04AE39AF2402 (void);
extern void AABBExtensions_ToBounds_m7CAFEDB45226C652830072153F7236BA97C6C520 (void);
extern void BatchLayer__ctor_m5B65286582FB01A261387F6EB54330C8E47C3CFD (void);
extern void DisallowGPUDrivenRendering_get_applyToChildrenRecursively_m3E929193F5CE5D66B2FE31D776EC2A2F2116CAA9 (void);
extern void DisallowGPUDrivenRendering_set_applyToChildrenRecursively_m68D41A53534B142755C97A1E90F85707979EF47E (void);
extern void DisallowGPUDrivenRendering_OnEnable_m249A20499E8492DEEBAA51A2F682BAF5832200BE (void);
extern void DisallowGPUDrivenRendering_OnDisable_m1F1E89249A83BC47FE22BBA3AFBB3E45AA8A9CA5 (void);
extern void DisallowGPUDrivenRendering_AllowGPUDrivenRendering_m20DF3D8C370F5104515C565084A83B476625F356 (void);
extern void DisallowGPUDrivenRendering_AllowGPUDrivenRenderingRecursively_m6A9F55C1FE2690255B488CBD65D4EC7A088795DB (void);
extern void DisallowGPUDrivenRendering_OnValidate_m6F127CE57094B0CAE61BA8B4918EECB80E37240D (void);
extern void DisallowGPUDrivenRendering__ctor_mE6D1309170C6045938779078895FBDC316CD22C8 (void);
extern void DisallowSmallMeshCulling_get_applyToChildrenRecursively_m07F3D5D527D2DEF50D7B02D214383B8AF78C4C64 (void);
extern void DisallowSmallMeshCulling_set_applyToChildrenRecursively_m938F4AF22EDE3BBF3FA63774848026C0D1BE66B7 (void);
extern void DisallowSmallMeshCulling_OnEnable_m794055654CE760B4FD1780DC20C0C94C84A99A87 (void);
extern void DisallowSmallMeshCulling_OnDisable_m75A11291A610DED6EADAF48AF38509DE765AA25F (void);
extern void DisallowSmallMeshCulling_AllowSmallMeshCulling_m8D38B7DB8F8A05A7839BC00395B7137C4688E996 (void);
extern void DisallowSmallMeshCulling_AllowSmallMeshCullingRecursively_m8D9B550BDF92C3C920B34AC0DDC65D5B095F9D22 (void);
extern void DisallowSmallMeshCulling_OnValidate_mF3C1B33DE96CA700BC01B5311F80AE4E99F29DA0 (void);
extern void DisallowSmallMeshCulling__ctor_m8EE3BCDB2DE6243C9C844C82F454D0845738A51D (void);
extern void DebugDisplayGPUResidentDrawer_get_displayBatcherStats_m210D40F2C66835ADDD79B906A5266E0F233D3C34 (void);
extern void DebugDisplayGPUResidentDrawer_set_displayBatcherStats_m59E27BDE577C32E337F723FEF8F63C6BC686C662 (void);
extern void DebugDisplayGPUResidentDrawer_GetOccluderViewInstanceID_m726FCBE5E8C19295040CED7A6F87E7F31DCC3CE8 (void);
extern void DebugDisplayGPUResidentDrawer_get_occlusionTestOverlayEnable_m9A235C5BC833535F37EF6521C8201C3CE29C51A5 (void);
extern void DebugDisplayGPUResidentDrawer_set_occlusionTestOverlayEnable_mA2EB2A26999F2D9AF42AA8E3E4636C26C2B742EB (void);
extern void DebugDisplayGPUResidentDrawer_get_occlusionTestOverlayCountVisible_m634A2AC553EFFABBE7867FF3F849B56854132881 (void);
extern void DebugDisplayGPUResidentDrawer_set_occlusionTestOverlayCountVisible_mB528723B0E9B869C1A316E1E96A992559BF6F7ED (void);
extern void DebugDisplayGPUResidentDrawer_get_overrideOcclusionTestToAlwaysPass_m540F53888B63B89065870B56B8981D23580A82E3 (void);
extern void DebugDisplayGPUResidentDrawer_set_overrideOcclusionTestToAlwaysPass_m20C4D9DC1E966A9C4C4D0D466F4BD38ADD30AE13 (void);
extern void DebugDisplayGPUResidentDrawer_GetInstanceCullerViewStats_m0FBAB4D8A7F7B2AD56BABE57E5E0648B5686A85C (void);
extern void DebugDisplayGPUResidentDrawer_GetInstanceOcclusionEventStats_m602D50534C866EEB3661DA78C183640EE3224B94 (void);
extern void DebugDisplayGPUResidentDrawer_GetOccluderStats_mC14EA7475AC1AFC3C3252E433ADB1C537CD934A4 (void);
extern void DebugDisplayGPUResidentDrawer_GetOcclusionContextsCounts_m60D0D1EBF103C8A3199281D3DC5CD2C318F98CE2 (void);
extern void DebugDisplayGPUResidentDrawer_GetInstanceCullerViewCount_m92894DC66DDDCC9E86096BBE082D1D306C2A085A (void);
extern void DebugDisplayGPUResidentDrawer_GetInstanceOcclusionEventCount_mD84250B5B4DF8C05FAC630C7E60E9E27125E14A8 (void);
extern void DebugDisplayGPUResidentDrawer_AddInstanceCullerViewDataRow_m9F1A1FB82CC3344A6A3A2DC88D8EF67A67F8A767 (void);
extern void DebugDisplayGPUResidentDrawer_OccluderVersionString_m7714E7A750D1B3965E3E07AC82B75EDCFFC0A265 (void);
extern void DebugDisplayGPUResidentDrawer_OcclusionTestString_m1B455D1A286D34A76D5A3F9F0583852B93AEDB4B (void);
extern void DebugDisplayGPUResidentDrawer_VisibleInstancesString_m379FD46F020F09A4F6104D0B903B87E6D5CF440E (void);
extern void DebugDisplayGPUResidentDrawer_CulledInstancesString_mD981EA1AFC5F7D9E5FE201A576EF96E47FD545E7 (void);
extern void DebugDisplayGPUResidentDrawer_AddInstanceOcclusionPassDataRow_mD27E269D6934863E7BD1A75433C1DD6F6B080A9B (void);
extern void DebugDisplayGPUResidentDrawer_AddOcclusionContextDataRow_mDCD25714EE18DC98C484BC29D6780364F36C371B (void);
extern void DebugDisplayGPUResidentDrawer_get_AreAnySettingsActive_m749C09E9C318C2B5073709110F7D2DEFA890D144 (void);
extern void DebugDisplayGPUResidentDrawer_get_IsPostProcessingAllowed_m18A00DF3C6845F6EB6E85FEF8FAFD3A4458B4DE3 (void);
extern void DebugDisplayGPUResidentDrawer_get_IsLightingActive_m043DDEA409C65069FE6EB254A58195135068A9A1 (void);
extern void DebugDisplayGPUResidentDrawer_TryGetScreenClearColor_m8EF35A1BD3CE911D205A4031178454C2354F76FB (void);
extern void DebugDisplayGPUResidentDrawer_UnityEngine_Rendering_IDebugDisplaySettingsData_CreatePanel_m742018A2D4F79F94FD7B9A29434307A7519FD155 (void);
extern void DebugDisplayGPUResidentDrawer__ctor_mC29313F658266A745F986BBC51F16C4AF1287947 (void);
extern void Strings__cctor_m68ACF98790846338FA3596EE3124ECAFDFC10204 (void);
extern void SettingsPanel_get_Flags_m748B011E300968BF170B06B15BCA80C9B4A7EF80 (void);
extern void SettingsPanel__ctor_mC4E295FA1EE20556ED1C2F8876B443177EF1250E (void);
extern void SettingsPanel_AddInstanceCullingStatsWidget_mF9C247F7AF56C7FDF3B0D459DF7387E1C209332B (void);
extern void SettingsPanel_AddOcclusionContextStatsWidget_mBBDAD684776194C51A2E3010D8A59938BD7F746B (void);
extern void U3CU3Ec__cctor_mA36DB0581BF5A4E8003BF3379BC4A176BBB178B0 (void);
extern void U3CU3Ec__ctor_m795644A9581215B808B0A8ACF217AF65CA840E5E (void);
extern void U3CU3Ec_U3C_ctorU3Eb__2_0_m093D8CEBBBCEA344A5E1B90ED8581185C5789207 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__2_1_m4C96510BFF1B8E053D21EFB7867B53E5158A24DB (void);
extern void U3CU3Ec_U3C_ctorU3Eb__2_2_m049FC2A0C266B14BEC7B2D95DD97ECC89013EDF4 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__2_16_m14E895F5EF397DED57D6677A0F4399A04482B31A (void);
extern void U3CU3Ec_U3C_ctorU3Eb__2_17_mF558F22E7A44DC1E2D23BE2D1429DFAC0D9BC095 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__2_26_m17AC5153064AE16E0E236E377556F79919FC35D7 (void);
extern void U3CU3Ec_U3CAddInstanceCullingStatsWidgetU3Eb__3_1_mEF4F8D1CC79D1CF85CCC4610DC06BCB76564CE91 (void);
extern void U3CU3Ec_U3CAddOcclusionContextStatsWidgetU3Eb__4_1_m50ECF46F715AD40AD546A000C171E6BF749A2D57 (void);
extern void U3CU3Ec__DisplayClass2_0__ctor_m65677D4DD004FEA95CEF164DA1CFCF023CAFE84D (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__3_mD932A7BDF2C4A95ADD0A793D88603399C0A30BBA (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__4_m2BC2F60CB8AF8FD2F1328543162B1E49774712AD (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__5_m8C9D186608F61193AE4207817E1ACDDE6C2726C0 (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__6_m721C13571D79B42825FC4C282072ED740F8C9BF6 (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__7_mEB15B8079DD79B3B3AA7221B93777D3A7D91B1C9 (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__8_m31DDC1A4B0CDD778030C7A154102549CAB2D8577 (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__9_mCC4BC12D4DC4F35BD4B7585162E160F09CC8DC27 (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__10_m12DA07F4057174A9C51C3490CAB31ABBDBCC65AC (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__11_m6F14E1BC75C6E8D7983FEC6A512122D33B8F5ED2 (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__12_m3C7B0AD686A0FD426273990498FF6BA571FFC16E (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__13_m5F4FF7135D0D1BD237D13C77CCA8525F0910867D (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__14_mD4833866F9E3D44ABCD198C96CAA9F73DBF3B674 (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__15_mE458635A19DCE0C2C9232654152704448A814E9F (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__18_m4BF1ED906D874B542CCBECE37769C95E0EEEEFE2 (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__19_m15765D8BE137D80E15D9E0DFEE2D1702F80D16D1 (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__20_m3F3D3E337916110ADA2E9AC04766373D4034E29F (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__21_m7C83A2EA4C56EB3016A830CBD6BC6D7348FB32A0 (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__22_m7C34E5A19AEEF6DEA46F39BD18FDCFA72F754EC6 (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__23_mA4FC48AC04F73412EB611F97E87ADF7D437F6596 (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__24_m3C25B58CBFF7425C921F2CFE1C797A3B3C1A5F83 (void);
extern void U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__25_mC056211293A3B7503C66790B6EC87EBEB2B0A824 (void);
extern void U3CU3Ec__DisplayClass3_0__ctor_m9D53BA0B98D193D6EC91584B253D22ECFDE1A89A (void);
extern void U3CU3Ec__DisplayClass3_0_U3CAddInstanceCullingStatsWidgetU3Eb__0_m9FE17BAAA2BA671C3681DC39225C9FD13BFB3973 (void);
extern void U3CU3Ec__DisplayClass3_0_U3CAddInstanceCullingStatsWidgetU3Eb__2_mC2A100474B64CA070D26D9A1F2C84420C0ED4A7E (void);
extern void U3CU3Ec__DisplayClass3_0_U3CAddInstanceCullingStatsWidgetU3Eb__3_m919970CD51797C207B5B20F4FA9574431116859C (void);
extern void U3CU3Ec__DisplayClass4_0__ctor_m4B6078FB02177992D2D89D03034193B119FCC121 (void);
extern void U3CU3Ec__DisplayClass4_0_U3CAddOcclusionContextStatsWidgetU3Eb__0_m71812BB64272745920C85327299F9D807A592B08 (void);
extern void U3CU3Ec__DisplayClass29_0__ctor_m9E9E0D304D4857CCC41C81920D8F9B2426B21022 (void);
extern void U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__0_mC0E8B5021FEC842FDA982AF122F22BB6340D35E9 (void);
extern void U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__1_m9AE4D495A32845644E11FB2B895DA5E75F8635F8 (void);
extern void U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__2_m95BA865F2DE7F6DBDD8FCEFE47701070C5AE3F98 (void);
extern void U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__3_m58F09A1804446AF0F5931AC7196D7BAC039F3EDB (void);
extern void U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__4_m3747952920734A24FC5B1F9DCFE309747F9C4C4D (void);
extern void U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__5_m74C8AB5E55872D1160D6F08B78F48718683BF499 (void);
extern void U3CU3Ec__DisplayClass34_0__ctor_m51E792F40771F45B88ABD1EE79EF175CCE76CD48 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__0_m27FB30AE227BED3DFA61625B55C8EBE3082D04C9 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__1_m1E2FEB11D28E7B45D586D165C7B58E8C799C2989 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__2_m4652608ED9683E21291FE5A1F074C1DC6235627B (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__3_m497AA999AF8B5BAC82C1131C80256A58497A0190 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__4_m94EE7C495806664154E3E3BDC30518483010AEF4 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__5_m8BF230F649C38952F63793E85645803C3E72E468 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__6_mAA756A602DE4CAA7F4F3ABF323D5D6A41FADF620 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__7_m111803CA6822911B48D35155FF2DE904987938BF (void);
extern void U3CU3Ec__DisplayClass35_0__ctor_mD214B5A8783451B94F4590004B1FE5F08EC6D092 (void);
extern void U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__0_m4C70823339569EBDDFBBE48D1E3EA1501DADE74D (void);
extern void U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__1_m28A3FB09CA136E909008FFC0989CF96328E6EBC6 (void);
extern void U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__2_m711316387059484363D585616C8D01248FCA7EED (void);
extern void U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__3_mA0FC30C2542A04A04E4E3C75BFDF01746FCCAF7B (void);
extern void Line_LineOfPlaneIntersectingPlane_m0ED39FE7E09D672A03C93AA51883CECB1FFF2EF8 (void);
extern void Line_PlaneContainingLineAndPoint_m59813FE596901FCDFCA7E64A83841D14AB030402 (void);
extern void Line_PlaneContainingLineWithNormalPerpendicularToVector_m34DB45E98946F82ECB81242D6CE074AEF43E0CF0 (void);
extern void ReceiverPlanes_IsSignBitSet_m115EC872F6B26CBF654D849659242E76E7850A09 (void);
extern void ReceiverPlanes_LightFacingFrustumPlaneSubArray_m995D1E681E0ED63DFA8489C582A2FCBAF1C9356B (void);
extern void ReceiverPlanes_SilhouettePlaneSubArray_m851F735918EE9297C64815C42AAD446BCBE55F4B (void);
extern void ReceiverPlanes_CreateEmptyForTesting_m31C17EE66110F80DE225AC49E8AC79ED4D838ADB (void);
extern void ReceiverPlanes_Dispose_mE53DCBAD81DDB002760FC5661F3B26330B62ACF4 (void);
extern void ReceiverPlanes_Create_mB53449C117AA6108D5F00315F573CE710945A81F (void);
extern void FrustumPlaneCuller_Dispose_m802E9F4605F17D55D46C6C0D308830667DE0A08B (void);
extern void FrustumPlaneCuller_Create_m4DD061D5BCFFFDBE074596E2373150D4787331F3 (void);
extern void FrustumPlaneCuller_ComputeSplitVisibilityMask_mCFC42DB8BADE15AB4A87F4BC60A3B5D7507745DC (void);
extern void PlanePacket4__ctor_m1B67A6C35FB1B6B6BE21D76B496DA3531F636CC7 (void);
extern void ReceiverSphereCuller_CreateEmptyForTesting_mC63AFF1A7B2CF647E5A605E41125BA5E5533AE0B (void);
extern void ReceiverSphereCuller_Dispose_mFCC83320D29032DBC7A3173ADFE8F764A472FFA5 (void);
extern void ReceiverSphereCuller_UseReceiverPlanes_mEA2EE8FE32563F3FBE40D6AADA9724719186674E (void);
extern void ReceiverSphereCuller_Create_m63514E053CF0DD1CD37702E56C84F4F0408A8B00 (void);
extern void ReceiverSphereCuller_DistanceUntilCylinderFullyCrossesPlane_m1E5B038B031E5090A38EE0FF6EFC405E4BDD63E1 (void);
extern void ReceiverSphereCuller_ComputeSplitVisibilityMask_m1188332DCE2CBF80B1A431AFB1BD49955898936F (void);
extern void GPUResidentBatcher_get_batchersContext_m832BD0381846BE2E11D425CE1319953A411D64CE (void);
extern void GPUResidentBatcher_get_occlusionCullingCommon_mCC135850F6B339139CBA5E60715E4B059195CBC7 (void);
extern void GPUResidentBatcher_get_instanceCullingBatcher_m9E7BF9BA5E63072AB043C86836AC4C036AD5D465 (void);
extern void GPUResidentBatcher__ctor_mE43D45A4CCB93B7DD21D5FB49C4D6BA16DEC72B2 (void);
extern void GPUResidentBatcher_Dispose_m027050D276B285BED80401912FB6B3D0BADC90AD (void);
extern void GPUResidentBatcher_OnBeginContextRendering_mFCE0FA21DAB11BC5B770CB5C63F14C32B2ECC30F (void);
extern void GPUResidentBatcher_OnEndContextRendering_mDC1CCC712A83C48E61D34E56BE21B5458C77BB61 (void);
extern void GPUResidentBatcher_OnBeginCameraRendering_m818CA5BB64950FFCF566414C70CC5D8B6D96A922 (void);
extern void GPUResidentBatcher_OnEndCameraRendering_mC31C1F8395CEED9B4AD6321DACC36E9115666885 (void);
extern void GPUResidentBatcher_UpdateFrame_mB0B6728FE5D08E785FD7747D9AF4D065705D944D (void);
extern void GPUResidentBatcher_DestroyMaterials_m0BEE5AE70E666A3E35E16EA8D941480E1351A243 (void);
extern void GPUResidentBatcher_DestroyDrawInstances_m9513A335709809ED850D1AF76E2A6FFCB8707AF9 (void);
extern void GPUResidentBatcher_DestroyMeshes_m4B3BF017904878023774926DE457D2476283B094 (void);
extern void GPUResidentBatcher_FreeRendererGroupInstances_mB2929BF0DE2234256EB75CC078DE8441CA9594E9 (void);
extern void GPUResidentBatcher_InstanceOcclusionTest_mCDEFBDADDB90AA06A2E1394B5B80E807CBB5AA43 (void);
extern void GPUResidentBatcher_UpdateInstanceOccluders_m5570E1F11C12314DD68EA4A1B9B4EE0473FD6A18 (void);
extern void GPUResidentBatcher_UpdateRenderers_m08752E00B533B9709C0EF2892D843BF10D24920B (void);
extern void GPUResidentBatcher_SchedulePackedMaterialCacheUpdate_mB5D6DC79D682FEAA1A5F68BEB685132B1F549881 (void);
extern void GPUResidentBatcher_PostCullBeginCameraRendering_m9C006EAC8C65FBC1B7DD3F9F4123E071EC1D2F44 (void);
extern void GPUResidentBatcher_OnSetupAmbientProbe_mDCAD4C9AE21158471F846321EA2A58DBDA1914A6 (void);
extern void GPUResidentBatcher_UpdateRendererInstancesAndBatches_m7CC59149342BCCB17DE20FCD2BF0294D613B48B9 (void);
extern void GPUResidentBatcher_UpdateRendererBatches_m7FDCEBC6D9743BA42ED99E545EBBF438702B56DC (void);
extern void GPUResidentBatcher_OnFinishedCulling_m3894D01381A5290564E27DC8315623AD4B21975D (void);
extern void GPUResidentBatcher_ProcessTrees_mA20D5412045E8AE5E485DE8F50B350F0A88538B7 (void);
extern void GPUResidentBatcher_UpdateSpeedTreeWindAndUploadWindParamsToGPU_m1E26807F0F67557341970AC983776178A0E90490 (void);
extern void GPUResidentDrawer_get_instance_m142CE6BEC88AA7FA34052B0138128C3B944FEBDD (void);
extern void GPUResidentDrawer_IsInstanceOcclusionCullingEnabled_m03F098AAAA5FCB8140B53C641EB2B0381669BC8E (void);
extern void GPUResidentDrawer_PostCullBeginCameraRendering_m3EB60CDFBF342ABD0B11B30439BB01B7CD6F1F77 (void);
extern void GPUResidentDrawer_OnSetupAmbientProbe_mF67DC77B41AD752F71A25EA4221AA3180AA236CC (void);
extern void GPUResidentDrawer_InstanceOcclusionTest_m0DD4F0A4685967C617984FBCE5A0B99A35790AFE (void);
extern void GPUResidentDrawer_UpdateInstanceOccluders_mD2F1BAB128CEB6B6A731FEA876A1E08A31C98B30 (void);
extern void GPUResidentDrawer_ReinitializeIfNeeded_mE8A70A9A6B9C8D4A341552E05D95E4D74B7D68D5 (void);
extern void GPUResidentDrawer_RenderDebugOcclusionTestOverlay_m17664202C62084572F6037B5F1C61E2FE8C0BFD0 (void);
extern void GPUResidentDrawer_RenderDebugOccluderOverlay_mB7819CD0C90F839351CE854B2DD297D14F5F830B (void);
extern void GPUResidentDrawer_GetDebugStats_m857EE673158C860D3471D0CC6203B60D0BC98B4D (void);
extern void GPUResidentDrawer_InsertIntoPlayerLoop_mBDEE8B11EE73F12439561D73E4A2A3C8D6861007 (void);
extern void GPUResidentDrawer_RemoveFromPlayerLoop_m18F5D085D7C67EEA7EFDB5ABC52AB8C343CA5CAF (void);
extern void GPUResidentDrawer_IsEnabled_m03CFAB6E2CE8D71361F5223C940F4C0A785A1116 (void);
extern void GPUResidentDrawer_GetGlobalSettingsFromRPAsset_m4710F7D4D983AEB1ADD00A40E7E4068C330C9A41 (void);
extern void GPUResidentDrawer_IsForcedOnViaCommandLine_mC2F9713BA2691A02C23E22E59DDC30E41289539F (void);
extern void GPUResidentDrawer_IsOcclusionForcedOnViaCommandLine_mA0CD2D200E26C820386D92E58645EF2FF0B02FDA (void);
extern void GPUResidentDrawer_get_MaintainContext_mC393C718E2CF175293B7B1E86C7C70A5AC6D046C (void);
extern void GPUResidentDrawer_set_MaintainContext_m4A9032B2B6255EDC8EE357CA1D9195F8D8CD9885 (void);
extern void GPUResidentDrawer_get_ForceOcclusion_m6C97AA5F01C6062E108460F76FB92745C07CB4CC (void);
extern void GPUResidentDrawer_set_ForceOcclusion_m0DCC2179BB8525CA02EB0E70D6C7AE21D03228D6 (void);
extern void GPUResidentDrawer_Reinitialize_m542E6537EC9C14A35291824BA6798D5D0D747190 (void);
extern void GPUResidentDrawer_CleanUp_mF773237C2F3AEF0251249FFD56C02F7A650EE9C2 (void);
extern void GPUResidentDrawer_Recreate_m09E096E3492D77EE4A3D0D070FA53D2017AD6874 (void);
extern void GPUResidentDrawer_get_batcher_m03715B9C280D664F90B0B1F592D9C3ADD212F9F3 (void);
extern void GPUResidentDrawer_get_settings_m3F0472441E9F1191B0E0FC43B6D8BBF004EAF3C6 (void);
extern void GPUResidentDrawer__ctor_m3B65B01D5C54231BF2D7C4C65B4FA11DDA8CCA1A (void);
extern void GPUResidentDrawer_Dispose_mD5709371AD0309C33F25511B22C7C1DCD6AC234D (void);
extern void GPUResidentDrawer_OnSceneLoaded_m4C2686BC1182C9327DBA362D670CB30601292F5A (void);
extern void GPUResidentDrawer_PostPostLateUpdateStatic_m2322043F8B8734792788BF29370233B9BFFBFF7F (void);
extern void GPUResidentDrawer_OnBeginContextRendering_m383B0726811F68E670753BDD5F4EE772DE4593C0 (void);
extern void GPUResidentDrawer_OnEndContextRendering_m4E432047D9A70FD9FE8718FB195A9477A657857A (void);
extern void GPUResidentDrawer_OnBeginCameraRendering_mDCB0CA5A9CB1F1BA25DB7214D3BD75975AA2B705 (void);
extern void GPUResidentDrawer_OnEndCameraRendering_m1F301D2884DCFB5ADD5CB533FEC803B898EDC690 (void);
extern void GPUResidentDrawer_PostPostLateUpdate_m94401477ABD5387DBAAD1D6A1CC39E59AE1E2EEB (void);
extern void GPUResidentDrawer_ProcessMaterials_m5F9A91DF336FD3CA1CFD81608475C39F85FD89D5 (void);
extern void GPUResidentDrawer_ProcessCameras_m9F6D7A6DB3E4162F0C6B2D0E8B712876B3D56640 (void);
extern void GPUResidentDrawer_ProcessMeshes_m254F0CB1EB50564FF5FCC3CDD00420784BB45C04 (void);
extern void GPUResidentDrawer_ProcessLODGroups_mFDBD9F9CD5F13FDE5AEC4817644BC1BEF6D71D7C (void);
extern void GPUResidentDrawer_ProcessRendererMaterialAndMeshChanges_mA6E500BC36021FA83526CD3495E5800A4084F6CC (void);
extern void GPUResidentDrawer_ProcessRenderers_m381BBD34CA5CDCABD580BD2464CBA98ABDBC9E87 (void);
extern void GPUResidentDrawer_TransformInstances_m20475CB2F2401DD9A54661E4EA63ACC2A5D72B49 (void);
extern void GPUResidentDrawer_FreeInstances_mDCFFEBAB0E14151660E84E08BEC9AF91F149F611 (void);
extern void GPUResidentDrawer_FreeRendererGroupInstances_m038FBB93EAF3C06AA447CBBC04E2F9CEC8675814 (void);
extern void GPUResidentDrawer_AppendNewInstance_mEB1EA724EECB7E96FB4A378582A650ABBD8E635E (void);
extern void GPUResidentDrawer_ScheduleQueryRendererGroupInstancesJob_mCC2105095C5D0AB94F74B0DF5033C72BF8F64E21 (void);
extern void GPUResidentDrawer_ScheduleQueryRendererGroupInstancesJob_m059C106F6838578EB820B0854214AD8E52414C43 (void);
extern void GPUResidentDrawer_ScheduleQueryRendererGroupInstancesJob_mC87594A6FDDAC454AA8ED09E3F975236FDE87F01 (void);
extern void GPUResidentDrawer_ScheduleQueryMeshInstancesJob_m63420C906AFFC9D2431B6FD4B71BEBC19F6B902C (void);
extern void GPUResidentDrawer_ClassifyMaterials_m07623E4BDE7E899A29EB6E9A9DA2B9FA78ACC722 (void);
extern void GPUResidentDrawer_FindUnsupportedRenderers_m1C0CB02546C7733938983343B8668CAC84E60772 (void);
extern void GPUResidentDrawer_GetMaterialsWithChangedPackedMaterial_m112BC45C2CEA7D3B26EDE1B217430AA49E5EE0E8 (void);
extern void GPUResidentDrawer_FindRenderersFromMaterialsOrMeshes_m8D69EF84FB8C5830ACB7E1261331459DF4DEE8F6 (void);
extern void GPUResidentDrawer_IsProjectSupported_m8070103F51F3975E8D573377D445553710EBA457 (void);
extern void GPUResidentDrawer_IsProjectSupported_m4F5CDE5F81A0BE7CF42E37F1545E85A8C5A07DE3 (void);
extern void GPUResidentDrawer_IsGPUResidentDrawerSupportedBySRP_m24CCB3D5623CD94D8DA06D20CE59806BC9D35922 (void);
extern void GPUResidentDrawer_LogMessage_m7FF3E65D1A87DF183F9D29254AE637D842B88D41 (void);
extern void FindRenderersFromMaterialOrMeshJob_Execute_m7B9713786ED58196A531F44ECABD1912DF45E59F (void);
extern void Strings__cctor_m40AEC7C35446DC97C6BA1EFB06EA5B4F5CAADAB4 (void);
extern void GPUResidentDrawerBurst_ClassifyMaterials_m9FEDC6820FD183791F7BD2B682CFFFBBF9DC9F40 (void);
extern void GPUResidentDrawerBurst_FindUnsupportedRenderers_m927B5E54923278315B6256EA8178A34AA566B4BD (void);
extern void GPUResidentDrawerBurst_GetMaterialsWithChangedPackedMaterial_mC2D4AB98974823074A7E8543ED7A3D5BF7DB26FA (void);
extern void GPUResidentDrawerBurst_ClassifyMaterialsU24BurstManaged_mB0D38D1C45E45E052FB7EDFF5FC730712F97C388 (void);
extern void GPUResidentDrawerBurst_FindUnsupportedRenderersU24BurstManaged_m342114EE5DA2CE7AFEEE16D6E6A0D5D4D0E8A070 (void);
extern void GPUResidentDrawerBurst_GetMaterialsWithChangedPackedMaterialU24BurstManaged_mF321C2281AE3F7783E5F716EF61481A4506456A6 (void);
extern void ClassifyMaterials_000000DFU24PostfixBurstDelegate__ctor_m8B9A48425138F06CB91FCFB9847C7EDADE8D6C8A (void);
extern void ClassifyMaterials_000000DFU24PostfixBurstDelegate_Invoke_m504925CD32DA1CECE183ACB073AA5553C9BE1335 (void);
extern void ClassifyMaterials_000000DFU24PostfixBurstDelegate_BeginInvoke_mAC098DDFC973A085C59587D110D134BAA7473483 (void);
extern void ClassifyMaterials_000000DFU24PostfixBurstDelegate_EndInvoke_m2BED6EE11407587C5183C5A1D9B8FAE535CFD783 (void);
extern void ClassifyMaterials_000000DFU24BurstDirectCall_GetFunctionPointerDiscard_m663B584727686396EF2384B509802C8A1BA5BA75 (void);
extern void ClassifyMaterials_000000DFU24BurstDirectCall_GetFunctionPointer_mCD9F3B28E3FDA82041695F6A56B92F9147DB64C7 (void);
extern void ClassifyMaterials_000000DFU24BurstDirectCall_Invoke_m2D913B510E274F214E285E47F08F53EED9AF2ED5 (void);
extern void FindUnsupportedRenderers_000000E0U24PostfixBurstDelegate__ctor_m3F0E6807A0453A2A7E093071DF88FAF68F7F9769 (void);
extern void FindUnsupportedRenderers_000000E0U24PostfixBurstDelegate_Invoke_m2D4B8679CE724602FCAEA81B61CC602063D4F8BA (void);
extern void FindUnsupportedRenderers_000000E0U24PostfixBurstDelegate_BeginInvoke_mAAA32C4ACDF77CD3EC117A715B369E60E18AF7FC (void);
extern void FindUnsupportedRenderers_000000E0U24PostfixBurstDelegate_EndInvoke_m5C0F442E260CBC350130C233C27C10284D3B9658 (void);
extern void FindUnsupportedRenderers_000000E0U24BurstDirectCall_GetFunctionPointerDiscard_m020B1C641A13BE1485101DF5EEAAF71F571DD613 (void);
extern void FindUnsupportedRenderers_000000E0U24BurstDirectCall_GetFunctionPointer_m3A9AFB78D0FA58D6EA215476C00FC86EFF91E11E (void);
extern void FindUnsupportedRenderers_000000E0U24BurstDirectCall_Invoke_m1AF7B065A13D19EEDA1D73DC030A23EFA4E2DE3E (void);
extern void GetMaterialsWithChangedPackedMaterial_000000E1U24PostfixBurstDelegate__ctor_m0BEB2507613A8C323F0511A47FF8DE9A23ADBEB2 (void);
extern void GetMaterialsWithChangedPackedMaterial_000000E1U24PostfixBurstDelegate_Invoke_mBD1AEBBE568EAA4C06BBDB38854175C80EF86E89 (void);
extern void GetMaterialsWithChangedPackedMaterial_000000E1U24PostfixBurstDelegate_BeginInvoke_m777BD7D6F9CFD660C0B451792CFA30D1ECF4848B (void);
extern void GetMaterialsWithChangedPackedMaterial_000000E1U24PostfixBurstDelegate_EndInvoke_mC41CB4CA8CCC693E7416D5C4BAA8AEEB20E39B05 (void);
extern void GetMaterialsWithChangedPackedMaterial_000000E1U24BurstDirectCall_GetFunctionPointerDiscard_mAD7975413E8F82BDAFA4DBA525CBC94380434275 (void);
extern void GetMaterialsWithChangedPackedMaterial_000000E1U24BurstDirectCall_GetFunctionPointer_mE8783CB36E722760B676A4C5EAADC88C985C08D9 (void);
extern void GetMaterialsWithChangedPackedMaterial_000000E1U24BurstDirectCall_Invoke_m0BED1FBE42E636941F4D317E8BD58807B91AAEDA (void);
extern void DebugRendererBatcherStats__ctor_mAE82DDFAB36DFF39F4B28D1D43B5896042B250B0 (void);
extern void DebugRendererBatcherStats_Dispose_m76B62E89A85CAA6D7108200B3C4BDAF9DB4832E7 (void);
extern void GPUResidentDrawerResources_UnityEngine_Rendering_IRenderPipelineGraphicsSettings_get_version_m136CCE7FDEBC9741FDCAB827F74D51A7B7DF6E07 (void);
extern void GPUResidentDrawerResources_get_instanceDataBufferCopyKernels_m4821367D99C54C654F6FC0F677D53038EACAEB40 (void);
extern void GPUResidentDrawerResources_set_instanceDataBufferCopyKernels_m126688C4BE7907A678DE23AF2E3332DD8A34827A (void);
extern void GPUResidentDrawerResources_get_instanceDataBufferUploadKernels_mA900FCDAA87450DEBC3C134E015FA14685ADA9EA (void);
extern void GPUResidentDrawerResources_set_instanceDataBufferUploadKernels_m54B6A238CECBBA7093D5D1FEDA9B2C8E56332678 (void);
extern void GPUResidentDrawerResources_get_transformUpdaterKernels_mCA7A4849EFDC13E448339A6AEC42065FDAB5C63C (void);
extern void GPUResidentDrawerResources_set_transformUpdaterKernels_mC87744E70D4108F953B0C637C483AA5ABE685709 (void);
extern void GPUResidentDrawerResources_get_windDataUpdaterKernels_m2C5FADD001A37D11A324FE865E925CD9A5501315 (void);
extern void GPUResidentDrawerResources_set_windDataUpdaterKernels_m7828D337715FCE91AFEC15C34DAC0F61753A725F (void);
extern void GPUResidentDrawerResources_get_occluderDepthPyramidKernels_m7006886C18CF45076331E4B6114CA37A3CE69532 (void);
extern void GPUResidentDrawerResources_set_occluderDepthPyramidKernels_m9103835307DA4F40F0439903A0E7DF5C8712B704 (void);
extern void GPUResidentDrawerResources_get_instanceOcclusionCullingKernels_m0096BB5665B29E5552385CC7C4990DDF95C6EDB1 (void);
extern void GPUResidentDrawerResources_set_instanceOcclusionCullingKernels_m06138322ED0CC1A22774E0D41C74B4CE691BFFEE (void);
extern void GPUResidentDrawerResources_get_occlusionCullingDebugKernels_m8B7B3517326F40890A0935A0DC1DD55C8B14F164 (void);
extern void GPUResidentDrawerResources_set_occlusionCullingDebugKernels_m94C497F616A9D90161CFEA216A07029CC55D0D27 (void);
extern void GPUResidentDrawerResources_get_debugOcclusionTestPS_m0A869F58FF84A5B43E925DBE72A100212D672BF2 (void);
extern void GPUResidentDrawerResources_set_debugOcclusionTestPS_m2B0F9F3EC01C30490B37C40D1BACDB2919E88ACD (void);
extern void GPUResidentDrawerResources_get_debugOccluderPS_m476766B8038CC61693711BEAB81BD5B65C95D9DD (void);
extern void GPUResidentDrawerResources_set_debugOccluderPS_m4A969CD0B817583E1A089F6C636A28F8F9F32835 (void);
extern void GPUResidentDrawerResources__ctor_m9A9FBC773137C24968523F68F1ED3D55922BAF1C (void);
extern void OcclusionTestMethods_GetBatchLayerMask_m1CC038C215B2531DDD0A4C8AF03E2DC518A43D09 (void);
extern void OcclusionCullingSettings__ctor_mE814849AC60B1DC1AC17E02F1AF2128DF38FE95A (void);
extern void OccluderSubviewUpdate__ctor_m482C60BE94ECE0B1F15E930936345D9F60E399A8 (void);
extern void OccluderParameters__ctor_mBBA1CD9856BD109C9F1D18715B28E2DA95CE83D9 (void);
extern void IGPUResidentRenderPipeline_ReinitializeGPUResidentDrawer_m6D9AB828C92C7E97A8B441028C3056A905005E3F (void);
extern void IGPUResidentRenderPipeline_IsGPUResidentDrawerSupportedBySRP_mFED552B69E782E4125B03C0EC1B2007FEB023553 (void);
extern void IGPUResidentRenderPipeline_IsGPUResidentDrawerSupportedBySRP_mAC46B52099ED2E34F12F8B7E802DC67E0113A0A9 (void);
extern void IGPUResidentRenderPipeline_IsGPUResidentDrawerSupportedByProjectConfiguration_m5CE31EEE661C5F8B23636A4F3452CEA8A7AC0B66 (void);
extern void IGPUResidentRenderPipeline_IsGPUResidentDrawerEnabled_m4B17CE9EBDCEADAB2C6378F8DACE655817DD1757 (void);
extern void RangeKey_Equals_m05E612C122D91758588CEB7529751045E6F09493 (void);
extern void RangeKey_GetHashCode_mC38845B6A1CC657D6F6B1149E7448AA6A3EF3257 (void);
extern void DrawKey_Equals_mBA2838871085C8944B81322DC6E851F4273E8E91 (void);
extern void DrawKey_GetHashCode_m6E3C5F3D42D02D8035AA7E7B5501FDBC1551F4E5 (void);
extern void BinningConfig_get_visibilityConfigCount_m0B698DBD65F8147B56C14CA7CFB741EAB701B3B9 (void);
extern void AnimateCrossFadeJob_Execute_mA3C0021BE25AE2F67FD93948B6115ECE990DAFE9 (void);
extern void CullingJob_PackFloatToUint8_m7E24A8A8334FF990F67C75BCE8BB991037645230 (void);
extern void CullingJob_CalculateLODVisibility_mF87528623E251C0B920199D5DFB1021842A5F774 (void);
extern void CullingJob_CalculateVisibilityMask_m285DA7E28A8582C95BAE2C6794C34FC3F8F195ED (void);
extern void CullingJob_ComputeMeshLODLevel_m0CF5B90B965B46AA61BD4B00EF15C1528BF03A0D (void);
extern void CullingJob_ComputeMeshLODCrossfade_mF911187408B6601080C28F1227FAF60DE832B750 (void);
extern void CullingJob_EnforcePreviousFrameMeshLOD_m20C1DF1641349AC2B1B455E7A49D0DE6BC3497B2 (void);
extern void CullingJob_Execute_m172CC218FA8F7198BD69C1C87294FD90D94DB884 (void);
extern void AllocateBinsPerBatch_IsInstanceFlipped_m5ED8B8D355142D10DB3983D15A256B5DBD4D6ABA (void);
extern void AllocateBinsPerBatch_IsMeshLodVisible_m9D1674B4CE766557E8D2234D56DF2BACC2EDB576 (void);
extern void AllocateBinsPerBatch_Execute_m8244D8F2860ED03B023A6D348D9471F2AF76E0F1 (void);
extern void PrefixSumDrawsAndInstances_Execute_m8EADFE8897EAE00866B67F8AE64CA774EE33B5F9 (void);
extern void DrawCommandOutputPerBatch_EncodeGPUInstanceIndexAndCrossFade_m6EFB0D6BB29BF2F65B66913BD2F82E4364ED8830 (void);
extern void DrawCommandOutputPerBatch_IsInstanceFlipped_m1AE2BD53A7D17783F262A94539417F423FD4CF79 (void);
extern void DrawCommandOutputPerBatch_IsMeshLodVisible_mF345AFA69786A2EB8E65B25BFDB933A6321F1054 (void);
extern void DrawCommandOutputPerBatch_Execute_mF995D35E2E640A3172F4BC62E37BF103CFBBBBA7 (void);
extern void CompactVisibilityMasksJob_Execute_m7D5B39DEFEC4944D35111B4B7D22267147B4AB1E (void);
extern void InstanceCullerSplitDebugArray_get_Counters_mBDB8C933AEEB9F934132442CA20FC54A9E9A01AB (void);
extern void InstanceCullerSplitDebugArray_Init_m0E461744D899802E8123D32B79AD2EEC7FF1ED66 (void);
extern void InstanceCullerSplitDebugArray_Dispose_m0FA405A9BA291E560088D998A71EE3FEF95100C1 (void);
extern void InstanceCullerSplitDebugArray_TryAddSplits_m8F0CAC17CB3DA747A187760D3961255C1E95BDF5 (void);
extern void InstanceCullerSplitDebugArray_AddSync_m05D0080857890E3C8E46730B1D4DC17E4FB71D90 (void);
extern void InstanceCullerSplitDebugArray_MoveToDebugStatsAndClear_mAA78838CCD17348C01F115022EF530C3F5B98AD4 (void);
extern void InstanceOcclusionEventDebugArray_get_CounterBuffer_mF25E7B744518F980443AFCF79E48EA3CCF852D04 (void);
extern void InstanceOcclusionEventDebugArray_Init_mF8AEF95440DA22ACC2DB5717CEBD9C8B8B3797D5 (void);
extern void InstanceOcclusionEventDebugArray_Dispose_m2D57744F19C60D3E9B6EFDF81BB08498853E37C1 (void);
extern void InstanceOcclusionEventDebugArray_TryAdd_m2FD080E0DDCA404C8C362A5933EC852A9CD5F109 (void);
extern void InstanceOcclusionEventDebugArray_MoveToDebugStatsAndClear_mFEF02F07D15F2F2942FABFD0C7C577FAB1859977 (void);
extern void Info_HasVersion_m9E077F93A4F89E728DF4FA88764ABF91C732D960 (void);
extern void InstanceCuller_Init_mDBBD40C7316EBE16746FACA43170B7E1001EC67F (void);
extern void InstanceCuller_AnimateCrossFades_m5BE8DFEE24C03638DF57E12C23D4C2C80DE8B510 (void);
extern void InstanceCuller_CreateFrustumCullingJob_m503045810FE1E550B3F436FD62750A505A4EAE3C (void);
extern void InstanceCuller_ComputeWorstCaseDrawCommandCount_mF523E170BDA54D6E88AD73742065FD88C60937DD (void);
extern void InstanceCuller_CreateCullJobTree_mF3C76EB7E9663B657C8C7F9ACA2E0995CF5D8A6B (void);
extern void InstanceCuller_CreateCompactedVisibilityMaskJob_m3A2F38959953B889E5E7456DE426ABA7134DFB9B (void);
extern void InstanceCuller_InstanceOccludersUpdated_mCA7D8171B44CF76E6A48314D7F97F2B698DCCE05 (void);
extern void InstanceCuller_DisposeCompactVisibilityMasks_mE05A5A23578160F5FDE1F8F2E59B7B8E2819BC76 (void);
extern void InstanceCuller_DisposeSceneViewHiddenBits_mB8548C25259A3AE546942634429EF581779992BD (void);
extern void InstanceCuller_GetCompactedVisibilityMasks_mED15105E7567F79042B542B56B4DE2ABEDE14C26 (void);
extern void InstanceCuller_InstanceOcclusionTest_m8E12352E27A10F62B9C737C3C7D0394DBC416F15 (void);
extern void InstanceCuller_EnsureValidOcclusionTestResults_m22DA66230772FAE8FA78B7E2B3839AB414BE8A22 (void);
extern void InstanceCuller_AddOcclusionCullingDispatch_mDA6F922B0734577B8F921E9AE4C02CEE4F8A32A5 (void);
extern void InstanceCuller_FlushDebugCounters_m1036A68627B296B0CE88254424A25EEA430BCCE6 (void);
extern void InstanceCuller_OnBeginSceneViewCameraRendering_m9F271E3A4A411E077FC767C85C1813F41C9ADA3D (void);
extern void InstanceCuller_OnEndSceneViewCameraRendering_m01D33D11A4853EB0397A2996F10795662828C2F8 (void);
extern void InstanceCuller_UpdateFrame_m9B437CF5FE15217CD1652135B256DFECE114418C (void);
extern void InstanceCuller_OnBeginCameraRendering_mB7CDBC0AD43EEC324B9584B96C25DF3B911EC907 (void);
extern void InstanceCuller_OnEndCameraRendering_mF0DF0261BF1978BAFAC10F5F9DB533FC86B8089B (void);
extern void InstanceCuller_Dispose_m7C8B413AE9FD5A5C4EAD81A22D2D4C1F2E6C368F (void);
extern void ShaderIDs__cctor_mBB02927959BF46D5749EF53F56D62D1C6736FEF2 (void);
extern void InstanceOcclusionTestPassData__ctor_m73F58387AFEBAB53120CC7AEF289EC50A82AFC8C (void);
extern void U3CU3Ec__cctor_m5495172327D2C037C7A396EB43420AD62D33BEBE (void);
extern void U3CU3Ec__ctor_mBE453CFF0731A8063B406EBCFB4E99157CE39C80 (void);
extern void U3CU3Ec_U3CInstanceOcclusionTestU3Eb__28_0_m018A8024AEBFAC4FDD9DEABAB3FE56C3E2516CCC (void);
extern void InstanceCullerBurst_SetupCullingJobInput_m48DA62BB1FCE439FD65DE65952C97249ECB28C56 (void);
extern void InstanceCullerBurst_SetupCullingJobInputU24BurstManaged_m029BAC91A512650E568924D2E203B6D7169B0FD6 (void);
extern void SetupCullingJobInput_0000013FU24PostfixBurstDelegate__ctor_m0C793C802CF5D1A0F93CB9ED0CEDDDC88F4E1056 (void);
extern void SetupCullingJobInput_0000013FU24PostfixBurstDelegate_Invoke_mF1051A9A19DE6F6EC13C0237CF169949C832D29C (void);
extern void SetupCullingJobInput_0000013FU24PostfixBurstDelegate_BeginInvoke_mE119DD748F0E770FAAD051D34717CC98406D1758 (void);
extern void SetupCullingJobInput_0000013FU24PostfixBurstDelegate_EndInvoke_m4673B6E8DF15C4DC7325F995018BFEEDBBCB13B5 (void);
extern void SetupCullingJobInput_0000013FU24BurstDirectCall_GetFunctionPointerDiscard_m456373FD45FD288FFC5D9A3899AB3A11424CD41D (void);
extern void SetupCullingJobInput_0000013FU24BurstDirectCall_GetFunctionPointer_mCE2F7319E705DCD79CAF8F501320858FCBFDB0DE (void);
extern void SetupCullingJobInput_0000013FU24BurstDirectCall_Invoke_m6F7F3A755EBAC8F64AF6083005924EB0A7703E06 (void);
extern void OnCullingCompleteCallback__ctor_m77440340DEF8EC177F2367F9CDFB4C7039B109CD (void);
extern void OnCullingCompleteCallback_Invoke_mC230E2F011722DC958EFACC67609C75FFB0A54C8 (void);
extern void OnCullingCompleteCallback_BeginInvoke_mAA8DD729BE78EF7C06DC33D3714B306D8793E492 (void);
extern void OnCullingCompleteCallback_EndInvoke_m2ABE1C345A8D041A537AD05BE2B8632D872865A0 (void);
extern void InstanceCullingBatcherDesc_NewDefault_mC543DB9EBF6418504763D8C66FCD457AC5A8B9AF (void);
extern void PrefixSumDrawInstancesJob_Execute_m6384DA1CB6A1E053F358640B9E2FE7F378677771 (void);
extern void BuildDrawListsJob_IncrementCounter_m2198A8B6F4713D070C44BF162EEAC564C15A120F (void);
extern void BuildDrawListsJob_Execute_m731DC4DAB67702B447641FF630CD1C60A596636E (void);
extern void FindDrawInstancesJob_Execute_m011CC04D8F9DA0E7E3F04783AA60964898BEDC1D (void);
extern void FindMaterialDrawInstancesJob_Execute_m0A8C723C617595319217DB5AFD151AC28F7759A0 (void);
extern void FindNonRegisteredMeshesJob_Execute_mC7CF993F6E8C86B01D3FE339DB98E19D5A6D25BD (void);
extern void FindNonRegisteredMaterialsJob_Execute_m45EED4BFBE595919A846376A102DE6DBE15BD404 (void);
extern void RegisterNewMeshesJob_Execute_mE945F1F57AB8D7AD4E3450E937E7955501E12427 (void);
extern void RegisterNewMaterialsJob_Execute_m2C03AA455A70CA6262A10E28A88752C1A9BBB45E (void);
extern void UpdatePackedMaterialDataCacheJob_ProcessMaterial_mCFF81645A1DB74D08DAAD17D679FA00A306E57E3 (void);
extern void UpdatePackedMaterialDataCacheJob_Execute_m117BE8C4EEAC8E86F3445464E84ECB6A954A3EBB (void);
extern void CPUDrawInstanceData_get_drawInstances_m5B182A75D8968C69C7EFF2CE773D0029E7412D68 (void);
extern void CPUDrawInstanceData_get_batchHash_m407E1D37D4A5D8BDE76DE45CF33C53643DA47C12 (void);
extern void CPUDrawInstanceData_get_drawBatches_m6E62CEC9E106C2CB84B185517A04105E77D875F6 (void);
extern void CPUDrawInstanceData_get_rangeHash_mF69F0BF1AFACDC62B7466A99F24DBE460E4BB9B6 (void);
extern void CPUDrawInstanceData_get_drawRanges_m980D02096E99CCD5299D5B06AAEC78E986A92754 (void);
extern void CPUDrawInstanceData_get_drawBatchIndices_m414A7D8E605519E4B6F5F3B1020BE3B21F991E29 (void);
extern void CPUDrawInstanceData_get_drawInstanceIndices_m22402BAB913DE193060C175FF253FC5C3D8D0D56 (void);
extern void CPUDrawInstanceData_get_valid_m224B4D6D342C4EAC30B8F8A5A34837827DEDCE3E (void);
extern void CPUDrawInstanceData_Initialize_m1D889D823B6B72F24F03385CBC492D3B18B510E4 (void);
extern void CPUDrawInstanceData_Dispose_m0F6E3A9F7C46E04F0C1A5073F3C3039BD1E29D3B (void);
extern void CPUDrawInstanceData_RebuildDrawListsIfNeeded_m8CE9E2B0870BADE0F90B83C54ED6A206B39F5248 (void);
extern void CPUDrawInstanceData_DestroyDrawInstanceIndices_m6B1D8988D3CE889D903F736E9814EAFFCCC35FC3 (void);
extern void CPUDrawInstanceData_DestroyDrawInstances_m6410B734F4067FA0A56142576467F65ACAF30343 (void);
extern void CPUDrawInstanceData_DestroyMaterialDrawInstances_mC3646BA15537CA5C257F05E06AC409B4ACA4E167 (void);
extern void CPUDrawInstanceData_NeedsRebuild_m0F632EDB133A8FFB533DB9A5BBCC199DEB9AC11C (void);
extern void CPUDrawInstanceData__ctor_m21E4C7096825C181C31F9F5A8600D2CF96F5B239 (void);
extern void InstanceCullingBatcher_get_batchMaterialHash_mF1798E2B3C1C885996C171F3F4EDFB7DFDC53151 (void);
extern void InstanceCullingBatcher_get_packedMaterialHash_m12837A329EFD8A76B3E25C2140F516E2847570EC (void);
extern void InstanceCullingBatcher__ctor_m98D5EFDC5F3AF7FF61DEE777748DBD66758A239B (void);
extern void InstanceCullingBatcher_get_culler_mBFCD2ACBB0F3D4A650233F186A5EB98D47A714D4 (void);
extern void InstanceCullingBatcher_Dispose_m6A9FED11F52B4FD30BAF1ECE6676B853B9BCCA42 (void);
extern void InstanceCullingBatcher_GetBatchID_m149BEE4723E1E52B23E9B63AE33960431ADAE18C (void);
extern void InstanceCullingBatcher_UpdateInstanceDataBufferLayoutVersion_mFFB18677B049FC0438D8169800EE48CE19594268 (void);
extern void InstanceCullingBatcher_GetDrawInstanceData_mED58B161705DB4D131F341DB74C65163A6920ABA (void);
extern void InstanceCullingBatcher_OnPerformCulling_mC82A6CB4199689882BEFB834A6E8CA9DFEFB02DD (void);
extern void InstanceCullingBatcher_OnFinishedCulling_mA2C92EE1760B83D8F49B13F81E6736E488ACBF70 (void);
extern void InstanceCullingBatcher_DestroyDrawInstances_m53512A8BD8DF31ADE89587FD55D5B4226F9F6A44 (void);
extern void InstanceCullingBatcher_DestroyMaterials_mF017E0AF2451934C0B8D3D9F7457FCDB3908F087 (void);
extern void InstanceCullingBatcher_DestroyMeshes_m7CB28EB447BDD8A7EF2780BB3BA51A16239B91BD (void);
extern void InstanceCullingBatcher_PostCullBeginCameraRendering_m8C7F421728438AB4A12BF864206727A08A2C30D6 (void);
extern void InstanceCullingBatcher_RegisterBatchMeshes_m59D3A05E2598309513C7DD095CD30F64C33C6214 (void);
extern void InstanceCullingBatcher_RegisterBatchMaterials_mB0F8BEB81FC2A03451C9A79341D0BA134B54D4B1 (void);
extern void InstanceCullingBatcher_SchedulePackedMaterialCacheUpdate_m22B6C778DE0258BCEA6BFBB9F9278637010B5A0C (void);
extern void InstanceCullingBatcher_BuildBatch_mCCF451A6EF0FDA419A13DA10D82E854480B2CFFD (void);
extern void InstanceCullingBatcher_InstanceOccludersUpdated_mB9D7CECE86473174B52A1D76E73DC546738C9A44 (void);
extern void InstanceCullingBatcher_UpdateFrame_mD1B5D19FB7AB428A0E766E54E1E58821EF647457 (void);
extern void InstanceCullingBatcher_GetCompactedVisibilityMasks_m56336BD173368549DEDC4D743382E3311D29B144 (void);
extern void InstanceCullingBatcher_OnEndContextRendering_mB4D41FEA107B3D5ECF9AF02F3937726021141A26 (void);
extern void InstanceCullingBatcher_OnBeginCameraRendering_mE389E1056E80885B9816E9231F9C3C2A42B72221 (void);
extern void InstanceCullingBatcher_OnEndCameraRendering_m189059E45FB5732F03BAFABF3421CDD68D81DEB1 (void);
extern void InstanceCullingBatcherBurst_RemoveDrawRange_m007B09A18C3EF35F9757051CCCB5D00D350C7F9F (void);
extern void InstanceCullingBatcherBurst_RemoveDrawBatch_mFD88A38A3AEF08C7E82617FB3A9CC9181F0B5104 (void);
extern void InstanceCullingBatcherBurst_RemoveDrawInstanceIndices_mCD31103C406E421E8E420A624BAE66CE0B58F140 (void);
extern void InstanceCullingBatcherBurst_EditDrawRange_mF55B010296BE2E67F660E55BAC21DF7C8F0E36CB (void);
extern void InstanceCullingBatcherBurst_EditDrawBatch_m576D7B695312D660921D4218FA15CA5F9B47E1A4 (void);
extern void InstanceCullingBatcherBurst_ProcessRenderer_mA0CA512208E88C6E3D1A5BDDCF673BC85722F551 (void);
extern void InstanceCullingBatcherBurst_CreateDrawBatches_m607108DE9C1C56A9A8AE1C377CA5C64D155497E3 (void);
extern void InstanceCullingBatcherBurst_RemoveDrawInstanceIndicesU24BurstManaged_mE9A62851F7A76105F82304233E4068418CD1C452 (void);
extern void InstanceCullingBatcherBurst_CreateDrawBatchesU24BurstManaged_m8D901A14C6C7B7D5D92D4C41CF483DB1F62F8F39 (void);
extern void RemoveDrawInstanceIndices_0000017AU24PostfixBurstDelegate__ctor_m51FF714E57C39CA7FF5F9FD6A2641F7684D7EDFA (void);
extern void RemoveDrawInstanceIndices_0000017AU24PostfixBurstDelegate_Invoke_mC0F3249CBD739427119D6F927B679FCD647605AB (void);
extern void RemoveDrawInstanceIndices_0000017AU24PostfixBurstDelegate_BeginInvoke_m05BC7477D57B6EBD6203D5978652F1C8E6B7EC44 (void);
extern void RemoveDrawInstanceIndices_0000017AU24PostfixBurstDelegate_EndInvoke_mD4FB0EA1AA272D4D179438FEF3C7475102EC78F7 (void);
extern void RemoveDrawInstanceIndices_0000017AU24BurstDirectCall_GetFunctionPointerDiscard_m75B8D0C8E75C4C423780A559EAD2D4313DB3478A (void);
extern void RemoveDrawInstanceIndices_0000017AU24BurstDirectCall_GetFunctionPointer_m8DA6114E555B987BE7E83843DE01C4176E1EF7D3 (void);
extern void RemoveDrawInstanceIndices_0000017AU24BurstDirectCall_Invoke_m677671DEC286D42996156D7D267B190E234B5199 (void);
extern void CreateDrawBatches_0000017EU24PostfixBurstDelegate__ctor_mDB3A117A6A1DC7EB41592212FC427342ACCC9274 (void);
extern void CreateDrawBatches_0000017EU24PostfixBurstDelegate_Invoke_mC0A8DE0E06BE7D08136C51D5656812B6F15C0A32 (void);
extern void CreateDrawBatches_0000017EU24PostfixBurstDelegate_BeginInvoke_mF8F1BEDAB745CD8EDA7F61799A9D6DED08BFDBF3 (void);
extern void CreateDrawBatches_0000017EU24PostfixBurstDelegate_EndInvoke_m44856AC167D8A5CA0A9FE12157F3E3C1FE9FDB36 (void);
extern void CreateDrawBatches_0000017EU24BurstDirectCall_GetFunctionPointerDiscard_m123958C31C51FDCB87F02CA26E5B555270424354 (void);
extern void CreateDrawBatches_0000017EU24BurstDirectCall_GetFunctionPointer_m7D9D9854D2D4E449B637DFE143058D89E0DC20BF (void);
extern void CreateDrawBatches_0000017EU24BurstDirectCall_Invoke_m1200DFE4C93B3E727F2FBD4278CF19A1E6CB2176 (void);
extern void GPUInstanceComponentDesc__ctor_m3D6BFA5A8BBF78939717DABD22CDEDE234780EE0 (void);
extern void GPUInstanceDataBuffer_NextVersion_m87213EAF8B57C72440D656BD00E246106CD9404F (void);
extern void GPUInstanceDataBuffer_get_valid_mBB8F7F2B22AA1450AD0944A8364F19025D0687F1 (void);
extern void GPUInstanceDataBuffer_CPUInstanceToGPUInstance_m1177E7984777339744077A74E281D84B17704592 (void);
extern void GPUInstanceDataBuffer_GetPropertyIndex_mF39E38B5B13B5BF4E45934C274E076B4401656DA (void);
extern void GPUInstanceDataBuffer_GetGpuAddress_m478AE68E3BE3FC5076A3D8C1D9F2CC20E11FD7EF (void);
extern void GPUInstanceDataBuffer_GetGpuAddress_mCDCEF5E738A3FE9E217D94ECA43A2AE5A6380225 (void);
extern void GPUInstanceDataBuffer_CPUInstanceToGPUInstance_m3C4863A5F6AC91EA128DE5FDD296B3159CCE218C (void);
extern void GPUInstanceDataBuffer_GPUInstanceToCPUInstance_m6861C120BA15E1AA9BB2116DDBF09CC4A68BC039 (void);
extern void GPUInstanceDataBuffer_CPUInstanceArrayToGPUInstanceArray_mE3CD8040A3236B9CA56E2CB69B90C68CB1BE42A3 (void);
extern void GPUInstanceDataBuffer_Dispose_m338824ADC36E89D59E8D1EC451F00A78337A4165 (void);
extern void GPUInstanceDataBuffer_AsReadOnly_m7E7EAB66B500E1CAA7AEB2C2F7CAEBE40CCE729F (void);
extern void GPUInstanceDataBuffer__ctor_m62C97070C67C69A70905B44F586178FEFB54C95E (void);
extern void ReadOnly__ctor_mF532002BB9D890FF1F0582B28EE81FFF81C4843D (void);
extern void ReadOnly_CPUInstanceToGPUInstance_m01CDC3239F33A9D1A861D1816930C4A7E71440E5 (void);
extern void ReadOnly_CPUInstanceArrayToGPUInstanceArray_m246175FBDE0C68B01CF4B8B0F73087368CA7ACF3 (void);
extern void ConvertCPUInstancesToGPUInstancesJob_Execute_m16B5AACD9BEEFFA2046C2E1999F8C4AA7FF2B1D9 (void);
extern void GPUInstanceDataBufferBuilder_CreateMetadataValue_m50A13411B4799C2C197B9C4EFD3EED2BB4C25036 (void);
extern void GPUInstanceDataBufferBuilder_AddComponent_m26713FB7D74F3F47052241EEDE3D95D1502C35E8 (void);
extern void GPUInstanceDataBufferBuilder_Build_mDEF5AC49115B5D3CD195C5802389B93AE1C25C8C (void);
extern void GPUInstanceDataBufferBuilder_Dispose_m1869839D0122EAE7FE7A7F9FE5356CCDE50D6636 (void);
extern void GPUInstanceDataBufferUploader__ctor_mAD57A016FC2E0940D4A469F418E161A1DCA8CC8C (void);
extern void GPUInstanceDataBufferUploader_GetUploadBufferPtr_m0CB3210A88F79E17329FDE29AD57C33C975D55DD (void);
extern void GPUInstanceDataBufferUploader_GetUIntPerInstance_mE171D8F73FE5CDE5D3F83C730F31877B8649A59C (void);
extern void GPUInstanceDataBufferUploader_GetParamUIntOffset_mF231C740B0554E7A82228DAEA5FE5F6BEBD978FD (void);
extern void GPUInstanceDataBufferUploader_AllocateUploadHandles_mAFB40A36A5FDAF5C560EB04B822012E57219A607 (void);
extern void GPUInstanceDataBufferUploader_SubmitToGpu_m352C6DB1B32BBB2D141C76ECCB19F8FF55940105 (void);
extern void GPUInstanceDataBufferUploader_SubmitToGpu_m6037322A91258909D757C712390F08395A36CF9C (void);
extern void GPUInstanceDataBufferUploader_Dispose_m0D8CBA64EB57BF6554F97E74B238F0A159B574B5 (void);
extern void UploadKernelIDs__cctor_m5DABBDCC0EDE576865CC927D7810EE469972169A (void);
extern void GPUResources_LoadShaders_m9ACB6FC1CAC3C07D223AB04073FEFEBCD55F5CDA (void);
extern void GPUResources_CreateResources_m07CBEC1195D765927E59B74DDFF7402B3608E22D (void);
extern void GPUResources_Dispose_m2D71F420E19EDC6E791D5FD0BDAA81501C6017E6 (void);
extern void WriteInstanceDataParameterJob_Execute_mFBD8B3E368892E290A864D47C75EC3958286BC25 (void);
extern void GPUInstanceDataBufferGrower__ctor_m2555453CA56595D3963837F7226E4F85D11379B1 (void);
extern void GPUInstanceDataBufferGrower_SubmitToGpu_mF6B94F8512B09B0E57404C840A3C22D621C8EE9A (void);
extern void GPUInstanceDataBufferGrower_Dispose_m24A1DE3F1FE59498294C9845783B541018F54BE3 (void);
extern void CopyInstancesKernelIDs__cctor_mE2A43876DE96902483CD0D2EFE3D31E698AA4715 (void);
extern void GPUResources_LoadShaders_m4C5A24A42FDCD3D04EBB7C2342E23D14748CBB89 (void);
extern void GPUResources_CreateResources_mD345D83E87BDDAF07F3A8628B9E1F1860BEC40C3 (void);
extern void GPUResources_Dispose_mBB023C03E82397C5CF141105338158EEDA6841AF (void);
extern void InstanceHandle_get_index_mD1303DDB5E62977480B92E3DCECC534B1465CB94 (void);
extern void InstanceHandle_set_index_mC56210BD1B26D7B86BCD9BF2CE5341188DC1A234 (void);
extern void InstanceHandle_get_instanceIndex_mD7BAC819F382904784AC46D1F6A631B4A21CA8B5 (void);
extern void InstanceHandle_get_type_mAC674106B77C6008F547FBAB2FE02F263D3FA40C (void);
extern void InstanceHandle_get_valid_m5629806E66255D75C3B1ED5826B0DA6D13B9E0CE (void);
extern void InstanceHandle_Create_mDF57F601CCEF1ED2DBFD880416FE0B5EB625DB2B (void);
extern void InstanceHandle_FromInt_m501BC299814E873C1040C63575F9391327992272 (void);
extern void InstanceHandle_Equals_mB7F950B845D19368217960B6FE7DBEA51B013C9E (void);
extern void InstanceHandle_CompareTo_mC8763D64CE799F220739F8D3880F0725ADA9A93D (void);
extern void InstanceHandle_GetHashCode_m256554043D1CEC35A1670DA2BDD39B453A4985D9 (void);
extern void InstanceHandle__cctor_m482B79BDF36DE1A1A2BDB9C9D97F597DE7ED7F77 (void);
extern void SharedInstanceHandle_get_index_mA8EF25F39F6B747A2984740DC8A6A7F7F3B31606 (void);
extern void SharedInstanceHandle_set_index_mBD1FDAC50BC4D743EF40A7195E2DF1E3B4BC1840 (void);
extern void SharedInstanceHandle_get_valid_m723EEF4B52F015C0A6EC4BB621966C396311C865 (void);
extern void SharedInstanceHandle_Equals_m8318CF0264558ADD64222B3A2593EACCED56BFE0 (void);
extern void SharedInstanceHandle_CompareTo_m31419B958041797369105E6105847547F39F98C4 (void);
extern void SharedInstanceHandle_GetHashCode_m5B97E179A78BD59969291F66E286E00873FC120D (void);
extern void SharedInstanceHandle__cctor_m1F42E7568683FEA4B78D0F18E1F5707420D6644E (void);
extern void GPUInstanceIndex_get_index_m0EDBD4FD5FC090990E2A24DCEBB5346B260C919D (void);
extern void GPUInstanceIndex_set_index_m24EF3293A5E4CA20F4186F53B459500CDAE40687 (void);
extern void GPUInstanceIndex_get_valid_m5DBD00DEF2B390C4DB2E0B41686B6A09A064D27D (void);
extern void GPUInstanceIndex_Equals_mEF7254FCD24AAD9A15FD3D1555306A5F1C908D23 (void);
extern void GPUInstanceIndex_CompareTo_m4CEFE1A49A0D625417B0123D3CA3AE385B785DCF (void);
extern void GPUInstanceIndex_GetHashCode_mD3CFF7872E06CD6F455F81B019F03672C213D970 (void);
extern void GPUInstanceIndex__cctor_mBE79D3EFAD002DBED18492AEDE53BD38FCA142E7 (void);
extern void InstanceAllocator_get_length_m762D1C624FF0B3D9F1DFED48EEA65465932C49DC (void);
extern void InstanceAllocator_set_length_m02BBC305E32676069A1D7855C5FF776FD7F24172 (void);
extern void InstanceAllocator_get_valid_m86EC99BF39D782407913E9BAC1DB2D7CBA1FD797 (void);
extern void InstanceAllocator_Initialize_m299908C0326706B4BF44DDEC7CDA3D1074347F01 (void);
extern void InstanceAllocator_Dispose_mB710FC50CF870DBD3B226AE4B28057364D67237A (void);
extern void InstanceAllocator_AllocateInstance_m846D1D4EA89D654F8AD4272579E3A7591FE155AB (void);
extern void InstanceAllocator_FreeInstance_m4017808CB3B95F6FC5079EFDCC7CA13217C42E3C (void);
extern void InstanceAllocator_GetNumAllocated_mC907C85B73DDEAFAE3606B4099F5C7E808DA8C18 (void);
extern void InstanceAllocators_Initialize_m1C63A61426D12441A3C4EABEC29B392FC93E90D9 (void);
extern void InstanceAllocators_Dispose_m0B2675D6FA6E95D9B9CC4617A94F408D9E80F5F7 (void);
extern void InstanceAllocators_GetInstanceAllocator_mF5B156CDAEDEEC2849590A544A5D99E691D0E37F (void);
extern void InstanceAllocators_GetInstanceHandlesLength_mBFEDAC598685E4B38B1E67DECA4E75B8E345ACE0 (void);
extern void InstanceAllocators_GetInstancesLength_m7F121F6138F0713B01D5379DCA36255DF631D1C3 (void);
extern void InstanceAllocators_AllocateInstance_m1BA734BD0D1B20F166DEBCCEE8AC5B2B0911D9A4 (void);
extern void InstanceAllocators_FreeInstance_m0EBB63A12A593C580C54770B70A6032C55237AF6 (void);
extern void InstanceAllocators_AllocateSharedInstance_mD61A96D871FD88F2826B307B6BBF976223E075CF (void);
extern void InstanceAllocators_FreeSharedInstance_m0B4E4FDCEA191C4829065D705C241F8667B658D5 (void);
extern void CPUInstanceData_get_instancesLength_mEF0DF28231C26F0C2E7EDA6A7115A51F51E5109C (void);
extern void CPUInstanceData_set_instancesLength_mA37EF6CFF372799E7F182362873F19898021B186 (void);
extern void CPUInstanceData_get_instancesCapacity_mB1E847621B15F7559FC491B24F54ABA253A73249 (void);
extern void CPUInstanceData_set_instancesCapacity_m2BC970C6B7AA237310D79F3994737B40F962EE1D (void);
extern void CPUInstanceData_get_handlesLength_mA9DC002A27BBD94B09B5944B84C6F47C30692F48 (void);
extern void CPUInstanceData_Initialize_mCB28B3C43BE61390B76A1B9C1C79BBFA33A33FD0 (void);
extern void CPUInstanceData_Dispose_mA6CD2735982440ECA65880116C2D214E97ADFC20 (void);
extern void CPUInstanceData_Grow_m54A454E32FB7C164E757F05A453DB71BBF6E1907 (void);
extern void CPUInstanceData_AddUnsafe_m1F5C8C517A8258670937584F1DD66E9D47724BC2 (void);
extern void CPUInstanceData_InstanceToIndex_m9D4701DC598D9046898239AF94F4E87F6A0F14EE (void);
extern void CPUInstanceData_IndexToInstance_m3C95DBA9646291FACE0234AC811183928C232321 (void);
extern void CPUInstanceData_IsValidInstance_m5B3FF0F8456CF25EE209E68A230D594BB5DB36A3 (void);
extern void CPUInstanceData_IsFreeInstanceHandle_mEEBB421BA3391592FEF206095FBBC6A4BF6D9589 (void);
extern void CPUInstanceData_IsValidIndex_m4BE52A79DE124AA4872297A76A0FEA1D114CA010 (void);
extern void CPUInstanceData_GetFreeInstancesCount_m1F0B5BDAFE8DC1DB434FC5682705494F3EDFE10C (void);
extern void CPUInstanceData_EnsureFreeInstances_mB7036C8793D1CB89C1C371A605581CBB4E177484 (void);
extern void CPUInstanceData_AddNoGrow_mEB9B4B4BCDB7C718DA14898D903B5E0D45DE0455 (void);
extern void CPUInstanceData_Add_m0736A5D9F85B1EC37E061411CC00335DFCC44D5A (void);
extern void CPUInstanceData_Remove_m599064EAE344F332CFE4CD00C9714E72623D6877 (void);
extern void CPUInstanceData_Set_mF5405865F66B4E168163746D8C5BCEE865DE81CD (void);
extern void CPUInstanceData_SetDefault_mDE2B13667675847C626111DE77EDE63FE25B5E42 (void);
extern void CPUInstanceData_Get_SharedInstance_mE5ED4915B38984690F4AB5089634500FBCB6C3CB (void);
extern void CPUInstanceData_Get_LocalToWorldIsFlipped_m19881EEED025A0FC45721F63DFCCC1C462F93107 (void);
extern void CPUInstanceData_Get_WorldAABB_mF9F14AE6B71FECD3BF51252AB06781687C9531EB (void);
extern void CPUInstanceData_Get_TetrahedronCacheIndex_m7D09D9EBD350591CCD9A948AF1F55DA98F85D1B6 (void);
extern void CPUInstanceData_Get_WorldBounds_m59B12DD936A814F2E056E962A4889CD3FD473417 (void);
extern void CPUInstanceData_Get_MovedInCurrentFrame_m58A543967F67149E003AD3FE6FE4C45F9E87C869 (void);
extern void CPUInstanceData_Get_MovedInPreviousFrame_m9A0B9988AF8004828D13C96F89981F3C6AB15C29 (void);
extern void CPUInstanceData_Get_VisibleInPreviousFrame_m3E67A47CD7703E4345BEE5A7068855FE6A589BB5 (void);
extern void CPUInstanceData_Get_MeshLodData_m2C413661D7B9E15E2B095F5AAEFFD3DCE65E914F (void);
extern void CPUInstanceData_Set_SharedInstance_mECEE018E02FA2F6502B59197A9AEF35FA2D35081 (void);
extern void CPUInstanceData_Set_LocalToWorldIsFlipped_mB2B243184FD3C74C54A5D8EC74542BC0E019EF29 (void);
extern void CPUInstanceData_Set_WorldAABB_m56D85CE12B7C589B053C471E803090DF9CA37073 (void);
extern void CPUInstanceData_Set_TetrahedronCacheIndex_mAC10B658EB1AB6EF405BC32A6DE8E88F7AA45AD5 (void);
extern void CPUInstanceData_Set_MovedInCurrentFrame_mB2081F967C2A8B6E6AC80F2E91DB3FD70851346F (void);
extern void CPUInstanceData_Set_MovedInPreviousFrame_m096AF4BED24D559F13122047DF89E56C3C431B4D (void);
extern void CPUInstanceData_Set_VisibleInPreviousFrame_m53D87829915906D1DD8E19F1F8D51631E9C02620 (void);
extern void CPUInstanceData_Set_MeshLodData_mB14CED13A879C4E0CA7A8E27228642E1AF7D44FF (void);
extern void CPUInstanceData_AsReadOnly_m90E90427C88E3F72D123C44F0E7957BACDDA8306 (void);
extern void ReadOnly_get_handlesLength_m50556F07B2F7144758202F76E202ED795E715DE9 (void);
extern void ReadOnly_get_instancesLength_m0E85E521D7DB61518C6988B08646386F20A17307 (void);
extern void ReadOnly__ctor_mFAEAA65432C0711F606AC98FD2320032B60A3309 (void);
extern void ReadOnly_InstanceToIndex_mA64D7447F6466C660DE83765B1B42673D922CCE2 (void);
extern void ReadOnly_IndexToInstance_m5BB2CA43E37DBA390AB7F29E6B5357A05226EE1B (void);
extern void ReadOnly_IsValidInstance_m560D63CCD78D3661349A6134A30A29EFEDD3C30A (void);
extern void ReadOnly_IsValidIndex_m2E036F94E4DFD41DF1CCA4148915594457CF0A9B (void);
extern void CPUPerCameraInstanceData_get_instancesLength_m2FCFD20153EC6AACB2340D337BB18BF616A4198F (void);
extern void CPUPerCameraInstanceData_set_instancesLength_m3CB40B1AB4F2FD153BD4F869F93AA91CB594C0A0 (void);
extern void CPUPerCameraInstanceData_get_instancesCapacity_m769EC460DAC66EE0EEAB7EDB3AC0184573C20B3B (void);
extern void CPUPerCameraInstanceData_set_instancesCapacity_m4B4D614181686095CD8983D09FACB5EE1EF30223 (void);
extern void CPUPerCameraInstanceData_get_cameraCount_m4ED64DF61C7EEFDD0B771A9F3F80A46F40266FA8 (void);
extern void CPUPerCameraInstanceData_Initialize_m8FF812DA16EBDB44A3342DB1FE0CA66B39CAF543 (void);
extern void CPUPerCameraInstanceData_DeallocateCameras_mD90424A33180D27D17C51148E7C439D19CCE79DF (void);
extern void CPUPerCameraInstanceData_AllocateCameras_m6C0A0DA2F8AB0E201801C3D5D67DCA28A677E23C (void);
extern void CPUPerCameraInstanceData_Remove_m49A44C5456AC0C589DAF869E031D7F0113BA86BD (void);
extern void CPUPerCameraInstanceData_IncreaseInstanceCount_mB3E25932F6796ED52CA77FD2CE1EF2C2F181F582 (void);
extern void CPUPerCameraInstanceData_Dispose_m217073CE624DE6B4D4087A252B22296C6B6D8964 (void);
extern void CPUPerCameraInstanceData_Grow_mA24034B63E5825884058DFE3C4BC602E61242846 (void);
extern void CPUPerCameraInstanceData_SetDefault_m8F8F7A49EBF71312638CD9E75C861AAEC1493B8B (void);
extern void PerCameraInstanceDataArrays_get_IsCreated_m36CA4102C1FC8F52C1C84D42C73DE22F4245EBB8 (void);
extern void PerCameraInstanceDataArrays__ctor_m1959ED65DC23B84993E219642F408B7E833EC465 (void);
extern void PerCameraInstanceDataArrays_Dispose_mB47A82DE41B69F0F6D0488317687ED8A0EBBE793 (void);
extern void PerCameraInstanceDataArrays_Remove_m937A118CFDBB9A6C9B7D8FF4481DC0673DA4974C (void);
extern void PerCameraInstanceDataArrays_Grow_m9ED1561118DD5ABF4A11D5F73359AE1D6A7D054A (void);
extern void PerCameraInstanceDataArrays_SetDefault_mB88E73D0AB2BC49675950883B49FE12625B0771D (void);
extern void CPUSharedInstanceData_get_instancesLength_m6C9D2E7F861FF43E23759E8816653646F41038B6 (void);
extern void CPUSharedInstanceData_set_instancesLength_m62AE1E8890A80D570FD3D11CD9E7C76B9D19124F (void);
extern void CPUSharedInstanceData_get_instancesCapacity_mE40EF07D597175079E8DB6639E444A8346BD5C67 (void);
extern void CPUSharedInstanceData_set_instancesCapacity_mEBAB461BBCD3CD8127252B3F6156431A24943A69 (void);
extern void CPUSharedInstanceData_get_handlesLength_mF238FD08383695F2D7F707F2FA87279FE939CE61 (void);
extern void CPUSharedInstanceData_Initialize_m0D70CFA6688D2B1A165CDB20AB1760CBB4604A6E (void);
extern void CPUSharedInstanceData_Dispose_m37AA8B81C3E42B81EC8E948B3F3DF9197B75E48C (void);
extern void CPUSharedInstanceData_Grow_m0AED29A47C913CD92A9EA6281FABBBE76C4C26F3 (void);
extern void CPUSharedInstanceData_AddUnsafe_mD66018426B29B8FC77CBA218E04D6470D3A08B69 (void);
extern void CPUSharedInstanceData_SharedInstanceToIndex_m8D1697AC229D8D0D83FA702F6F440132FA123282 (void);
extern void CPUSharedInstanceData_IndexToSharedInstance_m7E58052D57FC86E8F4C0571D1213F2D4774BD323 (void);
extern void CPUSharedInstanceData_InstanceToIndex_mA58DA1C16D52FECF5ED6238B168203837D75F87C (void);
extern void CPUSharedInstanceData_IsValidInstance_m33E12C093A6F0194B4D388782B15C2DD64616384 (void);
extern void CPUSharedInstanceData_IsFreeInstanceHandle_m972EDE9AF42DAADE66DAA3B1E1A8F8A85B594BFA (void);
extern void CPUSharedInstanceData_IsValidIndex_m24116C260518DD680C3CDBD5BB1797B7764DDBCD (void);
extern void CPUSharedInstanceData_GetFreeInstancesCount_mC18CC336EB3ED96DA46A39019C480485B58A26F5 (void);
extern void CPUSharedInstanceData_EnsureFreeInstances_m9276B1E0AE6DE0DFAE5F6FA4AFFB7DD9E6939CB0 (void);
extern void CPUSharedInstanceData_AddNoGrow_mAF3FBC643ABC12255E182EE8F8D26024AD8BBACD (void);
extern void CPUSharedInstanceData_Add_m3AFDBA31C2E77E106BD970496C5F0EFBDCB3FCD3 (void);
extern void CPUSharedInstanceData_Remove_m1BA13D76461FC093E7667EA3746FC4A6C3E99EF9 (void);
extern void CPUSharedInstanceData_Get_RendererGroupID_m09F08F1763DD649A37D88B169CA50B2BD4F71768 (void);
extern void CPUSharedInstanceData_Get_MeshID_m70DD07FFBE12B43BA2546547FD26BC268E28EF43 (void);
extern void CPUSharedInstanceData_Get_LocalAABB_mB7D5A9EA9CD43BBEE3E938D31500A518FD3C02D7 (void);
extern void CPUSharedInstanceData_Get_Flags_m4F22F6B4DB27966ED5F633612B135EDCD274E341 (void);
extern void CPUSharedInstanceData_Get_LODGroupAndMask_m4A750431421271D4D2960E84D4B3AD83F96A8A42 (void);
extern void CPUSharedInstanceData_Get_GameObjectLayer_m75D2D7344A04236BFAE513142B29AB6CC8E7CB4D (void);
extern void CPUSharedInstanceData_Get_RefCount_mE216F6749BB1B06E795C52C2433EE85E6C57DC01 (void);
extern void CPUSharedInstanceData_Get_MaterialIDs_mCE6EC8119990B7324D473B6DFB27B91A076404FC (void);
extern void CPUSharedInstanceData_Set_RendererGroupID_m06EFE6568E7FABED464A0A091DB7D8DE05FC8719 (void);
extern void CPUSharedInstanceData_Set_MeshID_m97990780D2C7B92B6A146C6FB243D08D2674EAA9 (void);
extern void CPUSharedInstanceData_Set_LocalAABB_mFA0C8491B2FCD9E1F5BB25FF598C7520D689D08C (void);
extern void CPUSharedInstanceData_Set_Flags_m9C1CE3AC11B09B3B2C9F65422B2FD31D23C49F4D (void);
extern void CPUSharedInstanceData_Set_LODGroupAndMask_mD27421C024B1779830BEFA1274063B72D7C182CA (void);
extern void CPUSharedInstanceData_Set_GameObjectLayer_m97AEE383F753133901141CCD0040B7D2C2E41EEC (void);
extern void CPUSharedInstanceData_Set_RefCount_m7C5AF143DDC85304B2840A688BB2DD095A03B7CC (void);
extern void CPUSharedInstanceData_Set_MaterialIDs_m3C8A0B397DBC9FEFF9661015CC32232A4910D812 (void);
extern void CPUSharedInstanceData_Set_m1236559425DDDA4FAF0765307F13B64FCA01C2BB (void);
extern void CPUSharedInstanceData_SetDefault_m9241E530C50F996744FC4691DCD87C0E30602237 (void);
extern void CPUSharedInstanceData_AsReadOnly_mC186E7142A7E53BFE6D90BB0AB1841600BE7CBF8 (void);
extern void ReadOnly_get_handlesLength_mEAE03E557C81624E1F2CD280610A867E764EE456 (void);
extern void ReadOnly_get_instancesLength_m82B5F22A5EE9DF8AB94546D3B739C1EB36AD38D6 (void);
extern void ReadOnly__ctor_mDBC17F900B61DCF328F4BE2CDF0464E97F44614D (void);
extern void ReadOnly_SharedInstanceToIndex_m7CD21D39C3285388B428D1CDCAA9AFB10A0178B4 (void);
extern void ReadOnly_IndexToSharedInstance_mC97CAB3F6DB3C92BCB528F85F5B9E42728DE3389 (void);
extern void ReadOnly_IsValidSharedInstance_mC2056D855CF0ED6FA050810CE1035680DC453A1A (void);
extern void ReadOnly_IsValidIndex_mA6B2DAE40129D5F001698A34D413EE0CEB24E61D (void);
extern void ReadOnly_InstanceToIndex_mE76C9EBF3ACCFDD5D73B3A99925CAE88DE2DDBE8 (void);
extern void SmallIntegerArray_get_Valid_m717ECD9B73FD5A373F40B0A623CB2C8E188012C0 (void);
extern void SmallIntegerArray_set_Valid_mF9C378DA26AB93E1AB38CCD7371CA23ABB6779D3 (void);
extern void SmallIntegerArray__ctor_mD7860879B9A093BE8CD0CFD815D125FDB746090E (void);
extern void SmallIntegerArray_get_Item_m81A85EC298981F31BB405CA783D90BB96697C2DE (void);
extern void SmallIntegerArray_set_Item_mFEF4007D7D6DA125990D22E32B34ADCBF7056EA2 (void);
extern void SmallIntegerArray_Dispose_m11D070ED0582D69C7F631CAF8C44F93183D73579 (void);
extern void EditorInstanceDataArrays_Initialize_m9893832043B3485AD59127B93D0872405CF77AFC (void);
extern void EditorInstanceDataArrays_Dispose_m16E99865063B2150E7BEFCE3BAE55831D9532B3B (void);
extern void EditorInstanceDataArrays_Grow_m57AFD6C22AA320DA1E98981C894EEB1F96C0B041 (void);
extern void EditorInstanceDataArrays_Remove_mC3970E725DCDCEA7B636C2DCB4732C432787AE35 (void);
extern void EditorInstanceDataArrays_SetDefault_mBB385CE94C9E46ADADB174CEFB393C2A19245724 (void);
extern void ReadOnly__ctor_m2029FDC41CDEC78F717316642DB33534904EC6E2 (void);
extern void PackedMatrix_FromMatrix4x4_mD8BF568A72FAFAA50614FD1F9DA6A7F257CB3E77 (void);
extern void PackedMatrix_FromFloat4x4_m2EEC3F97BB2E382DD01F31AE83B11D73F2579A9D (void);
extern void InstanceDataSystem_get_hasBoundingSpheres_mE95EB4398294EC395CE2A5A16F5D86EF8D86AFBF (void);
extern void InstanceDataSystem_get_instanceData_mCB763544E2728F9E48CEF5CB5284044D1C61CDF1 (void);
extern void InstanceDataSystem_get_perCameraInstanceData_mFE326CC0E7C00FDCF8279605C3D995AEAADEAB0D (void);
extern void InstanceDataSystem_get_cameraCount_mE0E41E2042BB55DC037F4B7F2FE08774B292E8D8 (void);
extern void InstanceDataSystem_get_sharedInstanceData_m917A6760CCBBEBB27FECC0035926431ED41D1BDF (void);
extern void InstanceDataSystem_get_aliveInstances_m24552E5DB0DD7022BEBC44E99BAD4E5B91C3FD89 (void);
extern void InstanceDataSystem__ctor_m5F7B568C5D6BF6507682A782B497C5DF9AF288E7 (void);
extern void InstanceDataSystem_Dispose_mD8F0ABE86EC7824BD24020C924702A073024A5FC (void);
extern void InstanceDataSystem_GetMaxInstancesOfType_mD0C2B5D78BAA3DF5116E66D663F0AB88A1267928 (void);
extern void InstanceDataSystem_GetAliveInstancesOfType_mACA5AF484D118330CACC8C0D919BAFDDA30D43FA (void);
extern void InstanceDataSystem_EnsureIndexQueueBufferCapacity_mEC63AEE12228511E02036542B749925C591E4190 (void);
extern void InstanceDataSystem_EnsureProbeBuffersCapacity_m5C8E2190B2C827606936372E985463BC746A65D2 (void);
extern void InstanceDataSystem_EnsureTransformBuffersCapacity_m8101997E233AADA2DFCA0C139B74927BAD65C221 (void);
extern void InstanceDataSystem_ScheduleInterpolateProbesAndUpdateTetrahedronCache_m138513395BC490C04056D11A8AE9A4017E69092D (void);
extern void InstanceDataSystem_DispatchProbeUpdateCommand_m89CB692F574017CDA489FFDC50B7D021F9BE624A (void);
extern void InstanceDataSystem_DispatchMotionUpdateCommand_m6CAB421EB2033825CFE9FF8C0A3F13FF6849BD53 (void);
extern void InstanceDataSystem_DispatchTransformUpdateCommand_m408F702045F0792E5FAB3D30089984D2AC68492F (void);
extern void InstanceDataSystem_DispatchWindDataCopyHistoryCommand_m3CE9A16E6EDD1B23B2A1844EB0C8FE63297FCF44 (void);
extern void InstanceDataSystem_UpdateInstanceMotionsData_m81BC58CE2698369C68C0E7AC1543A7AE4CD871FA (void);
extern void InstanceDataSystem_UpdateInstanceTransformsData_mA9273FAEEACA70AB121D953179312125AD328FCC (void);
extern void InstanceDataSystem_UpdateInstanceProbesData_m1CD19D71D15B03FC82F0D5434D43872B6482AEE2 (void);
extern void InstanceDataSystem_UpdateInstanceWindDataHistory_m9E2E361D86A93AEC4256E9E45E6FF8C25DDEF97E (void);
extern void InstanceDataSystem_ReallocateAndGetInstances_mD8B36795100226FED3AFE497FC9DED84FF4A6476 (void);
extern void InstanceDataSystem_FreeRendererGroupInstances_mDB237F9840CA6B5121A30D5238DEFCBBE2DC7B78 (void);
extern void InstanceDataSystem_FreeInstances_m1FCCBE915D86469CC20E2C01AE6FB341734F2AF9 (void);
extern void InstanceDataSystem_ScheduleUpdateInstanceDataJob_mEB4A7B9A770F619108268D0B11ABE99DCEFAC479 (void);
extern void InstanceDataSystem_UpdateAllInstanceProbes_m2544131305465C5C6DE3956ACE326DC2B9DB05AF (void);
extern void InstanceDataSystem_InitializeInstanceTransforms_mF2F8A8EEDBFFA25647574740B190DD2899B5B0F8 (void);
extern void InstanceDataSystem_UpdateInstanceTransforms_m7A0057B405E3D12CFF3EB78FCB3BE1D1593A0E43 (void);
extern void InstanceDataSystem_UpdateInstanceMotions_mDCDA88917F5E5B6CC8D8FCFB50744E529C11CDFF (void);
extern void InstanceDataSystem_ScheduleQueryRendererGroupInstancesJob_m4C0025CA86226F2D5A23C721CA42E7E8DF4C30B4 (void);
extern void InstanceDataSystem_ScheduleQueryRendererGroupInstancesJob_m04F92151A520DC0AF8F1FB4B7AFA040C0F625D0E (void);
extern void InstanceDataSystem_ScheduleQueryRendererGroupInstancesJob_mB490AA73553E991D855BFC67D1622FE3AC8C098E (void);
extern void InstanceDataSystem_ScheduleQuerySortedMeshInstancesJob_m04115ECA07C31067F98B727EE322A1786C70175C (void);
extern void InstanceDataSystem_ScheduleCollectInstancesLODGroupAndMasksJob_m9A29F99524770324E8E2896B54E5C08FF4A0979E (void);
extern void InstanceDataSystem_InternalSanityCheckStates_m972099150EFFB0CFB52E22F42C4E216C1B012A9B (void);
extern void InstanceDataSystem_GetVisibleTreeInstances_m215114432B8645A102573A589C21C9925471A451 (void);
extern void InstanceDataSystem_UpdatePerFrameInstanceVisibility_m1C6A42FA01165B8F7D05C4179DD093BE19AA4512 (void);
extern void InstanceDataSystem_DeallocatePerCameraInstanceData_m49AE69E176C67DAACE81A9C940F49C11B1970D8F (void);
extern void InstanceDataSystem_AllocatePerCameraInstanceData_m417012B36CE1176EB75BDE5052A9CCB91864389E (void);
extern void InstanceTransformUpdateIDs__cctor_m22E50C74A91C8F98F112D7D4E8AD2D3CA77829C5 (void);
extern void InstanceWindDataUpdateIDs__cctor_mDCE66DBD25DE0B17A7C41D329C4006A0AC407C09 (void);
extern void QueryRendererGroupInstancesCountJob_Execute_m78EBD1A4D27FC820DB994D726466672AE6698114 (void);
extern void ComputeInstancesOffsetAndResizeInstancesArrayJob_Execute_m7278A70AAF09ACD7DE06A58BBE53617ED59B3178 (void);
extern void QueryRendererGroupInstancesJob_Execute_m689B8C96E8D3739314972C71A89F43C5DD8DD5EC (void);
extern void QueryRendererGroupInstancesMultiJob_Execute_m3ED5F46C8E2BBCFD2302EBF30F55D07CB4292593 (void);
extern void QuerySortedMeshInstancesJob_Execute_m0B30D3DBCCB8FE5833D677F735387912A448CCDF (void);
extern void CalculateInterpolatedLightAndOcclusionProbesBatchJob_Execute_mB29926232C46662441F96B9405E9BE4D3A84109B (void);
extern void ScatterTetrahedronCacheIndicesJob_Execute_m157E3BAA3AA5106CD5629211DB4DF10C941B0942 (void);
extern void TransformUpdateJob_Execute_m0C99BC8B89AD60C1B495E471F9F02FBEEF7F8342 (void);
extern void ProbesUpdateJob_Execute_mFACBB95DE46DB9C879C2EE745CE37C4AEFB69AD6 (void);
extern void MotionUpdateJob_Execute_m266055F93AABA39FD53E7EDBD6BAFE55F586891B (void);
extern void UpdateRendererInstancesJob_Execute_m93597D2184F6460DD4610AD8E47868B1BA87A11C (void);
extern void CollectInstancesLODGroupsAndMasksJob_Execute_m2EFF9AB4F3476E491E51143F7F60F79020A0F780 (void);
extern void GetVisibleNonProcessedTreeInstancesJob_Execute_mEFBAD1E57760EF2F4C2A4F4E6F6F40E6261C284A (void);
extern void UpdateCompactedInstanceVisibilityJob_Execute_mDFAB7C16E38743439A74CCA9AA310B383D2BA55B (void);
extern void InstanceDataSystemBurst_ReallocateInstances_mF18C9347288DA7A9C194DFB92AC6A014D24975D9 (void);
extern void InstanceDataSystemBurst_FreeRendererGroupInstances_mE5B774F1873565C1564044629ADC0EC240EC6547 (void);
extern void InstanceDataSystemBurst_FreeInstances_m40A2076A7C0DE278135AD4A33911F95F2A94E630 (void);
extern void InstanceDataSystemBurst_ReallocateInstancesU24BurstManaged_m4FB41EFA348C0E272E99FA0ABBA3BD30F7562EB1 (void);
extern void InstanceDataSystemBurst_FreeRendererGroupInstancesU24BurstManaged_m68FAC42D1B0B2C0FFFC6CDAC4C3C106377565C0D (void);
extern void InstanceDataSystemBurst_FreeInstancesU24BurstManaged_mA15F79C9BB9E77720A6F7B2A613E9003DE42A6F4 (void);
extern void ReallocateInstances_00000292U24PostfixBurstDelegate__ctor_mD9065276756DD196E7FE3D81D84F61394C71521C (void);
extern void ReallocateInstances_00000292U24PostfixBurstDelegate_Invoke_m390CCE4527F706B664500B168B7344639B9481D0 (void);
extern void ReallocateInstances_00000292U24PostfixBurstDelegate_BeginInvoke_mDABDAB19D20886FBAA411CACEF20606B04C78E1F (void);
extern void ReallocateInstances_00000292U24PostfixBurstDelegate_EndInvoke_mA86A688427934286E9591256F06CAE23572B6DB9 (void);
extern void ReallocateInstances_00000292U24BurstDirectCall_GetFunctionPointerDiscard_mE9F4D6FA95C949818343733F248BC9E9983C3699 (void);
extern void ReallocateInstances_00000292U24BurstDirectCall_GetFunctionPointer_mAD16A11B02044AA091030482D1E74A8397F3955C (void);
extern void ReallocateInstances_00000292U24BurstDirectCall_Invoke_mF7C8CCEC334854360B5429B556BB614ECBBCC4AA (void);
extern void FreeRendererGroupInstances_00000293U24PostfixBurstDelegate__ctor_mE914EB226676111CE4E55313877CA2F97BD8A5D4 (void);
extern void FreeRendererGroupInstances_00000293U24PostfixBurstDelegate_Invoke_mD2868BD3CB139179449AB366C891A8BE9136B151 (void);
extern void FreeRendererGroupInstances_00000293U24PostfixBurstDelegate_BeginInvoke_m9ED7098EFE82ED4F212FA5571AF2E36B155035B9 (void);
extern void FreeRendererGroupInstances_00000293U24PostfixBurstDelegate_EndInvoke_mC1E8AFA593CC3DD496308AAF49D7A0DC5F2B1157 (void);
extern void FreeRendererGroupInstances_00000293U24BurstDirectCall_GetFunctionPointerDiscard_m80879D31A20C48C5DEB64181D465741614E910D3 (void);
extern void FreeRendererGroupInstances_00000293U24BurstDirectCall_GetFunctionPointer_m9E338F86369D4C7E24996419038598FD2D60A1D8 (void);
extern void FreeRendererGroupInstances_00000293U24BurstDirectCall_Invoke_m8285C71D4CA8A908CCDCEA2E4EFB9D64AE9C1AE0 (void);
extern void FreeInstances_00000294U24PostfixBurstDelegate__ctor_mE43DD855D2F853406A5E0636AC26D29AFE8BE6F5 (void);
extern void FreeInstances_00000294U24PostfixBurstDelegate_Invoke_m71300F65282D856F171CE0921EEA0876DAF1719C (void);
extern void FreeInstances_00000294U24PostfixBurstDelegate_BeginInvoke_mDF5C9A5B2833C9732EB0B8CCD3C4CC8C8CD55DB4 (void);
extern void FreeInstances_00000294U24PostfixBurstDelegate_EndInvoke_mB704DA4BCA7936CAF1B9FCDAF4015992333E8696 (void);
extern void FreeInstances_00000294U24BurstDirectCall_GetFunctionPointerDiscard_m086741F6985D0AB6F9A460B803A5AF856571E1EB (void);
extern void FreeInstances_00000294U24BurstDirectCall_GetFunctionPointer_m16CE891C70281D5FEC2670C3EB1CE4116231F47E (void);
extern void FreeInstances_00000294U24BurstDirectCall_Invoke_m577A8A1A7763E27AAF48BA6CE4ED033634C91AA4 (void);
extern void InstanceTypeInfo__cctor_m3B458B3343FBC2FD6C40D4FB48ED7A275F513140 (void);
extern void InstanceTypeInfo_InitParentTypes_m871A556C0C4609293ABEEBF110E20DD63FCD084C (void);
extern void InstanceTypeInfo_InitChildTypes_m172E3471BA713B1205C2B5BD08FC4E1D1518D41E (void);
extern void InstanceTypeInfo_GetMaxChildTypeRecursively_m145FABD376E475D616CC58209E97D94E586BC247 (void);
extern void InstanceTypeInfo_FlattenChildInstanceTypes_m815D9E228DD3FF4C86C5A233A038D551FBC28AC8 (void);
extern void InstanceTypeInfo_ValidateTypeRelationsAreCorrectlySorted_mD653B8D9D7F845BE0747448F358E441ADBB6D893 (void);
extern void InstanceTypeInfo_GetParentType_m4EFCC55DA43E58978C6A983D91BCB6FAAF147529 (void);
extern void InstanceTypeInfo_GetChildTypes_mA2B041904C54BC841991C707960DEF5842EA5093 (void);
extern void InstanceNumInfo_InitDefault_m8CC8A9E8EBF0EF16311F391E0312C46A7219A920 (void);
extern void InstanceNumInfo__ctor_m0C64766A7024C367CB84BA96F01861E951351650 (void);
extern void InstanceNumInfo__ctor_mCFED34B4FC73F15366339611E22502A8366B016C (void);
extern void InstanceNumInfo_GetInstanceNum_m42CC8341EB7A73444DB20B9B64ACF5377CA7CE19 (void);
extern void InstanceNumInfo_GetInstanceNumIncludingChildren_m45CB40CE2452143C10B4FFC0F05FA7751E416368 (void);
extern void InstanceNumInfo_GetTotalInstanceNum_m55D6043571FB3440889F98A7D638F6A43D060982 (void);
extern void OccluderDerivedData_FromParameters_mB285C6B3E3FBFB06A8E38D196D06D45FE722D88D (void);
extern void OccluderHandles_IsValid_m9FF256155930C1B40D4195C263AF38FCEB1B7904 (void);
extern void OccluderHandles_UseForOcclusionTest_m8E9F3CDFD218EC3C5A70DA375F156DC75CB2CAAD (void);
extern void OccluderHandles_UseForOccluderUpdate_m4376DEB9151FDF5678FDFE2ED525A3B3AC31AF03 (void);
extern void IndirectBufferAllocInfo_IsEmpty_m47785BE361D9B989BE0455AD1884831AB99E4009 (void);
extern void IndirectBufferAllocInfo_IsWithinLimits_mE8807A3A2DDE4F3E3E9091E2441D847F4BFC4067 (void);
extern void IndirectBufferAllocInfo_GetExtraDrawInfoSlotIndex_m2EAD4191867631265610C43212C34A56A2DAD969 (void);
extern void IndirectBufferContext__ctor_mC7F33D8E6E9DECDB5A167C9CFD68F1CBB6E7D7F5 (void);
extern void IndirectBufferContext_Matches_m40E2A7974D4B205D4BB770D4A68635D99FE4C721 (void);
extern void OccluderContext_get_subviewCount_m49FDF73077D5C9F1789BA0C35A39A7F78FCBF414 (void);
extern void OccluderContext_IsSubviewValid_m88BE2A076AC851E9D11AB02B30ECE80A7E4D6BE0 (void);
extern void OccluderContext_get_depthBufferSizeInOccluderPixels_mF734AB99EBF484188554B86CB2E07048E6138C36 (void);
extern void OccluderContext_Dispose_mF02789AFBB76CD0F4491CDD8A83BCD15938F22D8 (void);
extern void OccluderContext_UpdateMipBounds_m71B416ED1B6D8827B30F8B1C1E69870328E6FCD9 (void);
extern void OccluderContext_AllocateTexturesIfNecessary_m4F0340120C018852B2A697194F87A6C539D6C127 (void);
extern void OccluderContext_SetKeyword_m57CB9C813FA45672B4E4EAD297757E2C427EE0EE (void);
extern void OccluderContext_SetupFarDepthPyramidConstants_m69DC95369344843C149515C9A2A9312DD0E99571 (void);
extern void OccluderContext_CreateFarDepthPyramid_mAE9A7D75C802A5AB3F91ED35C2BF61DA52C0AB0F (void);
extern void OccluderContext_Import_m59CDBBE79F1A96B17BE009D5D561E35D69DA19D7 (void);
extern void OccluderContext_PrepareOccluders_m558D900C293C248A3CEE8FE6640C98873039DF1D (void);
extern void OccluderContext_GetDebugOutput_m0B03B350C81D50A7DA8CEB969E70AF0F782675C5 (void);
extern void ShaderIDs__cctor_mAF5448F8A3480811300797984917EC0136A2EEAE (void);
extern void InstanceOcclusionTestSubviewSettings_FromSpan_m23AA5216F285965B59FD98CCF986ABB9A0C527C5 (void);
extern void IndirectBufferContextHandles_UseForOcclusionTest_m098E1C673230461EF27D4BC76A7186A5DADEEDAB (void);
extern void IndirectBufferContextStorage_get_instanceBuffer_mD965845788AD240262E1C46BB4BA405B73C8EA27 (void);
extern void IndirectBufferContextStorage_get_instanceInfoBuffer_mC14C040CC88B2F11AFE1301EB1FB9C70E396E8E5 (void);
extern void IndirectBufferContextStorage_get_argsBuffer_m81BE13A707FAF6B8F9361AEBCDD9CBA69611C334 (void);
extern void IndirectBufferContextStorage_get_drawInfoBuffer_m0168780ED5CFCB3613F04F2C00561471352A5790 (void);
extern void IndirectBufferContextStorage_get_visibleInstanceBufferHandle_mBD86573F23F6B0F8BF6C46C2996C449E571DC87F (void);
extern void IndirectBufferContextStorage_get_indirectArgsBufferHandle_m7DF465FC2B94D42E1ABE0EE29E0EE4A1C850E925 (void);
extern void IndirectBufferContextStorage_ImportBuffers_mB024C5F1494D76DFEC07CDE6065AC7ACBB35E265 (void);
extern void IndirectBufferContextStorage_get_instanceInfoGlobalArray_m6D87D347531D8E3C00FB0967CCAEE69FED54FC41 (void);
extern void IndirectBufferContextStorage_get_drawInfoGlobalArray_mF90CEAECE8B83E043E462A4495421A574CC257D4 (void);
extern void IndirectBufferContextStorage_get_allocationCounters_mF22684F6D0214AE0755E3DFA8BA047C9E63C70FC (void);
extern void IndirectBufferContextStorage_Init_mE1D9E857917AF5759F2E08D6B008C991CE5558AF (void);
extern void IndirectBufferContextStorage_AllocateInstanceBuffers_m83064D5ABC47473BE4547D8CDB301CC989362AD4 (void);
extern void IndirectBufferContextStorage_FreeInstanceBuffers_mE035A24BD6179866735657D3D74C416AD39C597D (void);
extern void IndirectBufferContextStorage_AllocateDrawBuffers_mCB3267B00E8328A5031AAB859542061D665CE0A0 (void);
extern void IndirectBufferContextStorage_FreeDrawBuffers_mAB74E77AD8B6348D492585D0ACB5AE42C2CEB171 (void);
extern void IndirectBufferContextStorage_Dispose_mD7278C175C4C23BCB9190D68CE8343B543A24302 (void);
extern void IndirectBufferContextStorage_SyncContexts_mB63C2465AF6D851CA68041FA1C12E24F24874A32 (void);
extern void IndirectBufferContextStorage_ResetAllocators_m327B8163AEEAFE360011EAEBFBD80C96C6403467 (void);
extern void IndirectBufferContextStorage_GrowBuffers_mAB6F1A326FB73F38678D592C809333FAE5A25D44 (void);
extern void IndirectBufferContextStorage_ClearContextsAndGrowBuffers_m0B059924A2DB871C3C80B03DAF54A47C8A47E573 (void);
extern void IndirectBufferContextStorage_TryAllocateContext_mC8D472C07C986BDCFC55CF8C4CE324230A38618C (void);
extern void IndirectBufferContextStorage_TryGetContextIndex_m848B7C96E09D5CE2C6D12221D0B78EA4DCE77EF1 (void);
extern void IndirectBufferContextStorage_GetAllocInfoSubArray_m053D08700A1B5D4DB1AAF31B508E33E9FF418A4D (void);
extern void IndirectBufferContextStorage_GetAllocInfo_m9BF31C85DBB94B0D0F1B82AF66DA8493AB1B2595 (void);
extern void IndirectBufferContextStorage_CopyFromStaging_mE99297FD5192A7F628FD7DC704E877280DB03891 (void);
extern void IndirectBufferContextStorage_GetLimits_m2E8DDC54719944794D7342725107396F59D5CF71 (void);
extern void IndirectBufferContextStorage_GetBufferContext_mD76FD0DD95CEE4D7D9B62F81573C4A05A5519941 (void);
extern void IndirectBufferContextStorage_SetBufferContext_mEC8E692A99E595592E12F55F94B7E234A340DC2A (void);
extern void UpdateLODGroupTransformJob_Execute_m10AD64037A0D9FFAB351270A412B4B4342A6B8C5 (void);
extern void UpdateLODGroupDataJob_Execute_m37868AFCE3AFCC80D46C8156CA2713096FE5EC3D (void);
extern void LODGroupDataPool_get_lodGroupDataHash_m62280E732F32C5C35B2DADCD304E46662939F045 (void);
extern void LODGroupDataPool_get_lodGroupCullingData_m9D4FE39BAD4D72923936ABBBEBEEF7F2F3131865 (void);
extern void LODGroupDataPool_get_crossfadedRendererCount_m1851897792114FF4241A4099060D707ECAD45334 (void);
extern void LODGroupDataPool_get_activeLodGroupCount_m97904EE5C95159152B6C0A1ABC068C06B5079CD4 (void);
extern void LODGroupDataPool__ctor_m41A2B2D9392893C14F8F1CC08EDE34AE43CDBC8C (void);
extern void LODGroupDataPool_Dispose_m018568FAFC3BCCE2F577FC92B6A3223CC585AA91 (void);
extern void LODGroupDataPool_UpdateLODGroupTransformData_mA548FB2A357D0A1CF586FBD7B3D04B928BCE005A (void);
extern void LODGroupDataPool_UpdateLODGroupData_mF09A39F868F16124B4F2503B1F725FE54AE7A96B (void);
extern void LODGroupDataPool_FreeLODGroupData_m900936DC26BBC6F1ABF60871DAF69D93FB79C900 (void);
extern void LodGroupShaderIDs__cctor_m317D81DF99F20606D1C93B871FD9CE2083C6C42A (void);
extern void LODGroupDataPoolBurst_FreeLODGroupData_mA6615B0E58416F4443ED0744A145DCB275902C79 (void);
extern void LODGroupDataPoolBurst_AllocateOrGetLODGroupDataInstances_mBD22A32D0C093047BA4165B0D6E9A38EE73F96F5 (void);
extern void LODGroupDataPoolBurst_FreeLODGroupDataU24BurstManaged_mFDD1E6CDEFC57BC4784F8CD2E37F054D38188E7D (void);
extern void LODGroupDataPoolBurst_AllocateOrGetLODGroupDataInstancesU24BurstManaged_m2B8A0354B669C5690EA34BFF597F0B2DB9E15825 (void);
extern void FreeLODGroupData_000002E3U24PostfixBurstDelegate__ctor_m229C5CC31C289B026E490711E30E2FB38E95C29A (void);
extern void FreeLODGroupData_000002E3U24PostfixBurstDelegate_Invoke_mC8ED82EA2AFFA62FD75BA0227200518FD4A82E64 (void);
extern void FreeLODGroupData_000002E3U24PostfixBurstDelegate_BeginInvoke_m08EDBFB373BE8A6626CF31649403214FB479AEC6 (void);
extern void FreeLODGroupData_000002E3U24PostfixBurstDelegate_EndInvoke_mE2C3919B4A156BE64B8FB118A7ABC1C9968C25C4 (void);
extern void FreeLODGroupData_000002E3U24BurstDirectCall_GetFunctionPointerDiscard_m9A87CB76DC8609824A92E038F601C0B671B87111 (void);
extern void FreeLODGroupData_000002E3U24BurstDirectCall_GetFunctionPointer_mD8C9C4658C30241BC33B6B3EC927D180B03FBB27 (void);
extern void FreeLODGroupData_000002E3U24BurstDirectCall_Invoke_mFAD5EF75F4677A195F687926AA8C3AC1585F80AD (void);
extern void AllocateOrGetLODGroupDataInstances_000002E4U24PostfixBurstDelegate__ctor_m9100BF202ED6D913941FA1700A042352DA86E4EC (void);
extern void AllocateOrGetLODGroupDataInstances_000002E4U24PostfixBurstDelegate_Invoke_mBEC68E66FAF6B7C98A5F2904099BDB5853A682F5 (void);
extern void AllocateOrGetLODGroupDataInstances_000002E4U24PostfixBurstDelegate_BeginInvoke_m090D0850ED4D8495FA6AD87E7D94798194B073D1 (void);
extern void AllocateOrGetLODGroupDataInstances_000002E4U24PostfixBurstDelegate_EndInvoke_mA68CADA88E91B345495612678127CAF753608449 (void);
extern void AllocateOrGetLODGroupDataInstances_000002E4U24BurstDirectCall_GetFunctionPointerDiscard_m397A6E69B62224825804E78DA876584F055FDA67 (void);
extern void AllocateOrGetLODGroupDataInstances_000002E4U24BurstDirectCall_GetFunctionPointer_m20405B28DD3A1E6FD7D68B168E1C44BB4113B478 (void);
extern void AllocateOrGetLODGroupDataInstances_000002E4U24BurstDirectCall_Invoke_mEA274B6A86190854B061BA3B7B795770859A4246 (void);
extern void LODRenderingUtils_CalculateFOVHalfAngle_mC898C822144EDD265331807564B08A69B0C83477 (void);
extern void LODRenderingUtils_CalculateScreenRelativeMetricNoBias_m7B4E475A8D3B2DB91FC59D008FF21754EFF91CD9 (void);
extern void LODRenderingUtils_CalculateMeshLodConstant_m2E8D806679F80D578D1B49C0A004D742B83B0042 (void);
extern void LODRenderingUtils_CalculatePerspectiveDistance_mCF2D6802B8867080934633B981CC9749B48B147E (void);
extern void LODRenderingUtils_CalculateSqrPerspectiveDistance_mF050ADA4BADB90CC903419FDA4124CF1C4749CA8 (void);
extern void LODRenderingUtils_GetWorldReferencePoint_mB73DE0BD3DF41D4594CB01CC090594C7057F2121 (void);
extern void LODRenderingUtils_GetWorldSpaceScale_mEB5418524BEA8374FABEAFE0392B3F6728DD1A39 (void);
extern void LODRenderingUtils_GetWorldSpaceSize_m9D47A644A41926B6325DA1D690038561E6119017 (void);
extern void LODRenderingUtils_CalculateLODDistance_m37BB5F6DED42AD36FF3AD3C214164C4CA43AE33E (void);
extern void OcclusionTestComputeShader_Init_mC423CA21A41E4B44DB2715096F04D7498A80E960 (void);
extern void SilhouettePlaneCache_Init_mF14F33F1C7D6CD3704478C92314526747ABDFF0C (void);
extern void SilhouettePlaneCache_Dispose_m52386469CD058770AAD5B4E19ADF603598BECEC5 (void);
extern void SilhouettePlaneCache_Update_m08599EDAF7CC1D053E0E54A8DF15F55E1E0B6342 (void);
extern void SilhouettePlaneCache_FreeUnusedSlots_m2249464604B48996B77945B9BBCDBDCECD2074C2 (void);
extern void SilhouettePlaneCache_GetSubArray_m0527F754CEEB54300C083A4BDAE9B56D026DA63C (void);
extern void Slot__ctor_m0E99B79099FDB0098404A2FD223A9C029CFFF5D1 (void);
extern void OcclusionCullingCommon_Init_mB12BBAEE22EA6EA4C93640CF113484C45AB21128 (void);
extern void OcclusionCullingCommon_UseOcclusionDebug_mB3DD90044DC771F1A74BDEAC59C921AE66E9311D (void);
extern void OcclusionCullingCommon_PrepareCulling_mB0789630787C7E0CD26370E649348B1C2E368B0C (void);
extern void OcclusionCullingCommon_SetDepthPyramid_mD653D7921DC4590B1E5DDC848F3B3DDF10D15D07 (void);
extern void OcclusionCullingCommon_SetDebugPyramid_m7DB573CC2B23E59F6E09FE953D2953447FB4D8BE (void);
extern void OcclusionCullingCommon_RenderDebugOcclusionTestOverlay_mFC06DC3F4302109DCBCE0016F77FDC7221C0F850 (void);
extern void OcclusionCullingCommon_RenderDebugOccluderOverlay_mDCEE8545488D66BAFEEC82CA0A5B078EF76F1719 (void);
extern void OcclusionCullingCommon_DispatchDebugClear_mD07E3E63ABEB291DB36385737735511B88AD3AC2 (void);
extern void OcclusionCullingCommon_PrepareOccluders_mB04E538ADB8D350F2F77C2B0AEB3235B5537C78A (void);
extern void OcclusionCullingCommon_CreateFarDepthPyramid_mA599495FF407F8137E6B40745EFA5296FD390859 (void);
extern void OcclusionCullingCommon_UpdateInstanceOccluders_m66590207897221E9FA80265BBEB4E9E40708646D (void);
extern void OcclusionCullingCommon_UpdateSilhouettePlanes_m4576EBD18929EC7B7AAA98EA599CEB053033161E (void);
extern void OcclusionCullingCommon_GetOcclusionTestDebugOutput_m3F8B14753A940E66F3378EE0A13B467CD5B54163 (void);
extern void OcclusionCullingCommon_UpdateOccluderStats_mFCE4F68D13AD834D837ACC6CF5818BB454DEB374 (void);
extern void OcclusionCullingCommon_HasOccluderContext_m24FD8FB63CF4F73E28369A7C5E4AB1A4B0C6EF90 (void);
extern void OcclusionCullingCommon_GetOccluderContext_m5FA55C98ABA809491877468967428AEA6ED50AA9 (void);
extern void OcclusionCullingCommon_UpdateFrame_m62E1615FE4BB0184C70EF0D5A1B5341A9E6B439E (void);
extern void OcclusionCullingCommon_NewContext_m192A0843FCB88873DB0DBC0D30E85E34D9CD3724 (void);
extern void OcclusionCullingCommon_DeleteContext_mD0DD525EF7A79EDEC506F1FD27762960E7A9D773 (void);
extern void OcclusionCullingCommon_Dispose_mA5C16ABDC8FFDCBDF1B0BBDAAF046EB707CAB0BE (void);
extern void OcclusionCullingCommon__ctor_m3B0C90E1EF8186EB97881C43D58E13303CACED1C (void);
extern void OcclusionCullingCommon__cctor_m65EF7B748745B32F17F979959B56ABA54B68E19D (void);
extern void OcclusionCullingCommon_U3CRenderDebugOcclusionTestOverlayU3Eb__29_1_m9B31475AE7F1F1FB5043C7E6AE2AB37D0D901037 (void);
extern void ShaderIDs__cctor_mC4B7BFD4D1A496F04AC567A1D343648AF9932CDD (void);
extern void OcclusionTestOverlaySetupPassData__ctor_m319029C880BDA7B70BBB48CCC52A6DEEE84BC7AA (void);
extern void OcclusionTestOverlayPassData__ctor_m0D63CEF912BF6F987D0718384ED42945529D5FE0 (void);
extern void OccluderOverlayPassData__ctor_m2BBEDE9EE87B99D51BD3A55ADE85B0FF7191D88E (void);
extern void UpdateOccludersPassData__ctor_m780741CED9AA7DEA6E7F15F1125830643B0940A5 (void);
extern void U3CU3Ec__cctor_m69E4B9D0362E234583DB9D7CC8D28B7B958F008D (void);
extern void U3CU3Ec__ctor_m7AF3A5B26F1D35F52C4E1518DCB55AF32705CA12 (void);
extern void U3CU3Ec_U3CRenderDebugOcclusionTestOverlayU3Eb__29_0_m6B98C8D250CCC733E809FCD7A6BEF46BE6416D27 (void);
extern void U3CU3Ec_U3CRenderDebugOccluderOverlayU3Eb__32_0_m000074A9983218A19ECAA6BBF27D4DE6F0CEC6EC (void);
extern void U3CU3Ec_U3CUpdateInstanceOccludersU3Eb__37_0_m07755DD078337F25892B35E882F36CF2D77C600B (void);
extern void OcclusionCullingCommonShaderVariables__ctor_m6098CCD0E939B2F9DE8715FF129DAE892745C610 (void);
extern void RenderersBatchersContextDesc_NewDefault_m60D7888149F2142AA66FECE97FDB3D098A3EC7DA (void);
extern void RenderersBatchersContext_get_renderersParameters_mFAD826F98D88258ACEB3BAAB3BCA506E9DA8C54E (void);
extern void RenderersBatchersContext_get_gpuInstanceDataBuffer_m58A374780F991800398A3A5C43B900F17E49CA78 (void);
extern void RenderersBatchersContext_get_activeLodGroupCount_m67F17132BF666120D9AD4C4AF2B5332DA3C3B3E8 (void);
extern void RenderersBatchersContext_get_defaultDescriptions_m9E1716E5F3F0528BCEF408D48F5114377A4449D5 (void);
extern void RenderersBatchersContext_get_defaultMetadata_m2A1B42632AB9F8A3B0E162912B6E1E542AC52A78 (void);
extern void RenderersBatchersContext_get_lodGroupCullingData_m71D5CF459C1ED069E69F643AEF402CE8684925ED (void);
extern void RenderersBatchersContext_get_instanceDataBufferVersion_m9F26A5C73EE9A8C8848F3163AAF3B50FBC96EFE3 (void);
extern void RenderersBatchersContext_get_instanceDataBufferLayoutVersion_m633BE48CAD9AA78DD46E20B2208647B3A94D992D (void);
extern void RenderersBatchersContext_get_cachedAmbientProbe_m122AB618901D9C67E31A3E1994C09FAE04AEAFE1 (void);
extern void RenderersBatchersContext_get_hasBoundingSpheres_mA6745C1F53546E926C85BC0B69E1E176E5C07B54 (void);
extern void RenderersBatchersContext_get_cameraCount_m66EC4325C4D1B268E744B32F97E114A5DCB0C6E9 (void);
extern void RenderersBatchersContext_get_instanceData_mA110F9896EEF3B8277350408C9554A9CA4BBAA1F (void);
extern void RenderersBatchersContext_get_sharedInstanceData_m657B7F8E58C1857C9A941039A9C87EDEE14BE073 (void);
extern void RenderersBatchersContext_get_perCameraInstanceData_mEC2BF69B0E02674900CFE87797760E58484F16F3 (void);
extern void RenderersBatchersContext_get_instanceDataBuffer_m085CC45CC334F7C4AFFC82F08FE9041267BC3FC0 (void);
extern void RenderersBatchersContext_get_aliveInstances_m464BB51D736CC6E53816E92B54FA52E20A6AB992 (void);
extern void RenderersBatchersContext_get_smallMeshScreenPercentage_m20E6B516780C91E3EFFF054223A2AD8259D67CEA (void);
extern void RenderersBatchersContext_get_resources_m384802C47C8866FE84F3D19892ED70D03CAD5CF2 (void);
extern void RenderersBatchersContext_get_occlusionCullingCommon_mB5106ABB84E6D34B14EBA467B292E39DDCB60C1D (void);
extern void RenderersBatchersContext_get_debugStats_m26AAE0C2CF41DBE02DD210D1FDDB808F8A88CB87 (void);
extern void RenderersBatchersContext__ctor_m0284FF6010F6BE127276B918BCB7F8D488D82C33 (void);
extern void RenderersBatchersContext_Dispose_mD6CFED69D7F9007FBA28516C2A6CCD9394D1FC3E (void);
extern void RenderersBatchersContext_GetMaxInstancesOfType_mEF99113F1507ABC8426119B2F16B92114F19E1B3 (void);
extern void RenderersBatchersContext_GetAliveInstancesOfType_mAB16FC96B0BC9357E0DC9FA279AD4844AE0BBD60 (void);
extern void RenderersBatchersContext_GrowInstanceBuffer_m72EEF32E7D68892D6B6C686290FB074274AF33AD (void);
extern void RenderersBatchersContext_EnsureInstanceBufferCapacity_mE609DC40C454449FDFCD61C0347BF4F4C7CFC395 (void);
extern void RenderersBatchersContext_UpdateLODGroupData_mC3BBC143D600124BC3536CAFE8ADA3D80B9F4E1E (void);
extern void RenderersBatchersContext_TransformLODGroupData_m910C251DDACF06457FAB5E90FFE94CB76C84004E (void);
extern void RenderersBatchersContext_DestroyLODGroups_m2F2BB8BC930C966F0C1FD6392D669D26B2967675 (void);
extern void RenderersBatchersContext_UpdateLODGroups_mCC2A5E08EF4A3A88B195D71F252997FAE8255490 (void);
extern void RenderersBatchersContext_ReallocateAndGetInstances_m75003DE54327AFC9FC9226F543E2AA42ED4CA436 (void);
extern void RenderersBatchersContext_ScheduleUpdateInstanceDataJob_m52A9965BBC3ACB0F00144C8D39E46478543B623B (void);
extern void RenderersBatchersContext_FreeRendererGroupInstances_m1BBD1A75AFD3CED5F347ED940D15EF20D303EA17 (void);
extern void RenderersBatchersContext_FreeInstances_m8D8AFCF6F9AD2F684CBFCD5B9126C77B9BA856E0 (void);
extern void RenderersBatchersContext_ScheduleQueryRendererGroupInstancesJob_mEE8968FD91E2F49D2AE33D4A3D0E8C745FF489E4 (void);
extern void RenderersBatchersContext_ScheduleQueryRendererGroupInstancesJob_m41084D427885CB440E7ACDD227EF915E37B24FA3 (void);
extern void RenderersBatchersContext_ScheduleQueryRendererGroupInstancesJob_m07B14A5915E64E55DB6DEE709DABD8446F320E13 (void);
extern void RenderersBatchersContext_ScheduleQueryMeshInstancesJob_mEF8DDD764AFBFBFA23B708EF823511E8CD748966 (void);
extern void RenderersBatchersContext_ChangeInstanceBufferVersion_m8BDA9E1B471724D930283E832CDC1C4D1172499C (void);
extern void RenderersBatchersContext_CreateDataBufferUploader_mE83CA3760B15FBD7BD9E8166D38C01ACA6DC4385 (void);
extern void RenderersBatchersContext_SubmitToGpu_m7D51CAAFDF4D04FDB49B81F907ADA5C0023909BF (void);
extern void RenderersBatchersContext_SubmitToGpu_m522529681D96803ECD637E642083FD54D8FBAAB6 (void);
extern void RenderersBatchersContext_InitializeInstanceTransforms_m3346F85D05A58656054559FF0D221E5F5D42C813 (void);
extern void RenderersBatchersContext_UpdateInstanceTransforms_m83DE2D5F845C8D5C10B3E6B809BE32E00E1607AE (void);
extern void RenderersBatchersContext_UpdateAmbientProbeAndGpuBuffer_m9635A08E6A72E53938EA2C332B7F37BFD6925535 (void);
extern void RenderersBatchersContext_UpdateInstanceWindDataHistory_m08DA4EE6C170DEA9C8A9B876071CEB4804438173 (void);
extern void RenderersBatchersContext_UpdateInstanceMotions_m597C9A66CF49C8F6A010D5D7D0E866657DA207ED (void);
extern void RenderersBatchersContext_TransformLODGroups_mB0CB4CD84FB8FF1E35821FD3CB869166ED7D5B7D (void);
extern void RenderersBatchersContext_UpdatePerFrameInstanceVisibility_mBD8E7669A22B6C1D47BD0BF3BDC5E22BDD16FBB2 (void);
extern void RenderersBatchersContext_ScheduleCollectInstancesLODGroupAndMasksJob_mD6FC667C7E0C513173E0720521FD54C3A385737A (void);
extern void RenderersBatchersContext_GetRendererInstanceHandle_mF6127D9881FD12DFF2E5AB4132343A50E46E3FE3 (void);
extern void RenderersBatchersContext_GetVisibleTreeInstances_m5C91EC91858A7EA240EF72E870C8C6A14D1FCC7F (void);
extern void RenderersBatchersContext_GetInstanceDataBuffer_m7164DAD5855B34AA94DC599A67E3FCC547C6FC1E (void);
extern void RenderersBatchersContext_UpdateFrame_mCFA782A62647ADD043E3247EFF36079A2426DAD4 (void);
extern void RenderersBatchersContext_FreePerCameraInstanceData_mD63662D47C596B2B718DA97E662CCBB7B4D4D900 (void);
extern void RenderersBatchersContext_UpdateCameras_m894C3BAC8B3BD71A63212C2E49E40C72D46F352B (void);
extern void RenderersParameters_CreateInstanceDataBuffer_m945CE4EF304375414A46DDED06474BFC3132D971 (void);
extern void RenderersParameters__ctor_mD3445734819B50610F768B8E6EF49822D5ABEE8A (void);
extern void RenderersParameters__cctor_m8D5D5734DCF7E98603C17A197EC062D2B1D88F05 (void);
extern void RenderersParameters_U3C_ctorU3Eg__GetParamInfoU7C14_0_mD43A1760BB14DE3AF585F6E664A7641CA2E4560F (void);
extern void ParamNames__cctor_mAEF822BDB14694895783B71D8EACF1EEC9B15C91 (void);
extern void ParamInfo_get_valid_m198E32DB1AAD1F43EAD6964E4EF79E79D078AF7D (void);
extern void ParallelBitArray_get_Length_m82FE0E2AC9FAB29DA67E28FFAEA04EB642955B08 (void);
extern void ParallelBitArray_get_IsCreated_m31B6CFD3C95548F523C3D074463B827B7CD7A535 (void);
extern void ParallelBitArray__ctor_m7BB9EA31D2DF48FD4BE5D8773C539A76C5D4E6E4 (void);
extern void ParallelBitArray_Dispose_m24CBECA125F3D0090E9786E6AF56CB2E6DE452C6 (void);
extern void ParallelBitArray_Dispose_m3806D56E9788D3C69BD6C6985C6E2F949A89F9B7 (void);
extern void ParallelBitArray_Resize_mA268182EEF9B41198BFC3780A74CF12D9B232011 (void);
extern void ParallelBitArray_Set_mEDB30931801E1F71F11F62CCB23F3537EEE0F1EA (void);
extern void ParallelBitArray_Get_m250C22A6191BAF4C5B314EFAF451E391D251F2B0 (void);
extern void ParallelBitArray_GetChunk_m7303392F0138448DF74E7A709F38B500B9461ED2 (void);
extern void ParallelBitArray_SetChunk_m48943193199714BCF0925DD8E14C8EB651885629 (void);
extern void ParallelBitArray_InterlockedReadChunk_m19B068CDEB9686FE2DD8A42BF99D2011EEAFA84F (void);
extern void ParallelBitArray_InterlockedOrChunk_m51A85AD8A6A6FFD317303DEABA6B9797B79CE658 (void);
extern void ParallelBitArray_ChunkCount_mE27E6F3D861AF09C5B31BD7F3964796B26A9C3B5 (void);
extern void ParallelBitArray_GetSubArray_m45E14868BB90EC98D0467ABDA3DAD1BD4BFC49DD (void);
extern void ParallelBitArray_GetBitsArray_m31F100FDDB1EA0FE4E1592768ED14B843D0DD73D (void);
extern void ParallelBitArray_FillZeroes_mDAE3DE6ACB91DE00B5DC5D50415B7A0F938A08BB (void);
extern void ParallelSortExtensions_ParallelSort_m237D06D0D0DA504CE809A6FF2D2CEF9CE0221A08 (void);
extern void ParallelSortExtensions_U3CParallelSortU3Eg__SwapU7C2_0_mDD868A15D4BFD33E6DFF6107497D4EB6EE040E16 (void);
extern void RadixSortBucketCountJob_Execute_mF3ADEB0523C3DE92CB5CFEF01B65E72C9AB7C023 (void);
extern void RadixSortBatchPrefixSumJob_AtomicIncrement_m89775B1090C6296097B6445BC76D2C6BE88F199E (void);
extern void RadixSortBatchPrefixSumJob_JobIndexPrefixSum_m9C47BE4B67FCFF29A8FA94D39589F9B9A2840EE3 (void);
extern void RadixSortBatchPrefixSumJob_Execute_m434849692F7D93EF83545890B59FC96BF14AED93 (void);
extern void RadixSortPrefixSumJob_Execute_mF1969BD6160F81BA429AF74A8944935FC83BC551 (void);
extern void RadixSortBucketSortJob_Execute_mDB0AB3CD468DA898E41CBF3E9EF5BE26AD26E4D2 (void);
extern void __JobReflectionRegistrationOutput__15867191014387474753_CreateJobReflectionData_m61B92F5EF70DF366B7640CA4487293699C2E2A18 (void);
extern void __JobReflectionRegistrationOutput__15867191014387474753_EarlyInit_mCAFBD6F04F7737F01B0CA94B81910948BEB121CB (void);
extern void U24BurstDirectCallInitializer_Initialize_mDEDEEFEB21BD345F172B9A16BF0A65DDCCCC4A4F (void);
static Il2CppMethodPointer s_methodPointers[970] = 
{
	EmbeddedAttribute__ctor_mC74BAF246B6DBDC3E01CA6C01E9F99BEBFB85292,
	IsUnmanagedAttribute__ctor_mECABE2CB94F3EB55D9EBAB15A7E05DF541B049E3,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m1EDC75FA86A9139DD4A9DB0DAEFD85A1B786E080,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mBD0A412FCC9806186BDA6EE066831333F28A3414,
	AABB_get_min_m209EF3ECD01E859258E1BDE8780CA4C25276DF70,
	AABB_get_max_mCD29B3A2C2DEA60BBF50091D8E3C938511D82B08,
	AABB_ToString_mB0C529C757F63FD0F78316C90D2B86206159B678,
	AABB_RotateExtents_mE4E40331E25B5F3310112548831B83F096C98E06,
	AABB_Transform_m1B125B36873D7F03DCBB8817DE20E81FD17C149C,
	AABBExtensions_ToAABB_m367412CA9980E495609B7267A2CC04AE39AF2402,
	AABBExtensions_ToBounds_m7CAFEDB45226C652830072153F7236BA97C6C520,
	BatchLayer__ctor_m5B65286582FB01A261387F6EB54330C8E47C3CFD,
	DisallowGPUDrivenRendering_get_applyToChildrenRecursively_m3E929193F5CE5D66B2FE31D776EC2A2F2116CAA9,
	DisallowGPUDrivenRendering_set_applyToChildrenRecursively_m68D41A53534B142755C97A1E90F85707979EF47E,
	DisallowGPUDrivenRendering_OnEnable_m249A20499E8492DEEBAA51A2F682BAF5832200BE,
	DisallowGPUDrivenRendering_OnDisable_m1F1E89249A83BC47FE22BBA3AFBB3E45AA8A9CA5,
	DisallowGPUDrivenRendering_AllowGPUDrivenRendering_m20DF3D8C370F5104515C565084A83B476625F356,
	DisallowGPUDrivenRendering_AllowGPUDrivenRenderingRecursively_m6A9F55C1FE2690255B488CBD65D4EC7A088795DB,
	DisallowGPUDrivenRendering_OnValidate_m6F127CE57094B0CAE61BA8B4918EECB80E37240D,
	DisallowGPUDrivenRendering__ctor_mE6D1309170C6045938779078895FBDC316CD22C8,
	DisallowSmallMeshCulling_get_applyToChildrenRecursively_m07F3D5D527D2DEF50D7B02D214383B8AF78C4C64,
	DisallowSmallMeshCulling_set_applyToChildrenRecursively_m938F4AF22EDE3BBF3FA63774848026C0D1BE66B7,
	DisallowSmallMeshCulling_OnEnable_m794055654CE760B4FD1780DC20C0C94C84A99A87,
	DisallowSmallMeshCulling_OnDisable_m75A11291A610DED6EADAF48AF38509DE765AA25F,
	DisallowSmallMeshCulling_AllowSmallMeshCulling_m8D38B7DB8F8A05A7839BC00395B7137C4688E996,
	DisallowSmallMeshCulling_AllowSmallMeshCullingRecursively_m8D9B550BDF92C3C920B34AC0DDC65D5B095F9D22,
	DisallowSmallMeshCulling_OnValidate_mF3C1B33DE96CA700BC01B5311F80AE4E99F29DA0,
	DisallowSmallMeshCulling__ctor_m8EE3BCDB2DE6243C9C844C82F454D0845738A51D,
	DebugDisplayGPUResidentDrawer_get_displayBatcherStats_m210D40F2C66835ADDD79B906A5266E0F233D3C34,
	DebugDisplayGPUResidentDrawer_set_displayBatcherStats_m59E27BDE577C32E337F723FEF8F63C6BC686C662,
	DebugDisplayGPUResidentDrawer_GetOccluderViewInstanceID_m726FCBE5E8C19295040CED7A6F87E7F31DCC3CE8,
	DebugDisplayGPUResidentDrawer_get_occlusionTestOverlayEnable_m9A235C5BC833535F37EF6521C8201C3CE29C51A5,
	DebugDisplayGPUResidentDrawer_set_occlusionTestOverlayEnable_mA2EB2A26999F2D9AF42AA8E3E4636C26C2B742EB,
	DebugDisplayGPUResidentDrawer_get_occlusionTestOverlayCountVisible_m634A2AC553EFFABBE7867FF3F849B56854132881,
	DebugDisplayGPUResidentDrawer_set_occlusionTestOverlayCountVisible_mB528723B0E9B869C1A316E1E96A992559BF6F7ED,
	DebugDisplayGPUResidentDrawer_get_overrideOcclusionTestToAlwaysPass_m540F53888B63B89065870B56B8981D23580A82E3,
	DebugDisplayGPUResidentDrawer_set_overrideOcclusionTestToAlwaysPass_m20C4D9DC1E966A9C4C4D0D466F4BD38ADD30AE13,
	DebugDisplayGPUResidentDrawer_GetInstanceCullerViewStats_m0FBAB4D8A7F7B2AD56BABE57E5E0648B5686A85C,
	DebugDisplayGPUResidentDrawer_GetInstanceOcclusionEventStats_m602D50534C866EEB3661DA78C183640EE3224B94,
	DebugDisplayGPUResidentDrawer_GetOccluderStats_mC14EA7475AC1AFC3C3252E433ADB1C537CD934A4,
	DebugDisplayGPUResidentDrawer_GetOcclusionContextsCounts_m60D0D1EBF103C8A3199281D3DC5CD2C318F98CE2,
	DebugDisplayGPUResidentDrawer_GetInstanceCullerViewCount_m92894DC66DDDCC9E86096BBE082D1D306C2A085A,
	DebugDisplayGPUResidentDrawer_GetInstanceOcclusionEventCount_mD84250B5B4DF8C05FAC630C7E60E9E27125E14A8,
	DebugDisplayGPUResidentDrawer_AddInstanceCullerViewDataRow_m9F1A1FB82CC3344A6A3A2DC88D8EF67A67F8A767,
	DebugDisplayGPUResidentDrawer_OccluderVersionString_m7714E7A750D1B3965E3E07AC82B75EDCFFC0A265,
	DebugDisplayGPUResidentDrawer_OcclusionTestString_m1B455D1A286D34A76D5A3F9F0583852B93AEDB4B,
	DebugDisplayGPUResidentDrawer_VisibleInstancesString_m379FD46F020F09A4F6104D0B903B87E6D5CF440E,
	DebugDisplayGPUResidentDrawer_CulledInstancesString_mD981EA1AFC5F7D9E5FE201A576EF96E47FD545E7,
	DebugDisplayGPUResidentDrawer_AddInstanceOcclusionPassDataRow_mD27E269D6934863E7BD1A75433C1DD6F6B080A9B,
	DebugDisplayGPUResidentDrawer_AddOcclusionContextDataRow_mDCD25714EE18DC98C484BC29D6780364F36C371B,
	DebugDisplayGPUResidentDrawer_get_AreAnySettingsActive_m749C09E9C318C2B5073709110F7D2DEFA890D144,
	DebugDisplayGPUResidentDrawer_get_IsPostProcessingAllowed_m18A00DF3C6845F6EB6E85FEF8FAFD3A4458B4DE3,
	DebugDisplayGPUResidentDrawer_get_IsLightingActive_m043DDEA409C65069FE6EB254A58195135068A9A1,
	DebugDisplayGPUResidentDrawer_TryGetScreenClearColor_m8EF35A1BD3CE911D205A4031178454C2354F76FB,
	DebugDisplayGPUResidentDrawer_UnityEngine_Rendering_IDebugDisplaySettingsData_CreatePanel_m742018A2D4F79F94FD7B9A29434307A7519FD155,
	DebugDisplayGPUResidentDrawer__ctor_mC29313F658266A745F986BBC51F16C4AF1287947,
	Strings__cctor_m68ACF98790846338FA3596EE3124ECAFDFC10204,
	SettingsPanel_get_Flags_m748B011E300968BF170B06B15BCA80C9B4A7EF80,
	SettingsPanel__ctor_mC4E295FA1EE20556ED1C2F8876B443177EF1250E,
	SettingsPanel_AddInstanceCullingStatsWidget_mF9C247F7AF56C7FDF3B0D459DF7387E1C209332B,
	SettingsPanel_AddOcclusionContextStatsWidget_mBBDAD684776194C51A2E3010D8A59938BD7F746B,
	U3CU3Ec__cctor_mA36DB0581BF5A4E8003BF3379BC4A176BBB178B0,
	U3CU3Ec__ctor_m795644A9581215B808B0A8ACF217AF65CA840E5E,
	U3CU3Ec_U3C_ctorU3Eb__2_0_m093D8CEBBBCEA344A5E1B90ED8581185C5789207,
	U3CU3Ec_U3C_ctorU3Eb__2_1_m4C96510BFF1B8E053D21EFB7867B53E5158A24DB,
	U3CU3Ec_U3C_ctorU3Eb__2_2_m049FC2A0C266B14BEC7B2D95DD97ECC89013EDF4,
	U3CU3Ec_U3C_ctorU3Eb__2_16_m14E895F5EF397DED57D6677A0F4399A04482B31A,
	U3CU3Ec_U3C_ctorU3Eb__2_17_mF558F22E7A44DC1E2D23BE2D1429DFAC0D9BC095,
	U3CU3Ec_U3C_ctorU3Eb__2_26_m17AC5153064AE16E0E236E377556F79919FC35D7,
	U3CU3Ec_U3CAddInstanceCullingStatsWidgetU3Eb__3_1_mEF4F8D1CC79D1CF85CCC4610DC06BCB76564CE91,
	U3CU3Ec_U3CAddOcclusionContextStatsWidgetU3Eb__4_1_m50ECF46F715AD40AD546A000C171E6BF749A2D57,
	U3CU3Ec__DisplayClass2_0__ctor_m65677D4DD004FEA95CEF164DA1CFCF023CAFE84D,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__3_mD932A7BDF2C4A95ADD0A793D88603399C0A30BBA,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__4_m2BC2F60CB8AF8FD2F1328543162B1E49774712AD,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__5_m8C9D186608F61193AE4207817E1ACDDE6C2726C0,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__6_m721C13571D79B42825FC4C282072ED740F8C9BF6,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__7_mEB15B8079DD79B3B3AA7221B93777D3A7D91B1C9,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__8_m31DDC1A4B0CDD778030C7A154102549CAB2D8577,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__9_mCC4BC12D4DC4F35BD4B7585162E160F09CC8DC27,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__10_m12DA07F4057174A9C51C3490CAB31ABBDBCC65AC,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__11_m6F14E1BC75C6E8D7983FEC6A512122D33B8F5ED2,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__12_m3C7B0AD686A0FD426273990498FF6BA571FFC16E,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__13_m5F4FF7135D0D1BD237D13C77CCA8525F0910867D,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__14_mD4833866F9E3D44ABCD198C96CAA9F73DBF3B674,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__15_mE458635A19DCE0C2C9232654152704448A814E9F,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__18_m4BF1ED906D874B542CCBECE37769C95E0EEEEFE2,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__19_m15765D8BE137D80E15D9E0DFEE2D1702F80D16D1,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__20_m3F3D3E337916110ADA2E9AC04766373D4034E29F,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__21_m7C83A2EA4C56EB3016A830CBD6BC6D7348FB32A0,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__22_m7C34E5A19AEEF6DEA46F39BD18FDCFA72F754EC6,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__23_mA4FC48AC04F73412EB611F97E87ADF7D437F6596,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__24_m3C25B58CBFF7425C921F2CFE1C797A3B3C1A5F83,
	U3CU3Ec__DisplayClass2_0_U3C_ctorU3Eb__25_mC056211293A3B7503C66790B6EC87EBEB2B0A824,
	U3CU3Ec__DisplayClass3_0__ctor_m9D53BA0B98D193D6EC91584B253D22ECFDE1A89A,
	U3CU3Ec__DisplayClass3_0_U3CAddInstanceCullingStatsWidgetU3Eb__0_m9FE17BAAA2BA671C3681DC39225C9FD13BFB3973,
	U3CU3Ec__DisplayClass3_0_U3CAddInstanceCullingStatsWidgetU3Eb__2_mC2A100474B64CA070D26D9A1F2C84420C0ED4A7E,
	U3CU3Ec__DisplayClass3_0_U3CAddInstanceCullingStatsWidgetU3Eb__3_m919970CD51797C207B5B20F4FA9574431116859C,
	U3CU3Ec__DisplayClass4_0__ctor_m4B6078FB02177992D2D89D03034193B119FCC121,
	U3CU3Ec__DisplayClass4_0_U3CAddOcclusionContextStatsWidgetU3Eb__0_m71812BB64272745920C85327299F9D807A592B08,
	U3CU3Ec__DisplayClass29_0__ctor_m9E9E0D304D4857CCC41C81920D8F9B2426B21022,
	U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__0_mC0E8B5021FEC842FDA982AF122F22BB6340D35E9,
	U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__1_m9AE4D495A32845644E11FB2B895DA5E75F8635F8,
	U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__2_m95BA865F2DE7F6DBDD8FCEFE47701070C5AE3F98,
	U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__3_m58F09A1804446AF0F5931AC7196D7BAC039F3EDB,
	U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__4_m3747952920734A24FC5B1F9DCFE309747F9C4C4D,
	U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__5_m74C8AB5E55872D1160D6F08B78F48718683BF499,
	U3CU3Ec__DisplayClass34_0__ctor_m51E792F40771F45B88ABD1EE79EF175CCE76CD48,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__0_m27FB30AE227BED3DFA61625B55C8EBE3082D04C9,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__1_m1E2FEB11D28E7B45D586D165C7B58E8C799C2989,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__2_m4652608ED9683E21291FE5A1F074C1DC6235627B,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__3_m497AA999AF8B5BAC82C1131C80256A58497A0190,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__4_m94EE7C495806664154E3E3BDC30518483010AEF4,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__5_m8BF230F649C38952F63793E85645803C3E72E468,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__6_mAA756A602DE4CAA7F4F3ABF323D5D6A41FADF620,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__7_m111803CA6822911B48D35155FF2DE904987938BF,
	U3CU3Ec__DisplayClass35_0__ctor_mD214B5A8783451B94F4590004B1FE5F08EC6D092,
	U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__0_m4C70823339569EBDDFBBE48D1E3EA1501DADE74D,
	U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__1_m28A3FB09CA136E909008FFC0989CF96328E6EBC6,
	U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__2_m711316387059484363D585616C8D01248FCA7EED,
	U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__3_mA0FC30C2542A04A04E4E3C75BFDF01746FCCAF7B,
	Line_LineOfPlaneIntersectingPlane_m0ED39FE7E09D672A03C93AA51883CECB1FFF2EF8,
	Line_PlaneContainingLineAndPoint_m59813FE596901FCDFCA7E64A83841D14AB030402,
	Line_PlaneContainingLineWithNormalPerpendicularToVector_m34DB45E98946F82ECB81242D6CE074AEF43E0CF0,
	ReceiverPlanes_IsSignBitSet_m115EC872F6B26CBF654D849659242E76E7850A09,
	ReceiverPlanes_LightFacingFrustumPlaneSubArray_m995D1E681E0ED63DFA8489C582A2FCBAF1C9356B,
	ReceiverPlanes_SilhouettePlaneSubArray_m851F735918EE9297C64815C42AAD446BCBE55F4B,
	ReceiverPlanes_CreateEmptyForTesting_m31C17EE66110F80DE225AC49E8AC79ED4D838ADB,
	ReceiverPlanes_Dispose_mE53DCBAD81DDB002760FC5661F3B26330B62ACF4,
	ReceiverPlanes_Create_mB53449C117AA6108D5F00315F573CE710945A81F,
	FrustumPlaneCuller_Dispose_m802E9F4605F17D55D46C6C0D308830667DE0A08B,
	FrustumPlaneCuller_Create_m4DD061D5BCFFFDBE074596E2373150D4787331F3,
	FrustumPlaneCuller_ComputeSplitVisibilityMask_mCFC42DB8BADE15AB4A87F4BC60A3B5D7507745DC,
	PlanePacket4__ctor_m1B67A6C35FB1B6B6BE21D76B496DA3531F636CC7,
	ReceiverSphereCuller_CreateEmptyForTesting_mC63AFF1A7B2CF647E5A605E41125BA5E5533AE0B,
	ReceiverSphereCuller_Dispose_mFCC83320D29032DBC7A3173ADFE8F764A472FFA5,
	ReceiverSphereCuller_UseReceiverPlanes_mEA2EE8FE32563F3FBE40D6AADA9724719186674E,
	ReceiverSphereCuller_Create_m63514E053CF0DD1CD37702E56C84F4F0408A8B00,
	ReceiverSphereCuller_DistanceUntilCylinderFullyCrossesPlane_m1E5B038B031E5090A38EE0FF6EFC405E4BDD63E1,
	ReceiverSphereCuller_ComputeSplitVisibilityMask_m1188332DCE2CBF80B1A431AFB1BD49955898936F,
	GPUResidentBatcher_get_batchersContext_m832BD0381846BE2E11D425CE1319953A411D64CE,
	GPUResidentBatcher_get_occlusionCullingCommon_mCC135850F6B339139CBA5E60715E4B059195CBC7,
	GPUResidentBatcher_get_instanceCullingBatcher_m9E7BF9BA5E63072AB043C86836AC4C036AD5D465,
	GPUResidentBatcher__ctor_mE43D45A4CCB93B7DD21D5FB49C4D6BA16DEC72B2,
	GPUResidentBatcher_Dispose_m027050D276B285BED80401912FB6B3D0BADC90AD,
	GPUResidentBatcher_OnBeginContextRendering_mFCE0FA21DAB11BC5B770CB5C63F14C32B2ECC30F,
	GPUResidentBatcher_OnEndContextRendering_mDC1CCC712A83C48E61D34E56BE21B5458C77BB61,
	GPUResidentBatcher_OnBeginCameraRendering_m818CA5BB64950FFCF566414C70CC5D8B6D96A922,
	GPUResidentBatcher_OnEndCameraRendering_mC31C1F8395CEED9B4AD6321DACC36E9115666885,
	GPUResidentBatcher_UpdateFrame_mB0B6728FE5D08E785FD7747D9AF4D065705D944D,
	GPUResidentBatcher_DestroyMaterials_m0BEE5AE70E666A3E35E16EA8D941480E1351A243,
	GPUResidentBatcher_DestroyDrawInstances_m9513A335709809ED850D1AF76E2A6FFCB8707AF9,
	GPUResidentBatcher_DestroyMeshes_m4B3BF017904878023774926DE457D2476283B094,
	GPUResidentBatcher_FreeRendererGroupInstances_mB2929BF0DE2234256EB75CC078DE8441CA9594E9,
	GPUResidentBatcher_InstanceOcclusionTest_mCDEFBDADDB90AA06A2E1394B5B80E807CBB5AA43,
	GPUResidentBatcher_UpdateInstanceOccluders_m5570E1F11C12314DD68EA4A1B9B4EE0473FD6A18,
	GPUResidentBatcher_UpdateRenderers_m08752E00B533B9709C0EF2892D843BF10D24920B,
	GPUResidentBatcher_SchedulePackedMaterialCacheUpdate_mB5D6DC79D682FEAA1A5F68BEB685132B1F549881,
	GPUResidentBatcher_PostCullBeginCameraRendering_m9C006EAC8C65FBC1B7DD3F9F4123E071EC1D2F44,
	GPUResidentBatcher_OnSetupAmbientProbe_mDCAD4C9AE21158471F846321EA2A58DBDA1914A6,
	GPUResidentBatcher_UpdateRendererInstancesAndBatches_m7CC59149342BCCB17DE20FCD2BF0294D613B48B9,
	GPUResidentBatcher_UpdateRendererBatches_m7FDCEBC6D9743BA42ED99E545EBBF438702B56DC,
	GPUResidentBatcher_OnFinishedCulling_m3894D01381A5290564E27DC8315623AD4B21975D,
	GPUResidentBatcher_ProcessTrees_mA20D5412045E8AE5E485DE8F50B350F0A88538B7,
	GPUResidentBatcher_UpdateSpeedTreeWindAndUploadWindParamsToGPU_m1E26807F0F67557341970AC983776178A0E90490,
	GPUResidentDrawer_get_instance_m142CE6BEC88AA7FA34052B0138128C3B944FEBDD,
	GPUResidentDrawer_IsInstanceOcclusionCullingEnabled_m03F098AAAA5FCB8140B53C641EB2B0381669BC8E,
	GPUResidentDrawer_PostCullBeginCameraRendering_m3EB60CDFBF342ABD0B11B30439BB01B7CD6F1F77,
	GPUResidentDrawer_OnSetupAmbientProbe_mF67DC77B41AD752F71A25EA4221AA3180AA236CC,
	GPUResidentDrawer_InstanceOcclusionTest_m0DD4F0A4685967C617984FBCE5A0B99A35790AFE,
	GPUResidentDrawer_UpdateInstanceOccluders_mD2F1BAB128CEB6B6A731FEA876A1E08A31C98B30,
	GPUResidentDrawer_ReinitializeIfNeeded_mE8A70A9A6B9C8D4A341552E05D95E4D74B7D68D5,
	GPUResidentDrawer_RenderDebugOcclusionTestOverlay_m17664202C62084572F6037B5F1C61E2FE8C0BFD0,
	GPUResidentDrawer_RenderDebugOccluderOverlay_mB7819CD0C90F839351CE854B2DD297D14F5F830B,
	GPUResidentDrawer_GetDebugStats_m857EE673158C860D3471D0CC6203B60D0BC98B4D,
	GPUResidentDrawer_InsertIntoPlayerLoop_mBDEE8B11EE73F12439561D73E4A2A3C8D6861007,
	GPUResidentDrawer_RemoveFromPlayerLoop_m18F5D085D7C67EEA7EFDB5ABC52AB8C343CA5CAF,
	GPUResidentDrawer_IsEnabled_m03CFAB6E2CE8D71361F5223C940F4C0A785A1116,
	GPUResidentDrawer_GetGlobalSettingsFromRPAsset_m4710F7D4D983AEB1ADD00A40E7E4068C330C9A41,
	GPUResidentDrawer_IsForcedOnViaCommandLine_mC2F9713BA2691A02C23E22E59DDC30E41289539F,
	GPUResidentDrawer_IsOcclusionForcedOnViaCommandLine_mA0CD2D200E26C820386D92E58645EF2FF0B02FDA,
	GPUResidentDrawer_get_MaintainContext_mC393C718E2CF175293B7B1E86C7C70A5AC6D046C,
	GPUResidentDrawer_set_MaintainContext_m4A9032B2B6255EDC8EE357CA1D9195F8D8CD9885,
	GPUResidentDrawer_get_ForceOcclusion_m6C97AA5F01C6062E108460F76FB92745C07CB4CC,
	GPUResidentDrawer_set_ForceOcclusion_m0DCC2179BB8525CA02EB0E70D6C7AE21D03228D6,
	GPUResidentDrawer_Reinitialize_m542E6537EC9C14A35291824BA6798D5D0D747190,
	GPUResidentDrawer_CleanUp_mF773237C2F3AEF0251249FFD56C02F7A650EE9C2,
	GPUResidentDrawer_Recreate_m09E096E3492D77EE4A3D0D070FA53D2017AD6874,
	GPUResidentDrawer_get_batcher_m03715B9C280D664F90B0B1F592D9C3ADD212F9F3,
	GPUResidentDrawer_get_settings_m3F0472441E9F1191B0E0FC43B6D8BBF004EAF3C6,
	GPUResidentDrawer__ctor_m3B65B01D5C54231BF2D7C4C65B4FA11DDA8CCA1A,
	GPUResidentDrawer_Dispose_mD5709371AD0309C33F25511B22C7C1DCD6AC234D,
	GPUResidentDrawer_OnSceneLoaded_m4C2686BC1182C9327DBA362D670CB30601292F5A,
	GPUResidentDrawer_PostPostLateUpdateStatic_m2322043F8B8734792788BF29370233B9BFFBFF7F,
	GPUResidentDrawer_OnBeginContextRendering_m383B0726811F68E670753BDD5F4EE772DE4593C0,
	GPUResidentDrawer_OnEndContextRendering_m4E432047D9A70FD9FE8718FB195A9477A657857A,
	GPUResidentDrawer_OnBeginCameraRendering_mDCB0CA5A9CB1F1BA25DB7214D3BD75975AA2B705,
	GPUResidentDrawer_OnEndCameraRendering_m1F301D2884DCFB5ADD5CB533FEC803B898EDC690,
	GPUResidentDrawer_PostPostLateUpdate_m94401477ABD5387DBAAD1D6A1CC39E59AE1E2EEB,
	GPUResidentDrawer_ProcessMaterials_m5F9A91DF336FD3CA1CFD81608475C39F85FD89D5,
	GPUResidentDrawer_ProcessCameras_m9F6D7A6DB3E4162F0C6B2D0E8B712876B3D56640,
	GPUResidentDrawer_ProcessMeshes_m254F0CB1EB50564FF5FCC3CDD00420784BB45C04,
	GPUResidentDrawer_ProcessLODGroups_mFDBD9F9CD5F13FDE5AEC4817644BC1BEF6D71D7C,
	GPUResidentDrawer_ProcessRendererMaterialAndMeshChanges_mA6E500BC36021FA83526CD3495E5800A4084F6CC,
	GPUResidentDrawer_ProcessRenderers_m381BBD34CA5CDCABD580BD2464CBA98ABDBC9E87,
	GPUResidentDrawer_TransformInstances_m20475CB2F2401DD9A54661E4EA63ACC2A5D72B49,
	GPUResidentDrawer_FreeInstances_mDCFFEBAB0E14151660E84E08BEC9AF91F149F611,
	GPUResidentDrawer_FreeRendererGroupInstances_m038FBB93EAF3C06AA447CBBC04E2F9CEC8675814,
	GPUResidentDrawer_AppendNewInstance_mEB1EA724EECB7E96FB4A378582A650ABBD8E635E,
	GPUResidentDrawer_ScheduleQueryRendererGroupInstancesJob_mCC2105095C5D0AB94F74B0DF5033C72BF8F64E21,
	GPUResidentDrawer_ScheduleQueryRendererGroupInstancesJob_m059C106F6838578EB820B0854214AD8E52414C43,
	GPUResidentDrawer_ScheduleQueryRendererGroupInstancesJob_mC87594A6FDDAC454AA8ED09E3F975236FDE87F01,
	GPUResidentDrawer_ScheduleQueryMeshInstancesJob_m63420C906AFFC9D2431B6FD4B71BEBC19F6B902C,
	GPUResidentDrawer_ClassifyMaterials_m07623E4BDE7E899A29EB6E9A9DA2B9FA78ACC722,
	GPUResidentDrawer_FindUnsupportedRenderers_m1C0CB02546C7733938983343B8668CAC84E60772,
	GPUResidentDrawer_GetMaterialsWithChangedPackedMaterial_m112BC45C2CEA7D3B26EDE1B217430AA49E5EE0E8,
	GPUResidentDrawer_FindRenderersFromMaterialsOrMeshes_m8D69EF84FB8C5830ACB7E1261331459DF4DEE8F6,
	GPUResidentDrawer_IsProjectSupported_m8070103F51F3975E8D573377D445553710EBA457,
	GPUResidentDrawer_IsProjectSupported_m4F5CDE5F81A0BE7CF42E37F1545E85A8C5A07DE3,
	GPUResidentDrawer_IsGPUResidentDrawerSupportedBySRP_m24CCB3D5623CD94D8DA06D20CE59806BC9D35922,
	GPUResidentDrawer_LogMessage_m7FF3E65D1A87DF183F9D29254AE637D842B88D41,
	FindRenderersFromMaterialOrMeshJob_Execute_m7B9713786ED58196A531F44ECABD1912DF45E59F,
	Strings__cctor_m40AEC7C35446DC97C6BA1EFB06EA5B4F5CAADAB4,
	GPUResidentDrawerBurst_ClassifyMaterials_m9FEDC6820FD183791F7BD2B682CFFFBBF9DC9F40,
	GPUResidentDrawerBurst_FindUnsupportedRenderers_m927B5E54923278315B6256EA8178A34AA566B4BD,
	GPUResidentDrawerBurst_GetMaterialsWithChangedPackedMaterial_mC2D4AB98974823074A7E8543ED7A3D5BF7DB26FA,
	GPUResidentDrawerBurst_ClassifyMaterialsU24BurstManaged_mB0D38D1C45E45E052FB7EDFF5FC730712F97C388,
	GPUResidentDrawerBurst_FindUnsupportedRenderersU24BurstManaged_m342114EE5DA2CE7AFEEE16D6E6A0D5D4D0E8A070,
	GPUResidentDrawerBurst_GetMaterialsWithChangedPackedMaterialU24BurstManaged_mF321C2281AE3F7783E5F716EF61481A4506456A6,
	ClassifyMaterials_000000DFU24PostfixBurstDelegate__ctor_m8B9A48425138F06CB91FCFB9847C7EDADE8D6C8A,
	ClassifyMaterials_000000DFU24PostfixBurstDelegate_Invoke_m504925CD32DA1CECE183ACB073AA5553C9BE1335,
	ClassifyMaterials_000000DFU24PostfixBurstDelegate_BeginInvoke_mAC098DDFC973A085C59587D110D134BAA7473483,
	ClassifyMaterials_000000DFU24PostfixBurstDelegate_EndInvoke_m2BED6EE11407587C5183C5A1D9B8FAE535CFD783,
	ClassifyMaterials_000000DFU24BurstDirectCall_GetFunctionPointerDiscard_m663B584727686396EF2384B509802C8A1BA5BA75,
	ClassifyMaterials_000000DFU24BurstDirectCall_GetFunctionPointer_mCD9F3B28E3FDA82041695F6A56B92F9147DB64C7,
	ClassifyMaterials_000000DFU24BurstDirectCall_Invoke_m2D913B510E274F214E285E47F08F53EED9AF2ED5,
	FindUnsupportedRenderers_000000E0U24PostfixBurstDelegate__ctor_m3F0E6807A0453A2A7E093071DF88FAF68F7F9769,
	FindUnsupportedRenderers_000000E0U24PostfixBurstDelegate_Invoke_m2D4B8679CE724602FCAEA81B61CC602063D4F8BA,
	FindUnsupportedRenderers_000000E0U24PostfixBurstDelegate_BeginInvoke_mAAA32C4ACDF77CD3EC117A715B369E60E18AF7FC,
	FindUnsupportedRenderers_000000E0U24PostfixBurstDelegate_EndInvoke_m5C0F442E260CBC350130C233C27C10284D3B9658,
	FindUnsupportedRenderers_000000E0U24BurstDirectCall_GetFunctionPointerDiscard_m020B1C641A13BE1485101DF5EEAAF71F571DD613,
	FindUnsupportedRenderers_000000E0U24BurstDirectCall_GetFunctionPointer_m3A9AFB78D0FA58D6EA215476C00FC86EFF91E11E,
	FindUnsupportedRenderers_000000E0U24BurstDirectCall_Invoke_m1AF7B065A13D19EEDA1D73DC030A23EFA4E2DE3E,
	GetMaterialsWithChangedPackedMaterial_000000E1U24PostfixBurstDelegate__ctor_m0BEB2507613A8C323F0511A47FF8DE9A23ADBEB2,
	GetMaterialsWithChangedPackedMaterial_000000E1U24PostfixBurstDelegate_Invoke_mBD1AEBBE568EAA4C06BBDB38854175C80EF86E89,
	GetMaterialsWithChangedPackedMaterial_000000E1U24PostfixBurstDelegate_BeginInvoke_m777BD7D6F9CFD660C0B451792CFA30D1ECF4848B,
	GetMaterialsWithChangedPackedMaterial_000000E1U24PostfixBurstDelegate_EndInvoke_mC41CB4CA8CCC693E7416D5C4BAA8AEEB20E39B05,
	GetMaterialsWithChangedPackedMaterial_000000E1U24BurstDirectCall_GetFunctionPointerDiscard_mAD7975413E8F82BDAFA4DBA525CBC94380434275,
	GetMaterialsWithChangedPackedMaterial_000000E1U24BurstDirectCall_GetFunctionPointer_mE8783CB36E722760B676A4C5EAADC88C985C08D9,
	GetMaterialsWithChangedPackedMaterial_000000E1U24BurstDirectCall_Invoke_m0BED1FBE42E636941F4D317E8BD58807B91AAEDA,
	DebugRendererBatcherStats__ctor_mAE82DDFAB36DFF39F4B28D1D43B5896042B250B0,
	DebugRendererBatcherStats_Dispose_m76B62E89A85CAA6D7108200B3C4BDAF9DB4832E7,
	GPUResidentDrawerResources_UnityEngine_Rendering_IRenderPipelineGraphicsSettings_get_version_m136CCE7FDEBC9741FDCAB827F74D51A7B7DF6E07,
	GPUResidentDrawerResources_get_instanceDataBufferCopyKernels_m4821367D99C54C654F6FC0F677D53038EACAEB40,
	GPUResidentDrawerResources_set_instanceDataBufferCopyKernels_m126688C4BE7907A678DE23AF2E3332DD8A34827A,
	GPUResidentDrawerResources_get_instanceDataBufferUploadKernels_mA900FCDAA87450DEBC3C134E015FA14685ADA9EA,
	GPUResidentDrawerResources_set_instanceDataBufferUploadKernels_m54B6A238CECBBA7093D5D1FEDA9B2C8E56332678,
	GPUResidentDrawerResources_get_transformUpdaterKernels_mCA7A4849EFDC13E448339A6AEC42065FDAB5C63C,
	GPUResidentDrawerResources_set_transformUpdaterKernels_mC87744E70D4108F953B0C637C483AA5ABE685709,
	GPUResidentDrawerResources_get_windDataUpdaterKernels_m2C5FADD001A37D11A324FE865E925CD9A5501315,
	GPUResidentDrawerResources_set_windDataUpdaterKernels_m7828D337715FCE91AFEC15C34DAC0F61753A725F,
	GPUResidentDrawerResources_get_occluderDepthPyramidKernels_m7006886C18CF45076331E4B6114CA37A3CE69532,
	GPUResidentDrawerResources_set_occluderDepthPyramidKernels_m9103835307DA4F40F0439903A0E7DF5C8712B704,
	GPUResidentDrawerResources_get_instanceOcclusionCullingKernels_m0096BB5665B29E5552385CC7C4990DDF95C6EDB1,
	GPUResidentDrawerResources_set_instanceOcclusionCullingKernels_m06138322ED0CC1A22774E0D41C74B4CE691BFFEE,
	GPUResidentDrawerResources_get_occlusionCullingDebugKernels_m8B7B3517326F40890A0935A0DC1DD55C8B14F164,
	GPUResidentDrawerResources_set_occlusionCullingDebugKernels_m94C497F616A9D90161CFEA216A07029CC55D0D27,
	GPUResidentDrawerResources_get_debugOcclusionTestPS_m0A869F58FF84A5B43E925DBE72A100212D672BF2,
	GPUResidentDrawerResources_set_debugOcclusionTestPS_m2B0F9F3EC01C30490B37C40D1BACDB2919E88ACD,
	GPUResidentDrawerResources_get_debugOccluderPS_m476766B8038CC61693711BEAB81BD5B65C95D9DD,
	GPUResidentDrawerResources_set_debugOccluderPS_m4A969CD0B817583E1A089F6C636A28F8F9F32835,
	GPUResidentDrawerResources__ctor_m9A9FBC773137C24968523F68F1ED3D55922BAF1C,
	OcclusionTestMethods_GetBatchLayerMask_m1CC038C215B2531DDD0A4C8AF03E2DC518A43D09,
	OcclusionCullingSettings__ctor_mE814849AC60B1DC1AC17E02F1AF2128DF38FE95A,
	OccluderSubviewUpdate__ctor_m482C60BE94ECE0B1F15E930936345D9F60E399A8,
	OccluderParameters__ctor_mBBA1CD9856BD109C9F1D18715B28E2DA95CE83D9,
	NULL,
	NULL,
	NULL,
	IGPUResidentRenderPipeline_ReinitializeGPUResidentDrawer_m6D9AB828C92C7E97A8B441028C3056A905005E3F,
	IGPUResidentRenderPipeline_IsGPUResidentDrawerSupportedBySRP_mFED552B69E782E4125B03C0EC1B2007FEB023553,
	IGPUResidentRenderPipeline_IsGPUResidentDrawerSupportedBySRP_mAC46B52099ED2E34F12F8B7E802DC67E0113A0A9,
	IGPUResidentRenderPipeline_IsGPUResidentDrawerSupportedByProjectConfiguration_m5CE31EEE661C5F8B23636A4F3452CEA8A7AC0B66,
	IGPUResidentRenderPipeline_IsGPUResidentDrawerEnabled_m4B17CE9EBDCEADAB2C6378F8DACE655817DD1757,
	RangeKey_Equals_m05E612C122D91758588CEB7529751045E6F09493,
	RangeKey_GetHashCode_mC38845B6A1CC657D6F6B1149E7448AA6A3EF3257,
	DrawKey_Equals_mBA2838871085C8944B81322DC6E851F4273E8E91,
	DrawKey_GetHashCode_m6E3C5F3D42D02D8035AA7E7B5501FDBC1551F4E5,
	BinningConfig_get_visibilityConfigCount_m0B698DBD65F8147B56C14CA7CFB741EAB701B3B9,
	AnimateCrossFadeJob_Execute_mA3C0021BE25AE2F67FD93948B6115ECE990DAFE9,
	CullingJob_PackFloatToUint8_m7E24A8A8334FF990F67C75BCE8BB991037645230,
	CullingJob_CalculateLODVisibility_mF87528623E251C0B920199D5DFB1021842A5F774,
	CullingJob_CalculateVisibilityMask_m285DA7E28A8582C95BAE2C6794C34FC3F8F195ED,
	CullingJob_ComputeMeshLODLevel_m0CF5B90B965B46AA61BD4B00EF15C1528BF03A0D,
	CullingJob_ComputeMeshLODCrossfade_mF911187408B6601080C28F1227FAF60DE832B750,
	CullingJob_EnforcePreviousFrameMeshLOD_m20C1DF1641349AC2B1B455E7A49D0DE6BC3497B2,
	CullingJob_Execute_m172CC218FA8F7198BD69C1C87294FD90D94DB884,
	AllocateBinsPerBatch_IsInstanceFlipped_m5ED8B8D355142D10DB3983D15A256B5DBD4D6ABA,
	AllocateBinsPerBatch_IsMeshLodVisible_m9D1674B4CE766557E8D2234D56DF2BACC2EDB576,
	AllocateBinsPerBatch_Execute_m8244D8F2860ED03B023A6D348D9471F2AF76E0F1,
	PrefixSumDrawsAndInstances_Execute_m8EADFE8897EAE00866B67F8AE64CA774EE33B5F9,
	DrawCommandOutputPerBatch_EncodeGPUInstanceIndexAndCrossFade_m6EFB0D6BB29BF2F65B66913BD2F82E4364ED8830,
	DrawCommandOutputPerBatch_IsInstanceFlipped_m1AE2BD53A7D17783F262A94539417F423FD4CF79,
	DrawCommandOutputPerBatch_IsMeshLodVisible_mF345AFA69786A2EB8E65B25BFDB933A6321F1054,
	DrawCommandOutputPerBatch_Execute_mF995D35E2E640A3172F4BC62E37BF103CFBBBBA7,
	CompactVisibilityMasksJob_Execute_m7D5B39DEFEC4944D35111B4B7D22267147B4AB1E,
	InstanceCullerSplitDebugArray_get_Counters_mBDB8C933AEEB9F934132442CA20FC54A9E9A01AB,
	InstanceCullerSplitDebugArray_Init_m0E461744D899802E8123D32B79AD2EEC7FF1ED66,
	InstanceCullerSplitDebugArray_Dispose_m0FA405A9BA291E560088D998A71EE3FEF95100C1,
	InstanceCullerSplitDebugArray_TryAddSplits_m8F0CAC17CB3DA747A187760D3961255C1E95BDF5,
	InstanceCullerSplitDebugArray_AddSync_m05D0080857890E3C8E46730B1D4DC17E4FB71D90,
	InstanceCullerSplitDebugArray_MoveToDebugStatsAndClear_mAA78838CCD17348C01F115022EF530C3F5B98AD4,
	InstanceOcclusionEventDebugArray_get_CounterBuffer_mF25E7B744518F980443AFCF79E48EA3CCF852D04,
	InstanceOcclusionEventDebugArray_Init_mF8AEF95440DA22ACC2DB5717CEBD9C8B8B3797D5,
	InstanceOcclusionEventDebugArray_Dispose_m2D57744F19C60D3E9B6EFDF81BB08498853E37C1,
	InstanceOcclusionEventDebugArray_TryAdd_m2FD080E0DDCA404C8C362A5933EC852A9CD5F109,
	InstanceOcclusionEventDebugArray_MoveToDebugStatsAndClear_mFEF02F07D15F2F2942FABFD0C7C577FAB1859977,
	Info_HasVersion_m9E077F93A4F89E728DF4FA88764ABF91C732D960,
	InstanceCuller_Init_mDBBD40C7316EBE16746FACA43170B7E1001EC67F,
	InstanceCuller_AnimateCrossFades_m5BE8DFEE24C03638DF57E12C23D4C2C80DE8B510,
	InstanceCuller_CreateFrustumCullingJob_m503045810FE1E550B3F436FD62750A505A4EAE3C,
	InstanceCuller_ComputeWorstCaseDrawCommandCount_mF523E170BDA54D6E88AD73742065FD88C60937DD,
	InstanceCuller_CreateCullJobTree_mF3C76EB7E9663B657C8C7F9ACA2E0995CF5D8A6B,
	InstanceCuller_CreateCompactedVisibilityMaskJob_m3A2F38959953B889E5E7456DE426ABA7134DFB9B,
	InstanceCuller_InstanceOccludersUpdated_mCA7D8171B44CF76E6A48314D7F97F2B698DCCE05,
	InstanceCuller_DisposeCompactVisibilityMasks_mE05A5A23578160F5FDE1F8F2E59B7B8E2819BC76,
	InstanceCuller_DisposeSceneViewHiddenBits_mB8548C25259A3AE546942634429EF581779992BD,
	InstanceCuller_GetCompactedVisibilityMasks_mED15105E7567F79042B542B56B4DE2ABEDE14C26,
	InstanceCuller_InstanceOcclusionTest_m8E12352E27A10F62B9C737C3C7D0394DBC416F15,
	InstanceCuller_EnsureValidOcclusionTestResults_m22DA66230772FAE8FA78B7E2B3839AB414BE8A22,
	InstanceCuller_AddOcclusionCullingDispatch_mDA6F922B0734577B8F921E9AE4C02CEE4F8A32A5,
	InstanceCuller_FlushDebugCounters_m1036A68627B296B0CE88254424A25EEA430BCCE6,
	InstanceCuller_OnBeginSceneViewCameraRendering_m9F271E3A4A411E077FC767C85C1813F41C9ADA3D,
	InstanceCuller_OnEndSceneViewCameraRendering_m01D33D11A4853EB0397A2996F10795662828C2F8,
	InstanceCuller_UpdateFrame_m9B437CF5FE15217CD1652135B256DFECE114418C,
	InstanceCuller_OnBeginCameraRendering_mB7CDBC0AD43EEC324B9584B96C25DF3B911EC907,
	InstanceCuller_OnEndCameraRendering_mF0DF0261BF1978BAFAC10F5F9DB533FC86B8089B,
	InstanceCuller_Dispose_m7C8B413AE9FD5A5C4EAD81A22D2D4C1F2E6C368F,
	ShaderIDs__cctor_mBB02927959BF46D5749EF53F56D62D1C6736FEF2,
	InstanceOcclusionTestPassData__ctor_m73F58387AFEBAB53120CC7AEF289EC50A82AFC8C,
	U3CU3Ec__cctor_m5495172327D2C037C7A396EB43420AD62D33BEBE,
	U3CU3Ec__ctor_mBE453CFF0731A8063B406EBCFB4E99157CE39C80,
	U3CU3Ec_U3CInstanceOcclusionTestU3Eb__28_0_m018A8024AEBFAC4FDD9DEABAB3FE56C3E2516CCC,
	InstanceCullerBurst_SetupCullingJobInput_m48DA62BB1FCE439FD65DE65952C97249ECB28C56,
	InstanceCullerBurst_SetupCullingJobInputU24BurstManaged_m029BAC91A512650E568924D2E203B6D7169B0FD6,
	SetupCullingJobInput_0000013FU24PostfixBurstDelegate__ctor_m0C793C802CF5D1A0F93CB9ED0CEDDDC88F4E1056,
	SetupCullingJobInput_0000013FU24PostfixBurstDelegate_Invoke_mF1051A9A19DE6F6EC13C0237CF169949C832D29C,
	SetupCullingJobInput_0000013FU24PostfixBurstDelegate_BeginInvoke_mE119DD748F0E770FAAD051D34717CC98406D1758,
	SetupCullingJobInput_0000013FU24PostfixBurstDelegate_EndInvoke_m4673B6E8DF15C4DC7325F995018BFEEDBBCB13B5,
	SetupCullingJobInput_0000013FU24BurstDirectCall_GetFunctionPointerDiscard_m456373FD45FD288FFC5D9A3899AB3A11424CD41D,
	SetupCullingJobInput_0000013FU24BurstDirectCall_GetFunctionPointer_mCE2F7319E705DCD79CAF8F501320858FCBFDB0DE,
	SetupCullingJobInput_0000013FU24BurstDirectCall_Invoke_m6F7F3A755EBAC8F64AF6083005924EB0A7703E06,
	OnCullingCompleteCallback__ctor_m77440340DEF8EC177F2367F9CDFB4C7039B109CD,
	OnCullingCompleteCallback_Invoke_mC230E2F011722DC958EFACC67609C75FFB0A54C8,
	OnCullingCompleteCallback_BeginInvoke_mAA8DD729BE78EF7C06DC33D3714B306D8793E492,
	OnCullingCompleteCallback_EndInvoke_m2ABE1C345A8D041A537AD05BE2B8632D872865A0,
	InstanceCullingBatcherDesc_NewDefault_mC543DB9EBF6418504763D8C66FCD457AC5A8B9AF,
	PrefixSumDrawInstancesJob_Execute_m6384DA1CB6A1E053F358640B9E2FE7F378677771,
	BuildDrawListsJob_IncrementCounter_m2198A8B6F4713D070C44BF162EEAC564C15A120F,
	BuildDrawListsJob_Execute_m731DC4DAB67702B447641FF630CD1C60A596636E,
	FindDrawInstancesJob_Execute_m011CC04D8F9DA0E7E3F04783AA60964898BEDC1D,
	FindMaterialDrawInstancesJob_Execute_m0A8C723C617595319217DB5AFD151AC28F7759A0,
	FindNonRegisteredMeshesJob_Execute_mC7CF993F6E8C86B01D3FE339DB98E19D5A6D25BD,
	FindNonRegisteredMaterialsJob_Execute_m45EED4BFBE595919A846376A102DE6DBE15BD404,
	RegisterNewMeshesJob_Execute_mE945F1F57AB8D7AD4E3450E937E7955501E12427,
	RegisterNewMaterialsJob_Execute_m2C03AA455A70CA6262A10E28A88752C1A9BBB45E,
	UpdatePackedMaterialDataCacheJob_ProcessMaterial_mCFF81645A1DB74D08DAAD17D679FA00A306E57E3,
	UpdatePackedMaterialDataCacheJob_Execute_m117BE8C4EEAC8E86F3445464E84ECB6A954A3EBB,
	CPUDrawInstanceData_get_drawInstances_m5B182A75D8968C69C7EFF2CE773D0029E7412D68,
	CPUDrawInstanceData_get_batchHash_m407E1D37D4A5D8BDE76DE45CF33C53643DA47C12,
	CPUDrawInstanceData_get_drawBatches_m6E62CEC9E106C2CB84B185517A04105E77D875F6,
	CPUDrawInstanceData_get_rangeHash_mF69F0BF1AFACDC62B7466A99F24DBE460E4BB9B6,
	CPUDrawInstanceData_get_drawRanges_m980D02096E99CCD5299D5B06AAEC78E986A92754,
	CPUDrawInstanceData_get_drawBatchIndices_m414A7D8E605519E4B6F5F3B1020BE3B21F991E29,
	CPUDrawInstanceData_get_drawInstanceIndices_m22402BAB913DE193060C175FF253FC5C3D8D0D56,
	CPUDrawInstanceData_get_valid_m224B4D6D342C4EAC30B8F8A5A34837827DEDCE3E,
	CPUDrawInstanceData_Initialize_m1D889D823B6B72F24F03385CBC492D3B18B510E4,
	CPUDrawInstanceData_Dispose_m0F6E3A9F7C46E04F0C1A5073F3C3039BD1E29D3B,
	CPUDrawInstanceData_RebuildDrawListsIfNeeded_m8CE9E2B0870BADE0F90B83C54ED6A206B39F5248,
	CPUDrawInstanceData_DestroyDrawInstanceIndices_m6B1D8988D3CE889D903F736E9814EAFFCCC35FC3,
	CPUDrawInstanceData_DestroyDrawInstances_m6410B734F4067FA0A56142576467F65ACAF30343,
	CPUDrawInstanceData_DestroyMaterialDrawInstances_mC3646BA15537CA5C257F05E06AC409B4ACA4E167,
	CPUDrawInstanceData_NeedsRebuild_m0F632EDB133A8FFB533DB9A5BBCC199DEB9AC11C,
	CPUDrawInstanceData__ctor_m21E4C7096825C181C31F9F5A8600D2CF96F5B239,
	InstanceCullingBatcher_get_batchMaterialHash_mF1798E2B3C1C885996C171F3F4EDFB7DFDC53151,
	InstanceCullingBatcher_get_packedMaterialHash_m12837A329EFD8A76B3E25C2140F516E2847570EC,
	InstanceCullingBatcher__ctor_m98D5EFDC5F3AF7FF61DEE777748DBD66758A239B,
	InstanceCullingBatcher_get_culler_mBFCD2ACBB0F3D4A650233F186A5EB98D47A714D4,
	InstanceCullingBatcher_Dispose_m6A9FED11F52B4FD30BAF1ECE6676B853B9BCCA42,
	InstanceCullingBatcher_GetBatchID_m149BEE4723E1E52B23E9B63AE33960431ADAE18C,
	InstanceCullingBatcher_UpdateInstanceDataBufferLayoutVersion_mFFB18677B049FC0438D8169800EE48CE19594268,
	InstanceCullingBatcher_GetDrawInstanceData_mED58B161705DB4D131F341DB74C65163A6920ABA,
	InstanceCullingBatcher_OnPerformCulling_mC82A6CB4199689882BEFB834A6E8CA9DFEFB02DD,
	InstanceCullingBatcher_OnFinishedCulling_mA2C92EE1760B83D8F49B13F81E6736E488ACBF70,
	InstanceCullingBatcher_DestroyDrawInstances_m53512A8BD8DF31ADE89587FD55D5B4226F9F6A44,
	InstanceCullingBatcher_DestroyMaterials_mF017E0AF2451934C0B8D3D9F7457FCDB3908F087,
	InstanceCullingBatcher_DestroyMeshes_m7CB28EB447BDD8A7EF2780BB3BA51A16239B91BD,
	InstanceCullingBatcher_PostCullBeginCameraRendering_m8C7F421728438AB4A12BF864206727A08A2C30D6,
	InstanceCullingBatcher_RegisterBatchMeshes_m59D3A05E2598309513C7DD095CD30F64C33C6214,
	InstanceCullingBatcher_RegisterBatchMaterials_mB0F8BEB81FC2A03451C9A79341D0BA134B54D4B1,
	InstanceCullingBatcher_SchedulePackedMaterialCacheUpdate_m22B6C778DE0258BCEA6BFBB9F9278637010B5A0C,
	InstanceCullingBatcher_BuildBatch_mCCF451A6EF0FDA419A13DA10D82E854480B2CFFD,
	InstanceCullingBatcher_InstanceOccludersUpdated_mB9D7CECE86473174B52A1D76E73DC546738C9A44,
	InstanceCullingBatcher_UpdateFrame_mD1B5D19FB7AB428A0E766E54E1E58821EF647457,
	InstanceCullingBatcher_GetCompactedVisibilityMasks_m56336BD173368549DEDC4D743382E3311D29B144,
	InstanceCullingBatcher_OnEndContextRendering_mB4D41FEA107B3D5ECF9AF02F3937726021141A26,
	InstanceCullingBatcher_OnBeginCameraRendering_mE389E1056E80885B9816E9231F9C3C2A42B72221,
	InstanceCullingBatcher_OnEndCameraRendering_m189059E45FB5732F03BAFABF3421CDD68D81DEB1,
	InstanceCullingBatcherBurst_RemoveDrawRange_m007B09A18C3EF35F9757051CCCB5D00D350C7F9F,
	InstanceCullingBatcherBurst_RemoveDrawBatch_mFD88A38A3AEF08C7E82617FB3A9CC9181F0B5104,
	InstanceCullingBatcherBurst_RemoveDrawInstanceIndices_mCD31103C406E421E8E420A624BAE66CE0B58F140,
	InstanceCullingBatcherBurst_EditDrawRange_mF55B010296BE2E67F660E55BAC21DF7C8F0E36CB,
	InstanceCullingBatcherBurst_EditDrawBatch_m576D7B695312D660921D4218FA15CA5F9B47E1A4,
	InstanceCullingBatcherBurst_ProcessRenderer_mA0CA512208E88C6E3D1A5BDDCF673BC85722F551,
	InstanceCullingBatcherBurst_CreateDrawBatches_m607108DE9C1C56A9A8AE1C377CA5C64D155497E3,
	InstanceCullingBatcherBurst_RemoveDrawInstanceIndicesU24BurstManaged_mE9A62851F7A76105F82304233E4068418CD1C452,
	InstanceCullingBatcherBurst_CreateDrawBatchesU24BurstManaged_m8D901A14C6C7B7D5D92D4C41CF483DB1F62F8F39,
	RemoveDrawInstanceIndices_0000017AU24PostfixBurstDelegate__ctor_m51FF714E57C39CA7FF5F9FD6A2641F7684D7EDFA,
	RemoveDrawInstanceIndices_0000017AU24PostfixBurstDelegate_Invoke_mC0F3249CBD739427119D6F927B679FCD647605AB,
	RemoveDrawInstanceIndices_0000017AU24PostfixBurstDelegate_BeginInvoke_m05BC7477D57B6EBD6203D5978652F1C8E6B7EC44,
	RemoveDrawInstanceIndices_0000017AU24PostfixBurstDelegate_EndInvoke_mD4FB0EA1AA272D4D179438FEF3C7475102EC78F7,
	RemoveDrawInstanceIndices_0000017AU24BurstDirectCall_GetFunctionPointerDiscard_m75B8D0C8E75C4C423780A559EAD2D4313DB3478A,
	RemoveDrawInstanceIndices_0000017AU24BurstDirectCall_GetFunctionPointer_m8DA6114E555B987BE7E83843DE01C4176E1EF7D3,
	RemoveDrawInstanceIndices_0000017AU24BurstDirectCall_Invoke_m677671DEC286D42996156D7D267B190E234B5199,
	CreateDrawBatches_0000017EU24PostfixBurstDelegate__ctor_mDB3A117A6A1DC7EB41592212FC427342ACCC9274,
	CreateDrawBatches_0000017EU24PostfixBurstDelegate_Invoke_mC0A8DE0E06BE7D08136C51D5656812B6F15C0A32,
	CreateDrawBatches_0000017EU24PostfixBurstDelegate_BeginInvoke_mF8F1BEDAB745CD8EDA7F61799A9D6DED08BFDBF3,
	CreateDrawBatches_0000017EU24PostfixBurstDelegate_EndInvoke_m44856AC167D8A5CA0A9FE12157F3E3C1FE9FDB36,
	CreateDrawBatches_0000017EU24BurstDirectCall_GetFunctionPointerDiscard_m123958C31C51FDCB87F02CA26E5B555270424354,
	CreateDrawBatches_0000017EU24BurstDirectCall_GetFunctionPointer_m7D9D9854D2D4E449B637DFE143058D89E0DC20BF,
	CreateDrawBatches_0000017EU24BurstDirectCall_Invoke_m1200DFE4C93B3E727F2FBD4278CF19A1E6CB2176,
	GPUInstanceComponentDesc__ctor_m3D6BFA5A8BBF78939717DABD22CDEDE234780EE0,
	GPUInstanceDataBuffer_NextVersion_m87213EAF8B57C72440D656BD00E246106CD9404F,
	GPUInstanceDataBuffer_get_valid_mBB8F7F2B22AA1450AD0944A8364F19025D0687F1,
	GPUInstanceDataBuffer_CPUInstanceToGPUInstance_m1177E7984777339744077A74E281D84B17704592,
	GPUInstanceDataBuffer_GetPropertyIndex_mF39E38B5B13B5BF4E45934C274E076B4401656DA,
	GPUInstanceDataBuffer_GetGpuAddress_m478AE68E3BE3FC5076A3D8C1D9F2CC20E11FD7EF,
	GPUInstanceDataBuffer_GetGpuAddress_mCDCEF5E738A3FE9E217D94ECA43A2AE5A6380225,
	GPUInstanceDataBuffer_CPUInstanceToGPUInstance_m3C4863A5F6AC91EA128DE5FDD296B3159CCE218C,
	GPUInstanceDataBuffer_GPUInstanceToCPUInstance_m6861C120BA15E1AA9BB2116DDBF09CC4A68BC039,
	GPUInstanceDataBuffer_CPUInstanceArrayToGPUInstanceArray_mE3CD8040A3236B9CA56E2CB69B90C68CB1BE42A3,
	GPUInstanceDataBuffer_Dispose_m338824ADC36E89D59E8D1EC451F00A78337A4165,
	GPUInstanceDataBuffer_AsReadOnly_m7E7EAB66B500E1CAA7AEB2C2F7CAEBE40CCE729F,
	GPUInstanceDataBuffer__ctor_m62C97070C67C69A70905B44F586178FEFB54C95E,
	ReadOnly__ctor_mF532002BB9D890FF1F0582B28EE81FFF81C4843D,
	ReadOnly_CPUInstanceToGPUInstance_m01CDC3239F33A9D1A861D1816930C4A7E71440E5,
	ReadOnly_CPUInstanceArrayToGPUInstanceArray_m246175FBDE0C68B01CF4B8B0F73087368CA7ACF3,
	ConvertCPUInstancesToGPUInstancesJob_Execute_m16B5AACD9BEEFFA2046C2E1999F8C4AA7FF2B1D9,
	GPUInstanceDataBufferBuilder_CreateMetadataValue_m50A13411B4799C2C197B9C4EFD3EED2BB4C25036,
	NULL,
	GPUInstanceDataBufferBuilder_AddComponent_m26713FB7D74F3F47052241EEDE3D95D1502C35E8,
	GPUInstanceDataBufferBuilder_Build_mDEF5AC49115B5D3CD195C5802389B93AE1C25C8C,
	GPUInstanceDataBufferBuilder_Dispose_m1869839D0122EAE7FE7A7F9FE5356CCDE50D6636,
	GPUInstanceDataBufferUploader__ctor_mAD57A016FC2E0940D4A469F418E161A1DCA8CC8C,
	GPUInstanceDataBufferUploader_GetUploadBufferPtr_m0CB3210A88F79E17329FDE29AD57C33C975D55DD,
	GPUInstanceDataBufferUploader_GetUIntPerInstance_mE171D8F73FE5CDE5D3F83C730F31877B8649A59C,
	GPUInstanceDataBufferUploader_GetParamUIntOffset_mF231C740B0554E7A82228DAEA5FE5F6BEBD978FD,
	NULL,
	GPUInstanceDataBufferUploader_AllocateUploadHandles_mAFB40A36A5FDAF5C560EB04B822012E57219A607,
	NULL,
	NULL,
	GPUInstanceDataBufferUploader_SubmitToGpu_m352C6DB1B32BBB2D141C76ECCB19F8FF55940105,
	GPUInstanceDataBufferUploader_SubmitToGpu_m6037322A91258909D757C712390F08395A36CF9C,
	GPUInstanceDataBufferUploader_Dispose_m0D8CBA64EB57BF6554F97E74B238F0A159B574B5,
	UploadKernelIDs__cctor_m5DABBDCC0EDE576865CC927D7810EE469972169A,
	GPUResources_LoadShaders_m9ACB6FC1CAC3C07D223AB04073FEFEBCD55F5CDA,
	GPUResources_CreateResources_m07CBEC1195D765927E59B74DDFF7402B3608E22D,
	GPUResources_Dispose_m2D71F420E19EDC6E791D5FD0BDAA81501C6017E6,
	WriteInstanceDataParameterJob_Execute_mFBD8B3E368892E290A864D47C75EC3958286BC25,
	GPUInstanceDataBufferGrower__ctor_m2555453CA56595D3963837F7226E4F85D11379B1,
	GPUInstanceDataBufferGrower_SubmitToGpu_mF6B94F8512B09B0E57404C840A3C22D621C8EE9A,
	GPUInstanceDataBufferGrower_Dispose_m24A1DE3F1FE59498294C9845783B541018F54BE3,
	CopyInstancesKernelIDs__cctor_mE2A43876DE96902483CD0D2EFE3D31E698AA4715,
	GPUResources_LoadShaders_m4C5A24A42FDCD3D04EBB7C2342E23D14748CBB89,
	GPUResources_CreateResources_mD345D83E87BDDAF07F3A8628B9E1F1860BEC40C3,
	GPUResources_Dispose_mBB023C03E82397C5CF141105338158EEDA6841AF,
	InstanceHandle_get_index_mD1303DDB5E62977480B92E3DCECC534B1465CB94,
	InstanceHandle_set_index_mC56210BD1B26D7B86BCD9BF2CE5341188DC1A234,
	InstanceHandle_get_instanceIndex_mD7BAC819F382904784AC46D1F6A631B4A21CA8B5,
	InstanceHandle_get_type_mAC674106B77C6008F547FBAB2FE02F263D3FA40C,
	InstanceHandle_get_valid_m5629806E66255D75C3B1ED5826B0DA6D13B9E0CE,
	InstanceHandle_Create_mDF57F601CCEF1ED2DBFD880416FE0B5EB625DB2B,
	InstanceHandle_FromInt_m501BC299814E873C1040C63575F9391327992272,
	InstanceHandle_Equals_mB7F950B845D19368217960B6FE7DBEA51B013C9E,
	InstanceHandle_CompareTo_mC8763D64CE799F220739F8D3880F0725ADA9A93D,
	InstanceHandle_GetHashCode_m256554043D1CEC35A1670DA2BDD39B453A4985D9,
	InstanceHandle__cctor_m482B79BDF36DE1A1A2BDB9C9D97F597DE7ED7F77,
	SharedInstanceHandle_get_index_mA8EF25F39F6B747A2984740DC8A6A7F7F3B31606,
	SharedInstanceHandle_set_index_mBD1FDAC50BC4D743EF40A7195E2DF1E3B4BC1840,
	SharedInstanceHandle_get_valid_m723EEF4B52F015C0A6EC4BB621966C396311C865,
	SharedInstanceHandle_Equals_m8318CF0264558ADD64222B3A2593EACCED56BFE0,
	SharedInstanceHandle_CompareTo_m31419B958041797369105E6105847547F39F98C4,
	SharedInstanceHandle_GetHashCode_m5B97E179A78BD59969291F66E286E00873FC120D,
	SharedInstanceHandle__cctor_m1F42E7568683FEA4B78D0F18E1F5707420D6644E,
	GPUInstanceIndex_get_index_m0EDBD4FD5FC090990E2A24DCEBB5346B260C919D,
	GPUInstanceIndex_set_index_m24EF3293A5E4CA20F4186F53B459500CDAE40687,
	GPUInstanceIndex_get_valid_m5DBD00DEF2B390C4DB2E0B41686B6A09A064D27D,
	GPUInstanceIndex_Equals_mEF7254FCD24AAD9A15FD3D1555306A5F1C908D23,
	GPUInstanceIndex_CompareTo_m4CEFE1A49A0D625417B0123D3CA3AE385B785DCF,
	GPUInstanceIndex_GetHashCode_mD3CFF7872E06CD6F455F81B019F03672C213D970,
	GPUInstanceIndex__cctor_mBE79D3EFAD002DBED18492AEDE53BD38FCA142E7,
	InstanceAllocator_get_length_m762D1C624FF0B3D9F1DFED48EEA65465932C49DC,
	InstanceAllocator_set_length_m02BBC305E32676069A1D7855C5FF776FD7F24172,
	InstanceAllocator_get_valid_m86EC99BF39D782407913E9BAC1DB2D7CBA1FD797,
	InstanceAllocator_Initialize_m299908C0326706B4BF44DDEC7CDA3D1074347F01,
	InstanceAllocator_Dispose_mB710FC50CF870DBD3B226AE4B28057364D67237A,
	InstanceAllocator_AllocateInstance_m846D1D4EA89D654F8AD4272579E3A7591FE155AB,
	InstanceAllocator_FreeInstance_m4017808CB3B95F6FC5079EFDCC7CA13217C42E3C,
	InstanceAllocator_GetNumAllocated_mC907C85B73DDEAFAE3606B4099F5C7E808DA8C18,
	InstanceAllocators_Initialize_m1C63A61426D12441A3C4EABEC29B392FC93E90D9,
	InstanceAllocators_Dispose_m0B2675D6FA6E95D9B9CC4617A94F408D9E80F5F7,
	InstanceAllocators_GetInstanceAllocator_mF5B156CDAEDEEC2849590A544A5D99E691D0E37F,
	InstanceAllocators_GetInstanceHandlesLength_mBFEDAC598685E4B38B1E67DECA4E75B8E345ACE0,
	InstanceAllocators_GetInstancesLength_m7F121F6138F0713B01D5379DCA36255DF631D1C3,
	InstanceAllocators_AllocateInstance_m1BA734BD0D1B20F166DEBCCEE8AC5B2B0911D9A4,
	InstanceAllocators_FreeInstance_m0EBB63A12A593C580C54770B70A6032C55237AF6,
	InstanceAllocators_AllocateSharedInstance_mD61A96D871FD88F2826B307B6BBF976223E075CF,
	InstanceAllocators_FreeSharedInstance_m0B4E4FDCEA191C4829065D705C241F8667B658D5,
	CPUInstanceData_get_instancesLength_mEF0DF28231C26F0C2E7EDA6A7115A51F51E5109C,
	CPUInstanceData_set_instancesLength_mA37EF6CFF372799E7F182362873F19898021B186,
	CPUInstanceData_get_instancesCapacity_mB1E847621B15F7559FC491B24F54ABA253A73249,
	CPUInstanceData_set_instancesCapacity_m2BC970C6B7AA237310D79F3994737B40F962EE1D,
	CPUInstanceData_get_handlesLength_mA9DC002A27BBD94B09B5944B84C6F47C30692F48,
	CPUInstanceData_Initialize_mCB28B3C43BE61390B76A1B9C1C79BBFA33A33FD0,
	CPUInstanceData_Dispose_mA6CD2735982440ECA65880116C2D214E97ADFC20,
	CPUInstanceData_Grow_m54A454E32FB7C164E757F05A453DB71BBF6E1907,
	CPUInstanceData_AddUnsafe_m1F5C8C517A8258670937584F1DD66E9D47724BC2,
	CPUInstanceData_InstanceToIndex_m9D4701DC598D9046898239AF94F4E87F6A0F14EE,
	CPUInstanceData_IndexToInstance_m3C95DBA9646291FACE0234AC811183928C232321,
	CPUInstanceData_IsValidInstance_m5B3FF0F8456CF25EE209E68A230D594BB5DB36A3,
	CPUInstanceData_IsFreeInstanceHandle_mEEBB421BA3391592FEF206095FBBC6A4BF6D9589,
	CPUInstanceData_IsValidIndex_m4BE52A79DE124AA4872297A76A0FEA1D114CA010,
	CPUInstanceData_GetFreeInstancesCount_m1F0B5BDAFE8DC1DB434FC5682705494F3EDFE10C,
	CPUInstanceData_EnsureFreeInstances_mB7036C8793D1CB89C1C371A605581CBB4E177484,
	CPUInstanceData_AddNoGrow_mEB9B4B4BCDB7C718DA14898D903B5E0D45DE0455,
	CPUInstanceData_Add_m0736A5D9F85B1EC37E061411CC00335DFCC44D5A,
	CPUInstanceData_Remove_m599064EAE344F332CFE4CD00C9714E72623D6877,
	CPUInstanceData_Set_mF5405865F66B4E168163746D8C5BCEE865DE81CD,
	CPUInstanceData_SetDefault_mDE2B13667675847C626111DE77EDE63FE25B5E42,
	CPUInstanceData_Get_SharedInstance_mE5ED4915B38984690F4AB5089634500FBCB6C3CB,
	CPUInstanceData_Get_LocalToWorldIsFlipped_m19881EEED025A0FC45721F63DFCCC1C462F93107,
	CPUInstanceData_Get_WorldAABB_mF9F14AE6B71FECD3BF51252AB06781687C9531EB,
	CPUInstanceData_Get_TetrahedronCacheIndex_m7D09D9EBD350591CCD9A948AF1F55DA98F85D1B6,
	CPUInstanceData_Get_WorldBounds_m59B12DD936A814F2E056E962A4889CD3FD473417,
	CPUInstanceData_Get_MovedInCurrentFrame_m58A543967F67149E003AD3FE6FE4C45F9E87C869,
	CPUInstanceData_Get_MovedInPreviousFrame_m9A0B9988AF8004828D13C96F89981F3C6AB15C29,
	CPUInstanceData_Get_VisibleInPreviousFrame_m3E67A47CD7703E4345BEE5A7068855FE6A589BB5,
	CPUInstanceData_Get_MeshLodData_m2C413661D7B9E15E2B095F5AAEFFD3DCE65E914F,
	CPUInstanceData_Set_SharedInstance_mECEE018E02FA2F6502B59197A9AEF35FA2D35081,
	CPUInstanceData_Set_LocalToWorldIsFlipped_mB2B243184FD3C74C54A5D8EC74542BC0E019EF29,
	CPUInstanceData_Set_WorldAABB_m56D85CE12B7C589B053C471E803090DF9CA37073,
	CPUInstanceData_Set_TetrahedronCacheIndex_mAC10B658EB1AB6EF405BC32A6DE8E88F7AA45AD5,
	CPUInstanceData_Set_MovedInCurrentFrame_mB2081F967C2A8B6E6AC80F2E91DB3FD70851346F,
	CPUInstanceData_Set_MovedInPreviousFrame_m096AF4BED24D559F13122047DF89E56C3C431B4D,
	CPUInstanceData_Set_VisibleInPreviousFrame_m53D87829915906D1DD8E19F1F8D51631E9C02620,
	CPUInstanceData_Set_MeshLodData_mB14CED13A879C4E0CA7A8E27228642E1AF7D44FF,
	CPUInstanceData_AsReadOnly_m90E90427C88E3F72D123C44F0E7957BACDDA8306,
	ReadOnly_get_handlesLength_m50556F07B2F7144758202F76E202ED795E715DE9,
	ReadOnly_get_instancesLength_m0E85E521D7DB61518C6988B08646386F20A17307,
	ReadOnly__ctor_mFAEAA65432C0711F606AC98FD2320032B60A3309,
	ReadOnly_InstanceToIndex_mA64D7447F6466C660DE83765B1B42673D922CCE2,
	ReadOnly_IndexToInstance_m5BB2CA43E37DBA390AB7F29E6B5357A05226EE1B,
	ReadOnly_IsValidInstance_m560D63CCD78D3661349A6134A30A29EFEDD3C30A,
	ReadOnly_IsValidIndex_m2E036F94E4DFD41DF1CCA4148915594457CF0A9B,
	CPUPerCameraInstanceData_get_instancesLength_m2FCFD20153EC6AACB2340D337BB18BF616A4198F,
	CPUPerCameraInstanceData_set_instancesLength_m3CB40B1AB4F2FD153BD4F869F93AA91CB594C0A0,
	CPUPerCameraInstanceData_get_instancesCapacity_m769EC460DAC66EE0EEAB7EDB3AC0184573C20B3B,
	CPUPerCameraInstanceData_set_instancesCapacity_m4B4D614181686095CD8983D09FACB5EE1EF30223,
	CPUPerCameraInstanceData_get_cameraCount_m4ED64DF61C7EEFDD0B771A9F3F80A46F40266FA8,
	CPUPerCameraInstanceData_Initialize_m8FF812DA16EBDB44A3342DB1FE0CA66B39CAF543,
	CPUPerCameraInstanceData_DeallocateCameras_mD90424A33180D27D17C51148E7C439D19CCE79DF,
	CPUPerCameraInstanceData_AllocateCameras_m6C0A0DA2F8AB0E201801C3D5D67DCA28A677E23C,
	CPUPerCameraInstanceData_Remove_m49A44C5456AC0C589DAF869E031D7F0113BA86BD,
	CPUPerCameraInstanceData_IncreaseInstanceCount_mB3E25932F6796ED52CA77FD2CE1EF2C2F181F582,
	CPUPerCameraInstanceData_Dispose_m217073CE624DE6B4D4087A252B22296C6B6D8964,
	CPUPerCameraInstanceData_Grow_mA24034B63E5825884058DFE3C4BC602E61242846,
	CPUPerCameraInstanceData_SetDefault_m8F8F7A49EBF71312638CD9E75C861AAEC1493B8B,
	PerCameraInstanceDataArrays_get_IsCreated_m36CA4102C1FC8F52C1C84D42C73DE22F4245EBB8,
	PerCameraInstanceDataArrays__ctor_m1959ED65DC23B84993E219642F408B7E833EC465,
	PerCameraInstanceDataArrays_Dispose_mB47A82DE41B69F0F6D0488317687ED8A0EBBE793,
	PerCameraInstanceDataArrays_Remove_m937A118CFDBB9A6C9B7D8FF4481DC0673DA4974C,
	PerCameraInstanceDataArrays_Grow_m9ED1561118DD5ABF4A11D5F73359AE1D6A7D054A,
	PerCameraInstanceDataArrays_SetDefault_mB88E73D0AB2BC49675950883B49FE12625B0771D,
	CPUSharedInstanceData_get_instancesLength_m6C9D2E7F861FF43E23759E8816653646F41038B6,
	CPUSharedInstanceData_set_instancesLength_m62AE1E8890A80D570FD3D11CD9E7C76B9D19124F,
	CPUSharedInstanceData_get_instancesCapacity_mE40EF07D597175079E8DB6639E444A8346BD5C67,
	CPUSharedInstanceData_set_instancesCapacity_mEBAB461BBCD3CD8127252B3F6156431A24943A69,
	CPUSharedInstanceData_get_handlesLength_mF238FD08383695F2D7F707F2FA87279FE939CE61,
	CPUSharedInstanceData_Initialize_m0D70CFA6688D2B1A165CDB20AB1760CBB4604A6E,
	CPUSharedInstanceData_Dispose_m37AA8B81C3E42B81EC8E948B3F3DF9197B75E48C,
	CPUSharedInstanceData_Grow_m0AED29A47C913CD92A9EA6281FABBBE76C4C26F3,
	CPUSharedInstanceData_AddUnsafe_mD66018426B29B8FC77CBA218E04D6470D3A08B69,
	CPUSharedInstanceData_SharedInstanceToIndex_m8D1697AC229D8D0D83FA702F6F440132FA123282,
	CPUSharedInstanceData_IndexToSharedInstance_m7E58052D57FC86E8F4C0571D1213F2D4774BD323,
	CPUSharedInstanceData_InstanceToIndex_mA58DA1C16D52FECF5ED6238B168203837D75F87C,
	CPUSharedInstanceData_IsValidInstance_m33E12C093A6F0194B4D388782B15C2DD64616384,
	CPUSharedInstanceData_IsFreeInstanceHandle_m972EDE9AF42DAADE66DAA3B1E1A8F8A85B594BFA,
	CPUSharedInstanceData_IsValidIndex_m24116C260518DD680C3CDBD5BB1797B7764DDBCD,
	CPUSharedInstanceData_GetFreeInstancesCount_mC18CC336EB3ED96DA46A39019C480485B58A26F5,
	CPUSharedInstanceData_EnsureFreeInstances_m9276B1E0AE6DE0DFAE5F6FA4AFFB7DD9E6939CB0,
	CPUSharedInstanceData_AddNoGrow_mAF3FBC643ABC12255E182EE8F8D26024AD8BBACD,
	CPUSharedInstanceData_Add_m3AFDBA31C2E77E106BD970496C5F0EFBDCB3FCD3,
	CPUSharedInstanceData_Remove_m1BA13D76461FC093E7667EA3746FC4A6C3E99EF9,
	CPUSharedInstanceData_Get_RendererGroupID_m09F08F1763DD649A37D88B169CA50B2BD4F71768,
	CPUSharedInstanceData_Get_MeshID_m70DD07FFBE12B43BA2546547FD26BC268E28EF43,
	CPUSharedInstanceData_Get_LocalAABB_mB7D5A9EA9CD43BBEE3E938D31500A518FD3C02D7,
	CPUSharedInstanceData_Get_Flags_m4F22F6B4DB27966ED5F633612B135EDCD274E341,
	CPUSharedInstanceData_Get_LODGroupAndMask_m4A750431421271D4D2960E84D4B3AD83F96A8A42,
	CPUSharedInstanceData_Get_GameObjectLayer_m75D2D7344A04236BFAE513142B29AB6CC8E7CB4D,
	CPUSharedInstanceData_Get_RefCount_mE216F6749BB1B06E795C52C2433EE85E6C57DC01,
	CPUSharedInstanceData_Get_MaterialIDs_mCE6EC8119990B7324D473B6DFB27B91A076404FC,
	CPUSharedInstanceData_Set_RendererGroupID_m06EFE6568E7FABED464A0A091DB7D8DE05FC8719,
	CPUSharedInstanceData_Set_MeshID_m97990780D2C7B92B6A146C6FB243D08D2674EAA9,
	CPUSharedInstanceData_Set_LocalAABB_mFA0C8491B2FCD9E1F5BB25FF598C7520D689D08C,
	CPUSharedInstanceData_Set_Flags_m9C1CE3AC11B09B3B2C9F65422B2FD31D23C49F4D,
	CPUSharedInstanceData_Set_LODGroupAndMask_mD27421C024B1779830BEFA1274063B72D7C182CA,
	CPUSharedInstanceData_Set_GameObjectLayer_m97AEE383F753133901141CCD0040B7D2C2E41EEC,
	CPUSharedInstanceData_Set_RefCount_m7C5AF143DDC85304B2840A688BB2DD095A03B7CC,
	CPUSharedInstanceData_Set_MaterialIDs_m3C8A0B397DBC9FEFF9661015CC32232A4910D812,
	CPUSharedInstanceData_Set_m1236559425DDDA4FAF0765307F13B64FCA01C2BB,
	CPUSharedInstanceData_SetDefault_m9241E530C50F996744FC4691DCD87C0E30602237,
	CPUSharedInstanceData_AsReadOnly_mC186E7142A7E53BFE6D90BB0AB1841600BE7CBF8,
	ReadOnly_get_handlesLength_mEAE03E557C81624E1F2CD280610A867E764EE456,
	ReadOnly_get_instancesLength_m82B5F22A5EE9DF8AB94546D3B739C1EB36AD38D6,
	ReadOnly__ctor_mDBC17F900B61DCF328F4BE2CDF0464E97F44614D,
	ReadOnly_SharedInstanceToIndex_m7CD21D39C3285388B428D1CDCAA9AFB10A0178B4,
	ReadOnly_IndexToSharedInstance_mC97CAB3F6DB3C92BCB528F85F5B9E42728DE3389,
	ReadOnly_IsValidSharedInstance_mC2056D855CF0ED6FA050810CE1035680DC453A1A,
	ReadOnly_IsValidIndex_mA6B2DAE40129D5F001698A34D413EE0CEB24E61D,
	ReadOnly_InstanceToIndex_mE76C9EBF3ACCFDD5D73B3A99925CAE88DE2DDBE8,
	SmallIntegerArray_get_Valid_m717ECD9B73FD5A373F40B0A623CB2C8E188012C0,
	SmallIntegerArray_set_Valid_mF9C378DA26AB93E1AB38CCD7371CA23ABB6779D3,
	SmallIntegerArray__ctor_mD7860879B9A093BE8CD0CFD815D125FDB746090E,
	SmallIntegerArray_get_Item_m81A85EC298981F31BB405CA783D90BB96697C2DE,
	SmallIntegerArray_set_Item_mFEF4007D7D6DA125990D22E32B34ADCBF7056EA2,
	SmallIntegerArray_Dispose_m11D070ED0582D69C7F631CAF8C44F93183D73579,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	EditorInstanceDataArrays_Initialize_m9893832043B3485AD59127B93D0872405CF77AFC,
	EditorInstanceDataArrays_Dispose_m16E99865063B2150E7BEFCE3BAE55831D9532B3B,
	EditorInstanceDataArrays_Grow_m57AFD6C22AA320DA1E98981C894EEB1F96C0B041,
	EditorInstanceDataArrays_Remove_mC3970E725DCDCEA7B636C2DCB4732C432787AE35,
	EditorInstanceDataArrays_SetDefault_mBB385CE94C9E46ADADB174CEFB393C2A19245724,
	ReadOnly__ctor_m2029FDC41CDEC78F717316642DB33534904EC6E2,
	PackedMatrix_FromMatrix4x4_mD8BF568A72FAFAA50614FD1F9DA6A7F257CB3E77,
	PackedMatrix_FromFloat4x4_m2EEC3F97BB2E382DD01F31AE83B11D73F2579A9D,
	InstanceDataSystem_get_hasBoundingSpheres_mE95EB4398294EC395CE2A5A16F5D86EF8D86AFBF,
	InstanceDataSystem_get_instanceData_mCB763544E2728F9E48CEF5CB5284044D1C61CDF1,
	InstanceDataSystem_get_perCameraInstanceData_mFE326CC0E7C00FDCF8279605C3D995AEAADEAB0D,
	InstanceDataSystem_get_cameraCount_mE0E41E2042BB55DC037F4B7F2FE08774B292E8D8,
	InstanceDataSystem_get_sharedInstanceData_m917A6760CCBBEBB27FECC0035926431ED41D1BDF,
	InstanceDataSystem_get_aliveInstances_m24552E5DB0DD7022BEBC44E99BAD4E5B91C3FD89,
	InstanceDataSystem__ctor_m5F7B568C5D6BF6507682A782B497C5DF9AF288E7,
	InstanceDataSystem_Dispose_mD8F0ABE86EC7824BD24020C924702A073024A5FC,
	InstanceDataSystem_GetMaxInstancesOfType_mD0C2B5D78BAA3DF5116E66D663F0AB88A1267928,
	InstanceDataSystem_GetAliveInstancesOfType_mACA5AF484D118330CACC8C0D919BAFDDA30D43FA,
	InstanceDataSystem_EnsureIndexQueueBufferCapacity_mEC63AEE12228511E02036542B749925C591E4190,
	InstanceDataSystem_EnsureProbeBuffersCapacity_m5C8E2190B2C827606936372E985463BC746A65D2,
	InstanceDataSystem_EnsureTransformBuffersCapacity_m8101997E233AADA2DFCA0C139B74927BAD65C221,
	InstanceDataSystem_ScheduleInterpolateProbesAndUpdateTetrahedronCache_m138513395BC490C04056D11A8AE9A4017E69092D,
	InstanceDataSystem_DispatchProbeUpdateCommand_m89CB692F574017CDA489FFDC50B7D021F9BE624A,
	InstanceDataSystem_DispatchMotionUpdateCommand_m6CAB421EB2033825CFE9FF8C0A3F13FF6849BD53,
	InstanceDataSystem_DispatchTransformUpdateCommand_m408F702045F0792E5FAB3D30089984D2AC68492F,
	InstanceDataSystem_DispatchWindDataCopyHistoryCommand_m3CE9A16E6EDD1B23B2A1844EB0C8FE63297FCF44,
	InstanceDataSystem_UpdateInstanceMotionsData_m81BC58CE2698369C68C0E7AC1543A7AE4CD871FA,
	InstanceDataSystem_UpdateInstanceTransformsData_mA9273FAEEACA70AB121D953179312125AD328FCC,
	InstanceDataSystem_UpdateInstanceProbesData_m1CD19D71D15B03FC82F0D5434D43872B6482AEE2,
	InstanceDataSystem_UpdateInstanceWindDataHistory_m9E2E361D86A93AEC4256E9E45E6FF8C25DDEF97E,
	InstanceDataSystem_ReallocateAndGetInstances_mD8B36795100226FED3AFE497FC9DED84FF4A6476,
	InstanceDataSystem_FreeRendererGroupInstances_mDB237F9840CA6B5121A30D5238DEFCBBE2DC7B78,
	InstanceDataSystem_FreeInstances_m1FCCBE915D86469CC20E2C01AE6FB341734F2AF9,
	InstanceDataSystem_ScheduleUpdateInstanceDataJob_mEB4A7B9A770F619108268D0B11ABE99DCEFAC479,
	InstanceDataSystem_UpdateAllInstanceProbes_m2544131305465C5C6DE3956ACE326DC2B9DB05AF,
	InstanceDataSystem_InitializeInstanceTransforms_mF2F8A8EEDBFFA25647574740B190DD2899B5B0F8,
	InstanceDataSystem_UpdateInstanceTransforms_m7A0057B405E3D12CFF3EB78FCB3BE1D1593A0E43,
	InstanceDataSystem_UpdateInstanceMotions_mDCDA88917F5E5B6CC8D8FCFB50744E529C11CDFF,
	InstanceDataSystem_ScheduleQueryRendererGroupInstancesJob_m4C0025CA86226F2D5A23C721CA42E7E8DF4C30B4,
	InstanceDataSystem_ScheduleQueryRendererGroupInstancesJob_m04F92151A520DC0AF8F1FB4B7AFA040C0F625D0E,
	InstanceDataSystem_ScheduleQueryRendererGroupInstancesJob_mB490AA73553E991D855BFC67D1622FE3AC8C098E,
	InstanceDataSystem_ScheduleQuerySortedMeshInstancesJob_m04115ECA07C31067F98B727EE322A1786C70175C,
	InstanceDataSystem_ScheduleCollectInstancesLODGroupAndMasksJob_m9A29F99524770324E8E2896B54E5C08FF4A0979E,
	InstanceDataSystem_InternalSanityCheckStates_m972099150EFFB0CFB52E22F42C4E216C1B012A9B,
	InstanceDataSystem_GetVisibleTreeInstances_m215114432B8645A102573A589C21C9925471A451,
	InstanceDataSystem_UpdatePerFrameInstanceVisibility_m1C6A42FA01165B8F7D05C4179DD093BE19AA4512,
	InstanceDataSystem_DeallocatePerCameraInstanceData_m49AE69E176C67DAACE81A9C940F49C11B1970D8F,
	InstanceDataSystem_AllocatePerCameraInstanceData_m417012B36CE1176EB75BDE5052A9CCB91864389E,
	NULL,
	InstanceTransformUpdateIDs__cctor_m22E50C74A91C8F98F112D7D4E8AD2D3CA77829C5,
	InstanceWindDataUpdateIDs__cctor_mDCE66DBD25DE0B17A7C41D329C4006A0AC407C09,
	QueryRendererGroupInstancesCountJob_Execute_m78EBD1A4D27FC820DB994D726466672AE6698114,
	ComputeInstancesOffsetAndResizeInstancesArrayJob_Execute_m7278A70AAF09ACD7DE06A58BBE53617ED59B3178,
	QueryRendererGroupInstancesJob_Execute_m689B8C96E8D3739314972C71A89F43C5DD8DD5EC,
	QueryRendererGroupInstancesMultiJob_Execute_m3ED5F46C8E2BBCFD2302EBF30F55D07CB4292593,
	QuerySortedMeshInstancesJob_Execute_m0B30D3DBCCB8FE5833D677F735387912A448CCDF,
	CalculateInterpolatedLightAndOcclusionProbesBatchJob_Execute_mB29926232C46662441F96B9405E9BE4D3A84109B,
	ScatterTetrahedronCacheIndicesJob_Execute_m157E3BAA3AA5106CD5629211DB4DF10C941B0942,
	TransformUpdateJob_Execute_m0C99BC8B89AD60C1B495E471F9F02FBEEF7F8342,
	ProbesUpdateJob_Execute_mFACBB95DE46DB9C879C2EE745CE37C4AEFB69AD6,
	MotionUpdateJob_Execute_m266055F93AABA39FD53E7EDBD6BAFE55F586891B,
	UpdateRendererInstancesJob_Execute_m93597D2184F6460DD4610AD8E47868B1BA87A11C,
	CollectInstancesLODGroupsAndMasksJob_Execute_m2EFF9AB4F3476E491E51143F7F60F79020A0F780,
	GetVisibleNonProcessedTreeInstancesJob_Execute_mEFBAD1E57760EF2F4C2A4F4E6F6F40E6261C284A,
	UpdateCompactedInstanceVisibilityJob_Execute_mDFAB7C16E38743439A74CCA9AA310B383D2BA55B,
	InstanceDataSystemBurst_ReallocateInstances_mF18C9347288DA7A9C194DFB92AC6A014D24975D9,
	InstanceDataSystemBurst_FreeRendererGroupInstances_mE5B774F1873565C1564044629ADC0EC240EC6547,
	InstanceDataSystemBurst_FreeInstances_m40A2076A7C0DE278135AD4A33911F95F2A94E630,
	InstanceDataSystemBurst_ReallocateInstancesU24BurstManaged_m4FB41EFA348C0E272E99FA0ABBA3BD30F7562EB1,
	InstanceDataSystemBurst_FreeRendererGroupInstancesU24BurstManaged_m68FAC42D1B0B2C0FFFC6CDAC4C3C106377565C0D,
	InstanceDataSystemBurst_FreeInstancesU24BurstManaged_mA15F79C9BB9E77720A6F7B2A613E9003DE42A6F4,
	ReallocateInstances_00000292U24PostfixBurstDelegate__ctor_mD9065276756DD196E7FE3D81D84F61394C71521C,
	ReallocateInstances_00000292U24PostfixBurstDelegate_Invoke_m390CCE4527F706B664500B168B7344639B9481D0,
	ReallocateInstances_00000292U24PostfixBurstDelegate_BeginInvoke_mDABDAB19D20886FBAA411CACEF20606B04C78E1F,
	ReallocateInstances_00000292U24PostfixBurstDelegate_EndInvoke_mA86A688427934286E9591256F06CAE23572B6DB9,
	ReallocateInstances_00000292U24BurstDirectCall_GetFunctionPointerDiscard_mE9F4D6FA95C949818343733F248BC9E9983C3699,
	ReallocateInstances_00000292U24BurstDirectCall_GetFunctionPointer_mAD16A11B02044AA091030482D1E74A8397F3955C,
	ReallocateInstances_00000292U24BurstDirectCall_Invoke_mF7C8CCEC334854360B5429B556BB614ECBBCC4AA,
	FreeRendererGroupInstances_00000293U24PostfixBurstDelegate__ctor_mE914EB226676111CE4E55313877CA2F97BD8A5D4,
	FreeRendererGroupInstances_00000293U24PostfixBurstDelegate_Invoke_mD2868BD3CB139179449AB366C891A8BE9136B151,
	FreeRendererGroupInstances_00000293U24PostfixBurstDelegate_BeginInvoke_m9ED7098EFE82ED4F212FA5571AF2E36B155035B9,
	FreeRendererGroupInstances_00000293U24PostfixBurstDelegate_EndInvoke_mC1E8AFA593CC3DD496308AAF49D7A0DC5F2B1157,
	FreeRendererGroupInstances_00000293U24BurstDirectCall_GetFunctionPointerDiscard_m80879D31A20C48C5DEB64181D465741614E910D3,
	FreeRendererGroupInstances_00000293U24BurstDirectCall_GetFunctionPointer_m9E338F86369D4C7E24996419038598FD2D60A1D8,
	FreeRendererGroupInstances_00000293U24BurstDirectCall_Invoke_m8285C71D4CA8A908CCDCEA2E4EFB9D64AE9C1AE0,
	FreeInstances_00000294U24PostfixBurstDelegate__ctor_mE43DD855D2F853406A5E0636AC26D29AFE8BE6F5,
	FreeInstances_00000294U24PostfixBurstDelegate_Invoke_m71300F65282D856F171CE0921EEA0876DAF1719C,
	FreeInstances_00000294U24PostfixBurstDelegate_BeginInvoke_mDF5C9A5B2833C9732EB0B8CCD3C4CC8C8CD55DB4,
	FreeInstances_00000294U24PostfixBurstDelegate_EndInvoke_mB704DA4BCA7936CAF1B9FCDAF4015992333E8696,
	FreeInstances_00000294U24BurstDirectCall_GetFunctionPointerDiscard_m086741F6985D0AB6F9A460B803A5AF856571E1EB,
	FreeInstances_00000294U24BurstDirectCall_GetFunctionPointer_m16CE891C70281D5FEC2670C3EB1CE4116231F47E,
	FreeInstances_00000294U24BurstDirectCall_Invoke_m577A8A1A7763E27AAF48BA6CE4ED033634C91AA4,
	InstanceTypeInfo__cctor_m3B458B3343FBC2FD6C40D4FB48ED7A275F513140,
	InstanceTypeInfo_InitParentTypes_m871A556C0C4609293ABEEBF110E20DD63FCD084C,
	InstanceTypeInfo_InitChildTypes_m172E3471BA713B1205C2B5BD08FC4E1D1518D41E,
	InstanceTypeInfo_GetMaxChildTypeRecursively_m145FABD376E475D616CC58209E97D94E586BC247,
	InstanceTypeInfo_FlattenChildInstanceTypes_m815D9E228DD3FF4C86C5A233A038D551FBC28AC8,
	InstanceTypeInfo_ValidateTypeRelationsAreCorrectlySorted_mD653B8D9D7F845BE0747448F358E441ADBB6D893,
	InstanceTypeInfo_GetParentType_m4EFCC55DA43E58978C6A983D91BCB6FAAF147529,
	InstanceTypeInfo_GetChildTypes_mA2B041904C54BC841991C707960DEF5842EA5093,
	InstanceNumInfo_InitDefault_m8CC8A9E8EBF0EF16311F391E0312C46A7219A920,
	InstanceNumInfo__ctor_m0C64766A7024C367CB84BA96F01861E951351650,
	InstanceNumInfo__ctor_mCFED34B4FC73F15366339611E22502A8366B016C,
	InstanceNumInfo_GetInstanceNum_m42CC8341EB7A73444DB20B9B64ACF5377CA7CE19,
	InstanceNumInfo_GetInstanceNumIncludingChildren_m45CB40CE2452143C10B4FFC0F05FA7751E416368,
	InstanceNumInfo_GetTotalInstanceNum_m55D6043571FB3440889F98A7D638F6A43D060982,
	OccluderDerivedData_FromParameters_mB285C6B3E3FBFB06A8E38D196D06D45FE722D88D,
	OccluderHandles_IsValid_m9FF256155930C1B40D4195C263AF38FCEB1B7904,
	OccluderHandles_UseForOcclusionTest_m8E9F3CDFD218EC3C5A70DA375F156DC75CB2CAAD,
	OccluderHandles_UseForOccluderUpdate_m4376DEB9151FDF5678FDFE2ED525A3B3AC31AF03,
	IndirectBufferAllocInfo_IsEmpty_m47785BE361D9B989BE0455AD1884831AB99E4009,
	IndirectBufferAllocInfo_IsWithinLimits_mE8807A3A2DDE4F3E3E9091E2441D847F4BFC4067,
	IndirectBufferAllocInfo_GetExtraDrawInfoSlotIndex_m2EAD4191867631265610C43212C34A56A2DAD969,
	IndirectBufferContext__ctor_mC7F33D8E6E9DECDB5A167C9CFD68F1CBB6E7D7F5,
	IndirectBufferContext_Matches_m40E2A7974D4B205D4BB770D4A68635D99FE4C721,
	OccluderContext_get_subviewCount_m49FDF73077D5C9F1789BA0C35A39A7F78FCBF414,
	OccluderContext_IsSubviewValid_m88BE2A076AC851E9D11AB02B30ECE80A7E4D6BE0,
	OccluderContext_get_depthBufferSizeInOccluderPixels_mF734AB99EBF484188554B86CB2E07048E6138C36,
	OccluderContext_Dispose_mF02789AFBB76CD0F4491CDD8A83BCD15938F22D8,
	OccluderContext_UpdateMipBounds_m71B416ED1B6D8827B30F8B1C1E69870328E6FCD9,
	OccluderContext_AllocateTexturesIfNecessary_m4F0340120C018852B2A697194F87A6C539D6C127,
	OccluderContext_SetKeyword_m57CB9C813FA45672B4E4EAD297757E2C427EE0EE,
	OccluderContext_SetupFarDepthPyramidConstants_m69DC95369344843C149515C9A2A9312DD0E99571,
	OccluderContext_CreateFarDepthPyramid_mAE9A7D75C802A5AB3F91ED35C2BF61DA52C0AB0F,
	OccluderContext_Import_m59CDBBE79F1A96B17BE009D5D561E35D69DA19D7,
	OccluderContext_PrepareOccluders_m558D900C293C248A3CEE8FE6640C98873039DF1D,
	OccluderContext_GetDebugOutput_m0B03B350C81D50A7DA8CEB969E70AF0F782675C5,
	ShaderIDs__cctor_mAF5448F8A3480811300797984917EC0136A2EEAE,
	InstanceOcclusionTestSubviewSettings_FromSpan_m23AA5216F285965B59FD98CCF986ABB9A0C527C5,
	IndirectBufferContextHandles_UseForOcclusionTest_m098E1C673230461EF27D4BC76A7186A5DADEEDAB,
	IndirectBufferContextStorage_get_instanceBuffer_mD965845788AD240262E1C46BB4BA405B73C8EA27,
	IndirectBufferContextStorage_get_instanceInfoBuffer_mC14C040CC88B2F11AFE1301EB1FB9C70E396E8E5,
	IndirectBufferContextStorage_get_argsBuffer_m81BE13A707FAF6B8F9361AEBCDD9CBA69611C334,
	IndirectBufferContextStorage_get_drawInfoBuffer_m0168780ED5CFCB3613F04F2C00561471352A5790,
	IndirectBufferContextStorage_get_visibleInstanceBufferHandle_mBD86573F23F6B0F8BF6C46C2996C449E571DC87F,
	IndirectBufferContextStorage_get_indirectArgsBufferHandle_m7DF465FC2B94D42E1ABE0EE29E0EE4A1C850E925,
	IndirectBufferContextStorage_ImportBuffers_mB024C5F1494D76DFEC07CDE6065AC7ACBB35E265,
	IndirectBufferContextStorage_get_instanceInfoGlobalArray_m6D87D347531D8E3C00FB0967CCAEE69FED54FC41,
	IndirectBufferContextStorage_get_drawInfoGlobalArray_mF90CEAECE8B83E043E462A4495421A574CC257D4,
	IndirectBufferContextStorage_get_allocationCounters_mF22684F6D0214AE0755E3DFA8BA047C9E63C70FC,
	IndirectBufferContextStorage_Init_mE1D9E857917AF5759F2E08D6B008C991CE5558AF,
	IndirectBufferContextStorage_AllocateInstanceBuffers_m83064D5ABC47473BE4547D8CDB301CC989362AD4,
	IndirectBufferContextStorage_FreeInstanceBuffers_mE035A24BD6179866735657D3D74C416AD39C597D,
	IndirectBufferContextStorage_AllocateDrawBuffers_mCB3267B00E8328A5031AAB859542061D665CE0A0,
	IndirectBufferContextStorage_FreeDrawBuffers_mAB74E77AD8B6348D492585D0ACB5AE42C2CEB171,
	IndirectBufferContextStorage_Dispose_mD7278C175C4C23BCB9190D68CE8343B543A24302,
	IndirectBufferContextStorage_SyncContexts_mB63C2465AF6D851CA68041FA1C12E24F24874A32,
	IndirectBufferContextStorage_ResetAllocators_m327B8163AEEAFE360011EAEBFBD80C96C6403467,
	IndirectBufferContextStorage_GrowBuffers_mAB6F1A326FB73F38678D592C809333FAE5A25D44,
	IndirectBufferContextStorage_ClearContextsAndGrowBuffers_m0B059924A2DB871C3C80B03DAF54A47C8A47E573,
	IndirectBufferContextStorage_TryAllocateContext_mC8D472C07C986BDCFC55CF8C4CE324230A38618C,
	IndirectBufferContextStorage_TryGetContextIndex_m848B7C96E09D5CE2C6D12221D0B78EA4DCE77EF1,
	IndirectBufferContextStorage_GetAllocInfoSubArray_m053D08700A1B5D4DB1AAF31B508E33E9FF418A4D,
	IndirectBufferContextStorage_GetAllocInfo_m9BF31C85DBB94B0D0F1B82AF66DA8493AB1B2595,
	IndirectBufferContextStorage_CopyFromStaging_mE99297FD5192A7F628FD7DC704E877280DB03891,
	IndirectBufferContextStorage_GetLimits_m2E8DDC54719944794D7342725107396F59D5CF71,
	IndirectBufferContextStorage_GetBufferContext_mD76FD0DD95CEE4D7D9B62F81573C4A05A5519941,
	IndirectBufferContextStorage_SetBufferContext_mEC8E692A99E595592E12F55F94B7E234A340DC2A,
	UpdateLODGroupTransformJob_Execute_m10AD64037A0D9FFAB351270A412B4B4342A6B8C5,
	UpdateLODGroupDataJob_Execute_m37868AFCE3AFCC80D46C8156CA2713096FE5EC3D,
	LODGroupDataPool_get_lodGroupDataHash_m62280E732F32C5C35B2DADCD304E46662939F045,
	LODGroupDataPool_get_lodGroupCullingData_m9D4FE39BAD4D72923936ABBBEBEEF7F2F3131865,
	LODGroupDataPool_get_crossfadedRendererCount_m1851897792114FF4241A4099060D707ECAD45334,
	LODGroupDataPool_get_activeLodGroupCount_m97904EE5C95159152B6C0A1ABC068C06B5079CD4,
	LODGroupDataPool__ctor_m41A2B2D9392893C14F8F1CC08EDE34AE43CDBC8C,
	LODGroupDataPool_Dispose_m018568FAFC3BCCE2F577FC92B6A3223CC585AA91,
	LODGroupDataPool_UpdateLODGroupTransformData_mA548FB2A357D0A1CF586FBD7B3D04B928BCE005A,
	LODGroupDataPool_UpdateLODGroupData_mF09A39F868F16124B4F2503B1F725FE54AE7A96B,
	LODGroupDataPool_FreeLODGroupData_m900936DC26BBC6F1ABF60871DAF69D93FB79C900,
	LodGroupShaderIDs__cctor_m317D81DF99F20606D1C93B871FD9CE2083C6C42A,
	LODGroupDataPoolBurst_FreeLODGroupData_mA6615B0E58416F4443ED0744A145DCB275902C79,
	LODGroupDataPoolBurst_AllocateOrGetLODGroupDataInstances_mBD22A32D0C093047BA4165B0D6E9A38EE73F96F5,
	LODGroupDataPoolBurst_FreeLODGroupDataU24BurstManaged_mFDD1E6CDEFC57BC4784F8CD2E37F054D38188E7D,
	LODGroupDataPoolBurst_AllocateOrGetLODGroupDataInstancesU24BurstManaged_m2B8A0354B669C5690EA34BFF597F0B2DB9E15825,
	FreeLODGroupData_000002E3U24PostfixBurstDelegate__ctor_m229C5CC31C289B026E490711E30E2FB38E95C29A,
	FreeLODGroupData_000002E3U24PostfixBurstDelegate_Invoke_mC8ED82EA2AFFA62FD75BA0227200518FD4A82E64,
	FreeLODGroupData_000002E3U24PostfixBurstDelegate_BeginInvoke_m08EDBFB373BE8A6626CF31649403214FB479AEC6,
	FreeLODGroupData_000002E3U24PostfixBurstDelegate_EndInvoke_mE2C3919B4A156BE64B8FB118A7ABC1C9968C25C4,
	FreeLODGroupData_000002E3U24BurstDirectCall_GetFunctionPointerDiscard_m9A87CB76DC8609824A92E038F601C0B671B87111,
	FreeLODGroupData_000002E3U24BurstDirectCall_GetFunctionPointer_mD8C9C4658C30241BC33B6B3EC927D180B03FBB27,
	FreeLODGroupData_000002E3U24BurstDirectCall_Invoke_mFAD5EF75F4677A195F687926AA8C3AC1585F80AD,
	AllocateOrGetLODGroupDataInstances_000002E4U24PostfixBurstDelegate__ctor_m9100BF202ED6D913941FA1700A042352DA86E4EC,
	AllocateOrGetLODGroupDataInstances_000002E4U24PostfixBurstDelegate_Invoke_mBEC68E66FAF6B7C98A5F2904099BDB5853A682F5,
	AllocateOrGetLODGroupDataInstances_000002E4U24PostfixBurstDelegate_BeginInvoke_m090D0850ED4D8495FA6AD87E7D94798194B073D1,
	AllocateOrGetLODGroupDataInstances_000002E4U24PostfixBurstDelegate_EndInvoke_mA68CADA88E91B345495612678127CAF753608449,
	AllocateOrGetLODGroupDataInstances_000002E4U24BurstDirectCall_GetFunctionPointerDiscard_m397A6E69B62224825804E78DA876584F055FDA67,
	AllocateOrGetLODGroupDataInstances_000002E4U24BurstDirectCall_GetFunctionPointer_m20405B28DD3A1E6FD7D68B168E1C44BB4113B478,
	AllocateOrGetLODGroupDataInstances_000002E4U24BurstDirectCall_Invoke_mEA274B6A86190854B061BA3B7B795770859A4246,
	LODRenderingUtils_CalculateFOVHalfAngle_mC898C822144EDD265331807564B08A69B0C83477,
	LODRenderingUtils_CalculateScreenRelativeMetricNoBias_m7B4E475A8D3B2DB91FC59D008FF21754EFF91CD9,
	LODRenderingUtils_CalculateMeshLodConstant_m2E8D806679F80D578D1B49C0A004D742B83B0042,
	LODRenderingUtils_CalculatePerspectiveDistance_mCF2D6802B8867080934633B981CC9749B48B147E,
	LODRenderingUtils_CalculateSqrPerspectiveDistance_mF050ADA4BADB90CC903419FDA4124CF1C4749CA8,
	LODRenderingUtils_GetWorldReferencePoint_mB73DE0BD3DF41D4594CB01CC090594C7057F2121,
	LODRenderingUtils_GetWorldSpaceScale_mEB5418524BEA8374FABEAFE0392B3F6728DD1A39,
	LODRenderingUtils_GetWorldSpaceSize_m9D47A644A41926B6325DA1D690038561E6119017,
	LODRenderingUtils_CalculateLODDistance_m37BB5F6DED42AD36FF3AD3C214164C4CA43AE33E,
	OcclusionTestComputeShader_Init_mC423CA21A41E4B44DB2715096F04D7498A80E960,
	SilhouettePlaneCache_Init_mF14F33F1C7D6CD3704478C92314526747ABDFF0C,
	SilhouettePlaneCache_Dispose_m52386469CD058770AAD5B4E19ADF603598BECEC5,
	SilhouettePlaneCache_Update_m08599EDAF7CC1D053E0E54A8DF15F55E1E0B6342,
	SilhouettePlaneCache_FreeUnusedSlots_m2249464604B48996B77945B9BBCDBDCECD2074C2,
	SilhouettePlaneCache_GetSubArray_m0527F754CEEB54300C083A4BDAE9B56D026DA63C,
	Slot__ctor_m0E99B79099FDB0098404A2FD223A9C029CFFF5D1,
	OcclusionCullingCommon_Init_mB12BBAEE22EA6EA4C93640CF113484C45AB21128,
	OcclusionCullingCommon_UseOcclusionDebug_mB3DD90044DC771F1A74BDEAC59C921AE66E9311D,
	OcclusionCullingCommon_PrepareCulling_mB0789630787C7E0CD26370E649348B1C2E368B0C,
	OcclusionCullingCommon_SetDepthPyramid_mD653D7921DC4590B1E5DDC848F3B3DDF10D15D07,
	OcclusionCullingCommon_SetDebugPyramid_m7DB573CC2B23E59F6E09FE953D2953447FB4D8BE,
	OcclusionCullingCommon_RenderDebugOcclusionTestOverlay_mFC06DC3F4302109DCBCE0016F77FDC7221C0F850,
	OcclusionCullingCommon_RenderDebugOccluderOverlay_mDCEE8545488D66BAFEEC82CA0A5B078EF76F1719,
	OcclusionCullingCommon_DispatchDebugClear_mD07E3E63ABEB291DB36385737735511B88AD3AC2,
	OcclusionCullingCommon_PrepareOccluders_mB04E538ADB8D350F2F77C2B0AEB3235B5537C78A,
	OcclusionCullingCommon_CreateFarDepthPyramid_mA599495FF407F8137E6B40745EFA5296FD390859,
	OcclusionCullingCommon_UpdateInstanceOccluders_m66590207897221E9FA80265BBEB4E9E40708646D,
	OcclusionCullingCommon_UpdateSilhouettePlanes_m4576EBD18929EC7B7AAA98EA599CEB053033161E,
	OcclusionCullingCommon_GetOcclusionTestDebugOutput_m3F8B14753A940E66F3378EE0A13B467CD5B54163,
	OcclusionCullingCommon_UpdateOccluderStats_mFCE4F68D13AD834D837ACC6CF5818BB454DEB374,
	OcclusionCullingCommon_HasOccluderContext_m24FD8FB63CF4F73E28369A7C5E4AB1A4B0C6EF90,
	OcclusionCullingCommon_GetOccluderContext_m5FA55C98ABA809491877468967428AEA6ED50AA9,
	OcclusionCullingCommon_UpdateFrame_m62E1615FE4BB0184C70EF0D5A1B5341A9E6B439E,
	OcclusionCullingCommon_NewContext_m192A0843FCB88873DB0DBC0D30E85E34D9CD3724,
	OcclusionCullingCommon_DeleteContext_mD0DD525EF7A79EDEC506F1FD27762960E7A9D773,
	OcclusionCullingCommon_Dispose_mA5C16ABDC8FFDCBDF1B0BBDAAF046EB707CAB0BE,
	OcclusionCullingCommon__ctor_m3B0C90E1EF8186EB97881C43D58E13303CACED1C,
	OcclusionCullingCommon__cctor_m65EF7B748745B32F17F979959B56ABA54B68E19D,
	OcclusionCullingCommon_U3CRenderDebugOcclusionTestOverlayU3Eb__29_1_m9B31475AE7F1F1FB5043C7E6AE2AB37D0D901037,
	ShaderIDs__cctor_mC4B7BFD4D1A496F04AC567A1D343648AF9932CDD,
	OcclusionTestOverlaySetupPassData__ctor_m319029C880BDA7B70BBB48CCC52A6DEEE84BC7AA,
	OcclusionTestOverlayPassData__ctor_m0D63CEF912BF6F987D0718384ED42945529D5FE0,
	OccluderOverlayPassData__ctor_m2BBEDE9EE87B99D51BD3A55ADE85B0FF7191D88E,
	UpdateOccludersPassData__ctor_m780741CED9AA7DEA6E7F15F1125830643B0940A5,
	U3CU3Ec__cctor_m69E4B9D0362E234583DB9D7CC8D28B7B958F008D,
	U3CU3Ec__ctor_m7AF3A5B26F1D35F52C4E1518DCB55AF32705CA12,
	U3CU3Ec_U3CRenderDebugOcclusionTestOverlayU3Eb__29_0_m6B98C8D250CCC733E809FCD7A6BEF46BE6416D27,
	U3CU3Ec_U3CRenderDebugOccluderOverlayU3Eb__32_0_m000074A9983218A19ECAA6BBF27D4DE6F0CEC6EC,
	U3CU3Ec_U3CUpdateInstanceOccludersU3Eb__37_0_m07755DD078337F25892B35E882F36CF2D77C600B,
	OcclusionCullingCommonShaderVariables__ctor_m6098CCD0E939B2F9DE8715FF129DAE892745C610,
	RenderersBatchersContextDesc_NewDefault_m60D7888149F2142AA66FECE97FDB3D098A3EC7DA,
	RenderersBatchersContext_get_renderersParameters_mFAD826F98D88258ACEB3BAAB3BCA506E9DA8C54E,
	RenderersBatchersContext_get_gpuInstanceDataBuffer_m58A374780F991800398A3A5C43B900F17E49CA78,
	RenderersBatchersContext_get_activeLodGroupCount_m67F17132BF666120D9AD4C4AF2B5332DA3C3B3E8,
	RenderersBatchersContext_get_defaultDescriptions_m9E1716E5F3F0528BCEF408D48F5114377A4449D5,
	RenderersBatchersContext_get_defaultMetadata_m2A1B42632AB9F8A3B0E162912B6E1E542AC52A78,
	RenderersBatchersContext_get_lodGroupCullingData_m71D5CF459C1ED069E69F643AEF402CE8684925ED,
	RenderersBatchersContext_get_instanceDataBufferVersion_m9F26A5C73EE9A8C8848F3163AAF3B50FBC96EFE3,
	RenderersBatchersContext_get_instanceDataBufferLayoutVersion_m633BE48CAD9AA78DD46E20B2208647B3A94D992D,
	RenderersBatchersContext_get_cachedAmbientProbe_m122AB618901D9C67E31A3E1994C09FAE04AEAFE1,
	RenderersBatchersContext_get_hasBoundingSpheres_mA6745C1F53546E926C85BC0B69E1E176E5C07B54,
	RenderersBatchersContext_get_cameraCount_m66EC4325C4D1B268E744B32F97E114A5DCB0C6E9,
	RenderersBatchersContext_get_instanceData_mA110F9896EEF3B8277350408C9554A9CA4BBAA1F,
	RenderersBatchersContext_get_sharedInstanceData_m657B7F8E58C1857C9A941039A9C87EDEE14BE073,
	RenderersBatchersContext_get_perCameraInstanceData_mEC2BF69B0E02674900CFE87797760E58484F16F3,
	RenderersBatchersContext_get_instanceDataBuffer_m085CC45CC334F7C4AFFC82F08FE9041267BC3FC0,
	RenderersBatchersContext_get_aliveInstances_m464BB51D736CC6E53816E92B54FA52E20A6AB992,
	RenderersBatchersContext_get_smallMeshScreenPercentage_m20E6B516780C91E3EFFF054223A2AD8259D67CEA,
	RenderersBatchersContext_get_resources_m384802C47C8866FE84F3D19892ED70D03CAD5CF2,
	RenderersBatchersContext_get_occlusionCullingCommon_mB5106ABB84E6D34B14EBA467B292E39DDCB60C1D,
	RenderersBatchersContext_get_debugStats_m26AAE0C2CF41DBE02DD210D1FDDB808F8A88CB87,
	RenderersBatchersContext__ctor_m0284FF6010F6BE127276B918BCB7F8D488D82C33,
	RenderersBatchersContext_Dispose_mD6CFED69D7F9007FBA28516C2A6CCD9394D1FC3E,
	RenderersBatchersContext_GetMaxInstancesOfType_mEF99113F1507ABC8426119B2F16B92114F19E1B3,
	RenderersBatchersContext_GetAliveInstancesOfType_mAB16FC96B0BC9357E0DC9FA279AD4844AE0BBD60,
	RenderersBatchersContext_GrowInstanceBuffer_m72EEF32E7D68892D6B6C686290FB074274AF33AD,
	RenderersBatchersContext_EnsureInstanceBufferCapacity_mE609DC40C454449FDFCD61C0347BF4F4C7CFC395,
	RenderersBatchersContext_UpdateLODGroupData_mC3BBC143D600124BC3536CAFE8ADA3D80B9F4E1E,
	RenderersBatchersContext_TransformLODGroupData_m910C251DDACF06457FAB5E90FFE94CB76C84004E,
	RenderersBatchersContext_DestroyLODGroups_m2F2BB8BC930C966F0C1FD6392D669D26B2967675,
	RenderersBatchersContext_UpdateLODGroups_mCC2A5E08EF4A3A88B195D71F252997FAE8255490,
	RenderersBatchersContext_ReallocateAndGetInstances_m75003DE54327AFC9FC9226F543E2AA42ED4CA436,
	RenderersBatchersContext_ScheduleUpdateInstanceDataJob_m52A9965BBC3ACB0F00144C8D39E46478543B623B,
	RenderersBatchersContext_FreeRendererGroupInstances_m1BBD1A75AFD3CED5F347ED940D15EF20D303EA17,
	RenderersBatchersContext_FreeInstances_m8D8AFCF6F9AD2F684CBFCD5B9126C77B9BA856E0,
	RenderersBatchersContext_ScheduleQueryRendererGroupInstancesJob_mEE8968FD91E2F49D2AE33D4A3D0E8C745FF489E4,
	RenderersBatchersContext_ScheduleQueryRendererGroupInstancesJob_m41084D427885CB440E7ACDD227EF915E37B24FA3,
	RenderersBatchersContext_ScheduleQueryRendererGroupInstancesJob_m07B14A5915E64E55DB6DEE709DABD8446F320E13,
	RenderersBatchersContext_ScheduleQueryMeshInstancesJob_mEF8DDD764AFBFBFA23B708EF823511E8CD748966,
	RenderersBatchersContext_ChangeInstanceBufferVersion_m8BDA9E1B471724D930283E832CDC1C4D1172499C,
	RenderersBatchersContext_CreateDataBufferUploader_mE83CA3760B15FBD7BD9E8166D38C01ACA6DC4385,
	RenderersBatchersContext_SubmitToGpu_m7D51CAAFDF4D04FDB49B81F907ADA5C0023909BF,
	RenderersBatchersContext_SubmitToGpu_m522529681D96803ECD637E642083FD54D8FBAAB6,
	RenderersBatchersContext_InitializeInstanceTransforms_m3346F85D05A58656054559FF0D221E5F5D42C813,
	RenderersBatchersContext_UpdateInstanceTransforms_m83DE2D5F845C8D5C10B3E6B809BE32E00E1607AE,
	RenderersBatchersContext_UpdateAmbientProbeAndGpuBuffer_m9635A08E6A72E53938EA2C332B7F37BFD6925535,
	RenderersBatchersContext_UpdateInstanceWindDataHistory_m08DA4EE6C170DEA9C8A9B876071CEB4804438173,
	RenderersBatchersContext_UpdateInstanceMotions_m597C9A66CF49C8F6A010D5D7D0E866657DA207ED,
	RenderersBatchersContext_TransformLODGroups_mB0CB4CD84FB8FF1E35821FD3CB869166ED7D5B7D,
	RenderersBatchersContext_UpdatePerFrameInstanceVisibility_mBD8E7669A22B6C1D47BD0BF3BDC5E22BDD16FBB2,
	RenderersBatchersContext_ScheduleCollectInstancesLODGroupAndMasksJob_mD6FC667C7E0C513173E0720521FD54C3A385737A,
	RenderersBatchersContext_GetRendererInstanceHandle_mF6127D9881FD12DFF2E5AB4132343A50E46E3FE3,
	RenderersBatchersContext_GetVisibleTreeInstances_m5C91EC91858A7EA240EF72E870C8C6A14D1FCC7F,
	RenderersBatchersContext_GetInstanceDataBuffer_m7164DAD5855B34AA94DC599A67E3FCC547C6FC1E,
	RenderersBatchersContext_UpdateFrame_mCFA782A62647ADD043E3247EFF36079A2426DAD4,
	RenderersBatchersContext_FreePerCameraInstanceData_mD63662D47C596B2B718DA97E662CCBB7B4D4D900,
	RenderersBatchersContext_UpdateCameras_m894C3BAC8B3BD71A63212C2E49E40C72D46F352B,
	RenderersParameters_CreateInstanceDataBuffer_m945CE4EF304375414A46DDED06474BFC3132D971,
	RenderersParameters__ctor_mD3445734819B50610F768B8E6EF49822D5ABEE8A,
	RenderersParameters__cctor_m8D5D5734DCF7E98603C17A197EC062D2B1D88F05,
	RenderersParameters_U3C_ctorU3Eg__GetParamInfoU7C14_0_mD43A1760BB14DE3AF585F6E664A7641CA2E4560F,
	ParamNames__cctor_mAEF822BDB14694895783B71D8EACF1EEC9B15C91,
	ParamInfo_get_valid_m198E32DB1AAD1F43EAD6964E4EF79E79D078AF7D,
	NULL,
	NULL,
	ParallelBitArray_get_Length_m82FE0E2AC9FAB29DA67E28FFAEA04EB642955B08,
	ParallelBitArray_get_IsCreated_m31B6CFD3C95548F523C3D074463B827B7CD7A535,
	ParallelBitArray__ctor_m7BB9EA31D2DF48FD4BE5D8773C539A76C5D4E6E4,
	ParallelBitArray_Dispose_m24CBECA125F3D0090E9786E6AF56CB2E6DE452C6,
	ParallelBitArray_Dispose_m3806D56E9788D3C69BD6C6985C6E2F949A89F9B7,
	ParallelBitArray_Resize_mA268182EEF9B41198BFC3780A74CF12D9B232011,
	ParallelBitArray_Set_mEDB30931801E1F71F11F62CCB23F3537EEE0F1EA,
	ParallelBitArray_Get_m250C22A6191BAF4C5B314EFAF451E391D251F2B0,
	ParallelBitArray_GetChunk_m7303392F0138448DF74E7A709F38B500B9461ED2,
	ParallelBitArray_SetChunk_m48943193199714BCF0925DD8E14C8EB651885629,
	ParallelBitArray_InterlockedReadChunk_m19B068CDEB9686FE2DD8A42BF99D2011EEAFA84F,
	ParallelBitArray_InterlockedOrChunk_m51A85AD8A6A6FFD317303DEABA6B9797B79CE658,
	ParallelBitArray_ChunkCount_mE27E6F3D861AF09C5B31BD7F3964796B26A9C3B5,
	ParallelBitArray_GetSubArray_m45E14868BB90EC98D0467ABDA3DAD1BD4BFC49DD,
	ParallelBitArray_GetBitsArray_m31F100FDDB1EA0FE4E1592768ED14B843D0DD73D,
	ParallelBitArray_FillZeroes_mDAE3DE6ACB91DE00B5DC5D50415B7A0F938A08BB,
	ParallelSortExtensions_ParallelSort_m237D06D0D0DA504CE809A6FF2D2CEF9CE0221A08,
	ParallelSortExtensions_U3CParallelSortU3Eg__SwapU7C2_0_mDD868A15D4BFD33E6DFF6107497D4EB6EE040E16,
	RadixSortBucketCountJob_Execute_mF3ADEB0523C3DE92CB5CFEF01B65E72C9AB7C023,
	RadixSortBatchPrefixSumJob_AtomicIncrement_m89775B1090C6296097B6445BC76D2C6BE88F199E,
	RadixSortBatchPrefixSumJob_JobIndexPrefixSum_m9C47BE4B67FCFF29A8FA94D39589F9B9A2840EE3,
	RadixSortBatchPrefixSumJob_Execute_m434849692F7D93EF83545890B59FC96BF14AED93,
	RadixSortPrefixSumJob_Execute_mF1969BD6160F81BA429AF74A8944935FC83BC551,
	RadixSortBucketSortJob_Execute_mDB0AB3CD468DA898E41CBF3E9EF5BE26AD26E4D2,
	__JobReflectionRegistrationOutput__15867191014387474753_CreateJobReflectionData_m61B92F5EF70DF366B7640CA4487293699C2E2A18,
	__JobReflectionRegistrationOutput__15867191014387474753_EarlyInit_mCAFBD6F04F7737F01B0CA94B81910948BEB121CB,
	U24BurstDirectCallInitializer_Initialize_mDEDEEFEB21BD345F172B9A16BF0A65DDCCCC4A4F,
};
extern void AABB_get_min_m209EF3ECD01E859258E1BDE8780CA4C25276DF70_AdjustorThunk (void);
extern void AABB_get_max_mCD29B3A2C2DEA60BBF50091D8E3C938511D82B08_AdjustorThunk (void);
extern void AABB_ToString_mB0C529C757F63FD0F78316C90D2B86206159B678_AdjustorThunk (void);
extern void ReceiverPlanes_LightFacingFrustumPlaneSubArray_m995D1E681E0ED63DFA8489C582A2FCBAF1C9356B_AdjustorThunk (void);
extern void ReceiverPlanes_SilhouettePlaneSubArray_m851F735918EE9297C64815C42AAD446BCBE55F4B_AdjustorThunk (void);
extern void ReceiverPlanes_Dispose_mE53DCBAD81DDB002760FC5661F3B26330B62ACF4_AdjustorThunk (void);
extern void FrustumPlaneCuller_Dispose_m802E9F4605F17D55D46C6C0D308830667DE0A08B_AdjustorThunk (void);
extern void PlanePacket4__ctor_m1B67A6C35FB1B6B6BE21D76B496DA3531F636CC7_AdjustorThunk (void);
extern void ReceiverSphereCuller_Dispose_mFCC83320D29032DBC7A3173ADFE8F764A472FFA5_AdjustorThunk (void);
extern void ReceiverSphereCuller_UseReceiverPlanes_mEA2EE8FE32563F3FBE40D6AADA9724719186674E_AdjustorThunk (void);
extern void FindRenderersFromMaterialOrMeshJob_Execute_m7B9713786ED58196A531F44ECABD1912DF45E59F_AdjustorThunk (void);
extern void OcclusionCullingSettings__ctor_mE814849AC60B1DC1AC17E02F1AF2128DF38FE95A_AdjustorThunk (void);
extern void OccluderSubviewUpdate__ctor_m482C60BE94ECE0B1F15E930936345D9F60E399A8_AdjustorThunk (void);
extern void OccluderParameters__ctor_mBBA1CD9856BD109C9F1D18715B28E2DA95CE83D9_AdjustorThunk (void);
extern void RangeKey_Equals_m05E612C122D91758588CEB7529751045E6F09493_AdjustorThunk (void);
extern void RangeKey_GetHashCode_mC38845B6A1CC657D6F6B1149E7448AA6A3EF3257_AdjustorThunk (void);
extern void DrawKey_Equals_mBA2838871085C8944B81322DC6E851F4273E8E91_AdjustorThunk (void);
extern void DrawKey_GetHashCode_m6E3C5F3D42D02D8035AA7E7B5501FDBC1551F4E5_AdjustorThunk (void);
extern void BinningConfig_get_visibilityConfigCount_m0B698DBD65F8147B56C14CA7CFB741EAB701B3B9_AdjustorThunk (void);
extern void AnimateCrossFadeJob_Execute_mA3C0021BE25AE2F67FD93948B6115ECE990DAFE9_AdjustorThunk (void);
extern void CullingJob_CalculateLODVisibility_mF87528623E251C0B920199D5DFB1021842A5F774_AdjustorThunk (void);
extern void CullingJob_CalculateVisibilityMask_m285DA7E28A8582C95BAE2C6794C34FC3F8F195ED_AdjustorThunk (void);
extern void CullingJob_ComputeMeshLODLevel_m0CF5B90B965B46AA61BD4B00EF15C1528BF03A0D_AdjustorThunk (void);
extern void CullingJob_ComputeMeshLODCrossfade_mF911187408B6601080C28F1227FAF60DE832B750_AdjustorThunk (void);
extern void CullingJob_EnforcePreviousFrameMeshLOD_m20C1DF1641349AC2B1B455E7A49D0DE6BC3497B2_AdjustorThunk (void);
extern void CullingJob_Execute_m172CC218FA8F7198BD69C1C87294FD90D94DB884_AdjustorThunk (void);
extern void AllocateBinsPerBatch_IsInstanceFlipped_m5ED8B8D355142D10DB3983D15A256B5DBD4D6ABA_AdjustorThunk (void);
extern void AllocateBinsPerBatch_IsMeshLodVisible_m9D1674B4CE766557E8D2234D56DF2BACC2EDB576_AdjustorThunk (void);
extern void AllocateBinsPerBatch_Execute_m8244D8F2860ED03B023A6D348D9471F2AF76E0F1_AdjustorThunk (void);
extern void PrefixSumDrawsAndInstances_Execute_m8EADFE8897EAE00866B67F8AE64CA774EE33B5F9_AdjustorThunk (void);
extern void DrawCommandOutputPerBatch_EncodeGPUInstanceIndexAndCrossFade_m6EFB0D6BB29BF2F65B66913BD2F82E4364ED8830_AdjustorThunk (void);
extern void DrawCommandOutputPerBatch_IsInstanceFlipped_m1AE2BD53A7D17783F262A94539417F423FD4CF79_AdjustorThunk (void);
extern void DrawCommandOutputPerBatch_IsMeshLodVisible_mF345AFA69786A2EB8E65B25BFDB933A6321F1054_AdjustorThunk (void);
extern void DrawCommandOutputPerBatch_Execute_mF995D35E2E640A3172F4BC62E37BF103CFBBBBA7_AdjustorThunk (void);
extern void CompactVisibilityMasksJob_Execute_m7D5B39DEFEC4944D35111B4B7D22267147B4AB1E_AdjustorThunk (void);
extern void InstanceCullerSplitDebugArray_get_Counters_mBDB8C933AEEB9F934132442CA20FC54A9E9A01AB_AdjustorThunk (void);
extern void InstanceCullerSplitDebugArray_Init_m0E461744D899802E8123D32B79AD2EEC7FF1ED66_AdjustorThunk (void);
extern void InstanceCullerSplitDebugArray_Dispose_m0FA405A9BA291E560088D998A71EE3FEF95100C1_AdjustorThunk (void);
extern void InstanceCullerSplitDebugArray_TryAddSplits_m8F0CAC17CB3DA747A187760D3961255C1E95BDF5_AdjustorThunk (void);
extern void InstanceCullerSplitDebugArray_AddSync_m05D0080857890E3C8E46730B1D4DC17E4FB71D90_AdjustorThunk (void);
extern void InstanceCullerSplitDebugArray_MoveToDebugStatsAndClear_mAA78838CCD17348C01F115022EF530C3F5B98AD4_AdjustorThunk (void);
extern void InstanceOcclusionEventDebugArray_get_CounterBuffer_mF25E7B744518F980443AFCF79E48EA3CCF852D04_AdjustorThunk (void);
extern void InstanceOcclusionEventDebugArray_Init_mF8AEF95440DA22ACC2DB5717CEBD9C8B8B3797D5_AdjustorThunk (void);
extern void InstanceOcclusionEventDebugArray_Dispose_m2D57744F19C60D3E9B6EFDF81BB08498853E37C1_AdjustorThunk (void);
extern void InstanceOcclusionEventDebugArray_TryAdd_m2FD080E0DDCA404C8C362A5933EC852A9CD5F109_AdjustorThunk (void);
extern void InstanceOcclusionEventDebugArray_MoveToDebugStatsAndClear_mFEF02F07D15F2F2942FABFD0C7C577FAB1859977_AdjustorThunk (void);
extern void Info_HasVersion_m9E077F93A4F89E728DF4FA88764ABF91C732D960_AdjustorThunk (void);
extern void InstanceCuller_Init_mDBBD40C7316EBE16746FACA43170B7E1001EC67F_AdjustorThunk (void);
extern void InstanceCuller_AnimateCrossFades_m5BE8DFEE24C03638DF57E12C23D4C2C80DE8B510_AdjustorThunk (void);
extern void InstanceCuller_CreateFrustumCullingJob_m503045810FE1E550B3F436FD62750A505A4EAE3C_AdjustorThunk (void);
extern void InstanceCuller_ComputeWorstCaseDrawCommandCount_mF523E170BDA54D6E88AD73742065FD88C60937DD_AdjustorThunk (void);
extern void InstanceCuller_CreateCullJobTree_mF3C76EB7E9663B657C8C7F9ACA2E0995CF5D8A6B_AdjustorThunk (void);
extern void InstanceCuller_CreateCompactedVisibilityMaskJob_m3A2F38959953B889E5E7456DE426ABA7134DFB9B_AdjustorThunk (void);
extern void InstanceCuller_InstanceOccludersUpdated_mCA7D8171B44CF76E6A48314D7F97F2B698DCCE05_AdjustorThunk (void);
extern void InstanceCuller_DisposeCompactVisibilityMasks_mE05A5A23578160F5FDE1F8F2E59B7B8E2819BC76_AdjustorThunk (void);
extern void InstanceCuller_DisposeSceneViewHiddenBits_mB8548C25259A3AE546942634429EF581779992BD_AdjustorThunk (void);
extern void InstanceCuller_GetCompactedVisibilityMasks_mED15105E7567F79042B542B56B4DE2ABEDE14C26_AdjustorThunk (void);
extern void InstanceCuller_InstanceOcclusionTest_m8E12352E27A10F62B9C737C3C7D0394DBC416F15_AdjustorThunk (void);
extern void InstanceCuller_EnsureValidOcclusionTestResults_m22DA66230772FAE8FA78B7E2B3839AB414BE8A22_AdjustorThunk (void);
extern void InstanceCuller_AddOcclusionCullingDispatch_mDA6F922B0734577B8F921E9AE4C02CEE4F8A32A5_AdjustorThunk (void);
extern void InstanceCuller_FlushDebugCounters_m1036A68627B296B0CE88254424A25EEA430BCCE6_AdjustorThunk (void);
extern void InstanceCuller_OnBeginSceneViewCameraRendering_m9F271E3A4A411E077FC767C85C1813F41C9ADA3D_AdjustorThunk (void);
extern void InstanceCuller_OnEndSceneViewCameraRendering_m01D33D11A4853EB0397A2996F10795662828C2F8_AdjustorThunk (void);
extern void InstanceCuller_UpdateFrame_m9B437CF5FE15217CD1652135B256DFECE114418C_AdjustorThunk (void);
extern void InstanceCuller_OnBeginCameraRendering_mB7CDBC0AD43EEC324B9584B96C25DF3B911EC907_AdjustorThunk (void);
extern void InstanceCuller_OnEndCameraRendering_mF0DF0261BF1978BAFAC10F5F9DB533FC86B8089B_AdjustorThunk (void);
extern void InstanceCuller_Dispose_m7C8B413AE9FD5A5C4EAD81A22D2D4C1F2E6C368F_AdjustorThunk (void);
extern void PrefixSumDrawInstancesJob_Execute_m6384DA1CB6A1E053F358640B9E2FE7F378677771_AdjustorThunk (void);
extern void BuildDrawListsJob_Execute_m731DC4DAB67702B447641FF630CD1C60A596636E_AdjustorThunk (void);
extern void FindDrawInstancesJob_Execute_m011CC04D8F9DA0E7E3F04783AA60964898BEDC1D_AdjustorThunk (void);
extern void FindMaterialDrawInstancesJob_Execute_m0A8C723C617595319217DB5AFD151AC28F7759A0_AdjustorThunk (void);
extern void FindNonRegisteredMeshesJob_Execute_mC7CF993F6E8C86B01D3FE339DB98E19D5A6D25BD_AdjustorThunk (void);
extern void FindNonRegisteredMaterialsJob_Execute_m45EED4BFBE595919A846376A102DE6DBE15BD404_AdjustorThunk (void);
extern void RegisterNewMeshesJob_Execute_mE945F1F57AB8D7AD4E3450E937E7955501E12427_AdjustorThunk (void);
extern void RegisterNewMaterialsJob_Execute_m2C03AA455A70CA6262A10E28A88752C1A9BBB45E_AdjustorThunk (void);
extern void UpdatePackedMaterialDataCacheJob_ProcessMaterial_mCFF81645A1DB74D08DAAD17D679FA00A306E57E3_AdjustorThunk (void);
extern void UpdatePackedMaterialDataCacheJob_Execute_m117BE8C4EEAC8E86F3445464E84ECB6A954A3EBB_AdjustorThunk (void);
extern void GPUInstanceComponentDesc__ctor_m3D6BFA5A8BBF78939717DABD22CDEDE234780EE0_AdjustorThunk (void);
extern void ReadOnly__ctor_mF532002BB9D890FF1F0582B28EE81FFF81C4843D_AdjustorThunk (void);
extern void ReadOnly_CPUInstanceToGPUInstance_m01CDC3239F33A9D1A861D1816930C4A7E71440E5_AdjustorThunk (void);
extern void ReadOnly_CPUInstanceArrayToGPUInstanceArray_m246175FBDE0C68B01CF4B8B0F73087368CA7ACF3_AdjustorThunk (void);
extern void ConvertCPUInstancesToGPUInstancesJob_Execute_m16B5AACD9BEEFFA2046C2E1999F8C4AA7FF2B1D9_AdjustorThunk (void);
extern void GPUInstanceDataBufferBuilder_CreateMetadataValue_m50A13411B4799C2C197B9C4EFD3EED2BB4C25036_AdjustorThunk (void);
extern void GPUInstanceDataBufferBuilder_AddComponent_m26713FB7D74F3F47052241EEDE3D95D1502C35E8_AdjustorThunk (void);
extern void GPUInstanceDataBufferBuilder_Build_mDEF5AC49115B5D3CD195C5802389B93AE1C25C8C_AdjustorThunk (void);
extern void GPUInstanceDataBufferBuilder_Dispose_m1869839D0122EAE7FE7A7F9FE5356CCDE50D6636_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader__ctor_mAD57A016FC2E0940D4A469F418E161A1DCA8CC8C_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_GetUploadBufferPtr_m0CB3210A88F79E17329FDE29AD57C33C975D55DD_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_GetUIntPerInstance_mE171D8F73FE5CDE5D3F83C730F31877B8649A59C_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_GetParamUIntOffset_mF231C740B0554E7A82228DAEA5FE5F6BEBD978FD_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_AllocateUploadHandles_mAFB40A36A5FDAF5C560EB04B822012E57219A607_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_SubmitToGpu_m352C6DB1B32BBB2D141C76ECCB19F8FF55940105_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_SubmitToGpu_m6037322A91258909D757C712390F08395A36CF9C_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_Dispose_m0D8CBA64EB57BF6554F97E74B238F0A159B574B5_AdjustorThunk (void);
extern void GPUResources_LoadShaders_m9ACB6FC1CAC3C07D223AB04073FEFEBCD55F5CDA_AdjustorThunk (void);
extern void GPUResources_CreateResources_m07CBEC1195D765927E59B74DDFF7402B3608E22D_AdjustorThunk (void);
extern void GPUResources_Dispose_m2D71F420E19EDC6E791D5FD0BDAA81501C6017E6_AdjustorThunk (void);
extern void WriteInstanceDataParameterJob_Execute_mFBD8B3E368892E290A864D47C75EC3958286BC25_AdjustorThunk (void);
extern void GPUInstanceDataBufferGrower__ctor_m2555453CA56595D3963837F7226E4F85D11379B1_AdjustorThunk (void);
extern void GPUInstanceDataBufferGrower_SubmitToGpu_mF6B94F8512B09B0E57404C840A3C22D621C8EE9A_AdjustorThunk (void);
extern void GPUInstanceDataBufferGrower_Dispose_m24A1DE3F1FE59498294C9845783B541018F54BE3_AdjustorThunk (void);
extern void GPUResources_LoadShaders_m4C5A24A42FDCD3D04EBB7C2342E23D14748CBB89_AdjustorThunk (void);
extern void GPUResources_CreateResources_mD345D83E87BDDAF07F3A8628B9E1F1860BEC40C3_AdjustorThunk (void);
extern void GPUResources_Dispose_mBB023C03E82397C5CF141105338158EEDA6841AF_AdjustorThunk (void);
extern void InstanceHandle_get_index_mD1303DDB5E62977480B92E3DCECC534B1465CB94_AdjustorThunk (void);
extern void InstanceHandle_set_index_mC56210BD1B26D7B86BCD9BF2CE5341188DC1A234_AdjustorThunk (void);
extern void InstanceHandle_get_instanceIndex_mD7BAC819F382904784AC46D1F6A631B4A21CA8B5_AdjustorThunk (void);
extern void InstanceHandle_get_type_mAC674106B77C6008F547FBAB2FE02F263D3FA40C_AdjustorThunk (void);
extern void InstanceHandle_get_valid_m5629806E66255D75C3B1ED5826B0DA6D13B9E0CE_AdjustorThunk (void);
extern void InstanceHandle_Equals_mB7F950B845D19368217960B6FE7DBEA51B013C9E_AdjustorThunk (void);
extern void InstanceHandle_CompareTo_mC8763D64CE799F220739F8D3880F0725ADA9A93D_AdjustorThunk (void);
extern void InstanceHandle_GetHashCode_m256554043D1CEC35A1670DA2BDD39B453A4985D9_AdjustorThunk (void);
extern void SharedInstanceHandle_get_index_mA8EF25F39F6B747A2984740DC8A6A7F7F3B31606_AdjustorThunk (void);
extern void SharedInstanceHandle_set_index_mBD1FDAC50BC4D743EF40A7195E2DF1E3B4BC1840_AdjustorThunk (void);
extern void SharedInstanceHandle_get_valid_m723EEF4B52F015C0A6EC4BB621966C396311C865_AdjustorThunk (void);
extern void SharedInstanceHandle_Equals_m8318CF0264558ADD64222B3A2593EACCED56BFE0_AdjustorThunk (void);
extern void SharedInstanceHandle_CompareTo_m31419B958041797369105E6105847547F39F98C4_AdjustorThunk (void);
extern void SharedInstanceHandle_GetHashCode_m5B97E179A78BD59969291F66E286E00873FC120D_AdjustorThunk (void);
extern void GPUInstanceIndex_get_index_m0EDBD4FD5FC090990E2A24DCEBB5346B260C919D_AdjustorThunk (void);
extern void GPUInstanceIndex_set_index_m24EF3293A5E4CA20F4186F53B459500CDAE40687_AdjustorThunk (void);
extern void GPUInstanceIndex_get_valid_m5DBD00DEF2B390C4DB2E0B41686B6A09A064D27D_AdjustorThunk (void);
extern void GPUInstanceIndex_Equals_mEF7254FCD24AAD9A15FD3D1555306A5F1C908D23_AdjustorThunk (void);
extern void GPUInstanceIndex_CompareTo_m4CEFE1A49A0D625417B0123D3CA3AE385B785DCF_AdjustorThunk (void);
extern void GPUInstanceIndex_GetHashCode_mD3CFF7872E06CD6F455F81B019F03672C213D970_AdjustorThunk (void);
extern void InstanceAllocator_get_length_m762D1C624FF0B3D9F1DFED48EEA65465932C49DC_AdjustorThunk (void);
extern void InstanceAllocator_set_length_m02BBC305E32676069A1D7855C5FF776FD7F24172_AdjustorThunk (void);
extern void InstanceAllocator_get_valid_m86EC99BF39D782407913E9BAC1DB2D7CBA1FD797_AdjustorThunk (void);
extern void InstanceAllocator_Initialize_m299908C0326706B4BF44DDEC7CDA3D1074347F01_AdjustorThunk (void);
extern void InstanceAllocator_Dispose_mB710FC50CF870DBD3B226AE4B28057364D67237A_AdjustorThunk (void);
extern void InstanceAllocator_AllocateInstance_m846D1D4EA89D654F8AD4272579E3A7591FE155AB_AdjustorThunk (void);
extern void InstanceAllocator_FreeInstance_m4017808CB3B95F6FC5079EFDCC7CA13217C42E3C_AdjustorThunk (void);
extern void InstanceAllocator_GetNumAllocated_mC907C85B73DDEAFAE3606B4099F5C7E808DA8C18_AdjustorThunk (void);
extern void InstanceAllocators_Initialize_m1C63A61426D12441A3C4EABEC29B392FC93E90D9_AdjustorThunk (void);
extern void InstanceAllocators_Dispose_m0B2675D6FA6E95D9B9CC4617A94F408D9E80F5F7_AdjustorThunk (void);
extern void InstanceAllocators_GetInstanceAllocator_mF5B156CDAEDEEC2849590A544A5D99E691D0E37F_AdjustorThunk (void);
extern void InstanceAllocators_GetInstanceHandlesLength_mBFEDAC598685E4B38B1E67DECA4E75B8E345ACE0_AdjustorThunk (void);
extern void InstanceAllocators_GetInstancesLength_m7F121F6138F0713B01D5379DCA36255DF631D1C3_AdjustorThunk (void);
extern void InstanceAllocators_AllocateInstance_m1BA734BD0D1B20F166DEBCCEE8AC5B2B0911D9A4_AdjustorThunk (void);
extern void InstanceAllocators_FreeInstance_m0EBB63A12A593C580C54770B70A6032C55237AF6_AdjustorThunk (void);
extern void InstanceAllocators_AllocateSharedInstance_mD61A96D871FD88F2826B307B6BBF976223E075CF_AdjustorThunk (void);
extern void InstanceAllocators_FreeSharedInstance_m0B4E4FDCEA191C4829065D705C241F8667B658D5_AdjustorThunk (void);
extern void CPUInstanceData_get_instancesLength_mEF0DF28231C26F0C2E7EDA6A7115A51F51E5109C_AdjustorThunk (void);
extern void CPUInstanceData_set_instancesLength_mA37EF6CFF372799E7F182362873F19898021B186_AdjustorThunk (void);
extern void CPUInstanceData_get_instancesCapacity_mB1E847621B15F7559FC491B24F54ABA253A73249_AdjustorThunk (void);
extern void CPUInstanceData_set_instancesCapacity_m2BC970C6B7AA237310D79F3994737B40F962EE1D_AdjustorThunk (void);
extern void CPUInstanceData_get_handlesLength_mA9DC002A27BBD94B09B5944B84C6F47C30692F48_AdjustorThunk (void);
extern void CPUInstanceData_Initialize_mCB28B3C43BE61390B76A1B9C1C79BBFA33A33FD0_AdjustorThunk (void);
extern void CPUInstanceData_Dispose_mA6CD2735982440ECA65880116C2D214E97ADFC20_AdjustorThunk (void);
extern void CPUInstanceData_Grow_m54A454E32FB7C164E757F05A453DB71BBF6E1907_AdjustorThunk (void);
extern void CPUInstanceData_AddUnsafe_m1F5C8C517A8258670937584F1DD66E9D47724BC2_AdjustorThunk (void);
extern void CPUInstanceData_InstanceToIndex_m9D4701DC598D9046898239AF94F4E87F6A0F14EE_AdjustorThunk (void);
extern void CPUInstanceData_IndexToInstance_m3C95DBA9646291FACE0234AC811183928C232321_AdjustorThunk (void);
extern void CPUInstanceData_IsValidInstance_m5B3FF0F8456CF25EE209E68A230D594BB5DB36A3_AdjustorThunk (void);
extern void CPUInstanceData_IsFreeInstanceHandle_mEEBB421BA3391592FEF206095FBBC6A4BF6D9589_AdjustorThunk (void);
extern void CPUInstanceData_IsValidIndex_m4BE52A79DE124AA4872297A76A0FEA1D114CA010_AdjustorThunk (void);
extern void CPUInstanceData_GetFreeInstancesCount_m1F0B5BDAFE8DC1DB434FC5682705494F3EDFE10C_AdjustorThunk (void);
extern void CPUInstanceData_EnsureFreeInstances_mB7036C8793D1CB89C1C371A605581CBB4E177484_AdjustorThunk (void);
extern void CPUInstanceData_AddNoGrow_mEB9B4B4BCDB7C718DA14898D903B5E0D45DE0455_AdjustorThunk (void);
extern void CPUInstanceData_Add_m0736A5D9F85B1EC37E061411CC00335DFCC44D5A_AdjustorThunk (void);
extern void CPUInstanceData_Remove_m599064EAE344F332CFE4CD00C9714E72623D6877_AdjustorThunk (void);
extern void CPUInstanceData_Set_mF5405865F66B4E168163746D8C5BCEE865DE81CD_AdjustorThunk (void);
extern void CPUInstanceData_SetDefault_mDE2B13667675847C626111DE77EDE63FE25B5E42_AdjustorThunk (void);
extern void CPUInstanceData_Get_SharedInstance_mE5ED4915B38984690F4AB5089634500FBCB6C3CB_AdjustorThunk (void);
extern void CPUInstanceData_Get_LocalToWorldIsFlipped_m19881EEED025A0FC45721F63DFCCC1C462F93107_AdjustorThunk (void);
extern void CPUInstanceData_Get_WorldAABB_mF9F14AE6B71FECD3BF51252AB06781687C9531EB_AdjustorThunk (void);
extern void CPUInstanceData_Get_TetrahedronCacheIndex_m7D09D9EBD350591CCD9A948AF1F55DA98F85D1B6_AdjustorThunk (void);
extern void CPUInstanceData_Get_WorldBounds_m59B12DD936A814F2E056E962A4889CD3FD473417_AdjustorThunk (void);
extern void CPUInstanceData_Get_MovedInCurrentFrame_m58A543967F67149E003AD3FE6FE4C45F9E87C869_AdjustorThunk (void);
extern void CPUInstanceData_Get_MovedInPreviousFrame_m9A0B9988AF8004828D13C96F89981F3C6AB15C29_AdjustorThunk (void);
extern void CPUInstanceData_Get_VisibleInPreviousFrame_m3E67A47CD7703E4345BEE5A7068855FE6A589BB5_AdjustorThunk (void);
extern void CPUInstanceData_Get_MeshLodData_m2C413661D7B9E15E2B095F5AAEFFD3DCE65E914F_AdjustorThunk (void);
extern void CPUInstanceData_Set_SharedInstance_mECEE018E02FA2F6502B59197A9AEF35FA2D35081_AdjustorThunk (void);
extern void CPUInstanceData_Set_LocalToWorldIsFlipped_mB2B243184FD3C74C54A5D8EC74542BC0E019EF29_AdjustorThunk (void);
extern void CPUInstanceData_Set_WorldAABB_m56D85CE12B7C589B053C471E803090DF9CA37073_AdjustorThunk (void);
extern void CPUInstanceData_Set_TetrahedronCacheIndex_mAC10B658EB1AB6EF405BC32A6DE8E88F7AA45AD5_AdjustorThunk (void);
extern void CPUInstanceData_Set_MovedInCurrentFrame_mB2081F967C2A8B6E6AC80F2E91DB3FD70851346F_AdjustorThunk (void);
extern void CPUInstanceData_Set_MovedInPreviousFrame_m096AF4BED24D559F13122047DF89E56C3C431B4D_AdjustorThunk (void);
extern void CPUInstanceData_Set_VisibleInPreviousFrame_m53D87829915906D1DD8E19F1F8D51631E9C02620_AdjustorThunk (void);
extern void CPUInstanceData_Set_MeshLodData_mB14CED13A879C4E0CA7A8E27228642E1AF7D44FF_AdjustorThunk (void);
extern void CPUInstanceData_AsReadOnly_m90E90427C88E3F72D123C44F0E7957BACDDA8306_AdjustorThunk (void);
extern void ReadOnly_get_handlesLength_m50556F07B2F7144758202F76E202ED795E715DE9_AdjustorThunk (void);
extern void ReadOnly_get_instancesLength_m0E85E521D7DB61518C6988B08646386F20A17307_AdjustorThunk (void);
extern void ReadOnly__ctor_mFAEAA65432C0711F606AC98FD2320032B60A3309_AdjustorThunk (void);
extern void ReadOnly_InstanceToIndex_mA64D7447F6466C660DE83765B1B42673D922CCE2_AdjustorThunk (void);
extern void ReadOnly_IndexToInstance_m5BB2CA43E37DBA390AB7F29E6B5357A05226EE1B_AdjustorThunk (void);
extern void ReadOnly_IsValidInstance_m560D63CCD78D3661349A6134A30A29EFEDD3C30A_AdjustorThunk (void);
extern void ReadOnly_IsValidIndex_m2E036F94E4DFD41DF1CCA4148915594457CF0A9B_AdjustorThunk (void);
extern void CPUPerCameraInstanceData_get_instancesLength_m2FCFD20153EC6AACB2340D337BB18BF616A4198F_AdjustorThunk (void);
extern void CPUPerCameraInstanceData_set_instancesLength_m3CB40B1AB4F2FD153BD4F869F93AA91CB594C0A0_AdjustorThunk (void);
extern void CPUPerCameraInstanceData_get_instancesCapacity_m769EC460DAC66EE0EEAB7EDB3AC0184573C20B3B_AdjustorThunk (void);
extern void CPUPerCameraInstanceData_set_instancesCapacity_m4B4D614181686095CD8983D09FACB5EE1EF30223_AdjustorThunk (void);
extern void CPUPerCameraInstanceData_get_cameraCount_m4ED64DF61C7EEFDD0B771A9F3F80A46F40266FA8_AdjustorThunk (void);
extern void CPUPerCameraInstanceData_Initialize_m8FF812DA16EBDB44A3342DB1FE0CA66B39CAF543_AdjustorThunk (void);
extern void CPUPerCameraInstanceData_DeallocateCameras_mD90424A33180D27D17C51148E7C439D19CCE79DF_AdjustorThunk (void);
extern void CPUPerCameraInstanceData_AllocateCameras_m6C0A0DA2F8AB0E201801C3D5D67DCA28A677E23C_AdjustorThunk (void);
extern void CPUPerCameraInstanceData_Remove_m49A44C5456AC0C589DAF869E031D7F0113BA86BD_AdjustorThunk (void);
extern void CPUPerCameraInstanceData_IncreaseInstanceCount_mB3E25932F6796ED52CA77FD2CE1EF2C2F181F582_AdjustorThunk (void);
extern void CPUPerCameraInstanceData_Dispose_m217073CE624DE6B4D4087A252B22296C6B6D8964_AdjustorThunk (void);
extern void CPUPerCameraInstanceData_Grow_mA24034B63E5825884058DFE3C4BC602E61242846_AdjustorThunk (void);
extern void CPUPerCameraInstanceData_SetDefault_m8F8F7A49EBF71312638CD9E75C861AAEC1493B8B_AdjustorThunk (void);
extern void PerCameraInstanceDataArrays_get_IsCreated_m36CA4102C1FC8F52C1C84D42C73DE22F4245EBB8_AdjustorThunk (void);
extern void PerCameraInstanceDataArrays__ctor_m1959ED65DC23B84993E219642F408B7E833EC465_AdjustorThunk (void);
extern void PerCameraInstanceDataArrays_Dispose_mB47A82DE41B69F0F6D0488317687ED8A0EBBE793_AdjustorThunk (void);
extern void PerCameraInstanceDataArrays_Remove_m937A118CFDBB9A6C9B7D8FF4481DC0673DA4974C_AdjustorThunk (void);
extern void PerCameraInstanceDataArrays_Grow_m9ED1561118DD5ABF4A11D5F73359AE1D6A7D054A_AdjustorThunk (void);
extern void PerCameraInstanceDataArrays_SetDefault_mB88E73D0AB2BC49675950883B49FE12625B0771D_AdjustorThunk (void);
extern void CPUSharedInstanceData_get_instancesLength_m6C9D2E7F861FF43E23759E8816653646F41038B6_AdjustorThunk (void);
extern void CPUSharedInstanceData_set_instancesLength_m62AE1E8890A80D570FD3D11CD9E7C76B9D19124F_AdjustorThunk (void);
extern void CPUSharedInstanceData_get_instancesCapacity_mE40EF07D597175079E8DB6639E444A8346BD5C67_AdjustorThunk (void);
extern void CPUSharedInstanceData_set_instancesCapacity_mEBAB461BBCD3CD8127252B3F6156431A24943A69_AdjustorThunk (void);
extern void CPUSharedInstanceData_get_handlesLength_mF238FD08383695F2D7F707F2FA87279FE939CE61_AdjustorThunk (void);
extern void CPUSharedInstanceData_Initialize_m0D70CFA6688D2B1A165CDB20AB1760CBB4604A6E_AdjustorThunk (void);
extern void CPUSharedInstanceData_Dispose_m37AA8B81C3E42B81EC8E948B3F3DF9197B75E48C_AdjustorThunk (void);
extern void CPUSharedInstanceData_Grow_m0AED29A47C913CD92A9EA6281FABBBE76C4C26F3_AdjustorThunk (void);
extern void CPUSharedInstanceData_AddUnsafe_mD66018426B29B8FC77CBA218E04D6470D3A08B69_AdjustorThunk (void);
extern void CPUSharedInstanceData_SharedInstanceToIndex_m8D1697AC229D8D0D83FA702F6F440132FA123282_AdjustorThunk (void);
extern void CPUSharedInstanceData_IndexToSharedInstance_m7E58052D57FC86E8F4C0571D1213F2D4774BD323_AdjustorThunk (void);
extern void CPUSharedInstanceData_InstanceToIndex_mA58DA1C16D52FECF5ED6238B168203837D75F87C_AdjustorThunk (void);
extern void CPUSharedInstanceData_IsValidInstance_m33E12C093A6F0194B4D388782B15C2DD64616384_AdjustorThunk (void);
extern void CPUSharedInstanceData_IsFreeInstanceHandle_m972EDE9AF42DAADE66DAA3B1E1A8F8A85B594BFA_AdjustorThunk (void);
extern void CPUSharedInstanceData_IsValidIndex_m24116C260518DD680C3CDBD5BB1797B7764DDBCD_AdjustorThunk (void);
extern void CPUSharedInstanceData_GetFreeInstancesCount_mC18CC336EB3ED96DA46A39019C480485B58A26F5_AdjustorThunk (void);
extern void CPUSharedInstanceData_EnsureFreeInstances_m9276B1E0AE6DE0DFAE5F6FA4AFFB7DD9E6939CB0_AdjustorThunk (void);
extern void CPUSharedInstanceData_AddNoGrow_mAF3FBC643ABC12255E182EE8F8D26024AD8BBACD_AdjustorThunk (void);
extern void CPUSharedInstanceData_Add_m3AFDBA31C2E77E106BD970496C5F0EFBDCB3FCD3_AdjustorThunk (void);
extern void CPUSharedInstanceData_Remove_m1BA13D76461FC093E7667EA3746FC4A6C3E99EF9_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_RendererGroupID_m09F08F1763DD649A37D88B169CA50B2BD4F71768_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_MeshID_m70DD07FFBE12B43BA2546547FD26BC268E28EF43_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_LocalAABB_mB7D5A9EA9CD43BBEE3E938D31500A518FD3C02D7_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_Flags_m4F22F6B4DB27966ED5F633612B135EDCD274E341_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_LODGroupAndMask_m4A750431421271D4D2960E84D4B3AD83F96A8A42_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_GameObjectLayer_m75D2D7344A04236BFAE513142B29AB6CC8E7CB4D_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_RefCount_mE216F6749BB1B06E795C52C2433EE85E6C57DC01_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_MaterialIDs_mCE6EC8119990B7324D473B6DFB27B91A076404FC_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_RendererGroupID_m06EFE6568E7FABED464A0A091DB7D8DE05FC8719_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_MeshID_m97990780D2C7B92B6A146C6FB243D08D2674EAA9_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_LocalAABB_mFA0C8491B2FCD9E1F5BB25FF598C7520D689D08C_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_Flags_m9C1CE3AC11B09B3B2C9F65422B2FD31D23C49F4D_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_LODGroupAndMask_mD27421C024B1779830BEFA1274063B72D7C182CA_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_GameObjectLayer_m97AEE383F753133901141CCD0040B7D2C2E41EEC_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_RefCount_m7C5AF143DDC85304B2840A688BB2DD095A03B7CC_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_MaterialIDs_m3C8A0B397DBC9FEFF9661015CC32232A4910D812_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_m1236559425DDDA4FAF0765307F13B64FCA01C2BB_AdjustorThunk (void);
extern void CPUSharedInstanceData_SetDefault_m9241E530C50F996744FC4691DCD87C0E30602237_AdjustorThunk (void);
extern void CPUSharedInstanceData_AsReadOnly_mC186E7142A7E53BFE6D90BB0AB1841600BE7CBF8_AdjustorThunk (void);
extern void ReadOnly_get_handlesLength_mEAE03E557C81624E1F2CD280610A867E764EE456_AdjustorThunk (void);
extern void ReadOnly_get_instancesLength_m82B5F22A5EE9DF8AB94546D3B739C1EB36AD38D6_AdjustorThunk (void);
extern void ReadOnly__ctor_mDBC17F900B61DCF328F4BE2CDF0464E97F44614D_AdjustorThunk (void);
extern void ReadOnly_SharedInstanceToIndex_m7CD21D39C3285388B428D1CDCAA9AFB10A0178B4_AdjustorThunk (void);
extern void ReadOnly_IndexToSharedInstance_mC97CAB3F6DB3C92BCB528F85F5B9E42728DE3389_AdjustorThunk (void);
extern void ReadOnly_IsValidSharedInstance_mC2056D855CF0ED6FA050810CE1035680DC453A1A_AdjustorThunk (void);
extern void ReadOnly_IsValidIndex_mA6B2DAE40129D5F001698A34D413EE0CEB24E61D_AdjustorThunk (void);
extern void ReadOnly_InstanceToIndex_mE76C9EBF3ACCFDD5D73B3A99925CAE88DE2DDBE8_AdjustorThunk (void);
extern void SmallIntegerArray_get_Valid_m717ECD9B73FD5A373F40B0A623CB2C8E188012C0_AdjustorThunk (void);
extern void SmallIntegerArray_set_Valid_mF9C378DA26AB93E1AB38CCD7371CA23ABB6779D3_AdjustorThunk (void);
extern void SmallIntegerArray__ctor_mD7860879B9A093BE8CD0CFD815D125FDB746090E_AdjustorThunk (void);
extern void SmallIntegerArray_get_Item_m81A85EC298981F31BB405CA783D90BB96697C2DE_AdjustorThunk (void);
extern void SmallIntegerArray_set_Item_mFEF4007D7D6DA125990D22E32B34ADCBF7056EA2_AdjustorThunk (void);
extern void SmallIntegerArray_Dispose_m11D070ED0582D69C7F631CAF8C44F93183D73579_AdjustorThunk (void);
extern void EditorInstanceDataArrays_Initialize_m9893832043B3485AD59127B93D0872405CF77AFC_AdjustorThunk (void);
extern void EditorInstanceDataArrays_Dispose_m16E99865063B2150E7BEFCE3BAE55831D9532B3B_AdjustorThunk (void);
extern void EditorInstanceDataArrays_Grow_m57AFD6C22AA320DA1E98981C894EEB1F96C0B041_AdjustorThunk (void);
extern void EditorInstanceDataArrays_Remove_mC3970E725DCDCEA7B636C2DCB4732C432787AE35_AdjustorThunk (void);
extern void EditorInstanceDataArrays_SetDefault_mBB385CE94C9E46ADADB174CEFB393C2A19245724_AdjustorThunk (void);
extern void ReadOnly__ctor_m2029FDC41CDEC78F717316642DB33534904EC6E2_AdjustorThunk (void);
extern void QueryRendererGroupInstancesCountJob_Execute_m78EBD1A4D27FC820DB994D726466672AE6698114_AdjustorThunk (void);
extern void ComputeInstancesOffsetAndResizeInstancesArrayJob_Execute_m7278A70AAF09ACD7DE06A58BBE53617ED59B3178_AdjustorThunk (void);
extern void QueryRendererGroupInstancesJob_Execute_m689B8C96E8D3739314972C71A89F43C5DD8DD5EC_AdjustorThunk (void);
extern void QueryRendererGroupInstancesMultiJob_Execute_m3ED5F46C8E2BBCFD2302EBF30F55D07CB4292593_AdjustorThunk (void);
extern void QuerySortedMeshInstancesJob_Execute_m0B30D3DBCCB8FE5833D677F735387912A448CCDF_AdjustorThunk (void);
extern void CalculateInterpolatedLightAndOcclusionProbesBatchJob_Execute_mB29926232C46662441F96B9405E9BE4D3A84109B_AdjustorThunk (void);
extern void ScatterTetrahedronCacheIndicesJob_Execute_m157E3BAA3AA5106CD5629211DB4DF10C941B0942_AdjustorThunk (void);
extern void TransformUpdateJob_Execute_m0C99BC8B89AD60C1B495E471F9F02FBEEF7F8342_AdjustorThunk (void);
extern void ProbesUpdateJob_Execute_mFACBB95DE46DB9C879C2EE745CE37C4AEFB69AD6_AdjustorThunk (void);
extern void MotionUpdateJob_Execute_m266055F93AABA39FD53E7EDBD6BAFE55F586891B_AdjustorThunk (void);
extern void UpdateRendererInstancesJob_Execute_m93597D2184F6460DD4610AD8E47868B1BA87A11C_AdjustorThunk (void);
extern void CollectInstancesLODGroupsAndMasksJob_Execute_m2EFF9AB4F3476E491E51143F7F60F79020A0F780_AdjustorThunk (void);
extern void GetVisibleNonProcessedTreeInstancesJob_Execute_mEFBAD1E57760EF2F4C2A4F4E6F6F40E6261C284A_AdjustorThunk (void);
extern void UpdateCompactedInstanceVisibilityJob_Execute_mDFAB7C16E38743439A74CCA9AA310B383D2BA55B_AdjustorThunk (void);
extern void InstanceNumInfo_InitDefault_m8CC8A9E8EBF0EF16311F391E0312C46A7219A920_AdjustorThunk (void);
extern void InstanceNumInfo__ctor_m0C64766A7024C367CB84BA96F01861E951351650_AdjustorThunk (void);
extern void InstanceNumInfo__ctor_mCFED34B4FC73F15366339611E22502A8366B016C_AdjustorThunk (void);
extern void InstanceNumInfo_GetInstanceNum_m42CC8341EB7A73444DB20B9B64ACF5377CA7CE19_AdjustorThunk (void);
extern void InstanceNumInfo_GetInstanceNumIncludingChildren_m45CB40CE2452143C10B4FFC0F05FA7751E416368_AdjustorThunk (void);
extern void InstanceNumInfo_GetTotalInstanceNum_m55D6043571FB3440889F98A7D638F6A43D060982_AdjustorThunk (void);
extern void OccluderHandles_IsValid_m9FF256155930C1B40D4195C263AF38FCEB1B7904_AdjustorThunk (void);
extern void OccluderHandles_UseForOcclusionTest_m8E9F3CDFD218EC3C5A70DA375F156DC75CB2CAAD_AdjustorThunk (void);
extern void OccluderHandles_UseForOccluderUpdate_m4376DEB9151FDF5678FDFE2ED525A3B3AC31AF03_AdjustorThunk (void);
extern void IndirectBufferAllocInfo_IsEmpty_m47785BE361D9B989BE0455AD1884831AB99E4009_AdjustorThunk (void);
extern void IndirectBufferAllocInfo_IsWithinLimits_mE8807A3A2DDE4F3E3E9091E2441D847F4BFC4067_AdjustorThunk (void);
extern void IndirectBufferAllocInfo_GetExtraDrawInfoSlotIndex_m2EAD4191867631265610C43212C34A56A2DAD969_AdjustorThunk (void);
extern void IndirectBufferContext__ctor_mC7F33D8E6E9DECDB5A167C9CFD68F1CBB6E7D7F5_AdjustorThunk (void);
extern void IndirectBufferContext_Matches_m40E2A7974D4B205D4BB770D4A68635D99FE4C721_AdjustorThunk (void);
extern void OccluderContext_get_subviewCount_m49FDF73077D5C9F1789BA0C35A39A7F78FCBF414_AdjustorThunk (void);
extern void OccluderContext_IsSubviewValid_m88BE2A076AC851E9D11AB02B30ECE80A7E4D6BE0_AdjustorThunk (void);
extern void OccluderContext_get_depthBufferSizeInOccluderPixels_mF734AB99EBF484188554B86CB2E07048E6138C36_AdjustorThunk (void);
extern void OccluderContext_Dispose_mF02789AFBB76CD0F4491CDD8A83BCD15938F22D8_AdjustorThunk (void);
extern void OccluderContext_UpdateMipBounds_m71B416ED1B6D8827B30F8B1C1E69870328E6FCD9_AdjustorThunk (void);
extern void OccluderContext_AllocateTexturesIfNecessary_m4F0340120C018852B2A697194F87A6C539D6C127_AdjustorThunk (void);
extern void OccluderContext_SetupFarDepthPyramidConstants_m69DC95369344843C149515C9A2A9312DD0E99571_AdjustorThunk (void);
extern void OccluderContext_CreateFarDepthPyramid_mAE9A7D75C802A5AB3F91ED35C2BF61DA52C0AB0F_AdjustorThunk (void);
extern void OccluderContext_Import_m59CDBBE79F1A96B17BE009D5D561E35D69DA19D7_AdjustorThunk (void);
extern void OccluderContext_PrepareOccluders_m558D900C293C248A3CEE8FE6640C98873039DF1D_AdjustorThunk (void);
extern void OccluderContext_GetDebugOutput_m0B03B350C81D50A7DA8CEB969E70AF0F782675C5_AdjustorThunk (void);
extern void IndirectBufferContextHandles_UseForOcclusionTest_m098E1C673230461EF27D4BC76A7186A5DADEEDAB_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_instanceBuffer_mD965845788AD240262E1C46BB4BA405B73C8EA27_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_instanceInfoBuffer_mC14C040CC88B2F11AFE1301EB1FB9C70E396E8E5_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_argsBuffer_m81BE13A707FAF6B8F9361AEBCDD9CBA69611C334_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_drawInfoBuffer_m0168780ED5CFCB3613F04F2C00561471352A5790_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_visibleInstanceBufferHandle_mBD86573F23F6B0F8BF6C46C2996C449E571DC87F_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_indirectArgsBufferHandle_m7DF465FC2B94D42E1ABE0EE29E0EE4A1C850E925_AdjustorThunk (void);
extern void IndirectBufferContextStorage_ImportBuffers_mB024C5F1494D76DFEC07CDE6065AC7ACBB35E265_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_instanceInfoGlobalArray_m6D87D347531D8E3C00FB0967CCAEE69FED54FC41_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_drawInfoGlobalArray_mF90CEAECE8B83E043E462A4495421A574CC257D4_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_allocationCounters_mF22684F6D0214AE0755E3DFA8BA047C9E63C70FC_AdjustorThunk (void);
extern void IndirectBufferContextStorage_Init_mE1D9E857917AF5759F2E08D6B008C991CE5558AF_AdjustorThunk (void);
extern void IndirectBufferContextStorage_AllocateInstanceBuffers_m83064D5ABC47473BE4547D8CDB301CC989362AD4_AdjustorThunk (void);
extern void IndirectBufferContextStorage_FreeInstanceBuffers_mE035A24BD6179866735657D3D74C416AD39C597D_AdjustorThunk (void);
extern void IndirectBufferContextStorage_AllocateDrawBuffers_mCB3267B00E8328A5031AAB859542061D665CE0A0_AdjustorThunk (void);
extern void IndirectBufferContextStorage_FreeDrawBuffers_mAB74E77AD8B6348D492585D0ACB5AE42C2CEB171_AdjustorThunk (void);
extern void IndirectBufferContextStorage_Dispose_mD7278C175C4C23BCB9190D68CE8343B543A24302_AdjustorThunk (void);
extern void IndirectBufferContextStorage_SyncContexts_mB63C2465AF6D851CA68041FA1C12E24F24874A32_AdjustorThunk (void);
extern void IndirectBufferContextStorage_ResetAllocators_m327B8163AEEAFE360011EAEBFBD80C96C6403467_AdjustorThunk (void);
extern void IndirectBufferContextStorage_GrowBuffers_mAB6F1A326FB73F38678D592C809333FAE5A25D44_AdjustorThunk (void);
extern void IndirectBufferContextStorage_ClearContextsAndGrowBuffers_m0B059924A2DB871C3C80B03DAF54A47C8A47E573_AdjustorThunk (void);
extern void IndirectBufferContextStorage_TryAllocateContext_mC8D472C07C986BDCFC55CF8C4CE324230A38618C_AdjustorThunk (void);
extern void IndirectBufferContextStorage_TryGetContextIndex_m848B7C96E09D5CE2C6D12221D0B78EA4DCE77EF1_AdjustorThunk (void);
extern void IndirectBufferContextStorage_GetAllocInfoSubArray_m053D08700A1B5D4DB1AAF31B508E33E9FF418A4D_AdjustorThunk (void);
extern void IndirectBufferContextStorage_GetAllocInfo_m9BF31C85DBB94B0D0F1B82AF66DA8493AB1B2595_AdjustorThunk (void);
extern void IndirectBufferContextStorage_CopyFromStaging_mE99297FD5192A7F628FD7DC704E877280DB03891_AdjustorThunk (void);
extern void IndirectBufferContextStorage_GetLimits_m2E8DDC54719944794D7342725107396F59D5CF71_AdjustorThunk (void);
extern void IndirectBufferContextStorage_GetBufferContext_mD76FD0DD95CEE4D7D9B62F81573C4A05A5519941_AdjustorThunk (void);
extern void IndirectBufferContextStorage_SetBufferContext_mEC8E692A99E595592E12F55F94B7E234A340DC2A_AdjustorThunk (void);
extern void UpdateLODGroupTransformJob_Execute_m10AD64037A0D9FFAB351270A412B4B4342A6B8C5_AdjustorThunk (void);
extern void UpdateLODGroupDataJob_Execute_m37868AFCE3AFCC80D46C8156CA2713096FE5EC3D_AdjustorThunk (void);
extern void OcclusionTestComputeShader_Init_mC423CA21A41E4B44DB2715096F04D7498A80E960_AdjustorThunk (void);
extern void SilhouettePlaneCache_Init_mF14F33F1C7D6CD3704478C92314526747ABDFF0C_AdjustorThunk (void);
extern void SilhouettePlaneCache_Dispose_m52386469CD058770AAD5B4E19ADF603598BECEC5_AdjustorThunk (void);
extern void SilhouettePlaneCache_Update_m08599EDAF7CC1D053E0E54A8DF15F55E1E0B6342_AdjustorThunk (void);
extern void SilhouettePlaneCache_FreeUnusedSlots_m2249464604B48996B77945B9BBCDBDCECD2074C2_AdjustorThunk (void);
extern void SilhouettePlaneCache_GetSubArray_m0527F754CEEB54300C083A4BDAE9B56D026DA63C_AdjustorThunk (void);
extern void Slot__ctor_m0E99B79099FDB0098404A2FD223A9C029CFFF5D1_AdjustorThunk (void);
extern void OcclusionCullingCommonShaderVariables__ctor_m6098CCD0E939B2F9DE8715FF129DAE892745C610_AdjustorThunk (void);
extern void RenderersParameters__ctor_mD3445734819B50610F768B8E6EF49822D5ABEE8A_AdjustorThunk (void);
extern void ParamInfo_get_valid_m198E32DB1AAD1F43EAD6964E4EF79E79D078AF7D_AdjustorThunk (void);
extern void ParallelBitArray_get_Length_m82FE0E2AC9FAB29DA67E28FFAEA04EB642955B08_AdjustorThunk (void);
extern void ParallelBitArray_get_IsCreated_m31B6CFD3C95548F523C3D074463B827B7CD7A535_AdjustorThunk (void);
extern void ParallelBitArray__ctor_m7BB9EA31D2DF48FD4BE5D8773C539A76C5D4E6E4_AdjustorThunk (void);
extern void ParallelBitArray_Dispose_m24CBECA125F3D0090E9786E6AF56CB2E6DE452C6_AdjustorThunk (void);
extern void ParallelBitArray_Dispose_m3806D56E9788D3C69BD6C6985C6E2F949A89F9B7_AdjustorThunk (void);
extern void ParallelBitArray_Resize_mA268182EEF9B41198BFC3780A74CF12D9B232011_AdjustorThunk (void);
extern void ParallelBitArray_Set_mEDB30931801E1F71F11F62CCB23F3537EEE0F1EA_AdjustorThunk (void);
extern void ParallelBitArray_Get_m250C22A6191BAF4C5B314EFAF451E391D251F2B0_AdjustorThunk (void);
extern void ParallelBitArray_GetChunk_m7303392F0138448DF74E7A709F38B500B9461ED2_AdjustorThunk (void);
extern void ParallelBitArray_SetChunk_m48943193199714BCF0925DD8E14C8EB651885629_AdjustorThunk (void);
extern void ParallelBitArray_InterlockedReadChunk_m19B068CDEB9686FE2DD8A42BF99D2011EEAFA84F_AdjustorThunk (void);
extern void ParallelBitArray_InterlockedOrChunk_m51A85AD8A6A6FFD317303DEABA6B9797B79CE658_AdjustorThunk (void);
extern void ParallelBitArray_ChunkCount_mE27E6F3D861AF09C5B31BD7F3964796B26A9C3B5_AdjustorThunk (void);
extern void ParallelBitArray_GetSubArray_m45E14868BB90EC98D0467ABDA3DAD1BD4BFC49DD_AdjustorThunk (void);
extern void ParallelBitArray_GetBitsArray_m31F100FDDB1EA0FE4E1592768ED14B843D0DD73D_AdjustorThunk (void);
extern void ParallelBitArray_FillZeroes_mDAE3DE6ACB91DE00B5DC5D50415B7A0F938A08BB_AdjustorThunk (void);
extern void RadixSortBucketCountJob_Execute_mF3ADEB0523C3DE92CB5CFEF01B65E72C9AB7C023_AdjustorThunk (void);
extern void RadixSortBatchPrefixSumJob_JobIndexPrefixSum_m9C47BE4B67FCFF29A8FA94D39589F9B9A2840EE3_AdjustorThunk (void);
extern void RadixSortBatchPrefixSumJob_Execute_m434849692F7D93EF83545890B59FC96BF14AED93_AdjustorThunk (void);
extern void RadixSortPrefixSumJob_Execute_mF1969BD6160F81BA429AF74A8944935FC83BC551_AdjustorThunk (void);
extern void RadixSortBucketSortJob_Execute_mDB0AB3CD468DA898E41CBF3E9EF5BE26AD26E4D2_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[366] = 
{
	{ 0x06000005, AABB_get_min_m209EF3ECD01E859258E1BDE8780CA4C25276DF70_AdjustorThunk },
	{ 0x06000006, AABB_get_max_mCD29B3A2C2DEA60BBF50091D8E3C938511D82B08_AdjustorThunk },
	{ 0x06000007, AABB_ToString_mB0C529C757F63FD0F78316C90D2B86206159B678_AdjustorThunk },
	{ 0x0600007D, ReceiverPlanes_LightFacingFrustumPlaneSubArray_m995D1E681E0ED63DFA8489C582A2FCBAF1C9356B_AdjustorThunk },
	{ 0x0600007E, ReceiverPlanes_SilhouettePlaneSubArray_m851F735918EE9297C64815C42AAD446BCBE55F4B_AdjustorThunk },
	{ 0x06000080, ReceiverPlanes_Dispose_mE53DCBAD81DDB002760FC5661F3B26330B62ACF4_AdjustorThunk },
	{ 0x06000082, FrustumPlaneCuller_Dispose_m802E9F4605F17D55D46C6C0D308830667DE0A08B_AdjustorThunk },
	{ 0x06000085, PlanePacket4__ctor_m1B67A6C35FB1B6B6BE21D76B496DA3531F636CC7_AdjustorThunk },
	{ 0x06000087, ReceiverSphereCuller_Dispose_mFCC83320D29032DBC7A3173ADFE8F764A472FFA5_AdjustorThunk },
	{ 0x06000088, ReceiverSphereCuller_UseReceiverPlanes_mEA2EE8FE32563F3FBE40D6AADA9724719186674E_AdjustorThunk },
	{ 0x060000DD, FindRenderersFromMaterialOrMeshJob_Execute_m7B9713786ED58196A531F44ECABD1912DF45E59F_AdjustorThunk },
	{ 0x06000111, OcclusionCullingSettings__ctor_mE814849AC60B1DC1AC17E02F1AF2128DF38FE95A_AdjustorThunk },
	{ 0x06000112, OccluderSubviewUpdate__ctor_m482C60BE94ECE0B1F15E930936345D9F60E399A8_AdjustorThunk },
	{ 0x06000113, OccluderParameters__ctor_mBBA1CD9856BD109C9F1D18715B28E2DA95CE83D9_AdjustorThunk },
	{ 0x0600011C, RangeKey_Equals_m05E612C122D91758588CEB7529751045E6F09493_AdjustorThunk },
	{ 0x0600011D, RangeKey_GetHashCode_mC38845B6A1CC657D6F6B1149E7448AA6A3EF3257_AdjustorThunk },
	{ 0x0600011E, DrawKey_Equals_mBA2838871085C8944B81322DC6E851F4273E8E91_AdjustorThunk },
	{ 0x0600011F, DrawKey_GetHashCode_m6E3C5F3D42D02D8035AA7E7B5501FDBC1551F4E5_AdjustorThunk },
	{ 0x06000120, BinningConfig_get_visibilityConfigCount_m0B698DBD65F8147B56C14CA7CFB741EAB701B3B9_AdjustorThunk },
	{ 0x06000121, AnimateCrossFadeJob_Execute_mA3C0021BE25AE2F67FD93948B6115ECE990DAFE9_AdjustorThunk },
	{ 0x06000123, CullingJob_CalculateLODVisibility_mF87528623E251C0B920199D5DFB1021842A5F774_AdjustorThunk },
	{ 0x06000124, CullingJob_CalculateVisibilityMask_m285DA7E28A8582C95BAE2C6794C34FC3F8F195ED_AdjustorThunk },
	{ 0x06000125, CullingJob_ComputeMeshLODLevel_m0CF5B90B965B46AA61BD4B00EF15C1528BF03A0D_AdjustorThunk },
	{ 0x06000126, CullingJob_ComputeMeshLODCrossfade_mF911187408B6601080C28F1227FAF60DE832B750_AdjustorThunk },
	{ 0x06000127, CullingJob_EnforcePreviousFrameMeshLOD_m20C1DF1641349AC2B1B455E7A49D0DE6BC3497B2_AdjustorThunk },
	{ 0x06000128, CullingJob_Execute_m172CC218FA8F7198BD69C1C87294FD90D94DB884_AdjustorThunk },
	{ 0x06000129, AllocateBinsPerBatch_IsInstanceFlipped_m5ED8B8D355142D10DB3983D15A256B5DBD4D6ABA_AdjustorThunk },
	{ 0x0600012A, AllocateBinsPerBatch_IsMeshLodVisible_m9D1674B4CE766557E8D2234D56DF2BACC2EDB576_AdjustorThunk },
	{ 0x0600012B, AllocateBinsPerBatch_Execute_m8244D8F2860ED03B023A6D348D9471F2AF76E0F1_AdjustorThunk },
	{ 0x0600012C, PrefixSumDrawsAndInstances_Execute_m8EADFE8897EAE00866B67F8AE64CA774EE33B5F9_AdjustorThunk },
	{ 0x0600012D, DrawCommandOutputPerBatch_EncodeGPUInstanceIndexAndCrossFade_m6EFB0D6BB29BF2F65B66913BD2F82E4364ED8830_AdjustorThunk },
	{ 0x0600012E, DrawCommandOutputPerBatch_IsInstanceFlipped_m1AE2BD53A7D17783F262A94539417F423FD4CF79_AdjustorThunk },
	{ 0x0600012F, DrawCommandOutputPerBatch_IsMeshLodVisible_mF345AFA69786A2EB8E65B25BFDB933A6321F1054_AdjustorThunk },
	{ 0x06000130, DrawCommandOutputPerBatch_Execute_mF995D35E2E640A3172F4BC62E37BF103CFBBBBA7_AdjustorThunk },
	{ 0x06000131, CompactVisibilityMasksJob_Execute_m7D5B39DEFEC4944D35111B4B7D22267147B4AB1E_AdjustorThunk },
	{ 0x06000132, InstanceCullerSplitDebugArray_get_Counters_mBDB8C933AEEB9F934132442CA20FC54A9E9A01AB_AdjustorThunk },
	{ 0x06000133, InstanceCullerSplitDebugArray_Init_m0E461744D899802E8123D32B79AD2EEC7FF1ED66_AdjustorThunk },
	{ 0x06000134, InstanceCullerSplitDebugArray_Dispose_m0FA405A9BA291E560088D998A71EE3FEF95100C1_AdjustorThunk },
	{ 0x06000135, InstanceCullerSplitDebugArray_TryAddSplits_m8F0CAC17CB3DA747A187760D3961255C1E95BDF5_AdjustorThunk },
	{ 0x06000136, InstanceCullerSplitDebugArray_AddSync_m05D0080857890E3C8E46730B1D4DC17E4FB71D90_AdjustorThunk },
	{ 0x06000137, InstanceCullerSplitDebugArray_MoveToDebugStatsAndClear_mAA78838CCD17348C01F115022EF530C3F5B98AD4_AdjustorThunk },
	{ 0x06000138, InstanceOcclusionEventDebugArray_get_CounterBuffer_mF25E7B744518F980443AFCF79E48EA3CCF852D04_AdjustorThunk },
	{ 0x06000139, InstanceOcclusionEventDebugArray_Init_mF8AEF95440DA22ACC2DB5717CEBD9C8B8B3797D5_AdjustorThunk },
	{ 0x0600013A, InstanceOcclusionEventDebugArray_Dispose_m2D57744F19C60D3E9B6EFDF81BB08498853E37C1_AdjustorThunk },
	{ 0x0600013B, InstanceOcclusionEventDebugArray_TryAdd_m2FD080E0DDCA404C8C362A5933EC852A9CD5F109_AdjustorThunk },
	{ 0x0600013C, InstanceOcclusionEventDebugArray_MoveToDebugStatsAndClear_mFEF02F07D15F2F2942FABFD0C7C577FAB1859977_AdjustorThunk },
	{ 0x0600013D, Info_HasVersion_m9E077F93A4F89E728DF4FA88764ABF91C732D960_AdjustorThunk },
	{ 0x0600013E, InstanceCuller_Init_mDBBD40C7316EBE16746FACA43170B7E1001EC67F_AdjustorThunk },
	{ 0x0600013F, InstanceCuller_AnimateCrossFades_m5BE8DFEE24C03638DF57E12C23D4C2C80DE8B510_AdjustorThunk },
	{ 0x06000140, InstanceCuller_CreateFrustumCullingJob_m503045810FE1E550B3F436FD62750A505A4EAE3C_AdjustorThunk },
	{ 0x06000141, InstanceCuller_ComputeWorstCaseDrawCommandCount_mF523E170BDA54D6E88AD73742065FD88C60937DD_AdjustorThunk },
	{ 0x06000142, InstanceCuller_CreateCullJobTree_mF3C76EB7E9663B657C8C7F9ACA2E0995CF5D8A6B_AdjustorThunk },
	{ 0x06000143, InstanceCuller_CreateCompactedVisibilityMaskJob_m3A2F38959953B889E5E7456DE426ABA7134DFB9B_AdjustorThunk },
	{ 0x06000144, InstanceCuller_InstanceOccludersUpdated_mCA7D8171B44CF76E6A48314D7F97F2B698DCCE05_AdjustorThunk },
	{ 0x06000145, InstanceCuller_DisposeCompactVisibilityMasks_mE05A5A23578160F5FDE1F8F2E59B7B8E2819BC76_AdjustorThunk },
	{ 0x06000146, InstanceCuller_DisposeSceneViewHiddenBits_mB8548C25259A3AE546942634429EF581779992BD_AdjustorThunk },
	{ 0x06000147, InstanceCuller_GetCompactedVisibilityMasks_mED15105E7567F79042B542B56B4DE2ABEDE14C26_AdjustorThunk },
	{ 0x06000148, InstanceCuller_InstanceOcclusionTest_m8E12352E27A10F62B9C737C3C7D0394DBC416F15_AdjustorThunk },
	{ 0x06000149, InstanceCuller_EnsureValidOcclusionTestResults_m22DA66230772FAE8FA78B7E2B3839AB414BE8A22_AdjustorThunk },
	{ 0x0600014A, InstanceCuller_AddOcclusionCullingDispatch_mDA6F922B0734577B8F921E9AE4C02CEE4F8A32A5_AdjustorThunk },
	{ 0x0600014B, InstanceCuller_FlushDebugCounters_m1036A68627B296B0CE88254424A25EEA430BCCE6_AdjustorThunk },
	{ 0x0600014C, InstanceCuller_OnBeginSceneViewCameraRendering_m9F271E3A4A411E077FC767C85C1813F41C9ADA3D_AdjustorThunk },
	{ 0x0600014D, InstanceCuller_OnEndSceneViewCameraRendering_m01D33D11A4853EB0397A2996F10795662828C2F8_AdjustorThunk },
	{ 0x0600014E, InstanceCuller_UpdateFrame_m9B437CF5FE15217CD1652135B256DFECE114418C_AdjustorThunk },
	{ 0x0600014F, InstanceCuller_OnBeginCameraRendering_mB7CDBC0AD43EEC324B9584B96C25DF3B911EC907_AdjustorThunk },
	{ 0x06000150, InstanceCuller_OnEndCameraRendering_mF0DF0261BF1978BAFAC10F5F9DB533FC86B8089B_AdjustorThunk },
	{ 0x06000151, InstanceCuller_Dispose_m7C8B413AE9FD5A5C4EAD81A22D2D4C1F2E6C368F_AdjustorThunk },
	{ 0x06000165, PrefixSumDrawInstancesJob_Execute_m6384DA1CB6A1E053F358640B9E2FE7F378677771_AdjustorThunk },
	{ 0x06000167, BuildDrawListsJob_Execute_m731DC4DAB67702B447641FF630CD1C60A596636E_AdjustorThunk },
	{ 0x06000168, FindDrawInstancesJob_Execute_m011CC04D8F9DA0E7E3F04783AA60964898BEDC1D_AdjustorThunk },
	{ 0x06000169, FindMaterialDrawInstancesJob_Execute_m0A8C723C617595319217DB5AFD151AC28F7759A0_AdjustorThunk },
	{ 0x0600016A, FindNonRegisteredMeshesJob_Execute_mC7CF993F6E8C86B01D3FE339DB98E19D5A6D25BD_AdjustorThunk },
	{ 0x0600016B, FindNonRegisteredMaterialsJob_Execute_m45EED4BFBE595919A846376A102DE6DBE15BD404_AdjustorThunk },
	{ 0x0600016C, RegisterNewMeshesJob_Execute_mE945F1F57AB8D7AD4E3450E937E7955501E12427_AdjustorThunk },
	{ 0x0600016D, RegisterNewMaterialsJob_Execute_m2C03AA455A70CA6262A10E28A88752C1A9BBB45E_AdjustorThunk },
	{ 0x0600016E, UpdatePackedMaterialDataCacheJob_ProcessMaterial_mCFF81645A1DB74D08DAAD17D679FA00A306E57E3_AdjustorThunk },
	{ 0x0600016F, UpdatePackedMaterialDataCacheJob_Execute_m117BE8C4EEAC8E86F3445464E84ECB6A954A3EBB_AdjustorThunk },
	{ 0x060001AF, GPUInstanceComponentDesc__ctor_m3D6BFA5A8BBF78939717DABD22CDEDE234780EE0_AdjustorThunk },
	{ 0x060001BC, ReadOnly__ctor_mF532002BB9D890FF1F0582B28EE81FFF81C4843D_AdjustorThunk },
	{ 0x060001BD, ReadOnly_CPUInstanceToGPUInstance_m01CDC3239F33A9D1A861D1816930C4A7E71440E5_AdjustorThunk },
	{ 0x060001BE, ReadOnly_CPUInstanceArrayToGPUInstanceArray_m246175FBDE0C68B01CF4B8B0F73087368CA7ACF3_AdjustorThunk },
	{ 0x060001BF, ConvertCPUInstancesToGPUInstancesJob_Execute_m16B5AACD9BEEFFA2046C2E1999F8C4AA7FF2B1D9_AdjustorThunk },
	{ 0x060001C0, GPUInstanceDataBufferBuilder_CreateMetadataValue_m50A13411B4799C2C197B9C4EFD3EED2BB4C25036_AdjustorThunk },
	{ 0x060001C2, GPUInstanceDataBufferBuilder_AddComponent_m26713FB7D74F3F47052241EEDE3D95D1502C35E8_AdjustorThunk },
	{ 0x060001C3, GPUInstanceDataBufferBuilder_Build_mDEF5AC49115B5D3CD195C5802389B93AE1C25C8C_AdjustorThunk },
	{ 0x060001C4, GPUInstanceDataBufferBuilder_Dispose_m1869839D0122EAE7FE7A7F9FE5356CCDE50D6636_AdjustorThunk },
	{ 0x060001C5, GPUInstanceDataBufferUploader__ctor_mAD57A016FC2E0940D4A469F418E161A1DCA8CC8C_AdjustorThunk },
	{ 0x060001C6, GPUInstanceDataBufferUploader_GetUploadBufferPtr_m0CB3210A88F79E17329FDE29AD57C33C975D55DD_AdjustorThunk },
	{ 0x060001C7, GPUInstanceDataBufferUploader_GetUIntPerInstance_mE171D8F73FE5CDE5D3F83C730F31877B8649A59C_AdjustorThunk },
	{ 0x060001C8, GPUInstanceDataBufferUploader_GetParamUIntOffset_mF231C740B0554E7A82228DAEA5FE5F6BEBD978FD_AdjustorThunk },
	{ 0x060001CA, GPUInstanceDataBufferUploader_AllocateUploadHandles_mAFB40A36A5FDAF5C560EB04B822012E57219A607_AdjustorThunk },
	{ 0x060001CD, GPUInstanceDataBufferUploader_SubmitToGpu_m352C6DB1B32BBB2D141C76ECCB19F8FF55940105_AdjustorThunk },
	{ 0x060001CE, GPUInstanceDataBufferUploader_SubmitToGpu_m6037322A91258909D757C712390F08395A36CF9C_AdjustorThunk },
	{ 0x060001CF, GPUInstanceDataBufferUploader_Dispose_m0D8CBA64EB57BF6554F97E74B238F0A159B574B5_AdjustorThunk },
	{ 0x060001D1, GPUResources_LoadShaders_m9ACB6FC1CAC3C07D223AB04073FEFEBCD55F5CDA_AdjustorThunk },
	{ 0x060001D2, GPUResources_CreateResources_m07CBEC1195D765927E59B74DDFF7402B3608E22D_AdjustorThunk },
	{ 0x060001D3, GPUResources_Dispose_m2D71F420E19EDC6E791D5FD0BDAA81501C6017E6_AdjustorThunk },
	{ 0x060001D4, WriteInstanceDataParameterJob_Execute_mFBD8B3E368892E290A864D47C75EC3958286BC25_AdjustorThunk },
	{ 0x060001D5, GPUInstanceDataBufferGrower__ctor_m2555453CA56595D3963837F7226E4F85D11379B1_AdjustorThunk },
	{ 0x060001D6, GPUInstanceDataBufferGrower_SubmitToGpu_mF6B94F8512B09B0E57404C840A3C22D621C8EE9A_AdjustorThunk },
	{ 0x060001D7, GPUInstanceDataBufferGrower_Dispose_m24A1DE3F1FE59498294C9845783B541018F54BE3_AdjustorThunk },
	{ 0x060001D9, GPUResources_LoadShaders_m4C5A24A42FDCD3D04EBB7C2342E23D14748CBB89_AdjustorThunk },
	{ 0x060001DA, GPUResources_CreateResources_mD345D83E87BDDAF07F3A8628B9E1F1860BEC40C3_AdjustorThunk },
	{ 0x060001DB, GPUResources_Dispose_mBB023C03E82397C5CF141105338158EEDA6841AF_AdjustorThunk },
	{ 0x060001DC, InstanceHandle_get_index_mD1303DDB5E62977480B92E3DCECC534B1465CB94_AdjustorThunk },
	{ 0x060001DD, InstanceHandle_set_index_mC56210BD1B26D7B86BCD9BF2CE5341188DC1A234_AdjustorThunk },
	{ 0x060001DE, InstanceHandle_get_instanceIndex_mD7BAC819F382904784AC46D1F6A631B4A21CA8B5_AdjustorThunk },
	{ 0x060001DF, InstanceHandle_get_type_mAC674106B77C6008F547FBAB2FE02F263D3FA40C_AdjustorThunk },
	{ 0x060001E0, InstanceHandle_get_valid_m5629806E66255D75C3B1ED5826B0DA6D13B9E0CE_AdjustorThunk },
	{ 0x060001E3, InstanceHandle_Equals_mB7F950B845D19368217960B6FE7DBEA51B013C9E_AdjustorThunk },
	{ 0x060001E4, InstanceHandle_CompareTo_mC8763D64CE799F220739F8D3880F0725ADA9A93D_AdjustorThunk },
	{ 0x060001E5, InstanceHandle_GetHashCode_m256554043D1CEC35A1670DA2BDD39B453A4985D9_AdjustorThunk },
	{ 0x060001E7, SharedInstanceHandle_get_index_mA8EF25F39F6B747A2984740DC8A6A7F7F3B31606_AdjustorThunk },
	{ 0x060001E8, SharedInstanceHandle_set_index_mBD1FDAC50BC4D743EF40A7195E2DF1E3B4BC1840_AdjustorThunk },
	{ 0x060001E9, SharedInstanceHandle_get_valid_m723EEF4B52F015C0A6EC4BB621966C396311C865_AdjustorThunk },
	{ 0x060001EA, SharedInstanceHandle_Equals_m8318CF0264558ADD64222B3A2593EACCED56BFE0_AdjustorThunk },
	{ 0x060001EB, SharedInstanceHandle_CompareTo_m31419B958041797369105E6105847547F39F98C4_AdjustorThunk },
	{ 0x060001EC, SharedInstanceHandle_GetHashCode_m5B97E179A78BD59969291F66E286E00873FC120D_AdjustorThunk },
	{ 0x060001EE, GPUInstanceIndex_get_index_m0EDBD4FD5FC090990E2A24DCEBB5346B260C919D_AdjustorThunk },
	{ 0x060001EF, GPUInstanceIndex_set_index_m24EF3293A5E4CA20F4186F53B459500CDAE40687_AdjustorThunk },
	{ 0x060001F0, GPUInstanceIndex_get_valid_m5DBD00DEF2B390C4DB2E0B41686B6A09A064D27D_AdjustorThunk },
	{ 0x060001F1, GPUInstanceIndex_Equals_mEF7254FCD24AAD9A15FD3D1555306A5F1C908D23_AdjustorThunk },
	{ 0x060001F2, GPUInstanceIndex_CompareTo_m4CEFE1A49A0D625417B0123D3CA3AE385B785DCF_AdjustorThunk },
	{ 0x060001F3, GPUInstanceIndex_GetHashCode_mD3CFF7872E06CD6F455F81B019F03672C213D970_AdjustorThunk },
	{ 0x060001F5, InstanceAllocator_get_length_m762D1C624FF0B3D9F1DFED48EEA65465932C49DC_AdjustorThunk },
	{ 0x060001F6, InstanceAllocator_set_length_m02BBC305E32676069A1D7855C5FF776FD7F24172_AdjustorThunk },
	{ 0x060001F7, InstanceAllocator_get_valid_m86EC99BF39D782407913E9BAC1DB2D7CBA1FD797_AdjustorThunk },
	{ 0x060001F8, InstanceAllocator_Initialize_m299908C0326706B4BF44DDEC7CDA3D1074347F01_AdjustorThunk },
	{ 0x060001F9, InstanceAllocator_Dispose_mB710FC50CF870DBD3B226AE4B28057364D67237A_AdjustorThunk },
	{ 0x060001FA, InstanceAllocator_AllocateInstance_m846D1D4EA89D654F8AD4272579E3A7591FE155AB_AdjustorThunk },
	{ 0x060001FB, InstanceAllocator_FreeInstance_m4017808CB3B95F6FC5079EFDCC7CA13217C42E3C_AdjustorThunk },
	{ 0x060001FC, InstanceAllocator_GetNumAllocated_mC907C85B73DDEAFAE3606B4099F5C7E808DA8C18_AdjustorThunk },
	{ 0x060001FD, InstanceAllocators_Initialize_m1C63A61426D12441A3C4EABEC29B392FC93E90D9_AdjustorThunk },
	{ 0x060001FE, InstanceAllocators_Dispose_m0B2675D6FA6E95D9B9CC4617A94F408D9E80F5F7_AdjustorThunk },
	{ 0x060001FF, InstanceAllocators_GetInstanceAllocator_mF5B156CDAEDEEC2849590A544A5D99E691D0E37F_AdjustorThunk },
	{ 0x06000200, InstanceAllocators_GetInstanceHandlesLength_mBFEDAC598685E4B38B1E67DECA4E75B8E345ACE0_AdjustorThunk },
	{ 0x06000201, InstanceAllocators_GetInstancesLength_m7F121F6138F0713B01D5379DCA36255DF631D1C3_AdjustorThunk },
	{ 0x06000202, InstanceAllocators_AllocateInstance_m1BA734BD0D1B20F166DEBCCEE8AC5B2B0911D9A4_AdjustorThunk },
	{ 0x06000203, InstanceAllocators_FreeInstance_m0EBB63A12A593C580C54770B70A6032C55237AF6_AdjustorThunk },
	{ 0x06000204, InstanceAllocators_AllocateSharedInstance_mD61A96D871FD88F2826B307B6BBF976223E075CF_AdjustorThunk },
	{ 0x06000205, InstanceAllocators_FreeSharedInstance_m0B4E4FDCEA191C4829065D705C241F8667B658D5_AdjustorThunk },
	{ 0x06000206, CPUInstanceData_get_instancesLength_mEF0DF28231C26F0C2E7EDA6A7115A51F51E5109C_AdjustorThunk },
	{ 0x06000207, CPUInstanceData_set_instancesLength_mA37EF6CFF372799E7F182362873F19898021B186_AdjustorThunk },
	{ 0x06000208, CPUInstanceData_get_instancesCapacity_mB1E847621B15F7559FC491B24F54ABA253A73249_AdjustorThunk },
	{ 0x06000209, CPUInstanceData_set_instancesCapacity_m2BC970C6B7AA237310D79F3994737B40F962EE1D_AdjustorThunk },
	{ 0x0600020A, CPUInstanceData_get_handlesLength_mA9DC002A27BBD94B09B5944B84C6F47C30692F48_AdjustorThunk },
	{ 0x0600020B, CPUInstanceData_Initialize_mCB28B3C43BE61390B76A1B9C1C79BBFA33A33FD0_AdjustorThunk },
	{ 0x0600020C, CPUInstanceData_Dispose_mA6CD2735982440ECA65880116C2D214E97ADFC20_AdjustorThunk },
	{ 0x0600020D, CPUInstanceData_Grow_m54A454E32FB7C164E757F05A453DB71BBF6E1907_AdjustorThunk },
	{ 0x0600020E, CPUInstanceData_AddUnsafe_m1F5C8C517A8258670937584F1DD66E9D47724BC2_AdjustorThunk },
	{ 0x0600020F, CPUInstanceData_InstanceToIndex_m9D4701DC598D9046898239AF94F4E87F6A0F14EE_AdjustorThunk },
	{ 0x06000210, CPUInstanceData_IndexToInstance_m3C95DBA9646291FACE0234AC811183928C232321_AdjustorThunk },
	{ 0x06000211, CPUInstanceData_IsValidInstance_m5B3FF0F8456CF25EE209E68A230D594BB5DB36A3_AdjustorThunk },
	{ 0x06000212, CPUInstanceData_IsFreeInstanceHandle_mEEBB421BA3391592FEF206095FBBC6A4BF6D9589_AdjustorThunk },
	{ 0x06000213, CPUInstanceData_IsValidIndex_m4BE52A79DE124AA4872297A76A0FEA1D114CA010_AdjustorThunk },
	{ 0x06000214, CPUInstanceData_GetFreeInstancesCount_m1F0B5BDAFE8DC1DB434FC5682705494F3EDFE10C_AdjustorThunk },
	{ 0x06000215, CPUInstanceData_EnsureFreeInstances_mB7036C8793D1CB89C1C371A605581CBB4E177484_AdjustorThunk },
	{ 0x06000216, CPUInstanceData_AddNoGrow_mEB9B4B4BCDB7C718DA14898D903B5E0D45DE0455_AdjustorThunk },
	{ 0x06000217, CPUInstanceData_Add_m0736A5D9F85B1EC37E061411CC00335DFCC44D5A_AdjustorThunk },
	{ 0x06000218, CPUInstanceData_Remove_m599064EAE344F332CFE4CD00C9714E72623D6877_AdjustorThunk },
	{ 0x06000219, CPUInstanceData_Set_mF5405865F66B4E168163746D8C5BCEE865DE81CD_AdjustorThunk },
	{ 0x0600021A, CPUInstanceData_SetDefault_mDE2B13667675847C626111DE77EDE63FE25B5E42_AdjustorThunk },
	{ 0x0600021B, CPUInstanceData_Get_SharedInstance_mE5ED4915B38984690F4AB5089634500FBCB6C3CB_AdjustorThunk },
	{ 0x0600021C, CPUInstanceData_Get_LocalToWorldIsFlipped_m19881EEED025A0FC45721F63DFCCC1C462F93107_AdjustorThunk },
	{ 0x0600021D, CPUInstanceData_Get_WorldAABB_mF9F14AE6B71FECD3BF51252AB06781687C9531EB_AdjustorThunk },
	{ 0x0600021E, CPUInstanceData_Get_TetrahedronCacheIndex_m7D09D9EBD350591CCD9A948AF1F55DA98F85D1B6_AdjustorThunk },
	{ 0x0600021F, CPUInstanceData_Get_WorldBounds_m59B12DD936A814F2E056E962A4889CD3FD473417_AdjustorThunk },
	{ 0x06000220, CPUInstanceData_Get_MovedInCurrentFrame_m58A543967F67149E003AD3FE6FE4C45F9E87C869_AdjustorThunk },
	{ 0x06000221, CPUInstanceData_Get_MovedInPreviousFrame_m9A0B9988AF8004828D13C96F89981F3C6AB15C29_AdjustorThunk },
	{ 0x06000222, CPUInstanceData_Get_VisibleInPreviousFrame_m3E67A47CD7703E4345BEE5A7068855FE6A589BB5_AdjustorThunk },
	{ 0x06000223, CPUInstanceData_Get_MeshLodData_m2C413661D7B9E15E2B095F5AAEFFD3DCE65E914F_AdjustorThunk },
	{ 0x06000224, CPUInstanceData_Set_SharedInstance_mECEE018E02FA2F6502B59197A9AEF35FA2D35081_AdjustorThunk },
	{ 0x06000225, CPUInstanceData_Set_LocalToWorldIsFlipped_mB2B243184FD3C74C54A5D8EC74542BC0E019EF29_AdjustorThunk },
	{ 0x06000226, CPUInstanceData_Set_WorldAABB_m56D85CE12B7C589B053C471E803090DF9CA37073_AdjustorThunk },
	{ 0x06000227, CPUInstanceData_Set_TetrahedronCacheIndex_mAC10B658EB1AB6EF405BC32A6DE8E88F7AA45AD5_AdjustorThunk },
	{ 0x06000228, CPUInstanceData_Set_MovedInCurrentFrame_mB2081F967C2A8B6E6AC80F2E91DB3FD70851346F_AdjustorThunk },
	{ 0x06000229, CPUInstanceData_Set_MovedInPreviousFrame_m096AF4BED24D559F13122047DF89E56C3C431B4D_AdjustorThunk },
	{ 0x0600022A, CPUInstanceData_Set_VisibleInPreviousFrame_m53D87829915906D1DD8E19F1F8D51631E9C02620_AdjustorThunk },
	{ 0x0600022B, CPUInstanceData_Set_MeshLodData_mB14CED13A879C4E0CA7A8E27228642E1AF7D44FF_AdjustorThunk },
	{ 0x0600022C, CPUInstanceData_AsReadOnly_m90E90427C88E3F72D123C44F0E7957BACDDA8306_AdjustorThunk },
	{ 0x0600022D, ReadOnly_get_handlesLength_m50556F07B2F7144758202F76E202ED795E715DE9_AdjustorThunk },
	{ 0x0600022E, ReadOnly_get_instancesLength_m0E85E521D7DB61518C6988B08646386F20A17307_AdjustorThunk },
	{ 0x0600022F, ReadOnly__ctor_mFAEAA65432C0711F606AC98FD2320032B60A3309_AdjustorThunk },
	{ 0x06000230, ReadOnly_InstanceToIndex_mA64D7447F6466C660DE83765B1B42673D922CCE2_AdjustorThunk },
	{ 0x06000231, ReadOnly_IndexToInstance_m5BB2CA43E37DBA390AB7F29E6B5357A05226EE1B_AdjustorThunk },
	{ 0x06000232, ReadOnly_IsValidInstance_m560D63CCD78D3661349A6134A30A29EFEDD3C30A_AdjustorThunk },
	{ 0x06000233, ReadOnly_IsValidIndex_m2E036F94E4DFD41DF1CCA4148915594457CF0A9B_AdjustorThunk },
	{ 0x06000234, CPUPerCameraInstanceData_get_instancesLength_m2FCFD20153EC6AACB2340D337BB18BF616A4198F_AdjustorThunk },
	{ 0x06000235, CPUPerCameraInstanceData_set_instancesLength_m3CB40B1AB4F2FD153BD4F869F93AA91CB594C0A0_AdjustorThunk },
	{ 0x06000236, CPUPerCameraInstanceData_get_instancesCapacity_m769EC460DAC66EE0EEAB7EDB3AC0184573C20B3B_AdjustorThunk },
	{ 0x06000237, CPUPerCameraInstanceData_set_instancesCapacity_m4B4D614181686095CD8983D09FACB5EE1EF30223_AdjustorThunk },
	{ 0x06000238, CPUPerCameraInstanceData_get_cameraCount_m4ED64DF61C7EEFDD0B771A9F3F80A46F40266FA8_AdjustorThunk },
	{ 0x06000239, CPUPerCameraInstanceData_Initialize_m8FF812DA16EBDB44A3342DB1FE0CA66B39CAF543_AdjustorThunk },
	{ 0x0600023A, CPUPerCameraInstanceData_DeallocateCameras_mD90424A33180D27D17C51148E7C439D19CCE79DF_AdjustorThunk },
	{ 0x0600023B, CPUPerCameraInstanceData_AllocateCameras_m6C0A0DA2F8AB0E201801C3D5D67DCA28A677E23C_AdjustorThunk },
	{ 0x0600023C, CPUPerCameraInstanceData_Remove_m49A44C5456AC0C589DAF869E031D7F0113BA86BD_AdjustorThunk },
	{ 0x0600023D, CPUPerCameraInstanceData_IncreaseInstanceCount_mB3E25932F6796ED52CA77FD2CE1EF2C2F181F582_AdjustorThunk },
	{ 0x0600023E, CPUPerCameraInstanceData_Dispose_m217073CE624DE6B4D4087A252B22296C6B6D8964_AdjustorThunk },
	{ 0x0600023F, CPUPerCameraInstanceData_Grow_mA24034B63E5825884058DFE3C4BC602E61242846_AdjustorThunk },
	{ 0x06000240, CPUPerCameraInstanceData_SetDefault_m8F8F7A49EBF71312638CD9E75C861AAEC1493B8B_AdjustorThunk },
	{ 0x06000241, PerCameraInstanceDataArrays_get_IsCreated_m36CA4102C1FC8F52C1C84D42C73DE22F4245EBB8_AdjustorThunk },
	{ 0x06000242, PerCameraInstanceDataArrays__ctor_m1959ED65DC23B84993E219642F408B7E833EC465_AdjustorThunk },
	{ 0x06000243, PerCameraInstanceDataArrays_Dispose_mB47A82DE41B69F0F6D0488317687ED8A0EBBE793_AdjustorThunk },
	{ 0x06000244, PerCameraInstanceDataArrays_Remove_m937A118CFDBB9A6C9B7D8FF4481DC0673DA4974C_AdjustorThunk },
	{ 0x06000245, PerCameraInstanceDataArrays_Grow_m9ED1561118DD5ABF4A11D5F73359AE1D6A7D054A_AdjustorThunk },
	{ 0x06000246, PerCameraInstanceDataArrays_SetDefault_mB88E73D0AB2BC49675950883B49FE12625B0771D_AdjustorThunk },
	{ 0x06000247, CPUSharedInstanceData_get_instancesLength_m6C9D2E7F861FF43E23759E8816653646F41038B6_AdjustorThunk },
	{ 0x06000248, CPUSharedInstanceData_set_instancesLength_m62AE1E8890A80D570FD3D11CD9E7C76B9D19124F_AdjustorThunk },
	{ 0x06000249, CPUSharedInstanceData_get_instancesCapacity_mE40EF07D597175079E8DB6639E444A8346BD5C67_AdjustorThunk },
	{ 0x0600024A, CPUSharedInstanceData_set_instancesCapacity_mEBAB461BBCD3CD8127252B3F6156431A24943A69_AdjustorThunk },
	{ 0x0600024B, CPUSharedInstanceData_get_handlesLength_mF238FD08383695F2D7F707F2FA87279FE939CE61_AdjustorThunk },
	{ 0x0600024C, CPUSharedInstanceData_Initialize_m0D70CFA6688D2B1A165CDB20AB1760CBB4604A6E_AdjustorThunk },
	{ 0x0600024D, CPUSharedInstanceData_Dispose_m37AA8B81C3E42B81EC8E948B3F3DF9197B75E48C_AdjustorThunk },
	{ 0x0600024E, CPUSharedInstanceData_Grow_m0AED29A47C913CD92A9EA6281FABBBE76C4C26F3_AdjustorThunk },
	{ 0x0600024F, CPUSharedInstanceData_AddUnsafe_mD66018426B29B8FC77CBA218E04D6470D3A08B69_AdjustorThunk },
	{ 0x06000250, CPUSharedInstanceData_SharedInstanceToIndex_m8D1697AC229D8D0D83FA702F6F440132FA123282_AdjustorThunk },
	{ 0x06000251, CPUSharedInstanceData_IndexToSharedInstance_m7E58052D57FC86E8F4C0571D1213F2D4774BD323_AdjustorThunk },
	{ 0x06000252, CPUSharedInstanceData_InstanceToIndex_mA58DA1C16D52FECF5ED6238B168203837D75F87C_AdjustorThunk },
	{ 0x06000253, CPUSharedInstanceData_IsValidInstance_m33E12C093A6F0194B4D388782B15C2DD64616384_AdjustorThunk },
	{ 0x06000254, CPUSharedInstanceData_IsFreeInstanceHandle_m972EDE9AF42DAADE66DAA3B1E1A8F8A85B594BFA_AdjustorThunk },
	{ 0x06000255, CPUSharedInstanceData_IsValidIndex_m24116C260518DD680C3CDBD5BB1797B7764DDBCD_AdjustorThunk },
	{ 0x06000256, CPUSharedInstanceData_GetFreeInstancesCount_mC18CC336EB3ED96DA46A39019C480485B58A26F5_AdjustorThunk },
	{ 0x06000257, CPUSharedInstanceData_EnsureFreeInstances_m9276B1E0AE6DE0DFAE5F6FA4AFFB7DD9E6939CB0_AdjustorThunk },
	{ 0x06000258, CPUSharedInstanceData_AddNoGrow_mAF3FBC643ABC12255E182EE8F8D26024AD8BBACD_AdjustorThunk },
	{ 0x06000259, CPUSharedInstanceData_Add_m3AFDBA31C2E77E106BD970496C5F0EFBDCB3FCD3_AdjustorThunk },
	{ 0x0600025A, CPUSharedInstanceData_Remove_m1BA13D76461FC093E7667EA3746FC4A6C3E99EF9_AdjustorThunk },
	{ 0x0600025B, CPUSharedInstanceData_Get_RendererGroupID_m09F08F1763DD649A37D88B169CA50B2BD4F71768_AdjustorThunk },
	{ 0x0600025C, CPUSharedInstanceData_Get_MeshID_m70DD07FFBE12B43BA2546547FD26BC268E28EF43_AdjustorThunk },
	{ 0x0600025D, CPUSharedInstanceData_Get_LocalAABB_mB7D5A9EA9CD43BBEE3E938D31500A518FD3C02D7_AdjustorThunk },
	{ 0x0600025E, CPUSharedInstanceData_Get_Flags_m4F22F6B4DB27966ED5F633612B135EDCD274E341_AdjustorThunk },
	{ 0x0600025F, CPUSharedInstanceData_Get_LODGroupAndMask_m4A750431421271D4D2960E84D4B3AD83F96A8A42_AdjustorThunk },
	{ 0x06000260, CPUSharedInstanceData_Get_GameObjectLayer_m75D2D7344A04236BFAE513142B29AB6CC8E7CB4D_AdjustorThunk },
	{ 0x06000261, CPUSharedInstanceData_Get_RefCount_mE216F6749BB1B06E795C52C2433EE85E6C57DC01_AdjustorThunk },
	{ 0x06000262, CPUSharedInstanceData_Get_MaterialIDs_mCE6EC8119990B7324D473B6DFB27B91A076404FC_AdjustorThunk },
	{ 0x06000263, CPUSharedInstanceData_Set_RendererGroupID_m06EFE6568E7FABED464A0A091DB7D8DE05FC8719_AdjustorThunk },
	{ 0x06000264, CPUSharedInstanceData_Set_MeshID_m97990780D2C7B92B6A146C6FB243D08D2674EAA9_AdjustorThunk },
	{ 0x06000265, CPUSharedInstanceData_Set_LocalAABB_mFA0C8491B2FCD9E1F5BB25FF598C7520D689D08C_AdjustorThunk },
	{ 0x06000266, CPUSharedInstanceData_Set_Flags_m9C1CE3AC11B09B3B2C9F65422B2FD31D23C49F4D_AdjustorThunk },
	{ 0x06000267, CPUSharedInstanceData_Set_LODGroupAndMask_mD27421C024B1779830BEFA1274063B72D7C182CA_AdjustorThunk },
	{ 0x06000268, CPUSharedInstanceData_Set_GameObjectLayer_m97AEE383F753133901141CCD0040B7D2C2E41EEC_AdjustorThunk },
	{ 0x06000269, CPUSharedInstanceData_Set_RefCount_m7C5AF143DDC85304B2840A688BB2DD095A03B7CC_AdjustorThunk },
	{ 0x0600026A, CPUSharedInstanceData_Set_MaterialIDs_m3C8A0B397DBC9FEFF9661015CC32232A4910D812_AdjustorThunk },
	{ 0x0600026B, CPUSharedInstanceData_Set_m1236559425DDDA4FAF0765307F13B64FCA01C2BB_AdjustorThunk },
	{ 0x0600026C, CPUSharedInstanceData_SetDefault_m9241E530C50F996744FC4691DCD87C0E30602237_AdjustorThunk },
	{ 0x0600026D, CPUSharedInstanceData_AsReadOnly_mC186E7142A7E53BFE6D90BB0AB1841600BE7CBF8_AdjustorThunk },
	{ 0x0600026E, ReadOnly_get_handlesLength_mEAE03E557C81624E1F2CD280610A867E764EE456_AdjustorThunk },
	{ 0x0600026F, ReadOnly_get_instancesLength_m82B5F22A5EE9DF8AB94546D3B739C1EB36AD38D6_AdjustorThunk },
	{ 0x06000270, ReadOnly__ctor_mDBC17F900B61DCF328F4BE2CDF0464E97F44614D_AdjustorThunk },
	{ 0x06000271, ReadOnly_SharedInstanceToIndex_m7CD21D39C3285388B428D1CDCAA9AFB10A0178B4_AdjustorThunk },
	{ 0x06000272, ReadOnly_IndexToSharedInstance_mC97CAB3F6DB3C92BCB528F85F5B9E42728DE3389_AdjustorThunk },
	{ 0x06000273, ReadOnly_IsValidSharedInstance_mC2056D855CF0ED6FA050810CE1035680DC453A1A_AdjustorThunk },
	{ 0x06000274, ReadOnly_IsValidIndex_mA6B2DAE40129D5F001698A34D413EE0CEB24E61D_AdjustorThunk },
	{ 0x06000275, ReadOnly_InstanceToIndex_mE76C9EBF3ACCFDD5D73B3A99925CAE88DE2DDBE8_AdjustorThunk },
	{ 0x06000276, SmallIntegerArray_get_Valid_m717ECD9B73FD5A373F40B0A623CB2C8E188012C0_AdjustorThunk },
	{ 0x06000277, SmallIntegerArray_set_Valid_mF9C378DA26AB93E1AB38CCD7371CA23ABB6779D3_AdjustorThunk },
	{ 0x06000278, SmallIntegerArray__ctor_mD7860879B9A093BE8CD0CFD815D125FDB746090E_AdjustorThunk },
	{ 0x06000279, SmallIntegerArray_get_Item_m81A85EC298981F31BB405CA783D90BB96697C2DE_AdjustorThunk },
	{ 0x0600027A, SmallIntegerArray_set_Item_mFEF4007D7D6DA125990D22E32B34ADCBF7056EA2_AdjustorThunk },
	{ 0x0600027B, SmallIntegerArray_Dispose_m11D070ED0582D69C7F631CAF8C44F93183D73579_AdjustorThunk },
	{ 0x06000281, EditorInstanceDataArrays_Initialize_m9893832043B3485AD59127B93D0872405CF77AFC_AdjustorThunk },
	{ 0x06000282, EditorInstanceDataArrays_Dispose_m16E99865063B2150E7BEFCE3BAE55831D9532B3B_AdjustorThunk },
	{ 0x06000283, EditorInstanceDataArrays_Grow_m57AFD6C22AA320DA1E98981C894EEB1F96C0B041_AdjustorThunk },
	{ 0x06000284, EditorInstanceDataArrays_Remove_mC3970E725DCDCEA7B636C2DCB4732C432787AE35_AdjustorThunk },
	{ 0x06000285, EditorInstanceDataArrays_SetDefault_mBB385CE94C9E46ADADB174CEFB393C2A19245724_AdjustorThunk },
	{ 0x06000286, ReadOnly__ctor_m2029FDC41CDEC78F717316642DB33534904EC6E2_AdjustorThunk },
	{ 0x060002B4, QueryRendererGroupInstancesCountJob_Execute_m78EBD1A4D27FC820DB994D726466672AE6698114_AdjustorThunk },
	{ 0x060002B5, ComputeInstancesOffsetAndResizeInstancesArrayJob_Execute_m7278A70AAF09ACD7DE06A58BBE53617ED59B3178_AdjustorThunk },
	{ 0x060002B6, QueryRendererGroupInstancesJob_Execute_m689B8C96E8D3739314972C71A89F43C5DD8DD5EC_AdjustorThunk },
	{ 0x060002B7, QueryRendererGroupInstancesMultiJob_Execute_m3ED5F46C8E2BBCFD2302EBF30F55D07CB4292593_AdjustorThunk },
	{ 0x060002B8, QuerySortedMeshInstancesJob_Execute_m0B30D3DBCCB8FE5833D677F735387912A448CCDF_AdjustorThunk },
	{ 0x060002B9, CalculateInterpolatedLightAndOcclusionProbesBatchJob_Execute_mB29926232C46662441F96B9405E9BE4D3A84109B_AdjustorThunk },
	{ 0x060002BA, ScatterTetrahedronCacheIndicesJob_Execute_m157E3BAA3AA5106CD5629211DB4DF10C941B0942_AdjustorThunk },
	{ 0x060002BB, TransformUpdateJob_Execute_m0C99BC8B89AD60C1B495E471F9F02FBEEF7F8342_AdjustorThunk },
	{ 0x060002BC, ProbesUpdateJob_Execute_mFACBB95DE46DB9C879C2EE745CE37C4AEFB69AD6_AdjustorThunk },
	{ 0x060002BD, MotionUpdateJob_Execute_m266055F93AABA39FD53E7EDBD6BAFE55F586891B_AdjustorThunk },
	{ 0x060002BE, UpdateRendererInstancesJob_Execute_m93597D2184F6460DD4610AD8E47868B1BA87A11C_AdjustorThunk },
	{ 0x060002BF, CollectInstancesLODGroupsAndMasksJob_Execute_m2EFF9AB4F3476E491E51143F7F60F79020A0F780_AdjustorThunk },
	{ 0x060002C0, GetVisibleNonProcessedTreeInstancesJob_Execute_mEFBAD1E57760EF2F4C2A4F4E6F6F40E6261C284A_AdjustorThunk },
	{ 0x060002C1, UpdateCompactedInstanceVisibilityJob_Execute_mDFAB7C16E38743439A74CCA9AA310B383D2BA55B_AdjustorThunk },
	{ 0x060002E5, InstanceNumInfo_InitDefault_m8CC8A9E8EBF0EF16311F391E0312C46A7219A920_AdjustorThunk },
	{ 0x060002E6, InstanceNumInfo__ctor_m0C64766A7024C367CB84BA96F01861E951351650_AdjustorThunk },
	{ 0x060002E7, InstanceNumInfo__ctor_mCFED34B4FC73F15366339611E22502A8366B016C_AdjustorThunk },
	{ 0x060002E8, InstanceNumInfo_GetInstanceNum_m42CC8341EB7A73444DB20B9B64ACF5377CA7CE19_AdjustorThunk },
	{ 0x060002E9, InstanceNumInfo_GetInstanceNumIncludingChildren_m45CB40CE2452143C10B4FFC0F05FA7751E416368_AdjustorThunk },
	{ 0x060002EA, InstanceNumInfo_GetTotalInstanceNum_m55D6043571FB3440889F98A7D638F6A43D060982_AdjustorThunk },
	{ 0x060002EC, OccluderHandles_IsValid_m9FF256155930C1B40D4195C263AF38FCEB1B7904_AdjustorThunk },
	{ 0x060002ED, OccluderHandles_UseForOcclusionTest_m8E9F3CDFD218EC3C5A70DA375F156DC75CB2CAAD_AdjustorThunk },
	{ 0x060002EE, OccluderHandles_UseForOccluderUpdate_m4376DEB9151FDF5678FDFE2ED525A3B3AC31AF03_AdjustorThunk },
	{ 0x060002EF, IndirectBufferAllocInfo_IsEmpty_m47785BE361D9B989BE0455AD1884831AB99E4009_AdjustorThunk },
	{ 0x060002F0, IndirectBufferAllocInfo_IsWithinLimits_mE8807A3A2DDE4F3E3E9091E2441D847F4BFC4067_AdjustorThunk },
	{ 0x060002F1, IndirectBufferAllocInfo_GetExtraDrawInfoSlotIndex_m2EAD4191867631265610C43212C34A56A2DAD969_AdjustorThunk },
	{ 0x060002F2, IndirectBufferContext__ctor_mC7F33D8E6E9DECDB5A167C9CFD68F1CBB6E7D7F5_AdjustorThunk },
	{ 0x060002F3, IndirectBufferContext_Matches_m40E2A7974D4B205D4BB770D4A68635D99FE4C721_AdjustorThunk },
	{ 0x060002F4, OccluderContext_get_subviewCount_m49FDF73077D5C9F1789BA0C35A39A7F78FCBF414_AdjustorThunk },
	{ 0x060002F5, OccluderContext_IsSubviewValid_m88BE2A076AC851E9D11AB02B30ECE80A7E4D6BE0_AdjustorThunk },
	{ 0x060002F6, OccluderContext_get_depthBufferSizeInOccluderPixels_mF734AB99EBF484188554B86CB2E07048E6138C36_AdjustorThunk },
	{ 0x060002F7, OccluderContext_Dispose_mF02789AFBB76CD0F4491CDD8A83BCD15938F22D8_AdjustorThunk },
	{ 0x060002F8, OccluderContext_UpdateMipBounds_m71B416ED1B6D8827B30F8B1C1E69870328E6FCD9_AdjustorThunk },
	{ 0x060002F9, OccluderContext_AllocateTexturesIfNecessary_m4F0340120C018852B2A697194F87A6C539D6C127_AdjustorThunk },
	{ 0x060002FB, OccluderContext_SetupFarDepthPyramidConstants_m69DC95369344843C149515C9A2A9312DD0E99571_AdjustorThunk },
	{ 0x060002FC, OccluderContext_CreateFarDepthPyramid_mAE9A7D75C802A5AB3F91ED35C2BF61DA52C0AB0F_AdjustorThunk },
	{ 0x060002FD, OccluderContext_Import_m59CDBBE79F1A96B17BE009D5D561E35D69DA19D7_AdjustorThunk },
	{ 0x060002FE, OccluderContext_PrepareOccluders_m558D900C293C248A3CEE8FE6640C98873039DF1D_AdjustorThunk },
	{ 0x060002FF, OccluderContext_GetDebugOutput_m0B03B350C81D50A7DA8CEB969E70AF0F782675C5_AdjustorThunk },
	{ 0x06000302, IndirectBufferContextHandles_UseForOcclusionTest_m098E1C673230461EF27D4BC76A7186A5DADEEDAB_AdjustorThunk },
	{ 0x06000303, IndirectBufferContextStorage_get_instanceBuffer_mD965845788AD240262E1C46BB4BA405B73C8EA27_AdjustorThunk },
	{ 0x06000304, IndirectBufferContextStorage_get_instanceInfoBuffer_mC14C040CC88B2F11AFE1301EB1FB9C70E396E8E5_AdjustorThunk },
	{ 0x06000305, IndirectBufferContextStorage_get_argsBuffer_m81BE13A707FAF6B8F9361AEBCDD9CBA69611C334_AdjustorThunk },
	{ 0x06000306, IndirectBufferContextStorage_get_drawInfoBuffer_m0168780ED5CFCB3613F04F2C00561471352A5790_AdjustorThunk },
	{ 0x06000307, IndirectBufferContextStorage_get_visibleInstanceBufferHandle_mBD86573F23F6B0F8BF6C46C2996C449E571DC87F_AdjustorThunk },
	{ 0x06000308, IndirectBufferContextStorage_get_indirectArgsBufferHandle_m7DF465FC2B94D42E1ABE0EE29E0EE4A1C850E925_AdjustorThunk },
	{ 0x06000309, IndirectBufferContextStorage_ImportBuffers_mB024C5F1494D76DFEC07CDE6065AC7ACBB35E265_AdjustorThunk },
	{ 0x0600030A, IndirectBufferContextStorage_get_instanceInfoGlobalArray_m6D87D347531D8E3C00FB0967CCAEE69FED54FC41_AdjustorThunk },
	{ 0x0600030B, IndirectBufferContextStorage_get_drawInfoGlobalArray_mF90CEAECE8B83E043E462A4495421A574CC257D4_AdjustorThunk },
	{ 0x0600030C, IndirectBufferContextStorage_get_allocationCounters_mF22684F6D0214AE0755E3DFA8BA047C9E63C70FC_AdjustorThunk },
	{ 0x0600030D, IndirectBufferContextStorage_Init_mE1D9E857917AF5759F2E08D6B008C991CE5558AF_AdjustorThunk },
	{ 0x0600030E, IndirectBufferContextStorage_AllocateInstanceBuffers_m83064D5ABC47473BE4547D8CDB301CC989362AD4_AdjustorThunk },
	{ 0x0600030F, IndirectBufferContextStorage_FreeInstanceBuffers_mE035A24BD6179866735657D3D74C416AD39C597D_AdjustorThunk },
	{ 0x06000310, IndirectBufferContextStorage_AllocateDrawBuffers_mCB3267B00E8328A5031AAB859542061D665CE0A0_AdjustorThunk },
	{ 0x06000311, IndirectBufferContextStorage_FreeDrawBuffers_mAB74E77AD8B6348D492585D0ACB5AE42C2CEB171_AdjustorThunk },
	{ 0x06000312, IndirectBufferContextStorage_Dispose_mD7278C175C4C23BCB9190D68CE8343B543A24302_AdjustorThunk },
	{ 0x06000313, IndirectBufferContextStorage_SyncContexts_mB63C2465AF6D851CA68041FA1C12E24F24874A32_AdjustorThunk },
	{ 0x06000314, IndirectBufferContextStorage_ResetAllocators_m327B8163AEEAFE360011EAEBFBD80C96C6403467_AdjustorThunk },
	{ 0x06000315, IndirectBufferContextStorage_GrowBuffers_mAB6F1A326FB73F38678D592C809333FAE5A25D44_AdjustorThunk },
	{ 0x06000316, IndirectBufferContextStorage_ClearContextsAndGrowBuffers_m0B059924A2DB871C3C80B03DAF54A47C8A47E573_AdjustorThunk },
	{ 0x06000317, IndirectBufferContextStorage_TryAllocateContext_mC8D472C07C986BDCFC55CF8C4CE324230A38618C_AdjustorThunk },
	{ 0x06000318, IndirectBufferContextStorage_TryGetContextIndex_m848B7C96E09D5CE2C6D12221D0B78EA4DCE77EF1_AdjustorThunk },
	{ 0x06000319, IndirectBufferContextStorage_GetAllocInfoSubArray_m053D08700A1B5D4DB1AAF31B508E33E9FF418A4D_AdjustorThunk },
	{ 0x0600031A, IndirectBufferContextStorage_GetAllocInfo_m9BF31C85DBB94B0D0F1B82AF66DA8493AB1B2595_AdjustorThunk },
	{ 0x0600031B, IndirectBufferContextStorage_CopyFromStaging_mE99297FD5192A7F628FD7DC704E877280DB03891_AdjustorThunk },
	{ 0x0600031C, IndirectBufferContextStorage_GetLimits_m2E8DDC54719944794D7342725107396F59D5CF71_AdjustorThunk },
	{ 0x0600031D, IndirectBufferContextStorage_GetBufferContext_mD76FD0DD95CEE4D7D9B62F81573C4A05A5519941_AdjustorThunk },
	{ 0x0600031E, IndirectBufferContextStorage_SetBufferContext_mEC8E692A99E595592E12F55F94B7E234A340DC2A_AdjustorThunk },
	{ 0x0600031F, UpdateLODGroupTransformJob_Execute_m10AD64037A0D9FFAB351270A412B4B4342A6B8C5_AdjustorThunk },
	{ 0x06000320, UpdateLODGroupDataJob_Execute_m37868AFCE3AFCC80D46C8156CA2713096FE5EC3D_AdjustorThunk },
	{ 0x06000346, OcclusionTestComputeShader_Init_mC423CA21A41E4B44DB2715096F04D7498A80E960_AdjustorThunk },
	{ 0x06000347, SilhouettePlaneCache_Init_mF14F33F1C7D6CD3704478C92314526747ABDFF0C_AdjustorThunk },
	{ 0x06000348, SilhouettePlaneCache_Dispose_m52386469CD058770AAD5B4E19ADF603598BECEC5_AdjustorThunk },
	{ 0x06000349, SilhouettePlaneCache_Update_m08599EDAF7CC1D053E0E54A8DF15F55E1E0B6342_AdjustorThunk },
	{ 0x0600034A, SilhouettePlaneCache_FreeUnusedSlots_m2249464604B48996B77945B9BBCDBDCECD2074C2_AdjustorThunk },
	{ 0x0600034B, SilhouettePlaneCache_GetSubArray_m0527F754CEEB54300C083A4BDAE9B56D026DA63C_AdjustorThunk },
	{ 0x0600034C, Slot__ctor_m0E99B79099FDB0098404A2FD223A9C029CFFF5D1_AdjustorThunk },
	{ 0x0600036E, OcclusionCullingCommonShaderVariables__ctor_m6098CCD0E939B2F9DE8715FF129DAE892745C610_AdjustorThunk },
	{ 0x060003A9, RenderersParameters__ctor_mD3445734819B50610F768B8E6EF49822D5ABEE8A_AdjustorThunk },
	{ 0x060003AD, ParamInfo_get_valid_m198E32DB1AAD1F43EAD6964E4EF79E79D078AF7D_AdjustorThunk },
	{ 0x060003B0, ParallelBitArray_get_Length_m82FE0E2AC9FAB29DA67E28FFAEA04EB642955B08_AdjustorThunk },
	{ 0x060003B1, ParallelBitArray_get_IsCreated_m31B6CFD3C95548F523C3D074463B827B7CD7A535_AdjustorThunk },
	{ 0x060003B2, ParallelBitArray__ctor_m7BB9EA31D2DF48FD4BE5D8773C539A76C5D4E6E4_AdjustorThunk },
	{ 0x060003B3, ParallelBitArray_Dispose_m24CBECA125F3D0090E9786E6AF56CB2E6DE452C6_AdjustorThunk },
	{ 0x060003B4, ParallelBitArray_Dispose_m3806D56E9788D3C69BD6C6985C6E2F949A89F9B7_AdjustorThunk },
	{ 0x060003B5, ParallelBitArray_Resize_mA268182EEF9B41198BFC3780A74CF12D9B232011_AdjustorThunk },
	{ 0x060003B6, ParallelBitArray_Set_mEDB30931801E1F71F11F62CCB23F3537EEE0F1EA_AdjustorThunk },
	{ 0x060003B7, ParallelBitArray_Get_m250C22A6191BAF4C5B314EFAF451E391D251F2B0_AdjustorThunk },
	{ 0x060003B8, ParallelBitArray_GetChunk_m7303392F0138448DF74E7A709F38B500B9461ED2_AdjustorThunk },
	{ 0x060003B9, ParallelBitArray_SetChunk_m48943193199714BCF0925DD8E14C8EB651885629_AdjustorThunk },
	{ 0x060003BA, ParallelBitArray_InterlockedReadChunk_m19B068CDEB9686FE2DD8A42BF99D2011EEAFA84F_AdjustorThunk },
	{ 0x060003BB, ParallelBitArray_InterlockedOrChunk_m51A85AD8A6A6FFD317303DEABA6B9797B79CE658_AdjustorThunk },
	{ 0x060003BC, ParallelBitArray_ChunkCount_mE27E6F3D861AF09C5B31BD7F3964796B26A9C3B5_AdjustorThunk },
	{ 0x060003BD, ParallelBitArray_GetSubArray_m45E14868BB90EC98D0467ABDA3DAD1BD4BFC49DD_AdjustorThunk },
	{ 0x060003BE, ParallelBitArray_GetBitsArray_m31F100FDDB1EA0FE4E1592768ED14B843D0DD73D_AdjustorThunk },
	{ 0x060003BF, ParallelBitArray_FillZeroes_mDAE3DE6ACB91DE00B5DC5D50415B7A0F938A08BB_AdjustorThunk },
	{ 0x060003C2, RadixSortBucketCountJob_Execute_mF3ADEB0523C3DE92CB5CFEF01B65E72C9AB7C023_AdjustorThunk },
	{ 0x060003C4, RadixSortBatchPrefixSumJob_JobIndexPrefixSum_m9C47BE4B67FCFF29A8FA94D39589F9B9A2840EE3_AdjustorThunk },
	{ 0x060003C5, RadixSortBatchPrefixSumJob_Execute_m434849692F7D93EF83545890B59FC96BF14AED93_AdjustorThunk },
	{ 0x060003C6, RadixSortPrefixSumJob_Execute_mF1969BD6160F81BA429AF74A8944935FC83BC551_AdjustorThunk },
	{ 0x060003C7, RadixSortBucketSortJob_Execute_mDB0AB3CD468DA898E41CBF3E9EF5BE26AD26E4D2_AdjustorThunk },
};
static const int32_t s_InvokerIndices[970] = 
{
	21016,
	21016,
	34316,
	21016,
	21034,
	21034,
	20761,
	24739,
	26922,
	31503,
	31545,
	21016,
	20550,
	15757,
	21016,
	21016,
	29159,
	29159,
	21016,
	21016,
	20550,
	15757,
	21016,
	21016,
	29159,
	29159,
	21016,
	21016,
	20550,
	15757,
	11447,
	20550,
	15757,
	20550,
	15757,
	20550,
	15757,
	31736,
	31738,
	31637,
	34138,
	34138,
	34138,
	32085,
	32063,
	32063,
	32063,
	32063,
	32085,
	32085,
	20550,
	20550,
	20550,
	11447,
	20761,
	21016,
	34252,
	20694,
	15968,
	15968,
	15968,
	34252,
	21016,
	20761,
	20550,
	20550,
	20694,
	20694,
	20550,
	20761,
	20761,
	21016,
	20550,
	15757,
	20550,
	15757,
	20550,
	15757,
	20550,
	15757,
	20550,
	15757,
	20694,
	15903,
	20550,
	20873,
	16071,
	20550,
	20873,
	16071,
	20550,
	20550,
	15757,
	21016,
	20550,
	20550,
	20550,
	21016,
	20550,
	21016,
	20550,
	20761,
	20761,
	20761,
	20761,
	20761,
	21016,
	20550,
	20761,
	20761,
	20761,
	20761,
	20761,
	20761,
	20761,
	21016,
	20550,
	20761,
	20761,
	20761,
	27976,
	29628,
	29628,
	31573,
	18861,
	18861,
	32176,
	15915,
	28174,
	15915,
	23864,
	25816,
	3463,
	32177,
	15915,
	20550,
	28175,
	24386,
	24424,
	20761,
	20761,
	20761,
	3706,
	21016,
	21016,
	21016,
	15968,
	15968,
	21016,
	14816,
	14812,
	14816,
	14816,
	3687,
	3686,
	6317,
	6013,
	16030,
	21016,
	3530,
	3530,
	15906,
	21016,
	3456,
	34156,
	34103,
	32772,
	34252,
	26530,
	26529,
	34252,
	24653,
	23541,
	34156,
	21016,
	21016,
	34103,
	34125,
	34103,
	34103,
	34103,
	32749,
	34103,
	32749,
	34252,
	34252,
	32755,
	20761,
	20638,
	3571,
	21016,
	8099,
	34252,
	8102,
	8102,
	8102,
	8102,
	21016,
	6316,
	6316,
	14816,
	3457,
	2343,
	8145,
	6311,
	14812,
	6316,
	5364,
	6014,
	6015,
	2224,
	6015,
	1146,
	8991,
	2965,
	1355,
	34103,
	27387,
	25286,
	29169,
	7405,
	34252,
	23241,
	24458,
	24458,
	23241,
	24458,
	24458,
	7988,
	1152,
	376,
	15968,
	32748,
	34140,
	23241,
	7988,
	2363,
	574,
	15968,
	32748,
	34140,
	24458,
	7988,
	2363,
	574,
	15968,
	32748,
	34140,
	24458,
	21016,
	21016,
	20694,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	21016,
	32436,
	7405,
	15903,
	15903,
	-1,
	-1,
	-1,
	34252,
	11488,
	4834,
	31553,
	34103,
	11726,
	20694,
	11539,
	20694,
	20694,
	15903,
	32443,
	3417,
	3417,
	6215,
	6214,
	7288,
	15903,
	11619,
	3039,
	15903,
	21016,
	5648,
	11619,
	1437,
	15903,
	7405,
	18846,
	21016,
	21016,
	3222,
	7416,
	15968,
	20761,
	21016,
	21016,
	863,
	15968,
	20550,
	7995,
	2225,
	89,
	3207,
	90,
	3270,
	3624,
	21016,
	21016,
	13868,
	2716,
	15903,
	702,
	21016,
	21016,
	21016,
	15903,
	15968,
	15968,
	21016,
	34252,
	21016,
	34252,
	21016,
	7995,
	21758,
	21758,
	7988,
	324,
	113,
	15968,
	32748,
	34140,
	21758,
	7988,
	3676,
	1097,
	3516,
	34136,
	21016,
	31768,
	15903,
	7405,
	7405,
	7405,
	7405,
	15903,
	15903,
	15903,
	21016,
	18898,
	18905,
	18897,
	18909,
	18899,
	18846,
	18846,
	20550,
	21016,
	21016,
	21016,
	14816,
	14812,
	14885,
	21016,
	21016,
	18906,
	18907,
	3706,
	20521,
	21016,
	10249,
	21016,
	20761,
	2226,
	15906,
	14812,
	14816,
	14816,
	16030,
	14816,
	6503,
	6013,
	3454,
	7405,
	21016,
	13868,
	21016,
	15968,
	15968,
	26244,
	23241,
	22052,
	25119,
	23620,
	21434,
	21496,
	22052,
	21496,
	7988,
	648,
	268,
	15968,
	32748,
	34140,
	22052,
	7988,
	95,
	55,
	15968,
	32748,
	34140,
	21496,
	674,
	34138,
	20550,
	27731,
	5648,
	5701,
	5648,
	12356,
	12417,
	6310,
	21016,
	21106,
	21016,
	15968,
	12356,
	6310,
	15903,
	3273,
	-1,
	672,
	13792,
	21016,
	3521,
	20697,
	20694,
	13157,
	-1,
	15903,
	-1,
	-1,
	2596,
	2597,
	21016,
	34252,
	15968,
	2566,
	21016,
	15903,
	7957,
	13792,
	21016,
	34252,
	15968,
	21016,
	21016,
	20694,
	15903,
	20694,
	20694,
	20550,
	27750,
	31737,
	11615,
	13153,
	20694,
	34252,
	20694,
	15903,
	20550,
	11792,
	13290,
	20694,
	34252,
	20694,
	15903,
	20550,
	11571,
	13121,
	20694,
	34252,
	20694,
	15903,
	20550,
	7405,
	21016,
	20694,
	15903,
	20694,
	21016,
	21016,
	12415,
	13157,
	13157,
	12418,
	15897,
	20872,
	16070,
	20694,
	15903,
	20694,
	15903,
	20694,
	15903,
	21016,
	15903,
	15897,
	13153,
	12418,
	11615,
	11615,
	11619,
	20694,
	15903,
	15897,
	15897,
	15897,
	173,
	15897,
	14003,
	11615,
	10210,
	13153,
	10203,
	11615,
	11615,
	11615,
	12353,
	6764,
	6761,
	6760,
	6763,
	6761,
	6761,
	6761,
	6762,
	21059,
	20694,
	20694,
	15721,
	13153,
	12418,
	11615,
	11619,
	20694,
	15903,
	20694,
	15903,
	20694,
	15903,
	14816,
	14816,
	15903,
	21016,
	21016,
	15903,
	15903,
	20550,
	15903,
	21016,
	7405,
	7405,
	15903,
	20694,
	15903,
	20694,
	15903,
	20694,
	15903,
	21016,
	15903,
	16070,
	13290,
	14004,
	5580,
	11792,
	11792,
	11619,
	20694,
	15903,
	16070,
	16070,
	16070,
	13290,
	13290,
	10208,
	12231,
	14221,
	13290,
	13290,
	10208,
	8109,
	8109,
	8107,
	8108,
	8110,
	8109,
	8109,
	8107,
	104,
	16070,
	21061,
	20694,
	20694,
	15721,
	13290,
	14004,
	11792,
	11619,
	5580,
	20550,
	15757,
	7405,
	13157,
	7405,
	21016,
	-1,
	-1,
	-1,
	-1,
	-1,
	15903,
	21016,
	15903,
	7405,
	15903,
	15721,
	32130,
	32130,
	20550,
	21059,
	20551,
	20694,
	21061,
	18844,
	3612,
	21016,
	13157,
	13157,
	15903,
	15903,
	15903,
	541,
	669,
	2549,
	404,
	3451,
	6520,
	663,
	3455,
	3451,
	6481,
	14816,
	14812,
	3269,
	6520,
	1145,
	2342,
	6520,
	6014,
	6015,
	2224,
	6015,
	6011,
	20550,
	647,
	15721,
	14816,
	14816,
	-1,
	34252,
	34252,
	7405,
	21016,
	7405,
	7405,
	7405,
	15903,
	15903,
	7405,
	7405,
	15903,
	15903,
	15903,
	7405,
	7405,
	21496,
	22052,
	22052,
	21496,
	22052,
	22052,
	7988,
	95,
	55,
	15968,
	32748,
	34140,
	21496,
	7988,
	648,
	268,
	15968,
	32748,
	34140,
	22052,
	7988,
	648,
	268,
	15968,
	32748,
	34140,
	22052,
	34252,
	34252,
	34252,
	31782,
	28944,
	34252,
	31782,
	32085,
	21016,
	7405,
	7405,
	13157,
	13157,
	20694,
	32129,
	20550,
	15968,
	15968,
	20550,
	11447,
	20694,
	15915,
	3040,
	20694,
	11619,
	21002,
	21016,
	21016,
	15757,
	24643,
	6142,
	426,
	13860,
	15721,
	20769,
	34252,
	31739,
	15968,
	20761,
	20761,
	20761,
	20761,
	20654,
	20654,
	12397,
	18842,
	18841,
	18846,
	21016,
	15903,
	21016,
	15903,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	13157,
	13157,
	8911,
	12395,
	7957,
	12398,
	12396,
	7392,
	15903,
	15903,
	18908,
	18901,
	20694,
	20694,
	3715,
	21016,
	15721,
	15721,
	14816,
	34252,
	23891,
	21958,
	23891,
	21958,
	7988,
	1532,
	574,
	13212,
	32748,
	34140,
	23891,
	7988,
	535,
	268,
	13212,
	32748,
	34140,
	21958,
	32266,
	32261,
	25753,
	25765,
	25765,
	32696,
	32262,
	32262,
	28256,
	15968,
	21016,
	21016,
	3589,
	7405,
	8932,
	3622,
	15968,
	31550,
	701,
	24613,
	24613,
	2834,
	1303,
	7985,
	6143,
	2715,
	3079,
	7044,
	13865,
	15968,
	11619,
	4929,
	21016,
	13157,
	15903,
	21016,
	21016,
	34252,
	8000,
	34252,
	21016,
	21016,
	21016,
	21016,
	34252,
	21016,
	7995,
	8000,
	7995,
	2367,
	34190,
	20835,
	20761,
	20694,
	19230,
	18852,
	18901,
	20694,
	20694,
	20878,
	20550,
	20694,
	21059,
	21061,
	20551,
	21106,
	18844,
	20873,
	20761,
	20761,
	20761,
	3530,
	21016,
	13157,
	13157,
	15721,
	21016,
	15721,
	15721,
	14816,
	14816,
	6481,
	6012,
	14816,
	14812,
	6014,
	6015,
	2224,
	6015,
	21016,
	5353,
	3454,
	3450,
	3453,
	6311,
	15757,
	14803,
	21016,
	14816,
	15721,
	6011,
	12418,
	647,
	20761,
	21016,
	14816,
	14816,
	28033,
	15721,
	34252,
	26813,
	34252,
	20550,
	-1,
	-1,
	20694,
	20550,
	3622,
	21016,
	15915,
	15903,
	7312,
	11619,
	14225,
	7637,
	14225,
	7637,
	20694,
	13869,
	18848,
	15903,
	31859,
	28910,
	15903,
	31760,
	5649,
	15903,
	15903,
	15903,
	34252,
	34252,
	34252,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[11] = 
{
	{ 0x060000DF, 7,  (void**)&GPUResidentDrawerBurst_ClassifyMaterials_m9FEDC6820FD183791F7BD2B682CFFFBBF9DC9F40_RuntimeMethod_var, 0 },
	{ 0x060000E0, 8,  (void**)&GPUResidentDrawerBurst_FindUnsupportedRenderers_m927B5E54923278315B6256EA8178A34AA566B4BD_RuntimeMethod_var, 0 },
	{ 0x060000E1, 9,  (void**)&GPUResidentDrawerBurst_GetMaterialsWithChangedPackedMaterial_mC2D4AB98974823074A7E8543ED7A3D5BF7DB26FA_RuntimeMethod_var, 0 },
	{ 0x06000157, 10,  (void**)&InstanceCullerBurst_SetupCullingJobInput_m48DA62BB1FCE439FD65DE65952C97249ECB28C56_RuntimeMethod_var, 0 },
	{ 0x0600019A, 12,  (void**)&InstanceCullingBatcherBurst_RemoveDrawInstanceIndices_mCD31103C406E421E8E420A624BAE66CE0B58F140_RuntimeMethod_var, 0 },
	{ 0x0600019E, 11,  (void**)&InstanceCullingBatcherBurst_CreateDrawBatches_m607108DE9C1C56A9A8AE1C377CA5C64D155497E3_RuntimeMethod_var, 0 },
	{ 0x060002C2, 15,  (void**)&InstanceDataSystemBurst_ReallocateInstances_mF18C9347288DA7A9C194DFB92AC6A014D24975D9_RuntimeMethod_var, 0 },
	{ 0x060002C3, 14,  (void**)&InstanceDataSystemBurst_FreeRendererGroupInstances_mE5B774F1873565C1564044629ADC0EC240EC6547_RuntimeMethod_var, 0 },
	{ 0x060002C4, 13,  (void**)&InstanceDataSystemBurst_FreeInstances_m40A2076A7C0DE278135AD4A33911F95F2A94E630_RuntimeMethod_var, 0 },
	{ 0x0600032B, 17,  (void**)&LODGroupDataPoolBurst_FreeLODGroupData_mA6615B0E58416F4443ED0744A145DCB275902C79_RuntimeMethod_var, 0 },
	{ 0x0600032C, 16,  (void**)&LODGroupDataPoolBurst_AllocateOrGetLODGroupDataInstances_mBD22A32D0C093047BA4165B0D6E9A38EE73F96F5_RuntimeMethod_var, 0 },
};
static const Il2CppTokenRangePair s_rgctxIndices[7] = 
{
	{ 0x060001C1, { 0, 1 } },
	{ 0x060001C9, { 1, 1 } },
	{ 0x060001CB, { 2, 2 } },
	{ 0x060001CC, { 4, 4 } },
	{ 0x060002B1, { 8, 6 } },
	{ 0x060003AE, { 14, 3 } },
	{ 0x060003AF, { 17, 1 } },
};
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tB93F845DBDA86B86E875C4B8733BA04EF2079EF4_mC1117E52BBA0452311F896F34EC5F717A8C11EC9;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t9E2BC6384D3C627F4120CA7164476B41F6EF7AA4_m914262E127293EC1BCE79DEEB893EF0EB63C363A;
extern const uint32_t g_rgctx_NativeArray_1_t3CD1365D537A2522B7C4D7465DFD26F5A6648A37;
extern const uint32_t g_rgctx_GPUInstanceDataBufferUploader_WriteInstanceDataJob_TisT_t996EE38E80212E224255DA4B2B4FBEA40E749C74_m5B9DCE67AD22F60678E7A9BE1B97C03D8243A4C1;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tC0CB470D64AD7A60EAA5044E2C0D29708268A1E8_m53B5E2AC41CE70357DB9319D78311386CA0A6EB8;
extern const uint32_t g_rgctx_NativeArray_1_tFC41F54943C6ABF8383700B191400D9ACCC7F8A6;
extern const uint32_t g_rgctx_NativeArray_1_Reinterpret_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_mB2BE21A398F87A262169C466AA756EE105CB1143;
extern const uint32_t g_rgctx_NativeArray_1_tFC41F54943C6ABF8383700B191400D9ACCC7F8A6;
extern const uint32_t g_rgctx_NativeList_1U26_tA81C87FFF6BBD659600A85358F015CC2BE156C60;
extern const uint32_t g_rgctx_NativeList_1_tDD7B29B449688208F82E01BCB2FC739CC145640B;
extern const uint32_t g_rgctx_NativeList_1_GetUnsafeList_mC14052B83905500663287FDCDC2179ED7A31DA72;
extern const uint32_t g_rgctx_NativeList_1_tDD7B29B449688208F82E01BCB2FC739CC145640B;
extern const uint32_t g_rgctx_UnsafeList_1U2A_tD48451A85C691AF76336014E1209BDFAE40DFC06;
extern const uint32_t g_rgctx_UnsafeList_1_t0BE57485CACA9167C3BECBB0C3CADCB8B703D4D8;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t786800A697A5649BD68C130E20B971A79AB25767_mC00A89FBAFC0107F479321BF6CA5C7928FF5A7F6;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisT_t786800A697A5649BD68C130E20B971A79AB25767_m2E66B0CFB74EED0070E50CBC8F976A2A7D423B43;
extern const uint32_t g_rgctx_TU2A_t3DE8E903FF8FC7FF2141A921D9B72BEC4B18F6C2;
extern const uint32_t g_rgctx_TU2A_t5A4FC207791C4374F473C71836DCEB68BFDE4D31;
static const Il2CppRGCTXDefinition s_rgctxValues[18] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tB93F845DBDA86B86E875C4B8733BA04EF2079EF4_mC1117E52BBA0452311F896F34EC5F717A8C11EC9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t9E2BC6384D3C627F4120CA7164476B41F6EF7AA4_m914262E127293EC1BCE79DEEB893EF0EB63C363A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t3CD1365D537A2522B7C4D7465DFD26F5A6648A37 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GPUInstanceDataBufferUploader_WriteInstanceDataJob_TisT_t996EE38E80212E224255DA4B2B4FBEA40E749C74_m5B9DCE67AD22F60678E7A9BE1B97C03D8243A4C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tC0CB470D64AD7A60EAA5044E2C0D29708268A1E8_m53B5E2AC41CE70357DB9319D78311386CA0A6EB8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tFC41F54943C6ABF8383700B191400D9ACCC7F8A6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Reinterpret_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_mB2BE21A398F87A262169C466AA756EE105CB1143 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tFC41F54943C6ABF8383700B191400D9ACCC7F8A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1U26_tA81C87FFF6BBD659600A85358F015CC2BE156C60 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tDD7B29B449688208F82E01BCB2FC739CC145640B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_GetUnsafeList_mC14052B83905500663287FDCDC2179ED7A31DA72 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tDD7B29B449688208F82E01BCB2FC739CC145640B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U2A_tD48451A85C691AF76336014E1209BDFAE40DFC06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t0BE57485CACA9167C3BECBB0C3CADCB8B703D4D8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t786800A697A5649BD68C130E20B971A79AB25767_mC00A89FBAFC0107F479321BF6CA5C7928FF5A7F6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisT_t786800A697A5649BD68C130E20B971A79AB25767_m2E66B0CFB74EED0070E50CBC8F976A2A7D423B43 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t3DE8E903FF8FC7FF2141A921D9B72BEC4B18F6C2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t5A4FC207791C4374F473C71836DCEB68BFDE4D31 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_RenderPipelines_GPUDriven_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_Unity_RenderPipelines_GPUDriven_Runtime_CodeGenModule = 
{
	"Unity.RenderPipelines.GPUDriven.Runtime.dll",
	970,
	s_methodPointers,
	366,
	s_adjustorThunks,
	s_InvokerIndices,
	11,
	s_reversePInvokeIndices,
	7,
	s_rgctxIndices,
	18,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
