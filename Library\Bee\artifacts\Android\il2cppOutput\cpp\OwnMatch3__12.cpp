﻿#include "pch-cpp.hpp"





template <typename T1>
struct VirtualActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
struct InterfaceActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename T1>
struct InterfaceActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename T1, typename T2, typename T3>
struct InterfaceActionInvoker3
{
	typedef void (*Action)(void*, T1, T2, T3, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1, T2 p2, T3 p3)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, p2, p3, invokeData.method);
	}
};
template <typename R>
struct InterfaceFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename R, typename T1>
struct InterfaceFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};

struct Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247;
struct Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C;
struct Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C;
struct Action_1_t2E05EAC1651C7998A1B6C37649C36EA64DEC7D5D;
struct Action_1_tD69A6DC9FBE94131E52F5A73B2A9D4AB51EEC404;
struct Action_1_tC926860F20D428DA3E93D6FBA36420E904DD903B;
struct Action_1_tCAF76FCA3F9A43469074BE52052243FAC78CFA52;
struct Action_1_t8FD8058A6DC8585DE75B4D286B0D8AA7D726CA99;
struct Action_1_t938F308F20F338CACAEAD2DCC4632BA0F0D362A4;
struct Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87;
struct Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A;
struct Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A;
struct Action_2_t7B1DA294236CEB03E01D057BD5D4E8DCFCBF1811;
struct Action_2_t4A5313D1C1FEF099C0E5969104BDE957CD82CF22;
struct Action_2_t2399F3C34C43EB392520F878CA121755E120498E;
struct Dictionary_2_tCDC59AD672DE07C4D23574E46F1C927253117EC6;
struct Dictionary_2_t9899279A2990FB86A896C58A3F27F8E1705785C3;
struct Dictionary_2_tFCDE91BDD0207A67521C455FD60B9EAA6E0E6B62;
struct Dictionary_2_tA348003A3C1CEFB3096E9D2A0BC7F1AC8EC4F710;
struct Dictionary_2_t37E172EA5EF5D4001B91931B5FBEF439B7499E2B;
struct Dictionary_2_t984E19777ABBCF5CAB11835E614DD95C4E137051;
struct Dictionary_2_t521D022CF3349831437AFC237FC9EF148A72C4D4;
struct Dictionary_2_t61407CEA50C6BF647D81B5B21E0CB1D67776F495;
struct HashSet_1_t03A441EC1493E313B17FAEAEFD9B0BEBFA3F6206;
struct IDictionary_2_t619C54C30E0DED057B9E86BB2604052CE9B291B6;
struct IDictionary_2_tE66B36BC0260AF665716B53A2BD8FD968560B4D0;
struct IEqualityComparer_1_t3C976C5FF41D71E55DB897B2D836A522A1FB9576;
struct KeyCollection_t4173FF8BC3730396D8B845060061B2942C1E7527;
struct List_1_t4334C0EA8F95BFF669A1451DC268B77B13520BD9;
struct List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632;
struct List_1_t5E4E165F8C78864D70FD33EFCDBA722E1E376FDF;
struct List_1_t5905E46128E66911F2CCDF055565E1B0005905BC;
struct List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF;
struct List_1_t2CDCA768E7F493F5EDEBC75AEB200FD621354E35;
struct List_1_tFB26A4EB1E92D1F68E8C6023E6126DD2C6D082B7;
struct List_1_t7E5AEB03B96AE3E74F4CEF2E00D9B0BA4CE8B489;
struct List_1_tDA4D291C60B1EFA9EA50BBA3367C657CC9410576;
struct List_1_t9E248B8A0B2D528F9EFE9145F0C2D36B7D443F0B;
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D;
struct List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD;
struct Predicate_1_tA8A070E292A9041EB736A2817C2F70211A0F2AC8;
struct Predicate_1_tF189FD0FC04DE1D6B0F949BD66167FFEF719827F;
struct Predicate_1_t8342C85FF4E41CD1F7024AC0CDC3E5312A32CB12;
struct TweenRunner_1_t5BB0582F926E75E2FE795492679A6CF55A4B4BC4;
struct ValueCollection_tBD40C472D254D643AC074553E872962D5DC363ED;
struct EntryU5BU5D_t3A51889F634F501BA80146C82F4FBDAB70EFE9EB;
struct AvatarDataU5BU5D_t486E442862AFBDF45BD0AB0704E94E2B976A5F31;
struct BoosterDataU5BU5D_tCE57DE1906C24F58843CF0F7FF34AD44E48D1F33;
struct BoosterTypeU5BU5D_tCD95258C3CCE40CF91B1D57E1FB14476E27078D8;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct GemNewU5BU5D_t3FD048FD8F833043B8D93769C329DF682E64B842;
struct GemTileDataU5BU5D_t9A49E6D2B57F8186291E1A2267ED972304DD83D2;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct Int32EnumU5BU5D_t87B7DB802810C38016332669039EF42C487A081F;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct LevelPlayAdFormatU5BU5D_tFA25937CD58D80A76D4218FA619EB7762C3B9D0A;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct ObstacleTileDataU5BU5D_t1E0B9A65B78EF89DB7C7BA6F2F3E5556E91A7AF7;
struct SelectableU5BU5D_t4160E135F02A40F75A63F787D36F31FEC6FE91A9;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct UIVertexU5BU5D_tBC532486B45D071A520751A90E819C77BA4E3D2F;
struct Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA;
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C;
struct Vector3IntU5BU5D_t7A7C64EB93B0E0C1989C82C3D0A003294FC6EC5E;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct AdConfig_t80D1C0D2BD226B6F32829058D850E59BBF3A4190;
struct AdReward_tD6C5592436EB3F2368B8E7C5D03E95D32E5CB4F2;
struct AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066;
struct AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C;
struct AnimationTriggers_tA0DC06F89C5280C6DD972F6F4C8A56D7F4F79074;
struct AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0;
struct AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC;
struct AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277;
struct AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D;
struct BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF;
struct BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537;
struct BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453;
struct BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26;
struct Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098;
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184;
struct CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B;
struct Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26;
struct CanvasRenderer_tAB9A55A976C4E3B2B37D0CE5616E5685A8B43860;
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3;
struct Delegate_t;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct Exception_t;
struct FontData_tB8E562846C6CB59C43260F69AE346B9BF3157224;
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F;
struct GameProgressData_t8FB7F9141EA401B53E8DC816AF77C95A0A5CF17C;
struct GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0;
struct GemAttractorUI_tD926667F7BD66531DDCA8439DE6234FC88BDF05F;
struct GemNew_t83D4282356E02895E91046F39718D12838784383;
struct Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931;
struct IAsyncStateMachine_t0680C7F905C553076B552D5A1A6E39E2F0F36AA2;
struct ICryptoProvider_tA496D527FCBB6DF3E554D66AB63EDEF0BEB6C84A;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IStateMachineRunnerPromise_t5CE1FDF2F619E55F7DCFBE22E2B97835C6781C7B;
struct IUniTaskSource_t5AF6C202B2616C72E989C90B6E76DFEC4118174D;
struct InvokableCallList_t309E1C8C7CE885A0D2F98C84CEA77A8935688382;
struct LevelPlayBannerAd_tD79B76B88EAAF5B91D8F3F3A42D56E831E184296;
struct LevelPlayConfiguration_tDE7E2D4AE0247D4D6BBF893A605FCE643D7A5743;
struct LevelPlayImpressionData_t529FC6DFF4719D2F30A89C6FEAF9AABCE178A736;
struct LevelPlayInitError_tD9341B0F996A5A533D5DF493DBC19ACB90FB225D;
struct LevelPlayInterstitialAd_t9E3B7CA2EDAF58EE50B2D6387BA5792172CE95CE;
struct LevelPlayRewardedAd_t4EC2A91C90145BDC22E356908A2D40DC0625D8BC;
struct Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA;
struct MatchFinder_t69EE87B171310BCC4FEB04E42D261B6B18BC2928;
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3;
struct Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4;
struct MethodInfo_t;
struct MonoBehaviour_t532A11E697******************************;
struct Mouse_t9A9CC4636FA9CDBAD7FB7A02DB0D6395EDCC338F;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct PersistentCallGroup_tB826EDF15DC80F71BCBCD8E410FD959A04C33F25;
struct PlayerInput_t29D1DEE152982ECEDE9DE49AFD42896E9BFF3743;
struct RectMask2D_tACF92BE999C791A665BD1ADEABF5BCEB82846670;
struct RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A;
struct Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712;
struct ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156;
struct Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99;
struct SpriteRenderer_t1DD7FE258F072E1FA87D6577BA27225892B8047B;
struct StarThresholds_t5DBAFB1FD58B3EAF5D52F53B0E83245E197BDE43;
struct String_t;
struct SynchronizationContext_tCDB842BBE53B050802CBBB59C6E6DC45B5B06DC0;
struct Task_t751C4CC3ECD055BABA8A0B6A5DFBB4283DCA8572;
struct Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62;
struct TextGenerator_t85D00417640A53953556C01F9D4E7DDE1ABD8FEC;
struct Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4;
struct TileBase_t07019BD771D35E8EA68118157D6EEE4C770CF0F9;
struct Tilemap_t18C4166D0AC702D5BFC0C411FA73C4B61D9D1751;
struct UIManager_tDD254559375420E885C7ECC25D6C385FCF6C3EDB;
struct UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7;
struct UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977;
struct VertexHelper_tB905FCB02AE67CBEE5F265FE37A5938FC5D136FE;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct U3CU3Ec__DisplayClass2_0_tB74732BEAFB8CFE9A642E0BB4D724BC134BBFCB7;
struct U3CU3Ec__DisplayClass2_0_tF3AB6702B2FF545D80C5D1F0D565F11E0928A9E7;
struct ButtonClickedEvent_t8EA72E90B3BD1392FB3B3EF167D5121C23569E4C;
struct CullStateChangedEvent_t6073CD0D951EC1256BF74B8F9107D68FC89B99B8;

IL2CPP_EXTERN_C RuntimeClass* Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t8FD8058A6DC8585DE75B4D286B0D8AA7D726CA99_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t938F308F20F338CACAEAD2DCC4632BA0F0D362A4_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_tCAF76FCA3F9A43469074BE52052243FAC78CFA52_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_2_t2399F3C34C43EB392520F878CA121755E120498E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AwaiterActions_t5D05CAC006FDEBCF6B65E2B9224BC4B44783BBE5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Dictionary_2_tCDC59AD672DE07C4D23574E46F1C927253117EC6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* EarlyInitHelpers_tA67F29CEEF85CD33340F1A46E13686C44F97695A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Exception_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IStateMachineRunnerPromise_t5CE1FDF2F619E55F7DCFBE22E2B97835C6781C7B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IUniTaskSource_t5AF6C202B2616C72E989C90B6E76DFEC4118174D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* LevelPlay_t0AE0FE23B6C6C7B9FFE58376B5C3B8F85154BD71_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t5905E46128E66911F2CCDF055565E1B0005905BC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Predicate_1_tA8A070E292A9041EB736A2817C2F70211A0F2AC8_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Predicate_1_tF189FD0FC04DE1D6B0F949BD66167FFEF719827F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass2_0_tB74732BEAFB8CFE9A642E0BB4D724BC134BBFCB7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass2_0_tF3AB6702B2FF545D80C5D1F0D565F11E0928A9E7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral00E2A4B1B6CFDE3C09488F7CD792634F6B919861;
IL2CPP_EXTERN_C String_t* _stringLiteral06836AC70561A555D38A238A7F78810086548FF9;
IL2CPP_EXTERN_C String_t* _stringLiteral0D73F44B555B5454E0D888FAB419BD59BF0898BD;
IL2CPP_EXTERN_C String_t* _stringLiteral182F5F25B0C100DCC12581F3503D71A8BB778082;
IL2CPP_EXTERN_C String_t* _stringLiteral1E1A504B4707650AF3C34B98E6DFC9E30D2A8564;
IL2CPP_EXTERN_C String_t* _stringLiteral331105482F42E2071F4A0BC3D0340655ED5E55DB;
IL2CPP_EXTERN_C String_t* _stringLiteral342295F94D9C6893BA06433A2E6B1014710F1E8B;
IL2CPP_EXTERN_C String_t* _stringLiteral3B7A2CCA88B3675CD64BF54B46129FCE1D963037;
IL2CPP_EXTERN_C String_t* _stringLiteral3BD88F02C04BD5CD20FACAC36009CBA043D8259B;
IL2CPP_EXTERN_C String_t* _stringLiteral422990A45DA7F4F92B0DE9D8A17CFC1C1C150748;
IL2CPP_EXTERN_C String_t* _stringLiteral46091384BBC973B3E902010807A1D1448CFD51AC;
IL2CPP_EXTERN_C String_t* _stringLiteral51AF8BD092EC6256EE5A36095D9DB64C9854749C;
IL2CPP_EXTERN_C String_t* _stringLiteral66ED0A119B8EF0CA8D4239109466E3E063C75FD1;
IL2CPP_EXTERN_C String_t* _stringLiteral67BD88E5B7FABE853CF999857489C5BBDE908825;
IL2CPP_EXTERN_C String_t* _stringLiteral6F448909A101B8782582360C107AE0801A1A01F5;
IL2CPP_EXTERN_C String_t* _stringLiteral6F5EC7239B41C242FCB23B64D91DA0070FC1C044;
IL2CPP_EXTERN_C String_t* _stringLiteral7ABD5172E838AC0047853392A897E183DFFDA2D6;
IL2CPP_EXTERN_C String_t* _stringLiteral7FB5340B31482BCAB59C5B5F364A2A5293AEFEBE;
IL2CPP_EXTERN_C String_t* _stringLiteral88FE4AA56A9F59302841BAE8617419E05F3E98B7;
IL2CPP_EXTERN_C String_t* _stringLiteral939A3F92CB23E1B7814ADC682576F07325199C2D;
IL2CPP_EXTERN_C String_t* _stringLiteralA39B4D76A4506CE373573F59E5E8149786EC806B;
IL2CPP_EXTERN_C String_t* _stringLiteralAEE4C086C75EDA8DB6489E26ED81D481E4038D52;
IL2CPP_EXTERN_C String_t* _stringLiteralCAEEE88D9F057EF1E53C4DD5E9F7A3C5D5D7900A;
IL2CPP_EXTERN_C String_t* _stringLiteralCE22D8A1FC18A844A316CA35D8B8DAD9297958C5;
IL2CPP_EXTERN_C String_t* _stringLiteralCE2EA044C74BEE0039FDA1B71187A6900CC6BC21;
IL2CPP_EXTERN_C String_t* _stringLiteralD6C5C03A4F95450BCD8E8C6ED75A7DF38B9584EE;
IL2CPP_EXTERN_C String_t* _stringLiteralEEDFB936045C1501281E6A9EABC5551B5107AF45;
IL2CPP_EXTERN_C const RuntimeMethod* AdsManager_OnImpressionDataReady_m56BD3858A23D77361413172D08056EA54A11B12C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AdsManager_OnInitFailed_m3EF196F44709CC8AFF16BBADCE9D7F951E7F1890_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AdsManager_OnInitSuccess_mF2EC60B9E8CA37C0C8BF77862DA4158715C0513E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AdsSetupExample_OnAdsInitialized_m7AA1EF89BFBFD26DA95BF9BA248CA77F293A54A9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AdsSetupExample_OnBannerFailed_mA43362B2EB1C62ACEE1F68753531C320CDE50129_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AdsSetupExample_OnBannerLoaded_m727617E7481F01F14FAE4FF134634359BE6E40E0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AdsSetupExample_OnInterstitialFailed_mE1C0EFA693C079D46F51C65FE24E355DC2BB050D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AdsSetupExample_OnInterstitialShown_m48B9BCF52271A36178E34591FA12EAA4E53B2C80_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AdsSetupExample_OnRewardedVideoCompleted_m5F9D46DA8CB4D9C6543FA651EBACEE8F601087CD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AdsSetupExample_OnRewardedVideoFailed_mE234436E8F799D695445A9B99D9AF27DF9ACE3E4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AdsSetupExample_ShowInterstitial_mF695E724D4069EF91F8E4EDC7B880B1D52332CB9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AdsSetupExample_ShowRewardedVideoForGold_m0FBD6DAD447E91B293CD92A871C6662054F90309_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AdsSetupExample_ToggleBanner_m512FB8619E7F5FFBB47150D40557EA3E05E03A47_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AsyncUniTaskMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8_mF89DAED1592C898552A8C38223683928ED1CDB6E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AsyncVoidMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39_mA539A7378200E05B9219A20B2912925714355791_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponentInChildren_TisText_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_m1D5533D50D961602AC2CD364E03388FFE2985259_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2__ctor_m12E1C0ED46E9464C3016E84E4EC67E27E53D8D1F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_Dispose_m58E9E0886441A7CCD9D59ED3A25F30F04D0BDC1D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_Dispose_mCCC81CC95BF1E049B322A75E6C850F40A539BF96_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_MoveNext_m1D2A92CE9E05B442EDAA2B9BDF36D2ADDD45CE31_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_MoveNext_mF426A804C3EAFFBCCB63A791327B2E63F93DBF4C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_get_Current_m77874A5C46D351C6F16F2DFCDFF8559AE3392161_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_get_Current_mF1934C0DA5E45E0DF121ABC5B1CEBA197F0C3CE5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IJobExtensions_EarlyJobInit_TisCalculateFallJob_t12218BC3466B56D1F372DABE6E820A6676C90078_m4BDDFF3A5045B56D5489F4CD0340FC922BD5AE5D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IJobExtensions_EarlyJobInit_TisDetectMatchesJob_tE07BFE3943D37234CCA96A23B494634A5479E4FD_m6F6C36EC0F08D3ECF93B7B2E8B0F48925F2A9519_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IJobExtensions_EarlyJobInit_TisFindHintJob_tD765D2B4BD478CD3C28E1853DB36EBA43F92E388_m240E2BAC79A1C619D334A6043738AD05CA6CF3F7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IJobExtensions_EarlyJobInit_TisParseLevelJsonJob_t7241F5370B5A9BE7CD2B40CFBB4F05A2675AB552_m0FE87688F8511BE43D8D0B047F85850CFBA85B7E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IJobExtensions_EarlyJobInit_TisShuffleJob_tB72E45C0F54BBAF53B1DC8C152B42690022FC584_mD3A4478DA29AAB39D98EC98F122581CB2538B0CA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IJobExtensions_EarlyJobInit_TisShuffleJob_tE9A549CF475CC4946C934CFE41B0006ECC37A2DD_m6BF92EAF3C31DCBEACBF415B651D42788E5928D8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_mDFE0DEA453B1CC0B28177181E50A6475AF25F54D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_mF10DB1D3CBB0B14215F0E4F8AB4934A1955E5351_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Contains_m359254483BE42CAD4DCA8FBAFB87473FB4CF00E1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Find_m2152E27A258E71981F7E1DE6D1F5B6DA7F27AEE4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Find_mB6456BBAE8D17C041213EA3F254DFC24D2E55F52_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_GetEnumerator_m1500929B6B659F0084B77A78D7F01E0B01C52D7E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_GetEnumerator_m4FA30D8F70A2A9E393B26B2377DF62EC339ACCE4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m326B607FF8589404C24FFDB935228892676FC826_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m75E092D0D8E1D7464630D385EA1F241D31BD3D56_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_mB3D397E89B7F8DBDF91EDDC6DAE293EE8D58CEA3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_mCA8DD57EAC70C2B5923DBB9D5A77CEAC22E7068E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Object_FindFirstObjectByType_TisMatch3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA_mE778399552203976FD05E969433A2202FD3ABB2D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass2_0_U3CGetAvatarDataU3Eb__0_mC12F96F12BAE99C7E2478CDB26B8290DAD4333E7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass2_0_U3CGetBoosterDataU3Eb__0_m4557E3B3FA8680BEE6F51689ED9463E3BDD0C746_RuntimeMethod_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct Int32EnumU5BU5D_t87B7DB802810C38016332669039EF42C487A081F;
struct LevelPlayAdFormatU5BU5D_tFA25937CD58D80A76D4218FA619EB7762C3B9D0A;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct Dictionary_2_tCDC59AD672DE07C4D23574E46F1C927253117EC6  : public RuntimeObject
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ____buckets;
	EntryU5BU5D_t3A51889F634F501BA80146C82F4FBDAB70EFE9EB* ____entries;
	int32_t ____count;
	int32_t ____freeList;
	int32_t ____freeCount;
	int32_t ____version;
	RuntimeObject* ____comparer;
	KeyCollection_t4173FF8BC3730396D8B845060061B2942C1E7527* ____keys;
	ValueCollection_tBD40C472D254D643AC074553E872962D5DC363ED* ____values;
	RuntimeObject* ____syncRoot;
};
struct List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632  : public RuntimeObject
{
	AvatarDataU5BU5D_t486E442862AFBDF45BD0AB0704E94E2B976A5F31* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t5905E46128E66911F2CCDF055565E1B0005905BC  : public RuntimeObject
{
	BoosterDataU5BU5D_tCE57DE1906C24F58843CF0F7FF34AD44E48D1F33* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF  : public RuntimeObject
{
	BoosterTypeU5BU5D_tCD95258C3CCE40CF91B1D57E1FB14476E27078D8* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tDA4D291C60B1EFA9EA50BBA3367C657CC9410576  : public RuntimeObject
{
	Int32EnumU5BU5D_t87B7DB802810C38016332669039EF42C487A081F* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D  : public RuntimeObject
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD  : public RuntimeObject
{
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct U3CPrivateImplementationDetailsU3E_tBDE4452E24A75D5405DA4D5282FDB2807D701FB8  : public RuntimeObject
{
};
struct AwaiterActions_t5D05CAC006FDEBCF6B65E2B9224BC4B44783BBE5  : public RuntimeObject
{
};
struct BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453  : public RuntimeObject
{
	Dictionary_2_tCDC59AD672DE07C4D23574E46F1C927253117EC6* ___boosters;
};
struct LevelPlayConfiguration_t98005A9447ACE3DBCD8B1E92A8E1A55A4436A4C4  : public RuntimeObject
{
	bool ___U3CIsAdQualityEnabledU3Ek__BackingField;
};
struct LevelPlayImpressionData_t529FC6DFF4719D2F30A89C6FEAF9AABCE178A736  : public RuntimeObject
{
	String_t* ___U3CAllDataU3Ek__BackingField;
	Dictionary_2_tA348003A3C1CEFB3096E9D2A0BC7F1AC8EC4F710* ___InternalDictionary;
};
struct LevelPlayInitError_t8985554C2A7851780672887602EAC05A835A4BC5  : public RuntimeObject
{
	int32_t ___U3CErrorCodeU3Ek__BackingField;
	String_t* ___U3CErrorMessageU3Ek__BackingField;
};
struct ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156  : public RuntimeObject
{
	List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* ___purchasedItems;
	BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* ___boosterInventory;
	List_1_t4334C0EA8F95BFF669A1451DC268B77B13520BD9* ___avatarAdProgress;
	String_t* ___selectedAvatarId;
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct UnityEventBase_t4968A4C72559F35C0923E4BD9C042C3A842E1DB8  : public RuntimeObject
{
	InvokableCallList_t309E1C8C7CE885A0D2F98C84CEA77A8935688382* ___m_Calls;
	PersistentCallGroup_tB826EDF15DC80F71BCBCD8E410FD959A04C33F25* ___m_PersistentCalls;
	bool ___m_CallsDirty;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct __JobReflectionRegistrationOutput__5800224013931567721_t9EDF635DB17CA9177A253F51C7F274CFAF4A4BE5  : public RuntimeObject
{
};
struct U3CU3Ec__DisplayClass2_0_tB74732BEAFB8CFE9A642E0BB4D724BC134BBFCB7  : public RuntimeObject
{
	String_t* ___id;
};
struct Enumerator_t1DF7601FD8273A05A2858952CBA7B08984FE75A5 
{
	List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* ____list;
	int32_t ____index;
	int32_t ____version;
	AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* ____current;
};
struct Enumerator_t68640B372EDF30A473CF3EEC85B10E1E2C4A7A2A 
{
	List_1_t5905E46128E66911F2CCDF055565E1B0005905BC* ____list;
	int32_t ____index;
	int32_t ____version;
	BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* ____current;
};
struct Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A 
{
	List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* ____list;
	int32_t ____index;
	int32_t ____version;
	RuntimeObject* ____current;
};
struct AsyncMethodBuilderCore_tD5ABB3A2536319A3345B32A5481E37E23DD8CEDF 
{
	RuntimeObject* ___m_stateMachine;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___m_defaultContextAction;
};
struct AsyncMethodBuilderCore_tD5ABB3A2536319A3345B32A5481E37E23DD8CEDF_marshaled_pinvoke
{
	RuntimeObject* ___m_stateMachine;
	Il2CppMethodPointer ___m_defaultContextAction;
};
struct AsyncMethodBuilderCore_tD5ABB3A2536319A3345B32A5481E37E23DD8CEDF_marshaled_com
{
	RuntimeObject* ___m_stateMachine;
	Il2CppMethodPointer ___m_defaultContextAction;
};
struct AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0 
{
	RuntimeObject* ___runnerPromise;
	Exception_t* ___ex;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED 
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ____source;
};
struct CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_marshaled_pinvoke
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ____source;
};
struct CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_marshaled_com
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ____source;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17 
{
	Il2CppChar ___m_value;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D 
{
	uint64_t ____dateData;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175 
{
	int16_t ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08 
{
	uint64_t ___jobGroup;
	int32_t ___version;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct SpriteState_tC8199570BE6337FB5C49347C97892B4222E5AACD 
{
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_HighlightedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_PressedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_SelectedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_DisabledSprite;
};
struct SpriteState_tC8199570BE6337FB5C49347C97892B4222E5AACD_marshaled_pinvoke
{
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_HighlightedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_PressedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_SelectedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_DisabledSprite;
};
struct SpriteState_tC8199570BE6337FB5C49347C97892B4222E5AACD_marshaled_com
{
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_HighlightedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_PressedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_SelectedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_DisabledSprite;
};
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	uint32_t ___m_value;
};
struct UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 
{
	RuntimeObject* ___source;
	int16_t ___token;
};
struct UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977  : public UnityEventBase_t4968A4C72559F35C0923E4BD9C042C3A842E1DB8
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___m_InvokeArray;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	float ___x;
	float ___y;
};
struct Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 
{
	int32_t ___m_X;
	int32_t ___m_Y;
	int32_t ___m_Z;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D12_tC6609CB7A46A72951927C237745A264074A1C3E0 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D12_tC6609CB7A46A72951927C237745A264074A1C3E0__padding[12];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D14277_tDB9B2349A850C8C09297FD334DF899BAAE7759ED 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D14277_tDB9B2349A850C8C09297FD334DF899BAAE7759ED__padding[14277];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D15682_t3ED03694D51FC0591363F8B00919B806A73E849C 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D15682_t3ED03694D51FC0591363F8B00919B806A73E849C__padding[15682];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D16_t04CA12FF00241599CFDE66311A3718F106B474C9 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D16_t04CA12FF00241599CFDE66311A3718F106B474C9__padding[16];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D24_tE27AAC60D8EA4801AC95284BCF01843B6B446742 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D24_tE27AAC60D8EA4801AC95284BCF01843B6B446742__padding[24];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D40_t7F70CAC60BBBE4AD58C60984F969CE3A0F0BDA4D 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D40_t7F70CAC60BBBE4AD58C60984F969CE3A0F0BDA4D__padding[40];
	};
};
#pragma pack(pop, tp)
struct Nullable_1_tFB4A56FF9A8D4E35AF50EF50D7B137C9B7AD717B 
{
	bool ___hasValue;
	Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___value;
};
struct AdRewardType_tE53F244937DC7B7CE2682C65C314909CD21B14A2 
{
	int32_t ___value__;
};
struct Allocator_t996642592271AAD9EE688F142741D512C07B5824 
{
	int32_t ___value__;
};
struct AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D 
{
	SynchronizationContext_tCDB842BBE53B050802CBBB59C6E6DC45B5B06DC0* ___m_synchronizationContext;
	AsyncMethodBuilderCore_tD5ABB3A2536319A3345B32A5481E37E23DD8CEDF ___m_coreState;
	Task_t751C4CC3ECD055BABA8A0B6A5DFBB4283DCA8572* ___m_task;
};
struct AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D_marshaled_pinvoke
{
	SynchronizationContext_tCDB842BBE53B050802CBBB59C6E6DC45B5B06DC0* ___m_synchronizationContext;
	AsyncMethodBuilderCore_tD5ABB3A2536319A3345B32A5481E37E23DD8CEDF_marshaled_pinvoke ___m_coreState;
	Task_t751C4CC3ECD055BABA8A0B6A5DFBB4283DCA8572* ___m_task;
};
struct AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D_marshaled_com
{
	SynchronizationContext_tCDB842BBE53B050802CBBB59C6E6DC45B5B06DC0* ___m_synchronizationContext;
	AsyncMethodBuilderCore_tD5ABB3A2536319A3345B32A5481E37E23DD8CEDF_marshaled_com ___m_coreState;
	Task_t751C4CC3ECD055BABA8A0B6A5DFBB4283DCA8572* ___m_task;
};
struct AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0  : public RuntimeObject
{
	String_t* ___avatarId;
	int32_t ___adsWatched;
	int32_t ___adsRequired;
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___lastAdWatchTime;
	bool ___isCompleted;
};
struct BoosterType_t7C7C6EA3A9B130D2D3E89171DF61AB133190FCD6 
{
	int32_t ___value__;
};
struct ColorBlock_tDD7C62E7AFE442652FC98F8D058CE8AE6BFD7C11 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_NormalColor;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_HighlightedColor;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_PressedColor;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_SelectedColor;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_DisabledColor;
	float ___m_ColorMultiplier;
	float ___m_FadeDuration;
};
struct CurrencyType_t3D4043D5EE55AEE40DBB56F803085A0F202212F6 
{
	int32_t ___value__;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct EncryptionType_tFC142C496F7B1C6CF95BBBEB2E3241BAD42D85E3 
{
	int32_t ___value__;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct GemType_t72FB7004B372649025DC3390785C6F752199FD9E 
{
	int32_t ___value__;
};
struct Int32Enum_tCBAC8BA2BFF3A845FA599F303093BBBA374B6F0C 
{
	int32_t ___value__;
};
struct LevelPlayAdFormat_tB2D3FF956CF6A1C409A53274F51C15637ED12F66 
{
	int32_t ___value__;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct PlayerLoopTiming_tA0561E77DCF3749CC535F4F45642F515BDF040C2 
{
	int32_t ___value__;
};
struct SavedGameRequestStatus_t860E4308D493A29A4245D51498EF35AEE3A9B8A0 
{
	int32_t ___value__;
};
struct UniTaskStatus_tC898C29839EBB5DB7055C3DF299A2C276237CB70 
{
	int32_t ___value__;
};
struct ButtonClickedEvent_t8EA72E90B3BD1392FB3B3EF167D5121C23569E4C  : public UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977
{
};
struct Mode_t2D49D0E10E2FDA0026278C2400C16033888D0542 
{
	int32_t ___value__;
};
struct Transition_tF856A77C9FAC6D26EA3CA158CF68B739D35397B3 
{
	int32_t ___value__;
};
struct Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 
{
	UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 ___task;
};
struct Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_marshaled_pinvoke
{
	UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 ___task;
};
struct Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_marshaled_com
{
	UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 ___task;
};
struct NativeArray_1_tCE8097AF7BD84EDF9B6BDB72E17AD32DEAD27156 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct Nullable_1_t83CC6CB14E2A66E3BA6EDD7684E89AF41F290A1B 
{
	bool ___hasValue;
	int32_t ___value;
};
struct AdReward_tD6C5592436EB3F2368B8E7C5D03E95D32E5CB4F2  : public RuntimeObject
{
	int32_t ___type;
	int32_t ___amount;
	String_t* ___description;
	String_t* ___avatarId;
};
struct AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277  : public RuntimeObject
{
	String_t* ___id;
	String_t* ___name;
	String_t* ___description;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___icon;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___avatarSprite;
	int32_t ___currencyType;
	int32_t ___price;
	bool ___canPurchaseWithAds;
	int32_t ___adsRequired;
};
struct BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537  : public RuntimeObject
{
	int32_t ___type;
	String_t* ___name;
	String_t* ___description;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___icon;
	int32_t ___defaultPrice;
	int32_t ___defaultQuantity;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct Navigation_t4D2E201D65749CF4E104E8AC1232CF1D6F14795C 
{
	int32_t ___m_Mode;
	bool ___m_WrapAround;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnUp;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnDown;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnLeft;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnRight;
};
struct Navigation_t4D2E201D65749CF4E104E8AC1232CF1D6F14795C_marshaled_pinvoke
{
	int32_t ___m_Mode;
	int32_t ___m_WrapAround;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnUp;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnDown;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnLeft;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnRight;
};
struct Navigation_t4D2E201D65749CF4E104E8AC1232CF1D6F14795C_marshaled_com
{
	int32_t ___m_Mode;
	int32_t ___m_WrapAround;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnUp;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnDown;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnLeft;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnRight;
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_pinvoke : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_com : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
};
struct Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct U3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39 
{
	int32_t ___U3CU3E1__state;
	AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D ___U3CU3Et__builder;
	AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* ___U3CU3E4__this;
	Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 ___U3CU3Eu__1;
};
struct U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8 
{
	int32_t ___U3CU3E1__state;
	AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0 ___U3CU3Et__builder;
	AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* ___U3CU3E4__this;
	float ___U3CtimeoutU3E5__2;
	float ___U3CelapsedU3E5__3;
	Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 ___U3CU3Eu__1;
};
struct U3CU3Ec__DisplayClass2_0_tF3AB6702B2FF545D80C5D1F0D565F11E0928A9E7  : public RuntimeObject
{
	int32_t ___type;
};
struct Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247  : public MulticastDelegate_t
{
};
struct Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C  : public MulticastDelegate_t
{
};
struct Action_1_tC926860F20D428DA3E93D6FBA36420E904DD903B  : public MulticastDelegate_t
{
};
struct Action_1_tCAF76FCA3F9A43469074BE52052243FAC78CFA52  : public MulticastDelegate_t
{
};
struct Action_1_t8FD8058A6DC8585DE75B4D286B0D8AA7D726CA99  : public MulticastDelegate_t
{
};
struct Action_1_t938F308F20F338CACAEAD2DCC4632BA0F0D362A4  : public MulticastDelegate_t
{
};
struct Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87  : public MulticastDelegate_t
{
};
struct Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A  : public MulticastDelegate_t
{
};
struct Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A  : public MulticastDelegate_t
{
};
struct Action_2_t7B1DA294236CEB03E01D057BD5D4E8DCFCBF1811  : public MulticastDelegate_t
{
};
struct Action_2_t4A5313D1C1FEF099C0E5969104BDE957CD82CF22  : public MulticastDelegate_t
{
};
struct Action_2_t2399F3C34C43EB392520F878CA121755E120498E  : public MulticastDelegate_t
{
};
struct Predicate_1_tA8A070E292A9041EB736A2817C2F70211A0F2AC8  : public MulticastDelegate_t
{
};
struct Predicate_1_tF189FD0FC04DE1D6B0F949BD66167FFEF719827F  : public MulticastDelegate_t
{
};
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07  : public MulticastDelegate_t
{
};
struct AdConfig_t80D1C0D2BD226B6F32829058D850E59BBF3A4190  : public ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A
{
	String_t* ___androidAppKey;
	String_t* ___iosAppKey;
	String_t* ___androidBannerAdUnitId;
	String_t* ___iosBannerAdUnitId;
	String_t* ___androidInterstitialAdUnitId;
	String_t* ___iosInterstitialAdUnitId;
	String_t* ___androidRewardedVideoAdUnitId;
	String_t* ___iosRewardedVideoAdUnitId;
	bool ___useTestIds;
	String_t* ___testBannerAdUnitId;
	String_t* ___testInterstitialAdUnitId;
	String_t* ___testRewardedVideoAdUnitId;
};
struct AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC  : public ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A
{
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___defaultAvatarSprite;
	List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* ___avatars;
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF  : public ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A
{
	int32_t ___extraMovesAmount;
	List_1_t5905E46128E66911F2CCDF055565E1B0005905BC* ___boosters;
};
struct UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7  : public MulticastDelegate_t
{
};
struct MonoBehaviour_t532A11E697******************************  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ___m_CancellationTokenSource;
};
struct AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066  : public MonoBehaviour_t532A11E697******************************
{
	AdConfig_t80D1C0D2BD226B6F32829058D850E59BBF3A4190* ___adConfig;
	bool ___enableDebugLogs;
	bool ___autoInitialize;
	bool ___showBannerOnStart;
	LevelPlayBannerAd_tD79B76B88EAAF5B91D8F3F3A42D56E831E184296* ___bannerAd;
	LevelPlayInterstitialAd_t9E3B7CA2EDAF58EE50B2D6387BA5792172CE95CE* ___interstitialAd;
	LevelPlayRewardedAd_t4EC2A91C90145BDC22E356908A2D40DC0625D8BC* ___rewardedVideoAd;
	bool ___U3CIsInitializedU3Ek__BackingField;
	bool ___U3CIsBannerLoadedU3Ek__BackingField;
	bool ___U3CIsInterstitialLoadedU3Ek__BackingField;
	bool ___U3CIsRewardedVideoLoadedU3Ek__BackingField;
	bool ___U3CIsBannerVisibleU3Ek__BackingField;
	AdReward_tD6C5592436EB3F2368B8E7C5D03E95D32E5CB4F2* ___currentReward;
};
struct AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C  : public MonoBehaviour_t532A11E697******************************
{
	Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* ___rewardedVideoButton;
	Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* ___interstitialButton;
	Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* ___bannerToggleButton;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___statusText;
	int32_t ___goldRewardAmount;
	int32_t ___extraMovesAmount;
};
struct AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D  : public MonoBehaviour_t532A11E697******************************
{
	AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* ___avatarConfig;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* ___progressManager;
	String_t* ___currentSelectedAvatarId;
};
struct BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26  : public MonoBehaviour_t532A11E697******************************
{
	BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* ___boosterConfig;
	BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* ___inventory;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* ___progressManager;
};
struct GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0  : public MonoBehaviour_t532A11E697******************************
{
	String_t* ___saveFileName;
	bool ___autoSave;
	float ___autoSaveInterval;
	bool ___enableBackups;
	int32_t ___maxBackups;
	bool ___enableEncryption;
	String_t* ___encryptionKey;
	int32_t ___encryptionType;
	bool ___enableCloudSave;
	String_t* ___cloudSaveFileName;
	bool ___autoCloudSync;
	float ___cloudSyncInterval;
	bool ___preferCloudOnConflict;
	GameProgressData_t8FB7F9141EA401B53E8DC816AF77C95A0A5CF17C* ___currentProgress;
	float ___lastAutoSaveTime;
	RuntimeObject* ___cryptoProvider;
	bool ___isGooglePlayInitialized;
	bool ___isCloudSaveInProgress;
	float ___lastCloudSyncTime;
	int32_t ___lastCloudSaveStatus;
	Action_1_t2E05EAC1651C7998A1B6C37649C36EA64DEC7D5D* ___OnProgressLoaded;
	Action_1_t2E05EAC1651C7998A1B6C37649C36EA64DEC7D5D* ___OnProgressSaved;
	Action_1_tD69A6DC9FBE94131E52F5A73B2A9D4AB51EEC404* ___OnGoldChanged;
	Action_1_tD69A6DC9FBE94131E52F5A73B2A9D4AB51EEC404* ___OnStarsChanged;
	Action_1_tD69A6DC9FBE94131E52F5A73B2A9D4AB51EEC404* ___OnLevelUnlocked;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* ___OnCloudSaveStatusChanged;
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* ___OnCloudSaveError;
};
struct Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA  : public MonoBehaviour_t532A11E697******************************
{
	Tilemap_t18C4166D0AC702D5BFC0C411FA73C4B61D9D1751* ___GemsTilemap;
	TileBase_t07019BD771D35E8EA68118157D6EEE4C770CF0F9* ___gemPlacer;
	GemNewU5BU5D_t3FD048FD8F833043B8D93769C329DF682E64B842* ___gemTypes;
	ObstacleTileDataU5BU5D_t1E0B9A65B78EF89DB7C7BA6F2F3E5556E91A7AF7* ___obstacleTileMappings;
	GemTileDataU5BU5D_t9A49E6D2B57F8186291E1A2267ED972304DD83D2* ___gemTileMappings;
	float ___U3CfallSpeedU3Ek__BackingField;
	float ___tweenDuration;
	Tilemap_t18C4166D0AC702D5BFC0C411FA73C4B61D9D1751* ___BackgroundTilemap;
	TileBase_t07019BD771D35E8EA68118157D6EEE4C770CF0F9* ___BackgroundFillTile;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___backgroundSprite;
	SpriteRenderer_t1DD7FE258F072E1FA87D6577BA27225892B8047B* ___backgroundSpriteRenderer;
	int32_t ___totalMoves;
	int32_t ___movesRemaining;
	List_1_t7E5AEB03B96AE3E74F4CEF2E00D9B0BA4CE8B489* ___goals;
	bool ___isGameComplete;
	bool ___isGameOver;
	float ___hintInactivityTime;
	bool ___showHints;
	bool ___isShowingHint;
	Nullable_1_tFB4A56FF9A8D4E35AF50EF50D7B137C9B7AD717B ___hintStartPos;
	Nullable_1_tFB4A56FF9A8D4E35AF50EF50D7B137C9B7AD717B ___hintEndPos;
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ____hintTimerCts;
	bool ___isFindingHint;
	Dictionary_2_t9899279A2990FB86A896C58A3F27F8E1705785C3* ____cachedColumnY;
	List_1_t9E248B8A0B2D528F9EFE9145F0C2D36B7D443F0B* ___matchShapes;
	Dictionary_2_t521D022CF3349831437AFC237FC9EF148A72C4D4* ____gems;
	HashSet_1_t03A441EC1493E313B17FAEAEFD9B0BEBFA3F6206* ___validPositions;
	HashSet_1_t03A441EC1493E313B17FAEAEFD9B0BEBFA3F6206* ___pendingRefill;
	Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___mainCamera;
	Mouse_t9A9CC4636FA9CDBAD7FB7A02DB0D6395EDCC338F* ___mouse;
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ___cts;
	bool ___isResolvingMatches;
	bool ___isInitializing;
	bool ___isShuffling;
	bool ___needResolve;
	bool ___isTransitioning;
	bool ___isSwapping;
	Dictionary_2_t61407CEA50C6BF647D81B5B21E0CB1D67776F495* ___obstacles;
	List_1_tFB26A4EB1E92D1F68E8C6023E6126DD2C6D082B7* ___lastMatchedGemTypes;
	int32_t ___U3CComboChainLevelU3Ek__BackingField;
	bool ___U3CIsFirstMatchBatchU3Ek__BackingField;
	int32_t ___U3CCascadeIndexU3Ek__BackingField;
	StarThresholds_t5DBAFB1FD58B3EAF5D52F53B0E83245E197BDE43* ___U3CCurrentStarThresholdsU3Ek__BackingField;
	Nullable_1_tFB4A56FF9A8D4E35AF50EF50D7B137C9B7AD717B ___dragStartCell;
	PlayerInput_t29D1DEE152982ECEDE9DE49AFD42896E9BFF3743* ___input;
	bool ___enableTilemapFrame;
	UIManager_tDD254559375420E885C7ECC25D6C385FCF6C3EDB* ___uiManager;
	GemAttractorUI_tD926667F7BD66531DDCA8439DE6234FC88BDF05F* ___gemAttractorUI;
	NativeArray_1_tCE8097AF7BD84EDF9B6BDB72E17AD32DEAD27156 ____hintResult;
	JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08 ____hintJobHandle;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___touchStartPosition;
	Nullable_1_tFB4A56FF9A8D4E35AF50EF50D7B137C9B7AD717B ___lastSwappedFrom;
	Nullable_1_tFB4A56FF9A8D4E35AF50EF50D7B137C9B7AD717B ___lastSwappedTo;
	MatchFinder_t69EE87B171310BCC4FEB04E42D261B6B18BC2928* ____matchFinder;
	List_1_t5E4E165F8C78864D70FD33EFCDBA722E1E376FDF* ___bonusGems;
	GemNew_t83D4282356E02895E91046F39718D12838784383* ___horizontalRocketPrefab;
	GemNew_t83D4282356E02895E91046F39718D12838784383* ___verticalRocketPrefab;
	Dictionary_2_t37E172EA5EF5D4001B91931B5FBEF439B7499E2B* ___gemTileDict;
	Dictionary_2_t984E19777ABBCF5CAB11835E614DD95C4E137051* ___obstacleTileDict;
	float ___BORDER_MARGIN;
};
struct UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D  : public MonoBehaviour_t532A11E697******************************
{
};
struct Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931  : public UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_Material;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_Color;
	bool ___m_SkipLayoutUpdate;
	bool ___m_SkipMaterialUpdate;
	bool ___m_RaycastTarget;
	bool ___m_RaycastTargetCache;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___m_RaycastPadding;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_RectTransform;
	CanvasRenderer_tAB9A55A976C4E3B2B37D0CE5616E5685A8B43860* ___m_CanvasRenderer;
	Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26* ___m_Canvas;
	bool ___m_VertsDirty;
	bool ___m_MaterialDirty;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyLayoutCallback;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyVertsCallback;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyMaterialCallback;
	Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___m_CachedMesh;
	Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___m_CachedUvs;
	TweenRunner_1_t5BB0582F926E75E2FE795492679A6CF55A4B4BC4* ___m_ColorTweenRunner;
	bool ___U3CuseLegacyMeshGenerationU3Ek__BackingField;
};
struct Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712  : public UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D
{
	bool ___m_EnableCalled;
	Navigation_t4D2E201D65749CF4E104E8AC1232CF1D6F14795C ___m_Navigation;
	int32_t ___m_Transition;
	ColorBlock_tDD7C62E7AFE442652FC98F8D058CE8AE6BFD7C11 ___m_Colors;
	SpriteState_tC8199570BE6337FB5C49347C97892B4222E5AACD ___m_SpriteState;
	AnimationTriggers_tA0DC06F89C5280C6DD972F6F4C8A56D7F4F79074* ___m_AnimationTriggers;
	bool ___m_Interactable;
	Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* ___m_TargetGraphic;
	bool ___m_GroupsAllowInteraction;
	int32_t ___m_CurrentIndex;
	bool ___U3CisPointerInsideU3Ek__BackingField;
	bool ___U3CisPointerDownU3Ek__BackingField;
	bool ___U3ChasSelectionU3Ek__BackingField;
	List_1_t2CDCA768E7F493F5EDEBC75AEB200FD621354E35* ___m_CanvasGroupCache;
};
struct Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098  : public Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712
{
	ButtonClickedEvent_t8EA72E90B3BD1392FB3B3EF167D5121C23569E4C* ___m_OnClick;
};
struct MaskableGraphic_tFC5B6BE351C90DE53744DF2A70940242774B361E  : public Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931
{
	bool ___m_ShouldRecalculateStencil;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_MaskMaterial;
	RectMask2D_tACF92BE999C791A665BD1ADEABF5BCEB82846670* ___m_ParentMask;
	bool ___m_Maskable;
	bool ___m_IsMaskingGraphic;
	bool ___m_IncludeForMasking;
	CullStateChangedEvent_t6073CD0D951EC1256BF74B8F9107D68FC89B99B8* ___m_OnCullStateChanged;
	bool ___m_ShouldRecalculate;
	int32_t ___m_StencilValue;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___m_Corners;
};
struct Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62  : public MaskableGraphic_tFC5B6BE351C90DE53744DF2A70940242774B361E
{
	FontData_tB8E562846C6CB59C43260F69AE346B9BF3157224* ___m_FontData;
	String_t* ___m_Text;
	TextGenerator_t85D00417640A53953556C01F9D4E7DDE1ABD8FEC* ___m_TextCache;
	TextGenerator_t85D00417640A53953556C01F9D4E7DDE1ABD8FEC* ___m_TextCacheForLayout;
	bool ___m_DisableFontTextureRebuiltCallback;
	UIVertexU5BU5D_tBC532486B45D071A520751A90E819C77BA4E3D2F* ___m_TempVerts;
};
struct List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632_StaticFields
{
	AvatarDataU5BU5D_t486E442862AFBDF45BD0AB0704E94E2B976A5F31* ___s_emptyArray;
};
struct List_1_t5905E46128E66911F2CCDF055565E1B0005905BC_StaticFields
{
	BoosterDataU5BU5D_tCE57DE1906C24F58843CF0F7FF34AD44E48D1F33* ___s_emptyArray;
};
struct List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF_StaticFields
{
	BoosterTypeU5BU5D_tCD95258C3CCE40CF91B1D57E1FB14476E27078D8* ___s_emptyArray;
};
struct List_1_tDA4D291C60B1EFA9EA50BBA3367C657CC9410576_StaticFields
{
	Int32EnumU5BU5D_t87B7DB802810C38016332669039EF42C487A081F* ___s_emptyArray;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D_StaticFields
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___s_emptyArray;
};
struct List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD_StaticFields
{
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ___s_emptyArray;
};
struct U3CPrivateImplementationDetailsU3E_tBDE4452E24A75D5405DA4D5282FDB2807D701FB8_StaticFields
{
	__StaticArrayInitTypeSizeU3D40_t7F70CAC60BBBE4AD58C60984F969CE3A0F0BDA4D ___006D4B2FE54D18D63ECC9523B04DDF5F14F13BD7DB02985081FF75389E48807E;
	__StaticArrayInitTypeSizeU3D14277_tDB9B2349A850C8C09297FD334DF899BAAE7759ED ___29941E2EF88F6FF7FA0AB904393223767680C6E6749B5E37CE10D940645CC2C4;
	__StaticArrayInitTypeSizeU3D12_tC6609CB7A46A72951927C237745A264074A1C3E0 ___2C7D29C1C8726CFCDB9E0D98F6FBD9708D2FDF0E6DECABEE9979154FBD0465B7;
	__StaticArrayInitTypeSizeU3D15682_t3ED03694D51FC0591363F8B00919B806A73E849C ___329888EE238709C8066CD1D228B8AC257BA6CEE3A0285227DAFA08D07FB9C694;
	__StaticArrayInitTypeSizeU3D12_tC6609CB7A46A72951927C237745A264074A1C3E0 ___4636993D3E1DA4E9D6B8F87B79E8F7C6D018580D52661950EABC3845C5897A4D;
	__StaticArrayInitTypeSizeU3D24_tE27AAC60D8EA4801AC95284BCF01843B6B446742 ___90D856B7ECAC90C26898AF8A46404297AA0EF65768F62FDF8C3F08294BCBEE49;
	__StaticArrayInitTypeSizeU3D16_t04CA12FF00241599CFDE66311A3718F106B474C9 ___BAED642339816AFFB3FE8719792D0E4CE82F12DB72B7373D244EAA65445800FE;
	__StaticArrayInitTypeSizeU3D12_tC6609CB7A46A72951927C237745A264074A1C3E0 ___BEC37A9FCE0AC3B78CFD60F3DD5DE068ED287065B719D441691FBA961C0A61E4;
	__StaticArrayInitTypeSizeU3D16_t04CA12FF00241599CFDE66311A3718F106B474C9 ___DBE2C839F4852BD22F1DDA03BF584B563623BC76B88AC3DA14144AC88D1DC3AD;
	__StaticArrayInitTypeSizeU3D12_tC6609CB7A46A72951927C237745A264074A1C3E0 ___F8AD5C36761DDE85C080E4A60CE8EF1474C96E4ACB8EEE578D2D0047EAB3980C;
};
struct AwaiterActions_t5D05CAC006FDEBCF6B65E2B9224BC4B44783BBE5_StaticFields
{
	Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* ___InvokeContinuationDelegate;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_StaticFields
{
	Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* ___s_actionToActionObjShunt;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17_StaticFields
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___s_categoryForLatin1;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270_StaticFields
{
	UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 ___CanceledUniTask;
	UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 ___CompletedTask;
};
struct Exception_t_StaticFields
{
	RuntimeObject* ___s_EDILock;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
struct AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066_StaticFields
{
	AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* ___U3CInstanceU3Ek__BackingField;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdsInitialized;
	Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247* ___OnRewardedVideoCompleted;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnRewardedVideoFailed;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnInterstitialShown;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnInterstitialFailed;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnBannerLoaded;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnBannerFailed;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnBannerShown;
};
struct AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields
{
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* ___OnAvatarSelected;
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* ___OnAvatarUnlocked;
	Action_2_t2399F3C34C43EB392520F878CA121755E120498E* ___OnAvatarAdProgressChanged;
	AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* ___U3CInstanceU3Ek__BackingField;
};
struct BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields
{
	Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* ___OnBoosterCountChanged;
	Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* ___OnBoosterUsed;
	BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* ___U3CInstanceU3Ek__BackingField;
};
struct GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0_StaticFields
{
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* ___instance;
};
struct Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA_StaticFields
{
	Vector3IntU5BU5D_t7A7C64EB93B0E0C1989C82C3D0A003294FC6EC5E* ___DIRECTIONS;
	Nullable_1_t83CC6CB14E2A66E3BA6EDD7684E89AF41F290A1B ___lastColorBombTarget;
	Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* ___Instance;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___LevelCompletedEvent;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___GameOverEvent;
};
struct Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712_StaticFields
{
	SelectableU5BU5D_t4160E135F02A40F75A63F787D36F31FEC6FE91A9* ___s_Selectables;
	int32_t ___s_SelectableCount;
};
struct Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_StaticFields
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___s_DefaultText;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct LevelPlayAdFormatU5BU5D_tFA25937CD58D80A76D4218FA619EB7762C3B9D0A  : public RuntimeArray
{
	ALIGN_FIELD (8) int32_t m_Items[1];

	inline int32_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline int32_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, int32_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline int32_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline int32_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, int32_t value)
	{
		m_Items[index] = value;
	}
};
struct Int32EnumU5BU5D_t87B7DB802810C38016332669039EF42C487A081F  : public RuntimeArray
{
	ALIGN_FIELD (8) int32_t m_Items[1];

	inline int32_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline int32_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, int32_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline int32_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline int32_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, int32_t value)
	{
		m_Items[index] = value;
	}
};
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncVoidMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39_mA539A7378200E05B9219A20B2912925714355791_gshared (AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D* __this, Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* ___0_awaiter, U3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39* ___1_stateMachine, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AsyncUniTaskMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8_mF89DAED1592C898552A8C38223683928ED1CDB6E_gshared_inline (AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0* __this, Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* ___0_awaiter, U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8* ___1_stateMachine, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Component_GetComponentInChildren_TisRuntimeObject_mE483A27E876DE8E4E6901D6814837F81D7C42F65_gshared (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Predicate_1__ctor_m3E007299121A15DF80F4A210FF8C20E5DF688F20_gshared (Predicate_1_t8342C85FF4E41CD1F7024AC0CDC3E5312A32CB12* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* List_1_Find_m5E78A210541B0D844FE27B94F509313623BE33D3_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, Predicate_1_t8342C85FF4E41CD1F7024AC0CDC3E5312A32CB12* ___0_match, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m7C3D0A1FD36704AFBBE4FD4E69204B809D3FC90E_gshared (List_1_tDA4D291C60B1EFA9EA50BBA3367C657CC9410576* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_m809450298141D527D3A4FFAF77AE69D9B08CC17F_gshared_inline (List_1_tDA4D291C60B1EFA9EA50BBA3367C657CC9410576* __this, int32_t ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_2_Invoke_m3BE7DD18C4D59E8D0ECBBA5ED30F084F842415DD_gshared_inline (Action_2_t7B1DA294236CEB03E01D057BD5D4E8DCFCBF1811* __this, int32_t ___0_arg1, int32_t ___1_arg2, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mBF7DDBCD230E9D28EDF45D3E65F907DE1AE0CCBC_gshared_inline (Action_1_tC926860F20D428DA3E93D6FBA36420E904DD903B* __this, int32_t ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2__ctor_m7A818AF180482B7431F48C234C3A9C61BA601499_gshared (Dictionary_2_tFCDE91BDD0207A67521C455FD60B9EAA6E0E6B62* __this, RuntimeObject* ___0_dictionary, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Object_FindFirstObjectByType_TisRuntimeObject_mC5927319EB5B80095EFBA653D414D6F8AA87DC0A_gshared (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool List_1_Contains_m4C9139C2A6B23E9343D3F87807B32C6E2CFE660D_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_2_Invoke_mAFC1A6B82DBD3B488FF63EE80C4D280D6979260F_gshared_inline (Action_2_t4A5313D1C1FEF099C0E5969104BDE957CD82CF22* __this, RuntimeObject* ___0_arg1, float ___1_arg2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IJobExtensions_EarlyJobInit_TisParseLevelJsonJob_t7241F5370B5A9BE7CD2B40CFBB4F05A2675AB552_m0FE87688F8511BE43D8D0B047F85850CFBA85B7E_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IJobExtensions_EarlyJobInit_TisCalculateFallJob_t12218BC3466B56D1F372DABE6E820A6676C90078_m4BDDFF3A5045B56D5489F4CD0340FC922BD5AE5D_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IJobExtensions_EarlyJobInit_TisShuffleJob_tE9A549CF475CC4946C934CFE41B0006ECC37A2DD_m6BF92EAF3C31DCBEACBF415B651D42788E5928D8_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IJobExtensions_EarlyJobInit_TisDetectMatchesJob_tE07BFE3943D37234CCA96A23B494634A5479E4FD_m6F6C36EC0F08D3ECF93B7B2E8B0F48925F2A9519_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IJobExtensions_EarlyJobInit_TisFindHintJob_tD765D2B4BD478CD3C28E1853DB36EBA43F92E388_m240E2BAC79A1C619D334A6043738AD05CA6CF3F7_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IJobExtensions_EarlyJobInit_TisShuffleJob_tB72E45C0F54BBAF53B1DC8C152B42690022FC584_mD3A4478DA29AAB39D98EC98F122581CB2538B0CA_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncUniTask_1_SetStateMachine_mC01AE78C304544C088858602E092DF11755B2926_gshared (U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8* ___0_stateMachine, RuntimeObject** ___1_runnerPromiseFieldRef, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void List_1_AddWithResize_mAD159B09F28BD7914562E219CA52E2D4BDCF5530_gshared (List_1_tDA4D291C60B1EFA9EA50BBA3367C657CC9410576* __this, int32_t ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;

IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AdsManager_get_IsInitialized_mA114A221EEDEF44409F6FABF3070C12BCCF6D748_inline (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_LogDebug_m1FD00D0DCF2E0EDE397CE74223820756C11157BE (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_LogError_mC4C9BE08E43ED6A4EEFC9EA7D2A18414956F0520 (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_Log_m87A9A3C761FF5C43ED8A53B16190A53D08F818BB (RuntimeObject* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LevelPlay_ValidateIntegration_m099CA631FA230B3C9EAE2F9D005808746D51916E (const RuntimeMethod* method) ;
inline void Action_1__ctor_mC9F6546DA3E3FC6BADDC1A846976E6D0DCA7E643 (Action_1_tCAF76FCA3F9A43469074BE52052243FAC78CFA52* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_tCAF76FCA3F9A43469074BE52052243FAC78CFA52*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LevelPlay_add_OnInitSuccess_mA20A6967677EFA968A1B39FCE3BC4CDB0DE644D6 (Action_1_tCAF76FCA3F9A43469074BE52052243FAC78CFA52* ___0_value, const RuntimeMethod* method) ;
inline void Action_1__ctor_m0116316695141163F551BA515A87470E9DF5F401 (Action_1_t938F308F20F338CACAEAD2DCC4632BA0F0D362A4* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t938F308F20F338CACAEAD2DCC4632BA0F0D362A4*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LevelPlay_add_OnInitFailed_mFF1B9C5D58DE14D1C3B907F690CAECCE9BE5DA82 (Action_1_t938F308F20F338CACAEAD2DCC4632BA0F0D362A4* ___0_value, const RuntimeMethod* method) ;
inline void Action_1__ctor_m6520DA264AD20E163E4858213E399D165E757FD8 (Action_1_t8FD8058A6DC8585DE75B4D286B0D8AA7D726CA99* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t8FD8058A6DC8585DE75B4D286B0D8AA7D726CA99*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LevelPlay_add_OnImpressionDataReady_m420592125940BA583FFF62A8FB34620854D79EA8 (Action_1_t8FD8058A6DC8585DE75B4D286B0D8AA7D726CA99* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AdConfig_get_AppKey_m9359E4F90FC1343439AB202C5EEA51B0454D9F89_inline (AdConfig_t80D1C0D2BD226B6F32829058D850E59BBF3A4190* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LevelPlay_Init_m34F777F7B0639379E9C83D66E04EDB2FDE810B43 (String_t* ___0_appKey, String_t* ___1_userId, LevelPlayAdFormatU5BU5D_tFA25937CD58D80A76D4218FA619EB7762C3B9D0A* ___2_adFormats, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 AdsManager_WaitForInitialization_m18F05B14C709CBE75C5CE35C14FEBE0B5138BFA7 (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 UniTask_GetAwaiter_mF05A09B81913BECFD58FC67A16C0251FFCCAC939_inline (UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Awaiter_get_IsCompleted_m2D01E3AB8A7C5AA8AA1E1EF58D92A6A095C00B9A_inline (Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* __this, const RuntimeMethod* method) ;
inline void AsyncVoidMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39_mA539A7378200E05B9219A20B2912925714355791 (AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D* __this, Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* ___0_awaiter, U3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39* ___1_stateMachine, const RuntimeMethod* method)
{
	((  void (*) (AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D*, Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956*, U3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39*, const RuntimeMethod*))AsyncVoidMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39_mA539A7378200E05B9219A20B2912925714355791_gshared)(__this, ___0_awaiter, ___1_stateMachine, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Awaiter_GetResult_mC439993563D3BD49CEC67AAF6AFB3AEF72E916BD_inline (Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncVoidMethodBuilder_SetException_mD9A6F5D1A99A62AC9DF322901BFDE05193CB177B (AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D* __this, Exception_t* ___0_exception, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncVoidMethodBuilder_SetResult_m008490FDF057D5F5D871F537C7A58BE36027F3DC (AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CInitializeAdsU3Ed__56_MoveNext_m3161C23160159B721D56563AEBF3D1FECF8DEEA9 (U3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncVoidMethodBuilder_SetStateMachine_m48640FB81C34D4C2B5A5BBA7F5AE17DC50BF1A25 (AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D* __this, RuntimeObject* ___0_stateMachine, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CInitializeAdsU3Ed__56_SetStateMachine_mBBAB47B4727508F6FAACB2FE9CA89F110FD7F1A2 (U3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39* __this, RuntimeObject* ___0_stateMachine, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 UniTask_Delay_m0F6B9A8CE95AB6C59ACCCF49E96369AB9FAD4960 (int32_t ___0_millisecondsDelay, bool ___1_ignoreTimeScale, int32_t ___2_delayTiming, CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED ___3_cancellationToken, bool ___4_cancelImmediately, const RuntimeMethod* method) ;
inline void AsyncUniTaskMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8_mF89DAED1592C898552A8C38223683928ED1CDB6E_inline (AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0* __this, Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* ___0_awaiter, U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8* ___1_stateMachine, const RuntimeMethod* method)
{
	((  void (*) (AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0*, Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956*, U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8*, const RuntimeMethod*))AsyncUniTaskMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8_mF89DAED1592C898552A8C38223683928ED1CDB6E_gshared_inline)(__this, ___0_awaiter, ___1_stateMachine, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AsyncUniTaskMethodBuilder_SetException_m0D772D62D01CC371F4AB0F6943BBBE0FEAB19643_inline (AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0* __this, Exception_t* ___0_exception, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AsyncUniTaskMethodBuilder_SetResult_mBBA527F0F21E04D65A269C0D02597CE5B2D1E9CB_inline (AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CWaitForInitializationU3Ed__57_MoveNext_mE60DB999701EF66B8A268510213630A555E6CF16 (U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncUniTaskMethodBuilder_SetStateMachine_m466E6779CB9DEC814C0A59E7766D95EC2C3268CF (AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0* __this, RuntimeObject* ___0_stateMachine, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CWaitForInitializationU3Ed__57_SetStateMachine_m444F80048B4D4E1C17B42A9D272CAA397CA4E225 (U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8* __this, RuntimeObject* ___0_stateMachine, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_SetupButtons_mB5DFDF6D4E953C4C3993B91ED4B6C73FAC62E2EA (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_SubscribeToEvents_mAF987BD9F759001DE60A1C4821EBB576CCEC8ADE (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_UpdateButtonStates_m398B77CFAB56DB85E79EAEE8FC28A6C28CE85449 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ButtonClickedEvent_t8EA72E90B3BD1392FB3B3EF167D5121C23569E4C* Button_get_onClick_m701712A7F7F000CC80D517C4510697E15722C35C_inline (Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityAction__ctor_mC53E20D6B66E0D5688CD81B88DBB34F5A58B7131 (UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityEvent_AddListener_m8AA4287C16628486B41DA41CA5E7A856A706D302 (UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977* __this, UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___0_call, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_add_OnAdsInitialized_m288179E5EBAA9E05FB08007AAD46B4FE41471DF5 (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) ;
inline void Action_1__ctor_m44EC72C5D0EE856DCF158315D4B9C72356E4FA66 (Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_add_OnRewardedVideoCompleted_mB7A5AC61C3294D3EB5D7878DD0572DB3DDC994EB (Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_add_OnRewardedVideoFailed_mDAE830B4AF50E138C86B95297A5BAF3DA7BFDCD0 (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_add_OnInterstitialShown_m2255AC85C43D108113D88EA76BAB5A8273DACEDD (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_add_OnInterstitialFailed_mA06ABA80CE403D1E323DFDA8A6908A7842AF33FC (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_add_OnBannerLoaded_m111C57351E6512C53E8B472C7B2E6BF6C4CF2CFD (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_add_OnBannerFailed_m8997FAC1B9B917D80724C3B59BEED690C97BC551 (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_SetButtonsInteractable_m68E6DF3B0C64C3768661F10B14AE76FF49DD5AB7 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, bool ___0_interactable, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AdsManager_IsRewardedVideoAvailable_m462847E3858F15770DD343A4FD0A3A73B6989182 (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Selectable_set_interactable_m8DD581C1AD99B2EFA8B3EE9AF69EDDF26688B492 (Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AdsManager_IsInterstitialAvailable_m63811601029FA9E735FA2165427FCACBECBDD158 (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, const RuntimeMethod* method) ;
inline Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* Component_GetComponentInChildren_TisText_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_m1D5533D50D961602AC2CD364E03388FFE2985259 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponentInChildren_TisRuntimeObject_mE483A27E876DE8E4E6901D6814837F81D7C42F65_gshared)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AdsManager_get_IsBannerVisible_m0520218B5030FBB61969F27EF24D998876B12A07_inline (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9E3155FB84015C823606188F53B47CB44C444991 (String_t* ___0_str0, String_t* ___1_str1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_ShowRewardedVideoForGold_m2D89968C3AA4E95A332161EA49396E68108C633E (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, int32_t ___0_amount, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_ShowRewardedVideoForExtraMoves_m9B40E2C31ABB962772C2EA1C3C2C1DD31DA3C06E (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, int32_t ___0_moves, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_ShowInterstitial_m7DB331C0DAEC266FAE090FC8BA4BE2DBF275AF5B (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_HideBanner_m70733A75C8DAF41770678DB6E907B898D3A159FE (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_ShowBanner_m5335F2493F3290B9D8EB60B34B05B1899AE81E27 (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AdReward_get_Description_m3CEC0BEABF98834C5ED422D60D3C365E15C9120B_inline (AdReward_tD6C5592436EB3F2368B8E7C5D03E95D32E5CB4F2* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Time_get_frameCount_m4A42E558A71301A216BDC49EC402D62F19C79667 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_remove_OnAdsInitialized_m72D81F3653B45ED352A1DC54DA35FC7A9DC42EB7 (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_remove_OnRewardedVideoCompleted_m9FC72B9BC9B8F630BAFFC7B791CB4CADD7158879 (Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_remove_OnRewardedVideoFailed_m30542CB9C97A3A33CC40C7B17F72716C808D1DBD (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_remove_OnInterstitialShown_mB3CBF612EECD1C1BE4A947DD958ED1A143C19DBF (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_remove_OnInterstitialFailed_m352098F77F1AF8D2177A9A4AC5486AD30A4FA570 (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_remove_OnBannerLoaded_m7D91483B4A1153C02B140A7E5B0F2B5F7861447A (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsManager_remove_OnBannerFailed_mB7030FF38CAFF8378BE7477CE6EBCA798AAF3738 (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E (MonoBehaviour_t532A11E697******************************* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass2_0__ctor_m6C755DCC39F68254B6975024504077A3203C0C91 (U3CU3Ec__DisplayClass2_0_tF3AB6702B2FF545D80C5D1F0D565F11E0928A9E7* __this, const RuntimeMethod* method) ;
inline void Predicate_1__ctor_m1DC24E44234562B45FC10DB2C0BD5C0846044DCE (Predicate_1_tF189FD0FC04DE1D6B0F949BD66167FFEF719827F* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Predicate_1_tF189FD0FC04DE1D6B0F949BD66167FFEF719827F*, RuntimeObject*, intptr_t, const RuntimeMethod*))Predicate_1__ctor_m3E007299121A15DF80F4A210FF8C20E5DF688F20_gshared)(__this, ___0_object, ___1_method, method);
}
inline BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* List_1_Find_mB6456BBAE8D17C041213EA3F254DFC24D2E55F52 (List_1_t5905E46128E66911F2CCDF055565E1B0005905BC* __this, Predicate_1_tF189FD0FC04DE1D6B0F949BD66167FFEF719827F* ___0_match, const RuntimeMethod* method)
{
	return ((  BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* (*) (List_1_t5905E46128E66911F2CCDF055565E1B0005905BC*, Predicate_1_tF189FD0FC04DE1D6B0F949BD66167FFEF719827F*, const RuntimeMethod*))List_1_Find_m5E78A210541B0D844FE27B94F509313623BE33D3_gshared)(__this, ___0_match, method);
}
inline void List_1__ctor_m75E092D0D8E1D7464630D385EA1F241D31BD3D56 (List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF*, const RuntimeMethod*))List_1__ctor_m7C3D0A1FD36704AFBBE4FD4E69204B809D3FC90E_gshared)(__this, method);
}
inline Enumerator_t68640B372EDF30A473CF3EEC85B10E1E2C4A7A2A List_1_GetEnumerator_m1500929B6B659F0084B77A78D7F01E0B01C52D7E (List_1_t5905E46128E66911F2CCDF055565E1B0005905BC* __this, const RuntimeMethod* method)
{
	return ((  Enumerator_t68640B372EDF30A473CF3EEC85B10E1E2C4A7A2A (*) (List_1_t5905E46128E66911F2CCDF055565E1B0005905BC*, const RuntimeMethod*))List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared)(__this, method);
}
inline void Enumerator_Dispose_mCCC81CC95BF1E049B322A75E6C850F40A539BF96 (Enumerator_t68640B372EDF30A473CF3EEC85B10E1E2C4A7A2A* __this, const RuntimeMethod* method)
{
	((  void (*) (Enumerator_t68640B372EDF30A473CF3EEC85B10E1E2C4A7A2A*, const RuntimeMethod*))Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared)(__this, method);
}
inline BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* Enumerator_get_Current_mF1934C0DA5E45E0DF121ABC5B1CEBA197F0C3CE5_inline (Enumerator_t68640B372EDF30A473CF3EEC85B10E1E2C4A7A2A* __this, const RuntimeMethod* method)
{
	return ((  BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* (*) (Enumerator_t68640B372EDF30A473CF3EEC85B10E1E2C4A7A2A*, const RuntimeMethod*))Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline)(__this, method);
}
inline void List_1_Add_mDFE0DEA453B1CC0B28177181E50A6475AF25F54D_inline (List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* __this, int32_t ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF*, int32_t, const RuntimeMethod*))List_1_Add_m809450298141D527D3A4FFAF77AE69D9B08CC17F_gshared_inline)(__this, ___0_item, method);
}
inline bool Enumerator_MoveNext_m1D2A92CE9E05B442EDAA2B9BDF36D2ADDD45CE31 (Enumerator_t68640B372EDF30A473CF3EEC85B10E1E2C4A7A2A* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Enumerator_t68640B372EDF30A473CF3EEC85B10E1E2C4A7A2A*, const RuntimeMethod*))Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared)(__this, method);
}
inline void List_1__ctor_m326B607FF8589404C24FFDB935228892676FC826 (List_1_t5905E46128E66911F2CCDF055565E1B0005905BC* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t5905E46128E66911F2CCDF055565E1B0005905BC*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ScriptableObject__ctor_mD037FDB0B487295EA47F79A4DB1BF1846C9087FF (ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00 (Delegate_t* ___0_a, Delegate_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3 (Delegate_t* ___0_source, Delegate_t* ___1_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* BoosterManager_get_Instance_m2D6B03021BDA024FCE34BB36D3D56DD36F0E72CD_inline (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void BoosterManager_set_Instance_m632638C5325431D080248B2C9A7F82FBDB87FE9D_inline (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_DontDestroyOnLoad_m4B70C3AEF886C176543D1295507B6455C9DCAEA7 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_target, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_Initialize_mCD54F4A3AE51948393655861BD7C15B3675A8D91 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* GameProgressManager_get_Instance_m61456B3B17754A572AD056A8BF687A1CECFF767E (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* GameProgressManager_GetShopProgress_m8B6B107D22FB71F7F940241E51BA57E4D74E727E (GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterInventory__ctor_m0929A9BFD69BA97B1C5ADB92168C5C9727A472FA (BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t BoosterInventory_GetBoosterCount_m7BB355A0B384AA98B61E40E6238E54DEF9671019 (BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* __this, int32_t ___0_type, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterInventory_AddBoosters_mF1C3B05D27DFDA738E54878BFD942B6043E0E3E1 (BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* __this, int32_t ___0_type, int32_t ___1_quantity, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_SaveInventory_m77086B8D683CC403F676953B107EDEDB0DBC6733 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t BoosterManager_GetBoosterCount_m57950EB6555110735DA159B254584169E34E3BFF (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, int32_t ___0_type, const RuntimeMethod* method) ;
inline void Action_2_Invoke_m6D901F1610922A7B3DF33BE0B85F1DD5C37F52F7_inline (Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* __this, int32_t ___0_arg1, int32_t ___1_arg2, const RuntimeMethod* method)
{
	((  void (*) (Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A*, int32_t, int32_t, const RuntimeMethod*))Action_2_Invoke_m3BE7DD18C4D59E8D0ECBBA5ED30F084F842415DD_gshared_inline)(__this, ___0_arg1, ___1_arg2, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BoosterInventory_UseBoosters_m8AF1D93774E9D2A6321BAF7AEAE67D75257B327F (BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* __this, int32_t ___0_type, int32_t ___1_quantity, const RuntimeMethod* method) ;
inline void Action_1_Invoke_m67F0BA71EFF8FE17DEF0A02F92F0490D706D9D85_inline (Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* __this, int32_t ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C*, int32_t, const RuntimeMethod*))Action_1_Invoke_mBF7DDBCD230E9D28EDF45D3E65F907DE1AE0CCBC_gshared_inline)(__this, ___0_obj, method);
}
inline void Dictionary_2__ctor_m12E1C0ED46E9464C3016E84E4EC67E27E53D8D1F (Dictionary_2_tCDC59AD672DE07C4D23574E46F1C927253117EC6* __this, RuntimeObject* ___0_dictionary, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_tCDC59AD672DE07C4D23574E46F1C927253117EC6*, RuntimeObject*, const RuntimeMethod*))Dictionary_2__ctor_m7A818AF180482B7431F48C234C3A9C61BA601499_gshared)(__this, ___0_dictionary, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BoosterManager_HasBoosters_m0806AA2C8F5333AAE1E6C6756F24EA45C5D0D9A8 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, int32_t ___0_type, int32_t ___1_quantity, const RuntimeMethod* method) ;
inline Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* Object_FindFirstObjectByType_TisMatch3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA_mE778399552203976FD05E969433A2202FD3ABB2D (const RuntimeMethod* method)
{
	return ((  Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* (*) (const RuntimeMethod*))Object_FindFirstObjectByType_TisRuntimeObject_mC5927319EB5B80095EFBA653D414D6F8AA87DC0A_gshared)(method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_ApplyBombBooster_mEAD702E1FD2FCEED1F805DE44616D4A9BA4A43D7 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* ___0_board, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_ApplyLineRocketBooster_m1F44009C1F3DECE7F5F0207A9CEDEAA05D98C645 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* ___0_board, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_ApplyColorBombBooster_mC21557FD9FDDD13850E59A3FD845DA8F4F74E9E1 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* ___0_board, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_ApplyExtraMovesBooster_m3373C01DC300C1B180E17475378E7B7EB29E2B5C (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* ___0_board, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_ApplyShuffleBooster_mA99ECD605A418A0913E1FE871A7612C30AC2A6F3 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* ___0_board, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BoosterManager_UseBoosters_m8E0271EB22A07F38984DF0F77BA86CEE34D1C7F3 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, int32_t ___0_type, int32_t ___1_quantity, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 Match3Board_ShuffleBoardAsync_mC1F455BC3950BE8787A2D9ECDFA1AF1D69945BA0 (Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UniTaskExtensions_Forget_m8F82202C3DB2020AAE7F874AE049DA711A01DF13 (UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 ___0_task, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameProgressManager_UpdateShopProgress_mFFECAB62E06A9F8EA165BF157E5FDC57F3CFED6B (GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* __this, ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* ___0_shopProgress, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* BoosterConfiguration_GetBoosterData_mDA9004813F88050572A28CA454CE83194F9B3A0E (BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* __this, int32_t ___0_type, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* BoosterConfiguration_GetAvailableBoosterTypes_mBD16729F6FCFC9841BF1B09B770B1E4E44C70EBF (BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass2_0__ctor_mEC8F2DE72AE70D76E1AF19382A98B7215410D476 (U3CU3Ec__DisplayClass2_0_tB74732BEAFB8CFE9A642E0BB4D724BC134BBFCB7* __this, const RuntimeMethod* method) ;
inline void Predicate_1__ctor_mD7BA88E3CE9A84459305722FB9C418319F1C2C47 (Predicate_1_tA8A070E292A9041EB736A2817C2F70211A0F2AC8* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Predicate_1_tA8A070E292A9041EB736A2817C2F70211A0F2AC8*, RuntimeObject*, intptr_t, const RuntimeMethod*))Predicate_1__ctor_m3E007299121A15DF80F4A210FF8C20E5DF688F20_gshared)(__this, ___0_object, ___1_method, method);
}
inline AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* List_1_Find_m2152E27A258E71981F7E1DE6D1F5B6DA7F27AEE4 (List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* __this, Predicate_1_tA8A070E292A9041EB736A2817C2F70211A0F2AC8* ___0_match, const RuntimeMethod* method)
{
	return ((  AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* (*) (List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632*, Predicate_1_tA8A070E292A9041EB736A2817C2F70211A0F2AC8*, const RuntimeMethod*))List_1_Find_m5E78A210541B0D844FE27B94F509313623BE33D3_gshared)(__this, ___0_match, method);
}
inline void List_1__ctor_mCA8DD57EAC70C2B5923DBB9D5A77CEAC22E7068E (List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
inline void List_1_Add_mF10DB1D3CBB0B14215F0E4F8AB4934A1955E5351_inline (List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* __this, String_t* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD*, String_t*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
inline Enumerator_t1DF7601FD8273A05A2858952CBA7B08984FE75A5 List_1_GetEnumerator_m4FA30D8F70A2A9E393B26B2377DF62EC339ACCE4 (List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* __this, const RuntimeMethod* method)
{
	return ((  Enumerator_t1DF7601FD8273A05A2858952CBA7B08984FE75A5 (*) (List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632*, const RuntimeMethod*))List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared)(__this, method);
}
inline void Enumerator_Dispose_m58E9E0886441A7CCD9D59ED3A25F30F04D0BDC1D (Enumerator_t1DF7601FD8273A05A2858952CBA7B08984FE75A5* __this, const RuntimeMethod* method)
{
	((  void (*) (Enumerator_t1DF7601FD8273A05A2858952CBA7B08984FE75A5*, const RuntimeMethod*))Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared)(__this, method);
}
inline AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* Enumerator_get_Current_m77874A5C46D351C6F16F2DFCDFF8559AE3392161_inline (Enumerator_t1DF7601FD8273A05A2858952CBA7B08984FE75A5* __this, const RuntimeMethod* method)
{
	return ((  AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* (*) (Enumerator_t1DF7601FD8273A05A2858952CBA7B08984FE75A5*, const RuntimeMethod*))Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline)(__this, method);
}
inline bool List_1_Contains_m359254483BE42CAD4DCA8FBAFB87473FB4CF00E1 (List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* __this, String_t* ___0_item, const RuntimeMethod* method)
{
	return ((  bool (*) (List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD*, String_t*, const RuntimeMethod*))List_1_Contains_m4C9139C2A6B23E9343D3F87807B32C6E2CFE660D_gshared)(__this, ___0_item, method);
}
inline bool Enumerator_MoveNext_mF426A804C3EAFFBCCB63A791327B2E63F93DBF4C (Enumerator_t1DF7601FD8273A05A2858952CBA7B08984FE75A5* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Enumerator_t1DF7601FD8273A05A2858952CBA7B08984FE75A5*, const RuntimeMethod*))Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared)(__this, method);
}
inline void List_1__ctor_mB3D397E89B7F8DBDF91EDDC6DAE293EE8D58CEA3 (List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1 (String_t* ___0_a, String_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* AvatarManager_get_Instance_m8D181A3A524D62906BF6B92CFDA124AE22C89BC1_inline (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AvatarManager_set_Instance_m712FFBAE63721B0A96B127EC1F0D3604DDFF27A8_inline (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_Initialize_mD2084D10A65E28FC8C2AF639E3F996CBF7A07130 (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AvatarManager_IsAvatarUnlocked_mA65FE89F275311D51596803A07DC0171900ECEA8 (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, String_t* ___0_avatarId, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_UnlockAvatar_m6A246FB330B24F2677582F588044C75575506D6F (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, String_t* ___0_avatarId, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_IsNullOrEmpty_mEA9E3FB005AC28FE02E69FCF95A7B8456192B478 (String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AvatarManager_SelectAvatar_m576A58E8985F67B6822096927E3040CD5C0FA115 (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, String_t* ___0_avatarId, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* AvatarConfiguration_GetAvatarData_m699629AF5952D05D39A5F4DB1D1468C30B4A163D (AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* __this, String_t* ___0_id, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_SaveAvatarSelection_mAAD4FB71AF913195BAE9B44D921A46A46A2B1BDC (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, const RuntimeMethod* method) ;
inline void Action_1_Invoke_m690438AAE38F9762172E3AE0A33D0B42ACD35790_inline (Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* __this, String_t* ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*, String_t*, const RuntimeMethod*))Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline)(__this, ___0_obj, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ShopProgressData_IsItemPurchased_m0F5B4857B28C04AE8DD1A1A0CAE7189B308B4FA8 (ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* __this, String_t* ___0_itemId, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ShopProgressData_PurchaseItem_mEF81EA6D3FF7026D0BF740C62E31F078F7AAEB03 (ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* __this, String_t* ___0_itemId, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t GameProgressManager_GetStars_m7C4126CD1271867862EB4D02D2A60296527EA5EB (GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool GameProgressManager_SpendStars_mE67DD551A60048FAE3E3A9F465F5110F94F0FF5B (GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* __this, int32_t ___0_amount, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* ShopProgressData_GetAvatarAdProgress_mDB78C7835CC3228DF4B14730D260C497DE100695 (ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* __this, String_t* ___0_avatarId, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarAdProgress__ctor_m722A6F9841FDC79226C7D512E65D401B3247CAF8 (AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* __this, String_t* ___0_avatarId, int32_t ___1_adsRequired, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ShopProgressData_AddOrUpdateAvatarAdProgress_mCFBDB7E7E38D0FD38042B03F4975087EDAA8138D (ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* __this, AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* ___0_progress, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AvatarAdProgress_WatchAd_m9325C61AED518E86F74FE3F10F4CDF189060959C (AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float AvatarAdProgress_GetProgress_mE739AD1B09622E66E0178B544C5FA1D68746EF46 (AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* __this, const RuntimeMethod* method) ;
inline void Action_2_Invoke_m4394ACFFC2C8D7A7D849C0781B4212454875DBC2_inline (Action_2_t2399F3C34C43EB392520F878CA121755E120498E* __this, String_t* ___0_arg1, float ___1_arg2, const RuntimeMethod* method)
{
	((  void (*) (Action_2_t2399F3C34C43EB392520F878CA121755E120498E*, String_t*, float, const RuntimeMethod*))Action_2_Invoke_mAFC1A6B82DBD3B488FF63EE80C4D280D6979260F_gshared_inline)(__this, ___0_arg1, ___1_arg2, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarAdProgress_Reset_mC5CD2F83C5B82939D07A30C0B79C7916D038C59A (AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Il2CppChar String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3 (String_t* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline (String_t* __this, const RuntimeMethod* method) ;
inline void IJobExtensions_EarlyJobInit_TisParseLevelJsonJob_t7241F5370B5A9BE7CD2B40CFBB4F05A2675AB552_m0FE87688F8511BE43D8D0B047F85850CFBA85B7E (const RuntimeMethod* method)
{
	((  void (*) (const RuntimeMethod*))IJobExtensions_EarlyJobInit_TisParseLevelJsonJob_t7241F5370B5A9BE7CD2B40CFBB4F05A2675AB552_m0FE87688F8511BE43D8D0B047F85850CFBA85B7E_gshared)(method);
}
inline void IJobExtensions_EarlyJobInit_TisCalculateFallJob_t12218BC3466B56D1F372DABE6E820A6676C90078_m4BDDFF3A5045B56D5489F4CD0340FC922BD5AE5D (const RuntimeMethod* method)
{
	((  void (*) (const RuntimeMethod*))IJobExtensions_EarlyJobInit_TisCalculateFallJob_t12218BC3466B56D1F372DABE6E820A6676C90078_m4BDDFF3A5045B56D5489F4CD0340FC922BD5AE5D_gshared)(method);
}
inline void IJobExtensions_EarlyJobInit_TisShuffleJob_tE9A549CF475CC4946C934CFE41B0006ECC37A2DD_m6BF92EAF3C31DCBEACBF415B651D42788E5928D8 (const RuntimeMethod* method)
{
	((  void (*) (const RuntimeMethod*))IJobExtensions_EarlyJobInit_TisShuffleJob_tE9A549CF475CC4946C934CFE41B0006ECC37A2DD_m6BF92EAF3C31DCBEACBF415B651D42788E5928D8_gshared)(method);
}
inline void IJobExtensions_EarlyJobInit_TisDetectMatchesJob_tE07BFE3943D37234CCA96A23B494634A5479E4FD_m6F6C36EC0F08D3ECF93B7B2E8B0F48925F2A9519 (const RuntimeMethod* method)
{
	((  void (*) (const RuntimeMethod*))IJobExtensions_EarlyJobInit_TisDetectMatchesJob_tE07BFE3943D37234CCA96A23B494634A5479E4FD_m6F6C36EC0F08D3ECF93B7B2E8B0F48925F2A9519_gshared)(method);
}
inline void IJobExtensions_EarlyJobInit_TisFindHintJob_tD765D2B4BD478CD3C28E1853DB36EBA43F92E388_m240E2BAC79A1C619D334A6043738AD05CA6CF3F7 (const RuntimeMethod* method)
{
	((  void (*) (const RuntimeMethod*))IJobExtensions_EarlyJobInit_TisFindHintJob_tD765D2B4BD478CD3C28E1853DB36EBA43F92E388_m240E2BAC79A1C619D334A6043738AD05CA6CF3F7_gshared)(method);
}
inline void IJobExtensions_EarlyJobInit_TisShuffleJob_tB72E45C0F54BBAF53B1DC8C152B42690022FC584_mD3A4478DA29AAB39D98EC98F122581CB2538B0CA (const RuntimeMethod* method)
{
	((  void (*) (const RuntimeMethod*))IJobExtensions_EarlyJobInit_TisShuffleJob_tB72E45C0F54BBAF53B1DC8C152B42690022FC584_mD3A4478DA29AAB39D98EC98F122581CB2538B0CA_gshared)(method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EarlyInitHelpers_JobReflectionDataCreationFailed_mD6AB08D5BB411CCE38A87793C3C7062EC91FD1EC (Exception_t* ___0_ex, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void __JobReflectionRegistrationOutput__5800224013931567721_CreateJobReflectionData_mDF08400DD08C4241E3217A3EAB5C3A514DC1AAB4 (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Awaiter__ctor_m4154A3A6D62BB1657D17A8106633CD9E1CE51F04_inline (Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* __this, UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* ___0_task, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t UniTask_get_Status_mA15B0F13DE3CE36730357CF50F65AE99ADF564DA_inline (UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool UniTaskStatusExtensions_IsCompleted_mF43C41C9CEB640E381D1F7A8B40142843AED87AE_inline (int32_t ___0_status, const RuntimeMethod* method) ;
inline void AsyncUniTask_1_SetStateMachine_mC01AE78C304544C088858602E092DF11755B2926 (U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8* ___0_stateMachine, RuntimeObject** ___1_runnerPromiseFieldRef, const RuntimeMethod* method)
{
	((  void (*) (U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8*, RuntimeObject**, const RuntimeMethod*))AsyncUniTask_1_SetStateMachine_mC01AE78C304544C088858602E092DF11755B2926_gshared)(___0_stateMachine, ___1_runnerPromiseFieldRef, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Awaiter_UnsafeOnCompleted_m6C4775F8F4F2BEFB8118EBBA1EA621440CE84D62_inline (Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_continuation, const RuntimeMethod* method) ;
inline void List_1_AddWithResize_mAD159B09F28BD7914562E219CA52E2D4BDCF5530 (List_1_tDA4D291C60B1EFA9EA50BBA3367C657CC9410576* __this, int32_t ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tDA4D291C60B1EFA9EA50BBA3367C657CC9410576*, int32_t, const RuntimeMethod*))List_1_AddWithResize_mAD159B09F28BD7914562E219CA52E2D4BDCF5530_gshared)(__this, ___0_item, method);
}
inline void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4 (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D*, RuntimeObject*, const RuntimeMethod*))List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared)(__this, ___0_item, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CInitializeAdsU3Ed__56_MoveNext_m3161C23160159B721D56563AEBF3D1FECF8DEEA9 (U3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t8FD8058A6DC8585DE75B4D286B0D8AA7D726CA99_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t938F308F20F338CACAEAD2DCC4632BA0F0D362A4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tCAF76FCA3F9A43469074BE52052243FAC78CFA52_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsManager_OnImpressionDataReady_m56BD3858A23D77361413172D08056EA54A11B12C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsManager_OnInitFailed_m3EF196F44709CC8AFF16BBADCE9D7F951E7F1890_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsManager_OnInitSuccess_mF2EC60B9E8CA37C0C8BF77862DA4158715C0513E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AsyncVoidMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39_mA539A7378200E05B9219A20B2912925714355791_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&LevelPlay_t0AE0FE23B6C6C7B9FFE58376B5C3B8F85154BD71_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3BD88F02C04BD5CD20FACAC36009CBA043D8259B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral51AF8BD092EC6256EE5A36095D9DB64C9854749C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralAEE4C086C75EDA8DB6489E26ED81D481E4038D52);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCE22D8A1FC18A844A316CA35D8B8DAD9297958C5);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* V_1 = NULL;
	Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 V_2;
	memset((&V_2), 0, sizeof(V_2));
	UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 V_3;
	memset((&V_3), 0, sizeof(V_3));
	Exception_t* V_4 = NULL;
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
	}
	try
	{
		{
			int32_t L_2 = V_0;
			if (!L_2)
			{
				goto IL_00e1_1;
			}
		}
		{
			AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_3 = V_1;
			NullCheck(L_3);
			bool L_4;
			L_4 = AdsManager_get_IsInitialized_mA114A221EEDEF44409F6FABF3070C12BCCF6D748_inline(L_3, NULL);
			if (!L_4)
			{
				goto IL_002c_1;
			}
		}
		{
			AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_5 = V_1;
			NullCheck(L_5);
			AdsManager_LogDebug_m1FD00D0DCF2E0EDE397CE74223820756C11157BE(L_5, _stringLiteral51AF8BD092EC6256EE5A36095D9DB64C9854749C, NULL);
			goto IL_011f;
		}

IL_002c_1:
		{
			AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_6 = V_1;
			NullCheck(L_6);
			AdConfig_t80D1C0D2BD226B6F32829058D850E59BBF3A4190* L_7 = L_6->___adConfig;
			il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
			bool L_8;
			L_8 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_7, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
			if (!L_8)
			{
				goto IL_004a_1;
			}
		}
		{
			AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_9 = V_1;
			NullCheck(L_9);
			AdsManager_LogError_mC4C9BE08E43ED6A4EEFC9EA7D2A18414956F0520(L_9, _stringLiteralCE22D8A1FC18A844A316CA35D8B8DAD9297958C5, NULL);
			goto IL_011f;
		}

IL_004a_1:
		{
			AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_10 = V_1;
			NullCheck(L_10);
			AdsManager_LogDebug_m1FD00D0DCF2E0EDE397CE74223820756C11157BE(L_10, _stringLiteralAEE4C086C75EDA8DB6489E26ED81D481E4038D52, NULL);
			il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
			Debug_Log_m87A9A3C761FF5C43ED8A53B16190A53D08F818BB(_stringLiteral3BD88F02C04BD5CD20FACAC36009CBA043D8259B, NULL);
			il2cpp_codegen_runtime_class_init_inline(LevelPlay_t0AE0FE23B6C6C7B9FFE58376B5C3B8F85154BD71_il2cpp_TypeInfo_var);
			LevelPlay_ValidateIntegration_m099CA631FA230B3C9EAE2F9D005808746D51916E(NULL);
			AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_11 = V_1;
			Action_1_tCAF76FCA3F9A43469074BE52052243FAC78CFA52* L_12 = (Action_1_tCAF76FCA3F9A43469074BE52052243FAC78CFA52*)il2cpp_codegen_object_new(Action_1_tCAF76FCA3F9A43469074BE52052243FAC78CFA52_il2cpp_TypeInfo_var);
			Action_1__ctor_mC9F6546DA3E3FC6BADDC1A846976E6D0DCA7E643(L_12, L_11, (intptr_t)((void*)AdsManager_OnInitSuccess_mF2EC60B9E8CA37C0C8BF77862DA4158715C0513E_RuntimeMethod_var), NULL);
			LevelPlay_add_OnInitSuccess_mA20A6967677EFA968A1B39FCE3BC4CDB0DE644D6(L_12, NULL);
			AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_13 = V_1;
			Action_1_t938F308F20F338CACAEAD2DCC4632BA0F0D362A4* L_14 = (Action_1_t938F308F20F338CACAEAD2DCC4632BA0F0D362A4*)il2cpp_codegen_object_new(Action_1_t938F308F20F338CACAEAD2DCC4632BA0F0D362A4_il2cpp_TypeInfo_var);
			Action_1__ctor_m0116316695141163F551BA515A87470E9DF5F401(L_14, L_13, (intptr_t)((void*)AdsManager_OnInitFailed_m3EF196F44709CC8AFF16BBADCE9D7F951E7F1890_RuntimeMethod_var), NULL);
			LevelPlay_add_OnInitFailed_mFF1B9C5D58DE14D1C3B907F690CAECCE9BE5DA82(L_14, NULL);
			AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_15 = V_1;
			Action_1_t8FD8058A6DC8585DE75B4D286B0D8AA7D726CA99* L_16 = (Action_1_t8FD8058A6DC8585DE75B4D286B0D8AA7D726CA99*)il2cpp_codegen_object_new(Action_1_t8FD8058A6DC8585DE75B4D286B0D8AA7D726CA99_il2cpp_TypeInfo_var);
			Action_1__ctor_m6520DA264AD20E163E4858213E399D165E757FD8(L_16, L_15, (intptr_t)((void*)AdsManager_OnImpressionDataReady_m56BD3858A23D77361413172D08056EA54A11B12C_RuntimeMethod_var), NULL);
			LevelPlay_add_OnImpressionDataReady_m420592125940BA583FFF62A8FB34620854D79EA8(L_16, NULL);
			AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_17 = V_1;
			NullCheck(L_17);
			AdConfig_t80D1C0D2BD226B6F32829058D850E59BBF3A4190* L_18 = L_17->___adConfig;
			NullCheck(L_18);
			String_t* L_19;
			L_19 = AdConfig_get_AppKey_m9359E4F90FC1343439AB202C5EEA51B0454D9F89_inline(L_18, NULL);
			LevelPlay_Init_m34F777F7B0639379E9C83D66E04EDB2FDE810B43(L_19, (String_t*)NULL, (LevelPlayAdFormatU5BU5D_tFA25937CD58D80A76D4218FA619EB7762C3B9D0A*)NULL, NULL);
			AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_20 = V_1;
			NullCheck(L_20);
			UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 L_21;
			L_21 = AdsManager_WaitForInitialization_m18F05B14C709CBE75C5CE35C14FEBE0B5138BFA7(L_20, NULL);
			V_3 = L_21;
			il2cpp_codegen_runtime_class_init_inline(UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270_il2cpp_TypeInfo_var);
			Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 L_22;
			L_22 = UniTask_GetAwaiter_mF05A09B81913BECFD58FC67A16C0251FFCCAC939_inline((&V_3), NULL);
			V_2 = L_22;
			bool L_23;
			L_23 = Awaiter_get_IsCompleted_m2D01E3AB8A7C5AA8AA1E1EF58D92A6A095C00B9A_inline((&V_2), NULL);
			if (L_23)
			{
				goto IL_00fd_1;
			}
		}
		{
			int32_t L_24 = 0;
			V_0 = L_24;
			__this->___U3CU3E1__state = L_24;
			Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 L_25 = V_2;
			__this->___U3CU3Eu__1 = L_25;
			Il2CppCodeGenWriteBarrier((void**)&((&(((&__this->___U3CU3Eu__1))->___task))->___source), (void*)NULL);
			AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D* L_26 = (AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D*)(&__this->___U3CU3Et__builder);
			AsyncVoidMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39_mA539A7378200E05B9219A20B2912925714355791(L_26, (&V_2), __this, AsyncVoidMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39_mA539A7378200E05B9219A20B2912925714355791_RuntimeMethod_var);
			goto IL_0132;
		}

IL_00e1_1:
		{
			Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 L_27 = __this->___U3CU3Eu__1;
			V_2 = L_27;
			Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* L_28 = (Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956*)(&__this->___U3CU3Eu__1);
			il2cpp_codegen_initobj(L_28, sizeof(Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956));
			int32_t L_29 = (-1);
			V_0 = L_29;
			__this->___U3CU3E1__state = L_29;
		}

IL_00fd_1:
		{
			Awaiter_GetResult_mC439993563D3BD49CEC67AAF6AFB3AEF72E916BD_inline((&V_2), NULL);
			goto IL_011f;
		}
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_0106;
		}
		throw e;
	}

CATCH_0106:
	{
		Exception_t* L_30 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
		V_4 = L_30;
		__this->___U3CU3E1__state = ((int32_t)-2);
		AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D* L_31 = (AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D*)(&__this->___U3CU3Et__builder);
		Exception_t* L_32 = V_4;
		AsyncVoidMethodBuilder_SetException_mD9A6F5D1A99A62AC9DF322901BFDE05193CB177B(L_31, L_32, NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_0132;
	}

IL_011f:
	{
		__this->___U3CU3E1__state = ((int32_t)-2);
		AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D* L_33 = (AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D*)(&__this->___U3CU3Et__builder);
		AsyncVoidMethodBuilder_SetResult_m008490FDF057D5F5D871F537C7A58BE36027F3DC(L_33, NULL);
	}

IL_0132:
	{
		return;
	}
}
IL2CPP_EXTERN_C  void U3CInitializeAdsU3Ed__56_MoveNext_m3161C23160159B721D56563AEBF3D1FECF8DEEA9_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	U3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<U3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39*>(__this + _offset);
	U3CInitializeAdsU3Ed__56_MoveNext_m3161C23160159B721D56563AEBF3D1FECF8DEEA9(_thisAdjusted, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CInitializeAdsU3Ed__56_SetStateMachine_mBBAB47B4727508F6FAACB2FE9CA89F110FD7F1A2 (U3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39* __this, RuntimeObject* ___0_stateMachine, const RuntimeMethod* method) 
{
	{
		AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D* L_0 = (AsyncVoidMethodBuilder_t253E37B63E7E7B504878AE6563347C147F98EF2D*)(&__this->___U3CU3Et__builder);
		RuntimeObject* L_1 = ___0_stateMachine;
		AsyncVoidMethodBuilder_SetStateMachine_m48640FB81C34D4C2B5A5BBA7F5AE17DC50BF1A25(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C  void U3CInitializeAdsU3Ed__56_SetStateMachine_mBBAB47B4727508F6FAACB2FE9CA89F110FD7F1A2_AdjustorThunk (RuntimeObject* __this, RuntimeObject* ___0_stateMachine, const RuntimeMethod* method)
{
	U3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<U3CInitializeAdsU3Ed__56_tFE6B528D06D77DBB92448970E5904BEBCCBF6E39*>(__this + _offset);
	U3CInitializeAdsU3Ed__56_SetStateMachine_mBBAB47B4727508F6FAACB2FE9CA89F110FD7F1A2(_thisAdjusted, ___0_stateMachine, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CWaitForInitializationU3Ed__57_MoveNext_mE60DB999701EF66B8A268510213630A555E6CF16 (U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AsyncUniTaskMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8_mF89DAED1592C898552A8C38223683928ED1CDB6E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral00E2A4B1B6CFDE3C09488F7CD792634F6B919861);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* V_1 = NULL;
	Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 V_2;
	memset((&V_2), 0, sizeof(V_2));
	CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED V_3;
	memset((&V_3), 0, sizeof(V_3));
	UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 V_4;
	memset((&V_4), 0, sizeof(V_4));
	Exception_t* V_5 = NULL;
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
	}
	try
	{
		{
			int32_t L_2 = V_0;
			if (!L_2)
			{
				goto IL_0072_1;
			}
		}
		{
			__this->___U3CtimeoutU3E5__2 = (10.0f);
			__this->___U3CelapsedU3E5__3 = (0.0f);
			goto IL_00a7_1;
		}

IL_0029_1:
		{
			il2cpp_codegen_initobj((&V_3), sizeof(CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED));
			CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED L_3 = V_3;
			il2cpp_codegen_runtime_class_init_inline(UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270_il2cpp_TypeInfo_var);
			UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 L_4;
			L_4 = UniTask_Delay_m0F6B9A8CE95AB6C59ACCCF49E96369AB9FAD4960(((int32_t)100), (bool)0, 8, L_3, (bool)0, NULL);
			V_4 = L_4;
			Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 L_5;
			L_5 = UniTask_GetAwaiter_mF05A09B81913BECFD58FC67A16C0251FFCCAC939_inline((&V_4), NULL);
			V_2 = L_5;
			bool L_6;
			L_6 = Awaiter_get_IsCompleted_m2D01E3AB8A7C5AA8AA1E1EF58D92A6A095C00B9A_inline((&V_2), NULL);
			if (L_6)
			{
				goto IL_008e_1;
			}
		}
		{
			int32_t L_7 = 0;
			V_0 = L_7;
			__this->___U3CU3E1__state = L_7;
			Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 L_8 = V_2;
			__this->___U3CU3Eu__1 = L_8;
			Il2CppCodeGenWriteBarrier((void**)&((&(((&__this->___U3CU3Eu__1))->___task))->___source), (void*)NULL);
			AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0* L_9 = (AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0*)(&__this->___U3CU3Et__builder);
			AsyncUniTaskMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8_mF89DAED1592C898552A8C38223683928ED1CDB6E_inline(L_9, (&V_2), __this, AsyncUniTaskMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8_mF89DAED1592C898552A8C38223683928ED1CDB6E_RuntimeMethod_var);
			goto IL_0101;
		}

IL_0072_1:
		{
			Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 L_10 = __this->___U3CU3Eu__1;
			V_2 = L_10;
			Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* L_11 = (Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956*)(&__this->___U3CU3Eu__1);
			il2cpp_codegen_initobj(L_11, sizeof(Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956));
			int32_t L_12 = (-1);
			V_0 = L_12;
			__this->___U3CU3E1__state = L_12;
		}

IL_008e_1:
		{
			Awaiter_GetResult_mC439993563D3BD49CEC67AAF6AFB3AEF72E916BD_inline((&V_2), NULL);
			float L_13 = __this->___U3CelapsedU3E5__3;
			__this->___U3CelapsedU3E5__3 = ((float)il2cpp_codegen_add(L_13, (0.100000001f)));
		}

IL_00a7_1:
		{
			AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_14 = V_1;
			NullCheck(L_14);
			bool L_15;
			L_15 = AdsManager_get_IsInitialized_mA114A221EEDEF44409F6FABF3070C12BCCF6D748_inline(L_14, NULL);
			if (L_15)
			{
				goto IL_00c0_1;
			}
		}
		{
			float L_16 = __this->___U3CelapsedU3E5__3;
			float L_17 = __this->___U3CtimeoutU3E5__2;
			if ((((float)L_16) < ((float)L_17)))
			{
				goto IL_0029_1;
			}
		}

IL_00c0_1:
		{
			AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_18 = V_1;
			NullCheck(L_18);
			bool L_19;
			L_19 = AdsManager_get_IsInitialized_mA114A221EEDEF44409F6FABF3070C12BCCF6D748_inline(L_18, NULL);
			if (L_19)
			{
				goto IL_00d3_1;
			}
		}
		{
			AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_20 = V_1;
			NullCheck(L_20);
			AdsManager_LogError_mC4C9BE08E43ED6A4EEFC9EA7D2A18414956F0520(L_20, _stringLiteral00E2A4B1B6CFDE3C09488F7CD792634F6B919861, NULL);
		}

IL_00d3_1:
		{
			goto IL_00ee;
		}
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_00d5;
		}
		throw e;
	}

CATCH_00d5:
	{
		Exception_t* L_21 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
		V_5 = L_21;
		__this->___U3CU3E1__state = ((int32_t)-2);
		AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0* L_22 = (AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0*)(&__this->___U3CU3Et__builder);
		Exception_t* L_23 = V_5;
		AsyncUniTaskMethodBuilder_SetException_m0D772D62D01CC371F4AB0F6943BBBE0FEAB19643_inline(L_22, L_23, NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_0101;
	}

IL_00ee:
	{
		__this->___U3CU3E1__state = ((int32_t)-2);
		AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0* L_24 = (AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0*)(&__this->___U3CU3Et__builder);
		AsyncUniTaskMethodBuilder_SetResult_mBBA527F0F21E04D65A269C0D02597CE5B2D1E9CB_inline(L_24, NULL);
	}

IL_0101:
	{
		return;
	}
}
IL2CPP_EXTERN_C  void U3CWaitForInitializationU3Ed__57_MoveNext_mE60DB999701EF66B8A268510213630A555E6CF16_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8*>(__this + _offset);
	U3CWaitForInitializationU3Ed__57_MoveNext_mE60DB999701EF66B8A268510213630A555E6CF16(_thisAdjusted, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CWaitForInitializationU3Ed__57_SetStateMachine_m444F80048B4D4E1C17B42A9D272CAA397CA4E225 (U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8* __this, RuntimeObject* ___0_stateMachine, const RuntimeMethod* method) 
{
	{
		AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0* L_0 = (AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0*)(&__this->___U3CU3Et__builder);
		RuntimeObject* L_1 = ___0_stateMachine;
		AsyncUniTaskMethodBuilder_SetStateMachine_m466E6779CB9DEC814C0A59E7766D95EC2C3268CF(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C  void U3CWaitForInitializationU3Ed__57_SetStateMachine_m444F80048B4D4E1C17B42A9D272CAA397CA4E225_AdjustorThunk (RuntimeObject* __this, RuntimeObject* ___0_stateMachine, const RuntimeMethod* method)
{
	U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8*>(__this + _offset);
	U3CWaitForInitializationU3Ed__57_SetStateMachine_m444F80048B4D4E1C17B42A9D272CAA397CA4E225(_thisAdjusted, ___0_stateMachine, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_Start_mA42B04FC8C0D21D47A58B24515CFFC55FA45051A (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	{
		AdsSetupExample_SetupButtons_mB5DFDF6D4E953C4C3993B91ED4B6C73FAC62E2EA(__this, NULL);
		AdsSetupExample_SubscribeToEvents_mAF987BD9F759001DE60A1C4821EBB576CCEC8ADE(__this, NULL);
		AdsSetupExample_UpdateButtonStates_m398B77CFAB56DB85E79EAEE8FC28A6C28CE85449(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_SetupButtons_mB5DFDF6D4E953C4C3993B91ED4B6C73FAC62E2EA (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_ShowInterstitial_mF695E724D4069EF91F8E4EDC7B880B1D52332CB9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_ShowRewardedVideoForGold_m0FBD6DAD447E91B293CD92A871C6662054F90309_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_ToggleBanner_m512FB8619E7F5FFBB47150D40557EA3E05E03A47_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_0 = __this->___rewardedVideoButton;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_002a;
		}
	}
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_2 = __this->___rewardedVideoButton;
		NullCheck(L_2);
		ButtonClickedEvent_t8EA72E90B3BD1392FB3B3EF167D5121C23569E4C* L_3;
		L_3 = Button_get_onClick_m701712A7F7F000CC80D517C4510697E15722C35C_inline(L_2, NULL);
		UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* L_4 = (UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7*)il2cpp_codegen_object_new(UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7_il2cpp_TypeInfo_var);
		UnityAction__ctor_mC53E20D6B66E0D5688CD81B88DBB34F5A58B7131(L_4, __this, (intptr_t)((void*)AdsSetupExample_ShowRewardedVideoForGold_m0FBD6DAD447E91B293CD92A871C6662054F90309_RuntimeMethod_var), NULL);
		NullCheck(L_3);
		UnityEvent_AddListener_m8AA4287C16628486B41DA41CA5E7A856A706D302(L_3, L_4, NULL);
	}

IL_002a:
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_5 = __this->___interstitialButton;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_6;
		L_6 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_5, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_6)
		{
			goto IL_0054;
		}
	}
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_7 = __this->___interstitialButton;
		NullCheck(L_7);
		ButtonClickedEvent_t8EA72E90B3BD1392FB3B3EF167D5121C23569E4C* L_8;
		L_8 = Button_get_onClick_m701712A7F7F000CC80D517C4510697E15722C35C_inline(L_7, NULL);
		UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* L_9 = (UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7*)il2cpp_codegen_object_new(UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7_il2cpp_TypeInfo_var);
		UnityAction__ctor_mC53E20D6B66E0D5688CD81B88DBB34F5A58B7131(L_9, __this, (intptr_t)((void*)AdsSetupExample_ShowInterstitial_mF695E724D4069EF91F8E4EDC7B880B1D52332CB9_RuntimeMethod_var), NULL);
		NullCheck(L_8);
		UnityEvent_AddListener_m8AA4287C16628486B41DA41CA5E7A856A706D302(L_8, L_9, NULL);
	}

IL_0054:
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_10 = __this->___bannerToggleButton;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_11;
		L_11 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_10, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_11)
		{
			goto IL_007e;
		}
	}
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_12 = __this->___bannerToggleButton;
		NullCheck(L_12);
		ButtonClickedEvent_t8EA72E90B3BD1392FB3B3EF167D5121C23569E4C* L_13;
		L_13 = Button_get_onClick_m701712A7F7F000CC80D517C4510697E15722C35C_inline(L_12, NULL);
		UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* L_14 = (UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7*)il2cpp_codegen_object_new(UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7_il2cpp_TypeInfo_var);
		UnityAction__ctor_mC53E20D6B66E0D5688CD81B88DBB34F5A58B7131(L_14, __this, (intptr_t)((void*)AdsSetupExample_ToggleBanner_m512FB8619E7F5FFBB47150D40557EA3E05E03A47_RuntimeMethod_var), NULL);
		NullCheck(L_13);
		UnityEvent_AddListener_m8AA4287C16628486B41DA41CA5E7A856A706D302(L_13, L_14, NULL);
	}

IL_007e:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_SubscribeToEvents_mAF987BD9F759001DE60A1C4821EBB576CCEC8ADE (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnAdsInitialized_m7AA1EF89BFBFD26DA95BF9BA248CA77F293A54A9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnBannerFailed_mA43362B2EB1C62ACEE1F68753531C320CDE50129_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnBannerLoaded_m727617E7481F01F14FAE4FF134634359BE6E40E0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnInterstitialFailed_mE1C0EFA693C079D46F51C65FE24E355DC2BB050D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnInterstitialShown_m48B9BCF52271A36178E34591FA12EAA4E53B2C80_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnRewardedVideoCompleted_m5F9D46DA8CB4D9C6543FA651EBACEE8F601087CD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnRewardedVideoFailed_mE234436E8F799D695445A9B99D9AF27DF9ACE3E4_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)AdsSetupExample_OnAdsInitialized_m7AA1EF89BFBFD26DA95BF9BA248CA77F293A54A9_RuntimeMethod_var), NULL);
		AdsManager_add_OnAdsInitialized_m288179E5EBAA9E05FB08007AAD46B4FE41471DF5(L_0, NULL);
		Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247* L_1 = (Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247*)il2cpp_codegen_object_new(Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247_il2cpp_TypeInfo_var);
		Action_1__ctor_m44EC72C5D0EE856DCF158315D4B9C72356E4FA66(L_1, __this, (intptr_t)((void*)AdsSetupExample_OnRewardedVideoCompleted_m5F9D46DA8CB4D9C6543FA651EBACEE8F601087CD_RuntimeMethod_var), NULL);
		AdsManager_add_OnRewardedVideoCompleted_mB7A5AC61C3294D3EB5D7878DD0572DB3DDC994EB(L_1, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_2, __this, (intptr_t)((void*)AdsSetupExample_OnRewardedVideoFailed_mE234436E8F799D695445A9B99D9AF27DF9ACE3E4_RuntimeMethod_var), NULL);
		AdsManager_add_OnRewardedVideoFailed_mDAE830B4AF50E138C86B95297A5BAF3DA7BFDCD0(L_2, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_3, __this, (intptr_t)((void*)AdsSetupExample_OnInterstitialShown_m48B9BCF52271A36178E34591FA12EAA4E53B2C80_RuntimeMethod_var), NULL);
		AdsManager_add_OnInterstitialShown_m2255AC85C43D108113D88EA76BAB5A8273DACEDD(L_3, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_4, __this, (intptr_t)((void*)AdsSetupExample_OnInterstitialFailed_mE1C0EFA693C079D46F51C65FE24E355DC2BB050D_RuntimeMethod_var), NULL);
		AdsManager_add_OnInterstitialFailed_mA06ABA80CE403D1E323DFDA8A6908A7842AF33FC(L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_5 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_5, __this, (intptr_t)((void*)AdsSetupExample_OnBannerLoaded_m727617E7481F01F14FAE4FF134634359BE6E40E0_RuntimeMethod_var), NULL);
		AdsManager_add_OnBannerLoaded_m111C57351E6512C53E8B472C7B2E6BF6C4CF2CFD(L_5, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_6, __this, (intptr_t)((void*)AdsSetupExample_OnBannerFailed_mA43362B2EB1C62ACEE1F68753531C320CDE50129_RuntimeMethod_var), NULL);
		AdsManager_add_OnBannerFailed_m8997FAC1B9B917D80724C3B59BEED690C97BC551(L_6, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_UpdateButtonStates_m398B77CFAB56DB85E79EAEE8FC28A6C28CE85449 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponentInChildren_TisText_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_m1D5533D50D961602AC2CD364E03388FFE2985259_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral331105482F42E2071F4A0BC3D0340655ED5E55DB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral422990A45DA7F4F92B0DE9D8A17CFC1C1C150748);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA39B4D76A4506CE373573F59E5E8149786EC806B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCAEEE88D9F057EF1E53C4DD5E9F7A3C5D5D7900A);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCE2EA044C74BEE0039FDA1B71187A6900CC6BC21);
		s_Il2CppMethodInitialized = true;
	}
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* V_0 = NULL;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* G_B12_0 = NULL;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* G_B11_0 = NULL;
	String_t* G_B13_0 = NULL;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* G_B13_1 = NULL;
	{
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_0;
		L_0 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0020;
		}
	}
	{
		AdsSetupExample_SetButtonsInteractable_m68E6DF3B0C64C3768661F10B14AE76FF49DD5AB7(__this, (bool)0, NULL);
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteralCE2EA044C74BEE0039FDA1B71187A6900CC6BC21, NULL);
		return;
	}

IL_0020:
	{
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_2;
		L_2 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		NullCheck(L_2);
		bool L_3;
		L_3 = AdsManager_get_IsInitialized_mA114A221EEDEF44409F6FABF3070C12BCCF6D748_inline(L_2, NULL);
		if (L_3)
		{
			goto IL_003f;
		}
	}
	{
		AdsSetupExample_SetButtonsInteractable_m68E6DF3B0C64C3768661F10B14AE76FF49DD5AB7(__this, (bool)0, NULL);
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral331105482F42E2071F4A0BC3D0340655ED5E55DB, NULL);
		return;
	}

IL_003f:
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_4 = __this->___rewardedVideoButton;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_5;
		L_5 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_4, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_5)
		{
			goto IL_0062;
		}
	}
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_6 = __this->___rewardedVideoButton;
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_7;
		L_7 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		NullCheck(L_7);
		bool L_8;
		L_8 = AdsManager_IsRewardedVideoAvailable_m462847E3858F15770DD343A4FD0A3A73B6989182(L_7, NULL);
		NullCheck(L_6);
		Selectable_set_interactable_m8DD581C1AD99B2EFA8B3EE9AF69EDDF26688B492(L_6, L_8, NULL);
	}

IL_0062:
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_9 = __this->___interstitialButton;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_10;
		L_10 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_9, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_10)
		{
			goto IL_0085;
		}
	}
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_11 = __this->___interstitialButton;
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_12;
		L_12 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		NullCheck(L_12);
		bool L_13;
		L_13 = AdsManager_IsInterstitialAvailable_m63811601029FA9E735FA2165427FCACBECBDD158(L_12, NULL);
		NullCheck(L_11);
		Selectable_set_interactable_m8DD581C1AD99B2EFA8B3EE9AF69EDDF26688B492(L_11, L_13, NULL);
	}

IL_0085:
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_14 = __this->___bannerToggleButton;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_15;
		L_15 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_14, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_15)
		{
			goto IL_00d2;
		}
	}
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_16 = __this->___bannerToggleButton;
		NullCheck(L_16);
		Selectable_set_interactable_m8DD581C1AD99B2EFA8B3EE9AF69EDDF26688B492(L_16, (bool)1, NULL);
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_17 = __this->___bannerToggleButton;
		NullCheck(L_17);
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_18;
		L_18 = Component_GetComponentInChildren_TisText_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_m1D5533D50D961602AC2CD364E03388FFE2985259(L_17, Component_GetComponentInChildren_TisText_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_m1D5533D50D961602AC2CD364E03388FFE2985259_RuntimeMethod_var);
		V_0 = L_18;
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_19 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_20;
		L_20 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_19, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_20)
		{
			goto IL_00d2;
		}
	}
	{
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_21 = V_0;
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_22;
		L_22 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		NullCheck(L_22);
		bool L_23;
		L_23 = AdsManager_get_IsBannerVisible_m0520218B5030FBB61969F27EF24D998876B12A07_inline(L_22, NULL);
		if (L_23)
		{
			G_B12_0 = L_21;
			goto IL_00c8;
		}
		G_B11_0 = L_21;
	}
	{
		G_B13_0 = _stringLiteralA39B4D76A4506CE373573F59E5E8149786EC806B;
		G_B13_1 = G_B11_0;
		goto IL_00cd;
	}

IL_00c8:
	{
		G_B13_0 = _stringLiteral422990A45DA7F4F92B0DE9D8A17CFC1C1C150748;
		G_B13_1 = G_B12_0;
	}

IL_00cd:
	{
		NullCheck(G_B13_1);
		VirtualActionInvoker1< String_t* >::Invoke(75, G_B13_1, G_B13_0);
	}

IL_00d2:
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteralCAEEE88D9F057EF1E53C4DD5E9F7A3C5D5D7900A, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_SetButtonsInteractable_m68E6DF3B0C64C3768661F10B14AE76FF49DD5AB7 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, bool ___0_interactable, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_0 = __this->___rewardedVideoButton;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_001a;
		}
	}
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_2 = __this->___rewardedVideoButton;
		bool L_3 = ___0_interactable;
		NullCheck(L_2);
		Selectable_set_interactable_m8DD581C1AD99B2EFA8B3EE9AF69EDDF26688B492(L_2, L_3, NULL);
	}

IL_001a:
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_4 = __this->___interstitialButton;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_5;
		L_5 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_4, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_5)
		{
			goto IL_0034;
		}
	}
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_6 = __this->___interstitialButton;
		bool L_7 = ___0_interactable;
		NullCheck(L_6);
		Selectable_set_interactable_m8DD581C1AD99B2EFA8B3EE9AF69EDDF26688B492(L_6, L_7, NULL);
	}

IL_0034:
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_8 = __this->___bannerToggleButton;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_9;
		L_9 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_8, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_9)
		{
			goto IL_004e;
		}
	}
	{
		Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* L_10 = __this->___bannerToggleButton;
		bool L_11 = ___0_interactable;
		NullCheck(L_10);
		Selectable_set_interactable_m8DD581C1AD99B2EFA8B3EE9AF69EDDF26688B492(L_10, L_11, NULL);
	}

IL_004e:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, String_t* ___0_message, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral46091384BBC973B3E902010807A1D1448CFD51AC);
		s_Il2CppMethodInitialized = true;
	}
	{
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_0 = __this->___statusText;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0024;
		}
	}
	{
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_2 = __this->___statusText;
		String_t* L_3 = ___0_message;
		String_t* L_4;
		L_4 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(_stringLiteral46091384BBC973B3E902010807A1D1448CFD51AC, L_3, NULL);
		NullCheck(L_2);
		VirtualActionInvoker1< String_t* >::Invoke(75, L_2, L_4);
	}

IL_0024:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_ShowRewardedVideoForGold_m0FBD6DAD447E91B293CD92A871C6662054F90309 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1E1A504B4707650AF3C34B98E6DFC9E30D2A8564);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3B7A2CCA88B3675CD64BF54B46129FCE1D963037);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6F448909A101B8782582360C107AE0801A1A01F5);
		s_Il2CppMethodInitialized = true;
	}
	{
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_0;
		L_0 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0019;
		}
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral6F448909A101B8782582360C107AE0801A1A01F5, NULL);
		return;
	}

IL_0019:
	{
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_2;
		L_2 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		NullCheck(L_2);
		bool L_3;
		L_3 = AdsManager_IsRewardedVideoAvailable_m462847E3858F15770DD343A4FD0A3A73B6989182(L_2, NULL);
		if (L_3)
		{
			goto IL_0031;
		}
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral1E1A504B4707650AF3C34B98E6DFC9E30D2A8564, NULL);
		return;
	}

IL_0031:
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral3B7A2CCA88B3675CD64BF54B46129FCE1D963037, NULL);
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_4;
		L_4 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		int32_t L_5 = __this->___goldRewardAmount;
		NullCheck(L_4);
		AdsManager_ShowRewardedVideoForGold_m2D89968C3AA4E95A332161EA49396E68108C633E(L_4, L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_ShowRewardedVideoForExtraMoves_m0ACBD7E8B58B0CB73219CE7EBE774DA9CEA40ED5 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1E1A504B4707650AF3C34B98E6DFC9E30D2A8564);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6F448909A101B8782582360C107AE0801A1A01F5);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7ABD5172E838AC0047853392A897E183DFFDA2D6);
		s_Il2CppMethodInitialized = true;
	}
	{
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_0;
		L_0 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0019;
		}
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral6F448909A101B8782582360C107AE0801A1A01F5, NULL);
		return;
	}

IL_0019:
	{
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_2;
		L_2 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		NullCheck(L_2);
		bool L_3;
		L_3 = AdsManager_IsRewardedVideoAvailable_m462847E3858F15770DD343A4FD0A3A73B6989182(L_2, NULL);
		if (L_3)
		{
			goto IL_0031;
		}
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral1E1A504B4707650AF3C34B98E6DFC9E30D2A8564, NULL);
		return;
	}

IL_0031:
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral7ABD5172E838AC0047853392A897E183DFFDA2D6, NULL);
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_4;
		L_4 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		int32_t L_5 = __this->___extraMovesAmount;
		NullCheck(L_4);
		AdsManager_ShowRewardedVideoForExtraMoves_m9B40E2C31ABB962772C2EA1C3C2C1DD31DA3C06E(L_4, L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_ShowInterstitial_mF695E724D4069EF91F8E4EDC7B880B1D52332CB9 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral67BD88E5B7FABE853CF999857489C5BBDE908825);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6F448909A101B8782582360C107AE0801A1A01F5);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral939A3F92CB23E1B7814ADC682576F07325199C2D);
		s_Il2CppMethodInitialized = true;
	}
	{
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_0;
		L_0 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0019;
		}
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral6F448909A101B8782582360C107AE0801A1A01F5, NULL);
		return;
	}

IL_0019:
	{
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_2;
		L_2 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		NullCheck(L_2);
		bool L_3;
		L_3 = AdsManager_IsInterstitialAvailable_m63811601029FA9E735FA2165427FCACBECBDD158(L_2, NULL);
		if (L_3)
		{
			goto IL_0031;
		}
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral67BD88E5B7FABE853CF999857489C5BBDE908825, NULL);
		return;
	}

IL_0031:
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral939A3F92CB23E1B7814ADC682576F07325199C2D, NULL);
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_4;
		L_4 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		NullCheck(L_4);
		AdsManager_ShowInterstitial_m7DB331C0DAEC266FAE090FC8BA4BE2DBF275AF5B(L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_ToggleBanner_m512FB8619E7F5FFBB47150D40557EA3E05E03A47 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6F448909A101B8782582360C107AE0801A1A01F5);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7FB5340B31482BCAB59C5B5F364A2A5293AEFEBE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral88FE4AA56A9F59302841BAE8617419E05F3E98B7);
		s_Il2CppMethodInitialized = true;
	}
	{
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_0;
		L_0 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0019;
		}
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral6F448909A101B8782582360C107AE0801A1A01F5, NULL);
		return;
	}

IL_0019:
	{
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_2;
		L_2 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		NullCheck(L_2);
		bool L_3;
		L_3 = AdsManager_get_IsBannerVisible_m0520218B5030FBB61969F27EF24D998876B12A07_inline(L_2, NULL);
		if (!L_3)
		{
			goto IL_003b;
		}
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral7FB5340B31482BCAB59C5B5F364A2A5293AEFEBE, NULL);
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_4;
		L_4 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		NullCheck(L_4);
		AdsManager_HideBanner_m70733A75C8DAF41770678DB6E907B898D3A159FE(L_4, NULL);
		return;
	}

IL_003b:
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral88FE4AA56A9F59302841BAE8617419E05F3E98B7, NULL);
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_5;
		L_5 = AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline(NULL);
		NullCheck(L_5);
		AdsManager_ShowBanner_m5335F2493F3290B9D8EB60B34B05B1899AE81E27(L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_OnAdsInitialized_m7AA1EF89BFBFD26DA95BF9BA248CA77F293A54A9 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD6C5C03A4F95450BCD8E8C6ED75A7DF38B9584EE);
		s_Il2CppMethodInitialized = true;
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteralD6C5C03A4F95450BCD8E8C6ED75A7DF38B9584EE, NULL);
		AdsSetupExample_UpdateButtonStates_m398B77CFAB56DB85E79EAEE8FC28A6C28CE85449(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_OnRewardedVideoCompleted_m5F9D46DA8CB4D9C6543FA651EBACEE8F601087CD (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, AdReward_tD6C5592436EB3F2368B8E7C5D03E95D32E5CB4F2* ___0_reward, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral66ED0A119B8EF0CA8D4239109466E3E063C75FD1);
		s_Il2CppMethodInitialized = true;
	}
	{
		AdReward_tD6C5592436EB3F2368B8E7C5D03E95D32E5CB4F2* L_0 = ___0_reward;
		NullCheck(L_0);
		String_t* L_1;
		L_1 = AdReward_get_Description_m3CEC0BEABF98834C5ED422D60D3C365E15C9120B_inline(L_0, NULL);
		String_t* L_2;
		L_2 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(_stringLiteral66ED0A119B8EF0CA8D4239109466E3E063C75FD1, L_1, NULL);
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, L_2, NULL);
		AdsSetupExample_UpdateButtonStates_m398B77CFAB56DB85E79EAEE8FC28A6C28CE85449(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_OnRewardedVideoFailed_mE234436E8F799D695445A9B99D9AF27DF9ACE3E4 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral182F5F25B0C100DCC12581F3503D71A8BB778082);
		s_Il2CppMethodInitialized = true;
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral182F5F25B0C100DCC12581F3503D71A8BB778082, NULL);
		AdsSetupExample_UpdateButtonStates_m398B77CFAB56DB85E79EAEE8FC28A6C28CE85449(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_OnInterstitialShown_m48B9BCF52271A36178E34591FA12EAA4E53B2C80 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral06836AC70561A555D38A238A7F78810086548FF9);
		s_Il2CppMethodInitialized = true;
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral06836AC70561A555D38A238A7F78810086548FF9, NULL);
		AdsSetupExample_UpdateButtonStates_m398B77CFAB56DB85E79EAEE8FC28A6C28CE85449(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_OnInterstitialFailed_mE1C0EFA693C079D46F51C65FE24E355DC2BB050D (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEEDFB936045C1501281E6A9EABC5551B5107AF45);
		s_Il2CppMethodInitialized = true;
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteralEEDFB936045C1501281E6A9EABC5551B5107AF45, NULL);
		AdsSetupExample_UpdateButtonStates_m398B77CFAB56DB85E79EAEE8FC28A6C28CE85449(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_OnBannerLoaded_m727617E7481F01F14FAE4FF134634359BE6E40E0 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral342295F94D9C6893BA06433A2E6B1014710F1E8B);
		s_Il2CppMethodInitialized = true;
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral342295F94D9C6893BA06433A2E6B1014710F1E8B, NULL);
		AdsSetupExample_UpdateButtonStates_m398B77CFAB56DB85E79EAEE8FC28A6C28CE85449(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_OnBannerFailed_mA43362B2EB1C62ACEE1F68753531C320CDE50129 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0D73F44B555B5454E0D888FAB419BD59BF0898BD);
		s_Il2CppMethodInitialized = true;
	}
	{
		AdsSetupExample_UpdateStatus_mCA8D675164BE634FDB4BF2FFE75951E8D465A99C(__this, _stringLiteral0D73F44B555B5454E0D888FAB419BD59BF0898BD, NULL);
		AdsSetupExample_UpdateButtonStates_m398B77CFAB56DB85E79EAEE8FC28A6C28CE85449(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_Update_m2611CE9661156AAF46D62DFFDA05806192DC8531 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0;
		L_0 = Time_get_frameCount_m4A42E558A71301A216BDC49EC402D62F19C79667(NULL);
		if (((int32_t)(L_0%((int32_t)60))))
		{
			goto IL_0010;
		}
	}
	{
		AdsSetupExample_UpdateButtonStates_m398B77CFAB56DB85E79EAEE8FC28A6C28CE85449(__this, NULL);
	}

IL_0010:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample_OnDestroy_mA15E1C42AF4D348556781855130E890F09E76C45 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnAdsInitialized_m7AA1EF89BFBFD26DA95BF9BA248CA77F293A54A9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnBannerFailed_mA43362B2EB1C62ACEE1F68753531C320CDE50129_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnBannerLoaded_m727617E7481F01F14FAE4FF134634359BE6E40E0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnInterstitialFailed_mE1C0EFA693C079D46F51C65FE24E355DC2BB050D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnInterstitialShown_m48B9BCF52271A36178E34591FA12EAA4E53B2C80_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnRewardedVideoCompleted_m5F9D46DA8CB4D9C6543FA651EBACEE8F601087CD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsSetupExample_OnRewardedVideoFailed_mE234436E8F799D695445A9B99D9AF27DF9ACE3E4_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)AdsSetupExample_OnAdsInitialized_m7AA1EF89BFBFD26DA95BF9BA248CA77F293A54A9_RuntimeMethod_var), NULL);
		AdsManager_remove_OnAdsInitialized_m72D81F3653B45ED352A1DC54DA35FC7A9DC42EB7(L_0, NULL);
		Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247* L_1 = (Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247*)il2cpp_codegen_object_new(Action_1_tA5636D1A1E3CC996578F1239252B99472AA08247_il2cpp_TypeInfo_var);
		Action_1__ctor_m44EC72C5D0EE856DCF158315D4B9C72356E4FA66(L_1, __this, (intptr_t)((void*)AdsSetupExample_OnRewardedVideoCompleted_m5F9D46DA8CB4D9C6543FA651EBACEE8F601087CD_RuntimeMethod_var), NULL);
		AdsManager_remove_OnRewardedVideoCompleted_m9FC72B9BC9B8F630BAFFC7B791CB4CADD7158879(L_1, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_2, __this, (intptr_t)((void*)AdsSetupExample_OnRewardedVideoFailed_mE234436E8F799D695445A9B99D9AF27DF9ACE3E4_RuntimeMethod_var), NULL);
		AdsManager_remove_OnRewardedVideoFailed_m30542CB9C97A3A33CC40C7B17F72716C808D1DBD(L_2, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_3, __this, (intptr_t)((void*)AdsSetupExample_OnInterstitialShown_m48B9BCF52271A36178E34591FA12EAA4E53B2C80_RuntimeMethod_var), NULL);
		AdsManager_remove_OnInterstitialShown_mB3CBF612EECD1C1BE4A947DD958ED1A143C19DBF(L_3, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_4, __this, (intptr_t)((void*)AdsSetupExample_OnInterstitialFailed_mE1C0EFA693C079D46F51C65FE24E355DC2BB050D_RuntimeMethod_var), NULL);
		AdsManager_remove_OnInterstitialFailed_m352098F77F1AF8D2177A9A4AC5486AD30A4FA570(L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_5 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_5, __this, (intptr_t)((void*)AdsSetupExample_OnBannerLoaded_m727617E7481F01F14FAE4FF134634359BE6E40E0_RuntimeMethod_var), NULL);
		AdsManager_remove_OnBannerLoaded_m7D91483B4A1153C02B140A7E5B0F2B5F7861447A(L_5, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_6, __this, (intptr_t)((void*)AdsSetupExample_OnBannerFailed_mA43362B2EB1C62ACEE1F68753531C320CDE50129_RuntimeMethod_var), NULL);
		AdsManager_remove_OnBannerFailed_mB7030FF38CAFF8378BE7477CE6EBCA798AAF3738(L_6, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdsSetupExample__ctor_m6ADCCE605F36FE83CA4CF3D511DDBDC110D45B61 (AdsSetupExample_tF7B6627ED809AC44A2A7C2DD73A08ED22C90E08C* __this, const RuntimeMethod* method) 
{
	{
		__this->___goldRewardAmount = ((int32_t)250);
		__this->___extraMovesAmount = 5;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterData__ctor_m8FE536FAFEC133C2FFBB13F8EFB235A594BC23A8 (BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* __this, const RuntimeMethod* method) 
{
	{
		__this->___defaultQuantity = 1;
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* BoosterConfiguration_GetBoosterData_mDA9004813F88050572A28CA454CE83194F9B3A0E (BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* __this, int32_t ___0_type, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Find_mB6456BBAE8D17C041213EA3F254DFC24D2E55F52_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Predicate_1_tF189FD0FC04DE1D6B0F949BD66167FFEF719827F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass2_0_U3CGetBoosterDataU3Eb__0_m4557E3B3FA8680BEE6F51689ED9463E3BDD0C746_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass2_0_tF3AB6702B2FF545D80C5D1F0D565F11E0928A9E7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass2_0_tF3AB6702B2FF545D80C5D1F0D565F11E0928A9E7* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass2_0_tF3AB6702B2FF545D80C5D1F0D565F11E0928A9E7* L_0 = (U3CU3Ec__DisplayClass2_0_tF3AB6702B2FF545D80C5D1F0D565F11E0928A9E7*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass2_0_tF3AB6702B2FF545D80C5D1F0D565F11E0928A9E7_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass2_0__ctor_m6C755DCC39F68254B6975024504077A3203C0C91(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass2_0_tF3AB6702B2FF545D80C5D1F0D565F11E0928A9E7* L_1 = V_0;
		int32_t L_2 = ___0_type;
		NullCheck(L_1);
		L_1->___type = L_2;
		List_1_t5905E46128E66911F2CCDF055565E1B0005905BC* L_3 = __this->___boosters;
		U3CU3Ec__DisplayClass2_0_tF3AB6702B2FF545D80C5D1F0D565F11E0928A9E7* L_4 = V_0;
		Predicate_1_tF189FD0FC04DE1D6B0F949BD66167FFEF719827F* L_5 = (Predicate_1_tF189FD0FC04DE1D6B0F949BD66167FFEF719827F*)il2cpp_codegen_object_new(Predicate_1_tF189FD0FC04DE1D6B0F949BD66167FFEF719827F_il2cpp_TypeInfo_var);
		Predicate_1__ctor_m1DC24E44234562B45FC10DB2C0BD5C0846044DCE(L_5, L_4, (intptr_t)((void*)U3CU3Ec__DisplayClass2_0_U3CGetBoosterDataU3Eb__0_m4557E3B3FA8680BEE6F51689ED9463E3BDD0C746_RuntimeMethod_var), NULL);
		NullCheck(L_3);
		BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* L_6;
		L_6 = List_1_Find_mB6456BBAE8D17C041213EA3F254DFC24D2E55F52(L_3, L_5, List_1_Find_mB6456BBAE8D17C041213EA3F254DFC24D2E55F52_RuntimeMethod_var);
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* BoosterConfiguration_GetAvailableBoosterTypes_mBD16729F6FCFC9841BF1B09B770B1E4E44C70EBF (BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_Dispose_mCCC81CC95BF1E049B322A75E6C850F40A539BF96_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_MoveNext_m1D2A92CE9E05B442EDAA2B9BDF36D2ADDD45CE31_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_get_Current_mF1934C0DA5E45E0DF121ABC5B1CEBA197F0C3CE5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_mDFE0DEA453B1CC0B28177181E50A6475AF25F54D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_GetEnumerator_m1500929B6B659F0084B77A78D7F01E0B01C52D7E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m75E092D0D8E1D7464630D385EA1F241D31BD3D56_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* V_0 = NULL;
	Enumerator_t68640B372EDF30A473CF3EEC85B10E1E2C4A7A2A V_1;
	memset((&V_1), 0, sizeof(V_1));
	BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* V_2 = NULL;
	{
		List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* L_0 = (List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF*)il2cpp_codegen_object_new(List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF_il2cpp_TypeInfo_var);
		List_1__ctor_m75E092D0D8E1D7464630D385EA1F241D31BD3D56(L_0, List_1__ctor_m75E092D0D8E1D7464630D385EA1F241D31BD3D56_RuntimeMethod_var);
		V_0 = L_0;
		List_1_t5905E46128E66911F2CCDF055565E1B0005905BC* L_1 = __this->___boosters;
		NullCheck(L_1);
		Enumerator_t68640B372EDF30A473CF3EEC85B10E1E2C4A7A2A L_2;
		L_2 = List_1_GetEnumerator_m1500929B6B659F0084B77A78D7F01E0B01C52D7E(L_1, List_1_GetEnumerator_m1500929B6B659F0084B77A78D7F01E0B01C52D7E_RuntimeMethod_var);
		V_1 = L_2;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_0033:
			{
				Enumerator_Dispose_mCCC81CC95BF1E049B322A75E6C850F40A539BF96((&V_1), Enumerator_Dispose_mCCC81CC95BF1E049B322A75E6C850F40A539BF96_RuntimeMethod_var);
				return;
			}
		});
		try
		{
			{
				goto IL_0028_1;
			}

IL_0014_1:
			{
				BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* L_3;
				L_3 = Enumerator_get_Current_mF1934C0DA5E45E0DF121ABC5B1CEBA197F0C3CE5_inline((&V_1), Enumerator_get_Current_mF1934C0DA5E45E0DF121ABC5B1CEBA197F0C3CE5_RuntimeMethod_var);
				V_2 = L_3;
				List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* L_4 = V_0;
				BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* L_5 = V_2;
				NullCheck(L_5);
				int32_t L_6 = L_5->___type;
				NullCheck(L_4);
				List_1_Add_mDFE0DEA453B1CC0B28177181E50A6475AF25F54D_inline(L_4, L_6, List_1_Add_mDFE0DEA453B1CC0B28177181E50A6475AF25F54D_RuntimeMethod_var);
			}

IL_0028_1:
			{
				bool L_7;
				L_7 = Enumerator_MoveNext_m1D2A92CE9E05B442EDAA2B9BDF36D2ADDD45CE31((&V_1), Enumerator_MoveNext_m1D2A92CE9E05B442EDAA2B9BDF36D2ADDD45CE31_RuntimeMethod_var);
				if (L_7)
				{
					goto IL_0014_1;
				}
			}
			{
				goto IL_0041;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0041:
	{
		List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* L_8 = V_0;
		return L_8;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterConfiguration__ctor_m830C259A77617D1063A4E1F3DFD2A2692A20DEF6 (BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m326B607FF8589404C24FFDB935228892676FC826_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t5905E46128E66911F2CCDF055565E1B0005905BC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->___extraMovesAmount = 5;
		List_1_t5905E46128E66911F2CCDF055565E1B0005905BC* L_0 = (List_1_t5905E46128E66911F2CCDF055565E1B0005905BC*)il2cpp_codegen_object_new(List_1_t5905E46128E66911F2CCDF055565E1B0005905BC_il2cpp_TypeInfo_var);
		List_1__ctor_m326B607FF8589404C24FFDB935228892676FC826(L_0, List_1__ctor_m326B607FF8589404C24FFDB935228892676FC826_RuntimeMethod_var);
		__this->___boosters = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___boosters), (void*)L_0);
		ScriptableObject__ctor_mD037FDB0B487295EA47F79A4DB1BF1846C9087FF(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass2_0__ctor_m6C755DCC39F68254B6975024504077A3203C0C91 (U3CU3Ec__DisplayClass2_0_tF3AB6702B2FF545D80C5D1F0D565F11E0928A9E7* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CU3Ec__DisplayClass2_0_U3CGetBoosterDataU3Eb__0_m4557E3B3FA8680BEE6F51689ED9463E3BDD0C746 (U3CU3Ec__DisplayClass2_0_tF3AB6702B2FF545D80C5D1F0D565F11E0928A9E7* __this, BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* ___0_b, const RuntimeMethod* method) 
{
	{
		BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* L_0 = ___0_b;
		NullCheck(L_0);
		int32_t L_1 = L_0->___type;
		int32_t L_2 = __this->___type;
		return (bool)((((int32_t)L_1) == ((int32_t)L_2))? 1 : 0);
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_add_OnBoosterCountChanged_mDDAF41B33B5D2FE07A8F814E428CF0CB8B4372D8 (Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* V_0 = NULL;
	Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* V_1 = NULL;
	Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* V_2 = NULL;
	{
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_0 = ((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___OnBoosterCountChanged;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_1 = V_0;
		V_1 = L_1;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_2 = V_1;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A*)Castclass((RuntimeObject*)L_4, Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A_il2cpp_TypeInfo_var));
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_5 = V_2;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_6 = V_1;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A*>((&((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___OnBoosterCountChanged), L_5, L_6);
		V_0 = L_7;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_8 = V_0;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A*)L_8) == ((RuntimeObject*)(Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_remove_OnBoosterCountChanged_m87EDACFCF317422A00DB531DE306A8085819AB09 (Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* V_0 = NULL;
	Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* V_1 = NULL;
	Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* V_2 = NULL;
	{
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_0 = ((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___OnBoosterCountChanged;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_1 = V_0;
		V_1 = L_1;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_2 = V_1;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A*)Castclass((RuntimeObject*)L_4, Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A_il2cpp_TypeInfo_var));
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_5 = V_2;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_6 = V_1;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A*>((&((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___OnBoosterCountChanged), L_5, L_6);
		V_0 = L_7;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_8 = V_0;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A*)L_8) == ((RuntimeObject*)(Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_add_OnBoosterUsed_m79F0B8C726FC3CD8243B87D4849CF006A73EB8F6 (Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* V_0 = NULL;
	Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* V_1 = NULL;
	Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* V_2 = NULL;
	{
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_0 = ((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___OnBoosterUsed;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_1 = V_0;
		V_1 = L_1;
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_2 = V_1;
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C*)Castclass((RuntimeObject*)L_4, Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C_il2cpp_TypeInfo_var));
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_5 = V_2;
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_6 = V_1;
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C*>((&((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___OnBoosterUsed), L_5, L_6);
		V_0 = L_7;
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_8 = V_0;
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C*)L_8) == ((RuntimeObject*)(Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_remove_OnBoosterUsed_m34B646C5836521231B984F685634917F7267DDFA (Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* V_0 = NULL;
	Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* V_1 = NULL;
	Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* V_2 = NULL;
	{
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_0 = ((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___OnBoosterUsed;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_1 = V_0;
		V_1 = L_1;
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_2 = V_1;
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C*)Castclass((RuntimeObject*)L_4, Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C_il2cpp_TypeInfo_var));
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_5 = V_2;
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_6 = V_1;
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C*>((&((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___OnBoosterUsed), L_5, L_6);
		V_0 = L_7;
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_8 = V_0;
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C*)L_8) == ((RuntimeObject*)(Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* BoosterManager_get_Instance_m2D6B03021BDA024FCE34BB36D3D56DD36F0E72CD (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* L_0 = ((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___U3CInstanceU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_set_Instance_m632638C5325431D080248B2C9A7F82FBDB87FE9D (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* L_0 = ___0_value;
		((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___U3CInstanceU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___U3CInstanceU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_Awake_m433959833C6925D6B5BC39BC24B3C16D1C4D6A5E (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* L_0;
		L_0 = BoosterManager_get_Instance_m2D6B03021BDA024FCE34BB36D3D56DD36F0E72CD_inline(NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0025;
		}
	}
	{
		BoosterManager_set_Instance_m632638C5325431D080248B2C9A7F82FBDB87FE9D_inline(__this, NULL);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_2;
		L_2 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_DontDestroyOnLoad_m4B70C3AEF886C176543D1295507B6455C9DCAEA7(L_2, NULL);
		BoosterManager_Initialize_mCD54F4A3AE51948393655861BD7C15B3675A8D91(__this, NULL);
		return;
	}

IL_0025:
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_3;
		L_3 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB(L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_Initialize_mCD54F4A3AE51948393655861BD7C15B3675A8D91 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* V_0 = NULL;
	BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* G_B3_0 = NULL;
	BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* G_B3_1 = NULL;
	BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* G_B2_0 = NULL;
	BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* G_B2_1 = NULL;
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_0;
		L_0 = GameProgressManager_get_Instance_m61456B3B17754A572AD056A8BF687A1CECFF767E(NULL);
		__this->___progressManager = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___progressManager), (void*)L_0);
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_1 = __this->___progressManager;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_1, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_2)
		{
			goto IL_003b;
		}
	}
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_3 = __this->___progressManager;
		NullCheck(L_3);
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_4;
		L_4 = GameProgressManager_GetShopProgress_m8B6B107D22FB71F7F940241E51BA57E4D74E727E(L_3, NULL);
		V_0 = L_4;
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_5 = V_0;
		NullCheck(L_5);
		BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* L_6 = L_5->___boosterInventory;
		BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* L_7 = L_6;
		if (L_7)
		{
			G_B3_0 = L_7;
			G_B3_1 = __this;
			goto IL_0035;
		}
		G_B2_0 = L_7;
		G_B2_1 = __this;
	}
	{
		BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* L_8 = (BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453*)il2cpp_codegen_object_new(BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453_il2cpp_TypeInfo_var);
		BoosterInventory__ctor_m0929A9BFD69BA97B1C5ADB92168C5C9727A472FA(L_8, NULL);
		G_B3_0 = L_8;
		G_B3_1 = G_B2_1;
	}

IL_0035:
	{
		NullCheck(G_B3_1);
		G_B3_1->___inventory = G_B3_0;
		Il2CppCodeGenWriteBarrier((void**)(&G_B3_1->___inventory), (void*)G_B3_0);
		return;
	}

IL_003b:
	{
		BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* L_9 = (BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453*)il2cpp_codegen_object_new(BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453_il2cpp_TypeInfo_var);
		BoosterInventory__ctor_m0929A9BFD69BA97B1C5ADB92168C5C9727A472FA(L_9, NULL);
		__this->___inventory = L_9;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___inventory), (void*)L_9);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t BoosterManager_GetBoosterCount_m57950EB6555110735DA159B254584169E34E3BFF (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, int32_t ___0_type, const RuntimeMethod* method) 
{
	BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* G_B2_0 = NULL;
	BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* G_B1_0 = NULL;
	{
		BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* L_0 = __this->___inventory;
		BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000c;
		}
		G_B1_0 = L_1;
	}
	{
		return 0;
	}

IL_000c:
	{
		int32_t L_2 = ___0_type;
		NullCheck(G_B2_0);
		int32_t L_3;
		L_3 = BoosterInventory_GetBoosterCount_m7BB355A0B384AA98B61E40E6238E54DEF9671019(G_B2_0, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_AddBoosters_m1159A0CECDCEF3698F62F49C5F6E8A8AD5A5415D (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, int32_t ___0_type, int32_t ___1_quantity, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* G_B4_0 = NULL;
	Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* G_B3_0 = NULL;
	{
		int32_t L_0 = ___1_quantity;
		if ((((int32_t)L_0) > ((int32_t)0)))
		{
			goto IL_0005;
		}
	}
	{
		return;
	}

IL_0005:
	{
		BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* L_1 = __this->___inventory;
		int32_t L_2 = ___0_type;
		int32_t L_3 = ___1_quantity;
		NullCheck(L_1);
		BoosterInventory_AddBoosters_mF1C3B05D27DFDA738E54878BFD942B6043E0E3E1(L_1, L_2, L_3, NULL);
		BoosterManager_SaveInventory_m77086B8D683CC403F676953B107EDEDB0DBC6733(__this, NULL);
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_4 = ((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___OnBoosterCountChanged;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_5 = L_4;
		if (L_5)
		{
			G_B4_0 = L_5;
			goto IL_0022;
		}
		G_B3_0 = L_5;
	}
	{
		return;
	}

IL_0022:
	{
		int32_t L_6 = ___0_type;
		int32_t L_7 = ___0_type;
		int32_t L_8;
		L_8 = BoosterManager_GetBoosterCount_m57950EB6555110735DA159B254584169E34E3BFF(__this, L_7, NULL);
		NullCheck(G_B4_0);
		Action_2_Invoke_m6D901F1610922A7B3DF33BE0B85F1DD5C37F52F7_inline(G_B4_0, L_6, L_8, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BoosterManager_UseBoosters_m8E0271EB22A07F38984DF0F77BA86CEE34D1C7F3 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, int32_t ___0_type, int32_t ___1_quantity, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* G_B3_0 = NULL;
	Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* G_B2_0 = NULL;
	Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* G_B6_0 = NULL;
	Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* G_B5_0 = NULL;
	{
		BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* L_0 = __this->___inventory;
		int32_t L_1 = ___0_type;
		int32_t L_2 = ___1_quantity;
		NullCheck(L_0);
		bool L_3;
		L_3 = BoosterInventory_UseBoosters_m8AF1D93774E9D2A6321BAF7AEAE67D75257B327F(L_0, L_1, L_2, NULL);
		if (!L_3)
		{
			goto IL_0040;
		}
	}
	{
		BoosterManager_SaveInventory_m77086B8D683CC403F676953B107EDEDB0DBC6733(__this, NULL);
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_4 = ((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___OnBoosterCountChanged;
		Action_2_t770D4BD69AE4566F6F1167549DE7B1E64BFA676A* L_5 = L_4;
		if (L_5)
		{
			G_B3_0 = L_5;
			goto IL_0020;
		}
		G_B2_0 = L_5;
	}
	{
		goto IL_002d;
	}

IL_0020:
	{
		int32_t L_6 = ___0_type;
		int32_t L_7 = ___0_type;
		int32_t L_8;
		L_8 = BoosterManager_GetBoosterCount_m57950EB6555110735DA159B254584169E34E3BFF(__this, L_7, NULL);
		NullCheck(G_B3_0);
		Action_2_Invoke_m6D901F1610922A7B3DF33BE0B85F1DD5C37F52F7_inline(G_B3_0, L_6, L_8, NULL);
	}

IL_002d:
	{
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_9 = ((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___OnBoosterUsed;
		Action_1_t19E9A6049A54453A4A75B732F4FD8F984644E11C* L_10 = L_9;
		if (L_10)
		{
			G_B6_0 = L_10;
			goto IL_0038;
		}
		G_B5_0 = L_10;
	}
	{
		goto IL_003e;
	}

IL_0038:
	{
		int32_t L_11 = ___0_type;
		NullCheck(G_B6_0);
		Action_1_Invoke_m67F0BA71EFF8FE17DEF0A02F92F0490D706D9D85_inline(G_B6_0, L_11, NULL);
	}

IL_003e:
	{
		return (bool)1;
	}

IL_0040:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BoosterManager_HasBoosters_m0806AA2C8F5333AAE1E6C6756F24EA45C5D0D9A8 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, int32_t ___0_type, int32_t ___1_quantity, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_type;
		int32_t L_1;
		L_1 = BoosterManager_GetBoosterCount_m57950EB6555110735DA159B254584169E34E3BFF(__this, L_0, NULL);
		int32_t L_2 = ___1_quantity;
		return (bool)((((int32_t)((((int32_t)L_1) < ((int32_t)L_2))? 1 : 0)) == ((int32_t)0))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Dictionary_2_tCDC59AD672DE07C4D23574E46F1C927253117EC6* BoosterManager_GetAllBoosterCounts_m165B357D463CDCFCAFD7C9415493128A77B44C93 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2__ctor_m12E1C0ED46E9464C3016E84E4EC67E27E53D8D1F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_tCDC59AD672DE07C4D23574E46F1C927253117EC6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* L_0 = __this->___inventory;
		NullCheck(L_0);
		Dictionary_2_tCDC59AD672DE07C4D23574E46F1C927253117EC6* L_1 = L_0->___boosters;
		Dictionary_2_tCDC59AD672DE07C4D23574E46F1C927253117EC6* L_2 = (Dictionary_2_tCDC59AD672DE07C4D23574E46F1C927253117EC6*)il2cpp_codegen_object_new(Dictionary_2_tCDC59AD672DE07C4D23574E46F1C927253117EC6_il2cpp_TypeInfo_var);
		Dictionary_2__ctor_m12E1C0ED46E9464C3016E84E4EC67E27E53D8D1F(L_2, L_1, Dictionary_2__ctor_m12E1C0ED46E9464C3016E84E4EC67E27E53D8D1F_RuntimeMethod_var);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_ApplyBoosterEffect_m675DBCEC4DA41C49CAB7C35D6C35D86F6786D87D (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, int32_t ___0_type, Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* ___1_board, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_FindFirstObjectByType_TisMatch3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA_mE778399552203976FD05E969433A2202FD3ABB2D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = ___0_type;
		bool L_1;
		L_1 = BoosterManager_HasBoosters_m0806AA2C8F5333AAE1E6C6756F24EA45C5D0D9A8(__this, L_0, 1, NULL);
		if (L_1)
		{
			goto IL_000b;
		}
	}
	{
		return;
	}

IL_000b:
	{
		Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* L_2 = ___1_board;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_3;
		L_3 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_2, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_3)
		{
			goto IL_001b;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* L_4;
		L_4 = Object_FindFirstObjectByType_TisMatch3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA_mE778399552203976FD05E969433A2202FD3ABB2D(Object_FindFirstObjectByType_TisMatch3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA_mE778399552203976FD05E969433A2202FD3ABB2D_RuntimeMethod_var);
		___1_board = L_4;
	}

IL_001b:
	{
		Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* L_5 = ___1_board;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_6;
		L_6 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_5, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_6)
		{
			goto IL_0025;
		}
	}
	{
		return;
	}

IL_0025:
	{
		int32_t L_7 = ___0_type;
		switch (L_7)
		{
			case 0:
			{
				goto IL_0040;
			}
			case 1:
			{
				goto IL_0049;
			}
			case 2:
			{
				goto IL_0052;
			}
			case 3:
			{
				goto IL_005b;
			}
			case 4:
			{
				goto IL_0064;
			}
		}
	}
	{
		return;
	}

IL_0040:
	{
		Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* L_8 = ___1_board;
		BoosterManager_ApplyBombBooster_mEAD702E1FD2FCEED1F805DE44616D4A9BA4A43D7(__this, L_8, NULL);
		goto IL_006b;
	}

IL_0049:
	{
		Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* L_9 = ___1_board;
		BoosterManager_ApplyLineRocketBooster_m1F44009C1F3DECE7F5F0207A9CEDEAA05D98C645(__this, L_9, NULL);
		goto IL_006b;
	}

IL_0052:
	{
		Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* L_10 = ___1_board;
		BoosterManager_ApplyColorBombBooster_mC21557FD9FDDD13850E59A3FD845DA8F4F74E9E1(__this, L_10, NULL);
		goto IL_006b;
	}

IL_005b:
	{
		Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* L_11 = ___1_board;
		BoosterManager_ApplyExtraMovesBooster_m3373C01DC300C1B180E17475378E7B7EB29E2B5C(__this, L_11, NULL);
		goto IL_006b;
	}

IL_0064:
	{
		Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* L_12 = ___1_board;
		BoosterManager_ApplyShuffleBooster_mA99ECD605A418A0913E1FE871A7612C30AC2A6F3(__this, L_12, NULL);
	}

IL_006b:
	{
		int32_t L_13 = ___0_type;
		bool L_14;
		L_14 = BoosterManager_UseBoosters_m8E0271EB22A07F38984DF0F77BA86CEE34D1C7F3(__this, L_13, 1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_ApplyBombBooster_mEAD702E1FD2FCEED1F805DE44616D4A9BA4A43D7 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* ___0_board, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_ApplyLineRocketBooster_m1F44009C1F3DECE7F5F0207A9CEDEAA05D98C645 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* ___0_board, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_ApplyColorBombBooster_mC21557FD9FDDD13850E59A3FD845DA8F4F74E9E1 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* ___0_board, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_ApplyExtraMovesBooster_m3373C01DC300C1B180E17475378E7B7EB29E2B5C (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* ___0_board, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* G_B2_0 = NULL;
	BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* G_B1_0 = NULL;
	int32_t G_B3_0 = 0;
	{
		BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* L_0 = __this->___boosterConfig;
		BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000d;
		}
		G_B1_0 = L_1;
	}
	{
		G_B3_0 = 5;
		goto IL_0012;
	}

IL_000d:
	{
		NullCheck(G_B2_0);
		int32_t L_2 = G_B2_0->___extraMovesAmount;
		G_B3_0 = L_2;
	}

IL_0012:
	{
		V_0 = G_B3_0;
		Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* L_3 = ___0_board;
		Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* L_4 = L_3;
		NullCheck(L_4);
		int32_t L_5 = L_4->___movesRemaining;
		int32_t L_6 = V_0;
		NullCheck(L_4);
		L_4->___movesRemaining = ((int32_t)il2cpp_codegen_add(L_5, L_6));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_ApplyShuffleBooster_mA99ECD605A418A0913E1FE871A7612C30AC2A6F3 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* ___0_board, const RuntimeMethod* method) 
{
	{
		Match3Board_t008F1348CEBF76CEB20E67E482095CB4318FFCAA* L_0 = ___0_board;
		NullCheck(L_0);
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 L_1;
		L_1 = Match3Board_ShuffleBoardAsync_mC1F455BC3950BE8787A2D9ECDFA1AF1D69945BA0(L_0, NULL);
		UniTaskExtensions_Forget_m8F82202C3DB2020AAE7F874AE049DA711A01DF13(L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_SaveInventory_m77086B8D683CC403F676953B107EDEDB0DBC6733 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* V_0 = NULL;
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_0 = __this->___progressManager;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0032;
		}
	}
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_2 = __this->___progressManager;
		NullCheck(L_2);
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_3;
		L_3 = GameProgressManager_GetShopProgress_m8B6B107D22FB71F7F940241E51BA57E4D74E727E(L_2, NULL);
		V_0 = L_3;
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_4 = V_0;
		BoosterInventory_tB0950792B7DCA253B4B6FEBFE2A7CAE634C66453* L_5 = __this->___inventory;
		NullCheck(L_4);
		L_4->___boosterInventory = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_4->___boosterInventory), (void*)L_5);
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_6 = __this->___progressManager;
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_7 = V_0;
		NullCheck(L_6);
		GameProgressManager_UpdateShopProgress_mFFECAB62E06A9F8EA165BF157E5FDC57F3CFED6B(L_6, L_7, NULL);
	}

IL_0032:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* BoosterManager_GetBoosterData_mB88A8AA88E5451F2394E6F9FB10324722101F7B3 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, int32_t ___0_type, const RuntimeMethod* method) 
{
	BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* G_B2_0 = NULL;
	BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* G_B1_0 = NULL;
	{
		BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* L_0 = __this->___boosterConfig;
		BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000c;
		}
		G_B1_0 = L_1;
	}
	{
		return (BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537*)NULL;
	}

IL_000c:
	{
		int32_t L_2 = ___0_type;
		NullCheck(G_B2_0);
		BoosterData_t25C6C2B4035FDE4026DF2C451CF02802DDF1B537* L_3;
		L_3 = BoosterConfiguration_GetBoosterData_mDA9004813F88050572A28CA454CE83194F9B3A0E(G_B2_0, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* BoosterManager_GetAvailableBoosterTypes_m9C08344C73732A744CF77E7701D43615D40D983A (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m75E092D0D8E1D7464630D385EA1F241D31BD3D56_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* G_B2_0 = NULL;
	BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* G_B1_0 = NULL;
	List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* G_B3_0 = NULL;
	List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* G_B5_0 = NULL;
	List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* G_B4_0 = NULL;
	{
		BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* L_0 = __this->___boosterConfig;
		BoosterConfiguration_tE47DBDDDDA807B9ECCF777205B6732CB50F6A7FF* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000d;
		}
		G_B1_0 = L_1;
	}
	{
		G_B3_0 = ((List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF*)(NULL));
		goto IL_0012;
	}

IL_000d:
	{
		NullCheck(G_B2_0);
		List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* L_2;
		L_2 = BoosterConfiguration_GetAvailableBoosterTypes_mBD16729F6FCFC9841BF1B09B770B1E4E44C70EBF(G_B2_0, NULL);
		G_B3_0 = L_2;
	}

IL_0012:
	{
		List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* L_3 = G_B3_0;
		if (L_3)
		{
			G_B5_0 = L_3;
			goto IL_001b;
		}
		G_B4_0 = L_3;
	}
	{
		List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF* L_4 = (List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF*)il2cpp_codegen_object_new(List_1_t1236425A510CEAF1D8A8602A3B58C44EE9505DFF_il2cpp_TypeInfo_var);
		List_1__ctor_m75E092D0D8E1D7464630D385EA1F241D31BD3D56(L_4, List_1__ctor_m75E092D0D8E1D7464630D385EA1F241D31BD3D56_RuntimeMethod_var);
		G_B5_0 = L_4;
	}

IL_001b:
	{
		return G_B5_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager_OnDestroy_m59AABAF98E64146BF9F6F757B5C16CC8B3E5563E (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* L_0;
		L_0 = BoosterManager_get_Instance_m2D6B03021BDA024FCE34BB36D3D56DD36F0E72CD_inline(NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, __this, NULL);
		if (!L_1)
		{
			goto IL_0013;
		}
	}
	{
		BoosterManager_set_Instance_m632638C5325431D080248B2C9A7F82FBDB87FE9D_inline((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26*)NULL, NULL);
	}

IL_0013:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoosterManager__ctor_mAAE2E45FF06D86DDBB94313054E4F7872292CFF3 (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarData__ctor_mF8C3D85F8A96576778CF3F785C7286AAC2C6DABC (AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* __this, const RuntimeMethod* method) 
{
	{
		__this->___canPurchaseWithAds = (bool)1;
		__this->___adsRequired = 3;
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* AvatarConfiguration_GetAvatarData_m699629AF5952D05D39A5F4DB1D1468C30B4A163D (AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* __this, String_t* ___0_id, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Find_m2152E27A258E71981F7E1DE6D1F5B6DA7F27AEE4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Predicate_1_tA8A070E292A9041EB736A2817C2F70211A0F2AC8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass2_0_U3CGetAvatarDataU3Eb__0_mC12F96F12BAE99C7E2478CDB26B8290DAD4333E7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass2_0_tB74732BEAFB8CFE9A642E0BB4D724BC134BBFCB7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass2_0_tB74732BEAFB8CFE9A642E0BB4D724BC134BBFCB7* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass2_0_tB74732BEAFB8CFE9A642E0BB4D724BC134BBFCB7* L_0 = (U3CU3Ec__DisplayClass2_0_tB74732BEAFB8CFE9A642E0BB4D724BC134BBFCB7*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass2_0_tB74732BEAFB8CFE9A642E0BB4D724BC134BBFCB7_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass2_0__ctor_mEC8F2DE72AE70D76E1AF19382A98B7215410D476(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass2_0_tB74732BEAFB8CFE9A642E0BB4D724BC134BBFCB7* L_1 = V_0;
		String_t* L_2 = ___0_id;
		NullCheck(L_1);
		L_1->___id = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___id), (void*)L_2);
		List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* L_3 = __this->___avatars;
		U3CU3Ec__DisplayClass2_0_tB74732BEAFB8CFE9A642E0BB4D724BC134BBFCB7* L_4 = V_0;
		Predicate_1_tA8A070E292A9041EB736A2817C2F70211A0F2AC8* L_5 = (Predicate_1_tA8A070E292A9041EB736A2817C2F70211A0F2AC8*)il2cpp_codegen_object_new(Predicate_1_tA8A070E292A9041EB736A2817C2F70211A0F2AC8_il2cpp_TypeInfo_var);
		Predicate_1__ctor_mD7BA88E3CE9A84459305722FB9C418319F1C2C47(L_5, L_4, (intptr_t)((void*)U3CU3Ec__DisplayClass2_0_U3CGetAvatarDataU3Eb__0_mC12F96F12BAE99C7E2478CDB26B8290DAD4333E7_RuntimeMethod_var), NULL);
		NullCheck(L_3);
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_6;
		L_6 = List_1_Find_m2152E27A258E71981F7E1DE6D1F5B6DA7F27AEE4(L_3, L_5, List_1_Find_m2152E27A258E71981F7E1DE6D1F5B6DA7F27AEE4_RuntimeMethod_var);
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* AvatarConfiguration_GetAllAvatarIds_m93C6262D82AB460BC3B2970B7626EBE5D7F78DFD (AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_Dispose_m58E9E0886441A7CCD9D59ED3A25F30F04D0BDC1D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_MoveNext_mF426A804C3EAFFBCCB63A791327B2E63F93DBF4C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_get_Current_m77874A5C46D351C6F16F2DFCDFF8559AE3392161_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_mF10DB1D3CBB0B14215F0E4F8AB4934A1955E5351_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Contains_m359254483BE42CAD4DCA8FBAFB87473FB4CF00E1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_GetEnumerator_m4FA30D8F70A2A9E393B26B2377DF62EC339ACCE4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mCA8DD57EAC70C2B5923DBB9D5A77CEAC22E7068E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6F5EC7239B41C242FCB23B64D91DA0070FC1C044);
		s_Il2CppMethodInitialized = true;
	}
	List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* V_0 = NULL;
	Enumerator_t1DF7601FD8273A05A2858952CBA7B08984FE75A5 V_1;
	memset((&V_1), 0, sizeof(V_1));
	AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* V_2 = NULL;
	{
		List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* L_0 = (List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD*)il2cpp_codegen_object_new(List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD_il2cpp_TypeInfo_var);
		List_1__ctor_mCA8DD57EAC70C2B5923DBB9D5A77CEAC22E7068E(L_0, List_1__ctor_mCA8DD57EAC70C2B5923DBB9D5A77CEAC22E7068E_RuntimeMethod_var);
		List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* L_1 = L_0;
		NullCheck(L_1);
		List_1_Add_mF10DB1D3CBB0B14215F0E4F8AB4934A1955E5351_inline(L_1, _stringLiteral6F5EC7239B41C242FCB23B64D91DA0070FC1C044, List_1_Add_mF10DB1D3CBB0B14215F0E4F8AB4934A1955E5351_RuntimeMethod_var);
		V_0 = L_1;
		List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* L_2 = __this->___avatars;
		NullCheck(L_2);
		Enumerator_t1DF7601FD8273A05A2858952CBA7B08984FE75A5 L_3;
		L_3 = List_1_GetEnumerator_m4FA30D8F70A2A9E393B26B2377DF62EC339ACCE4(L_2, List_1_GetEnumerator_m4FA30D8F70A2A9E393B26B2377DF62EC339ACCE4_RuntimeMethod_var);
		V_1 = L_3;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_004c:
			{
				Enumerator_Dispose_m58E9E0886441A7CCD9D59ED3A25F30F04D0BDC1D((&V_1), Enumerator_Dispose_m58E9E0886441A7CCD9D59ED3A25F30F04D0BDC1D_RuntimeMethod_var);
				return;
			}
		});
		try
		{
			{
				goto IL_0041_1;
			}

IL_001f_1:
			{
				AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_4;
				L_4 = Enumerator_get_Current_m77874A5C46D351C6F16F2DFCDFF8559AE3392161_inline((&V_1), Enumerator_get_Current_m77874A5C46D351C6F16F2DFCDFF8559AE3392161_RuntimeMethod_var);
				V_2 = L_4;
				List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* L_5 = V_0;
				AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_6 = V_2;
				NullCheck(L_6);
				String_t* L_7 = L_6->___id;
				NullCheck(L_5);
				bool L_8;
				L_8 = List_1_Contains_m359254483BE42CAD4DCA8FBAFB87473FB4CF00E1(L_5, L_7, List_1_Contains_m359254483BE42CAD4DCA8FBAFB87473FB4CF00E1_RuntimeMethod_var);
				if (L_8)
				{
					goto IL_0041_1;
				}
			}
			{
				List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* L_9 = V_0;
				AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_10 = V_2;
				NullCheck(L_10);
				String_t* L_11 = L_10->___id;
				NullCheck(L_9);
				List_1_Add_mF10DB1D3CBB0B14215F0E4F8AB4934A1955E5351_inline(L_9, L_11, List_1_Add_mF10DB1D3CBB0B14215F0E4F8AB4934A1955E5351_RuntimeMethod_var);
			}

IL_0041_1:
			{
				bool L_12;
				L_12 = Enumerator_MoveNext_mF426A804C3EAFFBCCB63A791327B2E63F93DBF4C((&V_1), Enumerator_MoveNext_mF426A804C3EAFFBCCB63A791327B2E63F93DBF4C_RuntimeMethod_var);
				if (L_12)
				{
					goto IL_001f_1;
				}
			}
			{
				goto IL_005a;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_005a:
	{
		List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* L_13 = V_0;
		return L_13;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarConfiguration__ctor_m80CCF5983F69D6C660887E5A224791AE0F2456CF (AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mB3D397E89B7F8DBDF91EDDC6DAE293EE8D58CEA3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* L_0 = (List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632*)il2cpp_codegen_object_new(List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632_il2cpp_TypeInfo_var);
		List_1__ctor_mB3D397E89B7F8DBDF91EDDC6DAE293EE8D58CEA3(L_0, List_1__ctor_mB3D397E89B7F8DBDF91EDDC6DAE293EE8D58CEA3_RuntimeMethod_var);
		__this->___avatars = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___avatars), (void*)L_0);
		ScriptableObject__ctor_mD037FDB0B487295EA47F79A4DB1BF1846C9087FF(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass2_0__ctor_mEC8F2DE72AE70D76E1AF19382A98B7215410D476 (U3CU3Ec__DisplayClass2_0_tB74732BEAFB8CFE9A642E0BB4D724BC134BBFCB7* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CU3Ec__DisplayClass2_0_U3CGetAvatarDataU3Eb__0_mC12F96F12BAE99C7E2478CDB26B8290DAD4333E7 (U3CU3Ec__DisplayClass2_0_tB74732BEAFB8CFE9A642E0BB4D724BC134BBFCB7* __this, AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* ___0_a, const RuntimeMethod* method) 
{
	{
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_0 = ___0_a;
		NullCheck(L_0);
		String_t* L_1 = L_0->___id;
		String_t* L_2 = __this->___id;
		bool L_3;
		L_3 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_1, L_2, NULL);
		return L_3;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_add_OnAvatarSelected_mEEBF02D06FD866BB1C94E1E8DE6255B396086142 (Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* V_0 = NULL;
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* V_1 = NULL;
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* V_2 = NULL;
	{
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_0 = ((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarSelected;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_1 = V_0;
		V_1 = L_1;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_2 = V_1;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*)Castclass((RuntimeObject*)L_4, Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A_il2cpp_TypeInfo_var));
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_5 = V_2;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_6 = V_1;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*>((&((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarSelected), L_5, L_6);
		V_0 = L_7;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_8 = V_0;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*)L_8) == ((RuntimeObject*)(Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_remove_OnAvatarSelected_mE26691554B14E14486F1506B37C86890B9005D72 (Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* V_0 = NULL;
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* V_1 = NULL;
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* V_2 = NULL;
	{
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_0 = ((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarSelected;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_1 = V_0;
		V_1 = L_1;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_2 = V_1;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*)Castclass((RuntimeObject*)L_4, Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A_il2cpp_TypeInfo_var));
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_5 = V_2;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_6 = V_1;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*>((&((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarSelected), L_5, L_6);
		V_0 = L_7;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_8 = V_0;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*)L_8) == ((RuntimeObject*)(Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_add_OnAvatarUnlocked_m4A807787B0FB72AC89B3637A17F4F67E7E0678C0 (Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* V_0 = NULL;
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* V_1 = NULL;
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* V_2 = NULL;
	{
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_0 = ((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarUnlocked;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_1 = V_0;
		V_1 = L_1;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_2 = V_1;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*)Castclass((RuntimeObject*)L_4, Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A_il2cpp_TypeInfo_var));
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_5 = V_2;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_6 = V_1;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*>((&((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarUnlocked), L_5, L_6);
		V_0 = L_7;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_8 = V_0;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*)L_8) == ((RuntimeObject*)(Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_remove_OnAvatarUnlocked_m9A788FB74BEBD02FB45066B036C6DE550571D237 (Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* V_0 = NULL;
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* V_1 = NULL;
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* V_2 = NULL;
	{
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_0 = ((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarUnlocked;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_1 = V_0;
		V_1 = L_1;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_2 = V_1;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*)Castclass((RuntimeObject*)L_4, Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A_il2cpp_TypeInfo_var));
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_5 = V_2;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_6 = V_1;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*>((&((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarUnlocked), L_5, L_6);
		V_0 = L_7;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_8 = V_0;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*)L_8) == ((RuntimeObject*)(Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_add_OnAvatarAdProgressChanged_m84EEAEC416B53D41F80DB247419F7B0915602446 (Action_2_t2399F3C34C43EB392520F878CA121755E120498E* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_2_t2399F3C34C43EB392520F878CA121755E120498E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_2_t2399F3C34C43EB392520F878CA121755E120498E* V_0 = NULL;
	Action_2_t2399F3C34C43EB392520F878CA121755E120498E* V_1 = NULL;
	Action_2_t2399F3C34C43EB392520F878CA121755E120498E* V_2 = NULL;
	{
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_0 = ((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarAdProgressChanged;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_1 = V_0;
		V_1 = L_1;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_2 = V_1;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((Action_2_t2399F3C34C43EB392520F878CA121755E120498E*)Castclass((RuntimeObject*)L_4, Action_2_t2399F3C34C43EB392520F878CA121755E120498E_il2cpp_TypeInfo_var));
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_5 = V_2;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_6 = V_1;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_2_t2399F3C34C43EB392520F878CA121755E120498E*>((&((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarAdProgressChanged), L_5, L_6);
		V_0 = L_7;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_8 = V_0;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_2_t2399F3C34C43EB392520F878CA121755E120498E*)L_8) == ((RuntimeObject*)(Action_2_t2399F3C34C43EB392520F878CA121755E120498E*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_remove_OnAvatarAdProgressChanged_m21BEDED55DB59BFA2573DD286A6E1B13A8358968 (Action_2_t2399F3C34C43EB392520F878CA121755E120498E* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_2_t2399F3C34C43EB392520F878CA121755E120498E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_2_t2399F3C34C43EB392520F878CA121755E120498E* V_0 = NULL;
	Action_2_t2399F3C34C43EB392520F878CA121755E120498E* V_1 = NULL;
	Action_2_t2399F3C34C43EB392520F878CA121755E120498E* V_2 = NULL;
	{
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_0 = ((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarAdProgressChanged;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_1 = V_0;
		V_1 = L_1;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_2 = V_1;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((Action_2_t2399F3C34C43EB392520F878CA121755E120498E*)Castclass((RuntimeObject*)L_4, Action_2_t2399F3C34C43EB392520F878CA121755E120498E_il2cpp_TypeInfo_var));
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_5 = V_2;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_6 = V_1;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_2_t2399F3C34C43EB392520F878CA121755E120498E*>((&((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarAdProgressChanged), L_5, L_6);
		V_0 = L_7;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_8 = V_0;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_2_t2399F3C34C43EB392520F878CA121755E120498E*)L_8) == ((RuntimeObject*)(Action_2_t2399F3C34C43EB392520F878CA121755E120498E*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* AvatarManager_get_Instance_m8D181A3A524D62906BF6B92CFDA124AE22C89BC1 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* L_0 = ((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___U3CInstanceU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_set_Instance_m712FFBAE63721B0A96B127EC1F0D3604DDFF27A8 (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* L_0 = ___0_value;
		((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___U3CInstanceU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___U3CInstanceU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_Awake_m1B89296D661EDB89F814516CEF627A3ED66697FD (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* L_0;
		L_0 = AvatarManager_get_Instance_m8D181A3A524D62906BF6B92CFDA124AE22C89BC1_inline(NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0025;
		}
	}
	{
		AvatarManager_set_Instance_m712FFBAE63721B0A96B127EC1F0D3604DDFF27A8_inline(__this, NULL);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_2;
		L_2 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_DontDestroyOnLoad_m4B70C3AEF886C176543D1295507B6455C9DCAEA7(L_2, NULL);
		AvatarManager_Initialize_mD2084D10A65E28FC8C2AF639E3F996CBF7A07130(__this, NULL);
		return;
	}

IL_0025:
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_3;
		L_3 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB(L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_Initialize_mD2084D10A65E28FC8C2AF639E3F996CBF7A07130 (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6F5EC7239B41C242FCB23B64D91DA0070FC1C044);
		s_Il2CppMethodInitialized = true;
	}
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* V_0 = NULL;
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_0;
		L_0 = GameProgressManager_get_Instance_m61456B3B17754A572AD056A8BF687A1CECFF767E(NULL);
		__this->___progressManager = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___progressManager), (void*)L_0);
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_1 = __this->___progressManager;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_1, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_2)
		{
			goto IL_0063;
		}
	}
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_3 = __this->___progressManager;
		NullCheck(L_3);
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_4;
		L_4 = GameProgressManager_GetShopProgress_m8B6B107D22FB71F7F940241E51BA57E4D74E727E(L_3, NULL);
		V_0 = L_4;
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_5 = V_0;
		NullCheck(L_5);
		String_t* L_6 = L_5->___selectedAvatarId;
		__this->___currentSelectedAvatarId = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___currentSelectedAvatarId), (void*)L_6);
		bool L_7;
		L_7 = AvatarManager_IsAvatarUnlocked_mA65FE89F275311D51596803A07DC0171900ECEA8(__this, _stringLiteral6F5EC7239B41C242FCB23B64D91DA0070FC1C044, NULL);
		if (L_7)
		{
			goto IL_0049;
		}
	}
	{
		AvatarManager_UnlockAvatar_m6A246FB330B24F2677582F588044C75575506D6F(__this, _stringLiteral6F5EC7239B41C242FCB23B64D91DA0070FC1C044, NULL);
	}

IL_0049:
	{
		String_t* L_8 = __this->___currentSelectedAvatarId;
		bool L_9;
		L_9 = String_IsNullOrEmpty_mEA9E3FB005AC28FE02E69FCF95A7B8456192B478(L_8, NULL);
		if (!L_9)
		{
			goto IL_006e;
		}
	}
	{
		bool L_10;
		L_10 = AvatarManager_SelectAvatar_m576A58E8985F67B6822096927E3040CD5C0FA115(__this, _stringLiteral6F5EC7239B41C242FCB23B64D91DA0070FC1C044, NULL);
		return;
	}

IL_0063:
	{
		__this->___currentSelectedAvatarId = _stringLiteral6F5EC7239B41C242FCB23B64D91DA0070FC1C044;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___currentSelectedAvatarId), (void*)_stringLiteral6F5EC7239B41C242FCB23B64D91DA0070FC1C044);
	}

IL_006e:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* AvatarManager_GetSelectedAvatarId_m0B4C9AA4473D54CF53F92D772DD10B356058E4CB (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___currentSelectedAvatarId;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* AvatarManager_GetSelectedAvatarSprite_mDD11998F97D80AEAC3B1F9526DA03F060EFBDFAB (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, const RuntimeMethod* method) 
{
	AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* G_B2_0 = NULL;
	AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* G_B1_0 = NULL;
	AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* G_B3_0 = NULL;
	AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* G_B5_0 = NULL;
	AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* G_B4_0 = NULL;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* G_B6_0 = NULL;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* G_B10_0 = NULL;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* G_B7_0 = NULL;
	AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* G_B9_0 = NULL;
	AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* G_B8_0 = NULL;
	{
		AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* L_0 = __this->___avatarConfig;
		AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000d;
		}
		G_B1_0 = L_1;
	}
	{
		G_B3_0 = ((AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277*)(NULL));
		goto IL_0018;
	}

IL_000d:
	{
		String_t* L_2 = __this->___currentSelectedAvatarId;
		NullCheck(G_B2_0);
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_3;
		L_3 = AvatarConfiguration_GetAvatarData_m699629AF5952D05D39A5F4DB1D1468C30B4A163D(G_B2_0, L_2, NULL);
		G_B3_0 = L_3;
	}

IL_0018:
	{
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_4 = G_B3_0;
		if (L_4)
		{
			G_B5_0 = L_4;
			goto IL_001f;
		}
		G_B4_0 = L_4;
	}
	{
		G_B6_0 = ((Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99*)(NULL));
		goto IL_0024;
	}

IL_001f:
	{
		NullCheck(G_B5_0);
		Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* L_5 = G_B5_0->___avatarSprite;
		G_B6_0 = L_5;
	}

IL_0024:
	{
		Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* L_6 = G_B6_0;
		if (L_6)
		{
			G_B10_0 = L_6;
			goto IL_0039;
		}
		G_B7_0 = L_6;
	}
	{
		AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* L_7 = __this->___avatarConfig;
		AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* L_8 = L_7;
		if (L_8)
		{
			G_B9_0 = L_8;
			goto IL_0034;
		}
		G_B8_0 = L_8;
	}
	{
		return (Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99*)NULL;
	}

IL_0034:
	{
		NullCheck(G_B9_0);
		Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* L_9 = G_B9_0->___defaultAvatarSprite;
		G_B10_0 = L_9;
	}

IL_0039:
	{
		return G_B10_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AvatarManager_SelectAvatar_m576A58E8985F67B6822096927E3040CD5C0FA115 (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, String_t* ___0_avatarId, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* G_B4_0 = NULL;
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* G_B3_0 = NULL;
	{
		String_t* L_0 = ___0_avatarId;
		bool L_1;
		L_1 = AvatarManager_IsAvatarUnlocked_mA65FE89F275311D51596803A07DC0171900ECEA8(__this, L_0, NULL);
		if (L_1)
		{
			goto IL_000b;
		}
	}
	{
		return (bool)0;
	}

IL_000b:
	{
		String_t* L_2 = ___0_avatarId;
		__this->___currentSelectedAvatarId = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___currentSelectedAvatarId), (void*)L_2);
		AvatarManager_SaveAvatarSelection_mAAD4FB71AF913195BAE9B44D921A46A46A2B1BDC(__this, NULL);
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_3 = ((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarSelected;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_4 = L_3;
		if (L_4)
		{
			G_B4_0 = L_4;
			goto IL_0023;
		}
		G_B3_0 = L_4;
	}
	{
		goto IL_0029;
	}

IL_0023:
	{
		String_t* L_5 = ___0_avatarId;
		NullCheck(G_B4_0);
		Action_1_Invoke_m690438AAE38F9762172E3AE0A33D0B42ACD35790_inline(G_B4_0, L_5, NULL);
	}

IL_0029:
	{
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AvatarManager_IsAvatarUnlocked_mA65FE89F275311D51596803A07DC0171900ECEA8 (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, String_t* ___0_avatarId, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6F5EC7239B41C242FCB23B64D91DA0070FC1C044);
		s_Il2CppMethodInitialized = true;
	}
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B4_0 = NULL;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B3_0 = NULL;
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* G_B5_0 = NULL;
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* G_B7_0 = NULL;
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* G_B6_0 = NULL;
	{
		String_t* L_0 = ___0_avatarId;
		bool L_1;
		L_1 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_0, _stringLiteral6F5EC7239B41C242FCB23B64D91DA0070FC1C044, NULL);
		if (!L_1)
		{
			goto IL_000f;
		}
	}
	{
		return (bool)1;
	}

IL_000f:
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_2 = __this->___progressManager;
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_3 = L_2;
		if (L_3)
		{
			G_B4_0 = L_3;
			goto IL_001c;
		}
		G_B3_0 = L_3;
	}
	{
		G_B5_0 = ((ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156*)(NULL));
		goto IL_0021;
	}

IL_001c:
	{
		NullCheck(G_B4_0);
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_4;
		L_4 = GameProgressManager_GetShopProgress_m8B6B107D22FB71F7F940241E51BA57E4D74E727E(G_B4_0, NULL);
		G_B5_0 = L_4;
	}

IL_0021:
	{
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_5 = G_B5_0;
		if (L_5)
		{
			G_B7_0 = L_5;
			goto IL_0027;
		}
		G_B6_0 = L_5;
	}
	{
		return (bool)0;
	}

IL_0027:
	{
		String_t* L_6 = ___0_avatarId;
		NullCheck(G_B7_0);
		bool L_7;
		L_7 = ShopProgressData_IsItemPurchased_m0F5B4857B28C04AE8DD1A1A0CAE7189B308B4FA8(G_B7_0, L_6, NULL);
		return L_7;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_UnlockAvatar_m6A246FB330B24F2677582F588044C75575506D6F (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, String_t* ___0_avatarId, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* V_0 = NULL;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B4_0 = NULL;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B3_0 = NULL;
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* G_B5_0 = NULL;
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* G_B8_0 = NULL;
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* G_B7_0 = NULL;
	{
		String_t* L_0 = ___0_avatarId;
		bool L_1;
		L_1 = AvatarManager_IsAvatarUnlocked_mA65FE89F275311D51596803A07DC0171900ECEA8(__this, L_0, NULL);
		if (!L_1)
		{
			goto IL_000a;
		}
	}
	{
		return;
	}

IL_000a:
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_2 = __this->___progressManager;
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_3 = L_2;
		if (L_3)
		{
			G_B4_0 = L_3;
			goto IL_0017;
		}
		G_B3_0 = L_3;
	}
	{
		G_B5_0 = ((ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156*)(NULL));
		goto IL_001c;
	}

IL_0017:
	{
		NullCheck(G_B4_0);
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_4;
		L_4 = GameProgressManager_GetShopProgress_m8B6B107D22FB71F7F940241E51BA57E4D74E727E(G_B4_0, NULL);
		G_B5_0 = L_4;
	}

IL_001c:
	{
		V_0 = G_B5_0;
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_5 = V_0;
		if (!L_5)
		{
			goto IL_0043;
		}
	}
	{
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_6 = V_0;
		String_t* L_7 = ___0_avatarId;
		NullCheck(L_6);
		ShopProgressData_PurchaseItem_mEF81EA6D3FF7026D0BF740C62E31F078F7AAEB03(L_6, L_7, NULL);
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_8 = __this->___progressManager;
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_9 = V_0;
		NullCheck(L_8);
		GameProgressManager_UpdateShopProgress_mFFECAB62E06A9F8EA165BF157E5FDC57F3CFED6B(L_8, L_9, NULL);
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_10 = ((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarUnlocked;
		Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* L_11 = L_10;
		if (L_11)
		{
			G_B8_0 = L_11;
			goto IL_003d;
		}
		G_B7_0 = L_11;
	}
	{
		return;
	}

IL_003d:
	{
		String_t* L_12 = ___0_avatarId;
		NullCheck(G_B8_0);
		Action_1_Invoke_m690438AAE38F9762172E3AE0A33D0B42ACD35790_inline(G_B8_0, L_12, NULL);
	}

IL_0043:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AvatarManager_PurchaseAvatarWithStars_m20A5C37817D88473C86C4D584083F838B678C7C9 (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, String_t* ___0_avatarId, const RuntimeMethod* method) 
{
	AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* V_0 = NULL;
	AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* G_B2_0 = NULL;
	AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* G_B1_0 = NULL;
	AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* G_B3_0 = NULL;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B11_0 = NULL;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B10_0 = NULL;
	int32_t G_B12_0 = 0;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B16_0 = NULL;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B15_0 = NULL;
	int32_t G_B17_0 = 0;
	{
		AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* L_0 = __this->___avatarConfig;
		AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000d;
		}
		G_B1_0 = L_1;
	}
	{
		G_B3_0 = ((AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277*)(NULL));
		goto IL_0013;
	}

IL_000d:
	{
		String_t* L_2 = ___0_avatarId;
		NullCheck(G_B2_0);
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_3;
		L_3 = AvatarConfiguration_GetAvatarData_m699629AF5952D05D39A5F4DB1D1468C30B4A163D(G_B2_0, L_2, NULL);
		G_B3_0 = L_3;
	}

IL_0013:
	{
		V_0 = G_B3_0;
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_4 = V_0;
		if (L_4)
		{
			goto IL_0019;
		}
	}
	{
		return (bool)0;
	}

IL_0019:
	{
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_5 = V_0;
		NullCheck(L_5);
		int32_t L_6 = L_5->___currencyType;
		if ((((int32_t)L_6) == ((int32_t)1)))
		{
			goto IL_0024;
		}
	}
	{
		return (bool)0;
	}

IL_0024:
	{
		String_t* L_7 = ___0_avatarId;
		bool L_8;
		L_8 = AvatarManager_IsAvatarUnlocked_mA65FE89F275311D51596803A07DC0171900ECEA8(__this, L_7, NULL);
		if (!L_8)
		{
			goto IL_002f;
		}
	}
	{
		return (bool)0;
	}

IL_002f:
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_9 = __this->___progressManager;
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_10 = L_9;
		if (L_10)
		{
			G_B11_0 = L_10;
			goto IL_003c;
		}
		G_B10_0 = L_10;
	}
	{
		G_B12_0 = 0;
		goto IL_0041;
	}

IL_003c:
	{
		NullCheck(G_B11_0);
		int32_t L_11;
		L_11 = GameProgressManager_GetStars_m7C4126CD1271867862EB4D02D2A60296527EA5EB(G_B11_0, NULL);
		G_B12_0 = L_11;
	}

IL_0041:
	{
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_12 = V_0;
		NullCheck(L_12);
		int32_t L_13 = L_12->___price;
		if ((((int32_t)G_B12_0) >= ((int32_t)L_13)))
		{
			goto IL_004b;
		}
	}
	{
		return (bool)0;
	}

IL_004b:
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_14 = __this->___progressManager;
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_15 = L_14;
		if (L_15)
		{
			G_B16_0 = L_15;
			goto IL_0058;
		}
		G_B15_0 = L_15;
	}
	{
		G_B17_0 = 0;
		goto IL_0063;
	}

IL_0058:
	{
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_16 = V_0;
		NullCheck(L_16);
		int32_t L_17 = L_16->___price;
		NullCheck(G_B16_0);
		bool L_18;
		L_18 = GameProgressManager_SpendStars_mE67DD551A60048FAE3E3A9F465F5110F94F0FF5B(G_B16_0, L_17, NULL);
		G_B17_0 = ((int32_t)(L_18));
	}

IL_0063:
	{
		if (!G_B17_0)
		{
			goto IL_006e;
		}
	}
	{
		String_t* L_19 = ___0_avatarId;
		AvatarManager_UnlockAvatar_m6A246FB330B24F2677582F588044C75575506D6F(__this, L_19, NULL);
		return (bool)1;
	}

IL_006e:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AvatarManager_WatchAdForAvatar_mC9BF915A497EDD6B7DED321DFDF57419357C83E3 (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, String_t* ___0_avatarId, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* V_0 = NULL;
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* V_1 = NULL;
	AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* V_2 = NULL;
	AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* G_B2_0 = NULL;
	AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* G_B1_0 = NULL;
	AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* G_B3_0 = NULL;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B10_0 = NULL;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B9_0 = NULL;
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* G_B11_0 = NULL;
	Action_2_t2399F3C34C43EB392520F878CA121755E120498E* G_B17_0 = NULL;
	bool G_B17_1 = false;
	Action_2_t2399F3C34C43EB392520F878CA121755E120498E* G_B16_0 = NULL;
	bool G_B16_1 = false;
	bool G_B18_0 = false;
	{
		AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* L_0 = __this->___avatarConfig;
		AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000d;
		}
		G_B1_0 = L_1;
	}
	{
		G_B3_0 = ((AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277*)(NULL));
		goto IL_0013;
	}

IL_000d:
	{
		String_t* L_2 = ___0_avatarId;
		NullCheck(G_B2_0);
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_3;
		L_3 = AvatarConfiguration_GetAvatarData_m699629AF5952D05D39A5F4DB1D1468C30B4A163D(G_B2_0, L_2, NULL);
		G_B3_0 = L_3;
	}

IL_0013:
	{
		V_0 = G_B3_0;
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_4 = V_0;
		if (!L_4)
		{
			goto IL_001f;
		}
	}
	{
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_5 = V_0;
		NullCheck(L_5);
		bool L_6 = L_5->___canPurchaseWithAds;
		if (L_6)
		{
			goto IL_0021;
		}
	}

IL_001f:
	{
		return (bool)0;
	}

IL_0021:
	{
		String_t* L_7 = ___0_avatarId;
		bool L_8;
		L_8 = AvatarManager_IsAvatarUnlocked_mA65FE89F275311D51596803A07DC0171900ECEA8(__this, L_7, NULL);
		if (!L_8)
		{
			goto IL_002c;
		}
	}
	{
		return (bool)0;
	}

IL_002c:
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_9 = __this->___progressManager;
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_10 = L_9;
		if (L_10)
		{
			G_B10_0 = L_10;
			goto IL_0039;
		}
		G_B9_0 = L_10;
	}
	{
		G_B11_0 = ((ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156*)(NULL));
		goto IL_003e;
	}

IL_0039:
	{
		NullCheck(G_B10_0);
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_11;
		L_11 = GameProgressManager_GetShopProgress_m8B6B107D22FB71F7F940241E51BA57E4D74E727E(G_B10_0, NULL);
		G_B11_0 = L_11;
	}

IL_003e:
	{
		V_1 = G_B11_0;
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_12 = V_1;
		if (L_12)
		{
			goto IL_0044;
		}
	}
	{
		return (bool)0;
	}

IL_0044:
	{
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_13 = V_1;
		String_t* L_14 = ___0_avatarId;
		NullCheck(L_13);
		AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* L_15;
		L_15 = ShopProgressData_GetAvatarAdProgress_mDB78C7835CC3228DF4B14730D260C497DE100695(L_13, L_14, NULL);
		V_2 = L_15;
		AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* L_16 = V_2;
		if (L_16)
		{
			goto IL_0063;
		}
	}
	{
		String_t* L_17 = ___0_avatarId;
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_18 = V_0;
		NullCheck(L_18);
		int32_t L_19 = L_18->___adsRequired;
		AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* L_20 = (AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0*)il2cpp_codegen_object_new(AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0_il2cpp_TypeInfo_var);
		AvatarAdProgress__ctor_m722A6F9841FDC79226C7D512E65D401B3247CAF8(L_20, L_17, L_19, NULL);
		V_2 = L_20;
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_21 = V_1;
		AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* L_22 = V_2;
		NullCheck(L_21);
		ShopProgressData_AddOrUpdateAvatarAdProgress_mCFBDB7E7E38D0FD38042B03F4975087EDAA8138D(L_21, L_22, NULL);
	}

IL_0063:
	{
		AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* L_23 = V_2;
		NullCheck(L_23);
		bool L_24;
		L_24 = AvatarAdProgress_WatchAd_m9325C61AED518E86F74FE3F10F4CDF189060959C(L_23, NULL);
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_25 = __this->___progressManager;
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_26 = V_1;
		NullCheck(L_25);
		GameProgressManager_UpdateShopProgress_mFFECAB62E06A9F8EA165BF157E5FDC57F3CFED6B(L_25, L_26, NULL);
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_27 = ((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarAdProgressChanged;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_28 = L_27;
		if (L_28)
		{
			G_B17_0 = L_28;
			G_B17_1 = L_24;
			goto IL_0080;
		}
		G_B16_0 = L_28;
		G_B16_1 = L_24;
	}
	{
		G_B18_0 = G_B16_1;
		goto IL_008c;
	}

IL_0080:
	{
		String_t* L_29 = ___0_avatarId;
		AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* L_30 = V_2;
		NullCheck(L_30);
		float L_31;
		L_31 = AvatarAdProgress_GetProgress_mE739AD1B09622E66E0178B544C5FA1D68746EF46(L_30, NULL);
		NullCheck(G_B17_0);
		Action_2_Invoke_m4394ACFFC2C8D7A7D849C0781B4212454875DBC2_inline(G_B17_0, L_29, L_31, NULL);
		G_B18_0 = G_B17_1;
	}

IL_008c:
	{
		if (!G_B18_0)
		{
			goto IL_0095;
		}
	}
	{
		String_t* L_32 = ___0_avatarId;
		AvatarManager_UnlockAvatar_m6A246FB330B24F2677582F588044C75575506D6F(__this, L_32, NULL);
	}

IL_0095:
	{
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* AvatarManager_GetAvatarAdProgress_mDFC28E18FA778666C3244AC6FB501EFBE04FDC8A (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, String_t* ___0_avatarId, const RuntimeMethod* method) 
{
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B2_0 = NULL;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B1_0 = NULL;
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* G_B3_0 = NULL;
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* G_B5_0 = NULL;
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* G_B4_0 = NULL;
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_0 = __this->___progressManager;
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000d;
		}
		G_B1_0 = L_1;
	}
	{
		G_B3_0 = ((ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156*)(NULL));
		goto IL_0012;
	}

IL_000d:
	{
		NullCheck(G_B2_0);
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_2;
		L_2 = GameProgressManager_GetShopProgress_m8B6B107D22FB71F7F940241E51BA57E4D74E727E(G_B2_0, NULL);
		G_B3_0 = L_2;
	}

IL_0012:
	{
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_3 = G_B3_0;
		if (L_3)
		{
			G_B5_0 = L_3;
			goto IL_0018;
		}
		G_B4_0 = L_3;
	}
	{
		return (AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0*)NULL;
	}

IL_0018:
	{
		String_t* L_4 = ___0_avatarId;
		NullCheck(G_B5_0);
		AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* L_5;
		L_5 = ShopProgressData_GetAvatarAdProgress_mDB78C7835CC3228DF4B14730D260C497DE100695(G_B5_0, L_4, NULL);
		return L_5;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_ResetAvatarAdProgress_m8685ACC4EE99C649D4EAF9F3E6419CE1565CAE4C (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, String_t* ___0_avatarId, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* V_0 = NULL;
	AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* V_1 = NULL;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B2_0 = NULL;
	GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* G_B1_0 = NULL;
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* G_B3_0 = NULL;
	Action_2_t2399F3C34C43EB392520F878CA121755E120498E* G_B8_0 = NULL;
	Action_2_t2399F3C34C43EB392520F878CA121755E120498E* G_B7_0 = NULL;
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_0 = __this->___progressManager;
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000d;
		}
		G_B1_0 = L_1;
	}
	{
		G_B3_0 = ((ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156*)(NULL));
		goto IL_0012;
	}

IL_000d:
	{
		NullCheck(G_B2_0);
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_2;
		L_2 = GameProgressManager_GetShopProgress_m8B6B107D22FB71F7F940241E51BA57E4D74E727E(G_B2_0, NULL);
		G_B3_0 = L_2;
	}

IL_0012:
	{
		V_0 = G_B3_0;
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_3 = V_0;
		if (L_3)
		{
			goto IL_0017;
		}
	}
	{
		return;
	}

IL_0017:
	{
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_4 = V_0;
		String_t* L_5 = ___0_avatarId;
		NullCheck(L_4);
		AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* L_6;
		L_6 = ShopProgressData_GetAvatarAdProgress_mDB78C7835CC3228DF4B14730D260C497DE100695(L_4, L_5, NULL);
		V_1 = L_6;
		AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* L_7 = V_1;
		if (!L_7)
		{
			goto IL_0049;
		}
	}
	{
		AvatarAdProgress_tD246E37FB1F7A5C8EE56B2F0EB2B2B20EC2CC7D0* L_8 = V_1;
		NullCheck(L_8);
		AvatarAdProgress_Reset_mC5CD2F83C5B82939D07A30C0B79C7916D038C59A(L_8, NULL);
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_9 = __this->___progressManager;
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_10 = V_0;
		NullCheck(L_9);
		GameProgressManager_UpdateShopProgress_mFFECAB62E06A9F8EA165BF157E5FDC57F3CFED6B(L_9, L_10, NULL);
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_11 = ((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___OnAvatarAdProgressChanged;
		Action_2_t2399F3C34C43EB392520F878CA121755E120498E* L_12 = L_11;
		if (L_12)
		{
			G_B8_0 = L_12;
			goto IL_003e;
		}
		G_B7_0 = L_12;
	}
	{
		return;
	}

IL_003e:
	{
		String_t* L_13 = ___0_avatarId;
		NullCheck(G_B8_0);
		Action_2_Invoke_m4394ACFFC2C8D7A7D849C0781B4212454875DBC2_inline(G_B8_0, L_13, (0.0f), NULL);
	}

IL_0049:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* AvatarManager_GetAllAvatars_m5A45879A0DC383CFE3B92B08B44080F0B437A4C1 (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mB3D397E89B7F8DBDF91EDDC6DAE293EE8D58CEA3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* G_B2_0 = NULL;
	AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* G_B1_0 = NULL;
	List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* G_B3_0 = NULL;
	List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* G_B5_0 = NULL;
	List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* G_B4_0 = NULL;
	{
		AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* L_0 = __this->___avatarConfig;
		AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000d;
		}
		G_B1_0 = L_1;
	}
	{
		G_B3_0 = ((List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632*)(NULL));
		goto IL_0012;
	}

IL_000d:
	{
		NullCheck(G_B2_0);
		List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* L_2 = G_B2_0->___avatars;
		G_B3_0 = L_2;
	}

IL_0012:
	{
		List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* L_3 = G_B3_0;
		if (L_3)
		{
			G_B5_0 = L_3;
			goto IL_001b;
		}
		G_B4_0 = L_3;
	}
	{
		List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632* L_4 = (List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632*)il2cpp_codegen_object_new(List_1_t4E99E1BC06BE8119C1E36E81286D0B9ED1AF7632_il2cpp_TypeInfo_var);
		List_1__ctor_mB3D397E89B7F8DBDF91EDDC6DAE293EE8D58CEA3(L_4, List_1__ctor_mB3D397E89B7F8DBDF91EDDC6DAE293EE8D58CEA3_RuntimeMethod_var);
		G_B5_0 = L_4;
	}

IL_001b:
	{
		return G_B5_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* AvatarManager_GetAvatarData_m0A7A6DCE60F4EB439BEB4C8D70B2D1368B1EDA3D (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, String_t* ___0_avatarId, const RuntimeMethod* method) 
{
	AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* G_B2_0 = NULL;
	AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* G_B1_0 = NULL;
	{
		AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* L_0 = __this->___avatarConfig;
		AvatarConfiguration_t4C3DE21A6E9C85062C9FD63E8A5A5D5A06101BFC* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000c;
		}
		G_B1_0 = L_1;
	}
	{
		return (AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277*)NULL;
	}

IL_000c:
	{
		String_t* L_2 = ___0_avatarId;
		NullCheck(G_B2_0);
		AvatarData_t6F2355B4A1847D861FB3A646BEDE9F17B9663277* L_3;
		L_3 = AvatarConfiguration_GetAvatarData_m699629AF5952D05D39A5F4DB1D1468C30B4A163D(G_B2_0, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_SaveAvatarSelection_mAAD4FB71AF913195BAE9B44D921A46A46A2B1BDC (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* V_0 = NULL;
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_0 = __this->___progressManager;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0032;
		}
	}
	{
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_2 = __this->___progressManager;
		NullCheck(L_2);
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_3;
		L_3 = GameProgressManager_GetShopProgress_m8B6B107D22FB71F7F940241E51BA57E4D74E727E(L_2, NULL);
		V_0 = L_3;
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_4 = V_0;
		String_t* L_5 = __this->___currentSelectedAvatarId;
		NullCheck(L_4);
		L_4->___selectedAvatarId = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_4->___selectedAvatarId), (void*)L_5);
		GameProgressManager_t0A12F1CAB8329FB5DEFE2AA1D74A1F32F3E1A2A0* L_6 = __this->___progressManager;
		ShopProgressData_tF1A881F9B18D0313C53714B647713D2998329156* L_7 = V_0;
		NullCheck(L_6);
		GameProgressManager_UpdateShopProgress_mFFECAB62E06A9F8EA165BF157E5FDC57F3CFED6B(L_6, L_7, NULL);
	}

IL_0032:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager_OnDestroy_mD27037027AE60DBDADB58712BAA63346F99C2E8C (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* L_0;
		L_0 = AvatarManager_get_Instance_m8D181A3A524D62906BF6B92CFDA124AE22C89BC1_inline(NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, __this, NULL);
		if (!L_1)
		{
			goto IL_0013;
		}
	}
	{
		AvatarManager_set_Instance_m712FFBAE63721B0A96B127EC1F0D3604DDFF27A8_inline((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D*)NULL, NULL);
	}

IL_0013:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AvatarManager__ctor_mA53426C22278C06A686E6A46761A986CB813DFC7 (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t U3CPrivateImplementationDetailsU3E_ComputeStringHash_m2D743935D227377D9AEBDCE3D9A724E3807A8D5A (String_t* ___0_s, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	int32_t V_1 = 0;
	{
		String_t* L_0 = ___0_s;
		if (!L_0)
		{
			goto IL_002a;
		}
	}
	{
		V_0 = ((int32_t)-2128831035);
		V_1 = 0;
		goto IL_0021;
	}

IL_000d:
	{
		String_t* L_1 = ___0_s;
		int32_t L_2 = V_1;
		NullCheck(L_1);
		Il2CppChar L_3;
		L_3 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_1, L_2, NULL);
		uint32_t L_4 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_multiply(((int32_t)((int32_t)L_3^(int32_t)L_4)), ((int32_t)16777619)));
		int32_t L_5 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_5, 1));
	}

IL_0021:
	{
		int32_t L_6 = V_1;
		String_t* L_7 = ___0_s;
		NullCheck(L_7);
		int32_t L_8;
		L_8 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_7, NULL);
		if ((((int32_t)L_6) < ((int32_t)L_8)))
		{
			goto IL_000d;
		}
	}

IL_002a:
	{
		uint32_t L_9 = V_0;
		return L_9;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void __JobReflectionRegistrationOutput__5800224013931567721_CreateJobReflectionData_mDF08400DD08C4241E3217A3EAB5C3A514DC1AAB4 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IJobExtensions_EarlyJobInit_TisCalculateFallJob_t12218BC3466B56D1F372DABE6E820A6676C90078_m4BDDFF3A5045B56D5489F4CD0340FC922BD5AE5D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IJobExtensions_EarlyJobInit_TisDetectMatchesJob_tE07BFE3943D37234CCA96A23B494634A5479E4FD_m6F6C36EC0F08D3ECF93B7B2E8B0F48925F2A9519_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IJobExtensions_EarlyJobInit_TisFindHintJob_tD765D2B4BD478CD3C28E1853DB36EBA43F92E388_m240E2BAC79A1C619D334A6043738AD05CA6CF3F7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IJobExtensions_EarlyJobInit_TisParseLevelJsonJob_t7241F5370B5A9BE7CD2B40CFBB4F05A2675AB552_m0FE87688F8511BE43D8D0B047F85850CFBA85B7E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IJobExtensions_EarlyJobInit_TisShuffleJob_tB72E45C0F54BBAF53B1DC8C152B42690022FC584_mD3A4478DA29AAB39D98EC98F122581CB2538B0CA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IJobExtensions_EarlyJobInit_TisShuffleJob_tE9A549CF475CC4946C934CFE41B0006ECC37A2DD_m6BF92EAF3C31DCBEACBF415B651D42788E5928D8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	try
	{
		IJobExtensions_EarlyJobInit_TisParseLevelJsonJob_t7241F5370B5A9BE7CD2B40CFBB4F05A2675AB552_m0FE87688F8511BE43D8D0B047F85850CFBA85B7E(IJobExtensions_EarlyJobInit_TisParseLevelJsonJob_t7241F5370B5A9BE7CD2B40CFBB4F05A2675AB552_m0FE87688F8511BE43D8D0B047F85850CFBA85B7E_RuntimeMethod_var);
		IJobExtensions_EarlyJobInit_TisCalculateFallJob_t12218BC3466B56D1F372DABE6E820A6676C90078_m4BDDFF3A5045B56D5489F4CD0340FC922BD5AE5D(IJobExtensions_EarlyJobInit_TisCalculateFallJob_t12218BC3466B56D1F372DABE6E820A6676C90078_m4BDDFF3A5045B56D5489F4CD0340FC922BD5AE5D_RuntimeMethod_var);
		IJobExtensions_EarlyJobInit_TisShuffleJob_tE9A549CF475CC4946C934CFE41B0006ECC37A2DD_m6BF92EAF3C31DCBEACBF415B651D42788E5928D8(IJobExtensions_EarlyJobInit_TisShuffleJob_tE9A549CF475CC4946C934CFE41B0006ECC37A2DD_m6BF92EAF3C31DCBEACBF415B651D42788E5928D8_RuntimeMethod_var);
		IJobExtensions_EarlyJobInit_TisDetectMatchesJob_tE07BFE3943D37234CCA96A23B494634A5479E4FD_m6F6C36EC0F08D3ECF93B7B2E8B0F48925F2A9519(IJobExtensions_EarlyJobInit_TisDetectMatchesJob_tE07BFE3943D37234CCA96A23B494634A5479E4FD_m6F6C36EC0F08D3ECF93B7B2E8B0F48925F2A9519_RuntimeMethod_var);
		IJobExtensions_EarlyJobInit_TisFindHintJob_tD765D2B4BD478CD3C28E1853DB36EBA43F92E388_m240E2BAC79A1C619D334A6043738AD05CA6CF3F7(IJobExtensions_EarlyJobInit_TisFindHintJob_tD765D2B4BD478CD3C28E1853DB36EBA43F92E388_m240E2BAC79A1C619D334A6043738AD05CA6CF3F7_RuntimeMethod_var);
		IJobExtensions_EarlyJobInit_TisShuffleJob_tB72E45C0F54BBAF53B1DC8C152B42690022FC584_mD3A4478DA29AAB39D98EC98F122581CB2538B0CA(IJobExtensions_EarlyJobInit_TisShuffleJob_tB72E45C0F54BBAF53B1DC8C152B42690022FC584_mD3A4478DA29AAB39D98EC98F122581CB2538B0CA_RuntimeMethod_var);
		goto IL_002f;
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_0024;
		}
		throw e;
	}

CATCH_0024:
	{
		Exception_t* L_0 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
		il2cpp_codegen_runtime_class_init_inline(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&EarlyInitHelpers_tA67F29CEEF85CD33340F1A46E13686C44F97695A_il2cpp_TypeInfo_var)));
		EarlyInitHelpers_JobReflectionDataCreationFailed_mD6AB08D5BB411CCE38A87793C3C7062EC91FD1EC(L_0, NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_002f;
	}

IL_002f:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void __JobReflectionRegistrationOutput__5800224013931567721_EarlyInit_mBE3752126500BBA39310F02C965DE7CA2A912229 (const RuntimeMethod* method) 
{
	{
		__JobReflectionRegistrationOutput__5800224013931567721_CreateJobReflectionData_mDF08400DD08C4241E3217A3EAB5C3A514DC1AAB4(NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AdsManager_get_IsInitialized_mA114A221EEDEF44409F6FABF3070C12BCCF6D748_inline (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CIsInitializedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AdConfig_get_AppKey_m9359E4F90FC1343439AB202C5EEA51B0454D9F89_inline (AdConfig_t80D1C0D2BD226B6F32829058D850E59BBF3A4190* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___androidAppKey;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 UniTask_GetAwaiter_mF05A09B81913BECFD58FC67A16C0251FFCCAC939_inline (UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* __this, const RuntimeMethod* method) 
{
	{
		Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956 L_0;
		memset((&L_0), 0, sizeof(L_0));
		Awaiter__ctor_m4154A3A6D62BB1657D17A8106633CD9E1CE51F04_inline((&L_0), __this, NULL);
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Awaiter_get_IsCompleted_m2D01E3AB8A7C5AA8AA1E1EF58D92A6A095C00B9A_inline (Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* L_0 = (UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270*)(&__this->___task);
		il2cpp_codegen_runtime_class_init_inline(UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270_il2cpp_TypeInfo_var);
		int32_t L_1;
		L_1 = UniTask_get_Status_mA15B0F13DE3CE36730357CF50F65AE99ADF564DA_inline(L_0, NULL);
		bool L_2;
		L_2 = UniTaskStatusExtensions_IsCompleted_mF43C41C9CEB640E381D1F7A8B40142843AED87AE_inline(L_1, NULL);
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Awaiter_GetResult_mC439993563D3BD49CEC67AAF6AFB3AEF72E916BD_inline (Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUniTaskSource_t5AF6C202B2616C72E989C90B6E76DFEC4118174D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* L_0 = (UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270*)(&__this->___task);
		RuntimeObject* L_1 = L_0->___source;
		if (L_1)
		{
			goto IL_000e;
		}
	}
	{
		return;
	}

IL_000e:
	{
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* L_2 = (UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270*)(&__this->___task);
		RuntimeObject* L_3 = L_2->___source;
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* L_4 = (UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270*)(&__this->___task);
		int16_t L_5 = L_4->___token;
		NullCheck(L_3);
		InterfaceActionInvoker1< int16_t >::Invoke(2, IUniTaskSource_t5AF6C202B2616C72E989C90B6E76DFEC4118174D_il2cpp_TypeInfo_var, L_3, L_5);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AsyncUniTaskMethodBuilder_SetException_m0D772D62D01CC371F4AB0F6943BBBE0FEAB19643_inline (AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0* __this, Exception_t* ___0_exception, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IStateMachineRunnerPromise_t5CE1FDF2F619E55F7DCFBE22E2B97835C6781C7B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->___runnerPromise;
		if (L_0)
		{
			goto IL_0010;
		}
	}
	{
		Exception_t* L_1 = ___0_exception;
		__this->___ex = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___ex), (void*)L_1);
		return;
	}

IL_0010:
	{
		RuntimeObject* L_2 = __this->___runnerPromise;
		Exception_t* L_3 = ___0_exception;
		NullCheck(L_2);
		InterfaceActionInvoker1< Exception_t* >::Invoke(3, IStateMachineRunnerPromise_t5CE1FDF2F619E55F7DCFBE22E2B97835C6781C7B_il2cpp_TypeInfo_var, L_2, L_3);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AsyncUniTaskMethodBuilder_SetResult_mBBA527F0F21E04D65A269C0D02597CE5B2D1E9CB_inline (AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IStateMachineRunnerPromise_t5CE1FDF2F619E55F7DCFBE22E2B97835C6781C7B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->___runnerPromise;
		if (!L_0)
		{
			goto IL_0013;
		}
	}
	{
		RuntimeObject* L_1 = __this->___runnerPromise;
		NullCheck(L_1);
		InterfaceActionInvoker0::Invoke(2, IStateMachineRunnerPromise_t5CE1FDF2F619E55F7DCFBE22E2B97835C6781C7B_il2cpp_TypeInfo_var, L_1);
	}

IL_0013:
	{
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ButtonClickedEvent_t8EA72E90B3BD1392FB3B3EF167D5121C23569E4C* Button_get_onClick_m701712A7F7F000CC80D517C4510697E15722C35C_inline (Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* __this, const RuntimeMethod* method) 
{
	{
		ButtonClickedEvent_t8EA72E90B3BD1392FB3B3EF167D5121C23569E4C* L_0 = __this->___m_OnClick;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* AdsManager_get_Instance_mE565526FA45F88FBD4F2D4BE2A9F4954C1C53B3A_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* L_0 = ((AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066_StaticFields*)il2cpp_codegen_static_fields_for(AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066_il2cpp_TypeInfo_var))->___U3CInstanceU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AdsManager_get_IsBannerVisible_m0520218B5030FBB61969F27EF24D998876B12A07_inline (AdsManager_tCCE9C0B4A7E9E1C310BFE1E43345D04CAD3B4066* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CIsBannerVisibleU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AdReward_get_Description_m3CEC0BEABF98834C5ED422D60D3C365E15C9120B_inline (AdReward_tD6C5592436EB3F2368B8E7C5D03E95D32E5CB4F2* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___description;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* BoosterManager_get_Instance_m2D6B03021BDA024FCE34BB36D3D56DD36F0E72CD_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* L_0 = ((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___U3CInstanceU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void BoosterManager_set_Instance_m632638C5325431D080248B2C9A7F82FBDB87FE9D_inline (BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26* L_0 = ___0_value;
		((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___U3CInstanceU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_StaticFields*)il2cpp_codegen_static_fields_for(BoosterManager_tCD8199C19A19F591CF0074D01DC97EDF9E5C5D26_il2cpp_TypeInfo_var))->___U3CInstanceU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* AvatarManager_get_Instance_m8D181A3A524D62906BF6B92CFDA124AE22C89BC1_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* L_0 = ((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___U3CInstanceU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AvatarManager_set_Instance_m712FFBAE63721B0A96B127EC1F0D3604DDFF27A8_inline (AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D* L_0 = ___0_value;
		((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___U3CInstanceU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_StaticFields*)il2cpp_codegen_static_fields_for(AvatarManager_t0983270A7AFD5B67CB0ABCDA53676A2975CF649D_il2cpp_TypeInfo_var))->___U3CInstanceU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline (String_t* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____stringLength;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AsyncUniTaskMethodBuilder_AwaitUnsafeOnCompleted_TisAwaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956_TisU3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8_mF89DAED1592C898552A8C38223683928ED1CDB6E_gshared_inline (AsyncUniTaskMethodBuilder_t490751EC621C472E098B12103AF16BC549912BB0* __this, Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* ___0_awaiter, U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8* ___1_stateMachine, const RuntimeMethod* method) 
{
	if (!il2cpp_rgctx_is_initialized(method))
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IStateMachineRunnerPromise_t5CE1FDF2F619E55F7DCFBE22E2B97835C6781C7B_il2cpp_TypeInfo_var);
		il2cpp_rgctx_method_init(method);
	}
	{
		RuntimeObject* L_0 = __this->___runnerPromise;
		if (L_0)
		{
			goto IL_0014;
		}
	}
	{
		U3CWaitForInitializationU3Ed__57_t0395545586C28B19232DA8E00EC98F24F4754EE8* L_1 = ___1_stateMachine;
		RuntimeObject** L_2 = (RuntimeObject**)(&__this->___runnerPromise);
		il2cpp_codegen_runtime_class_init_inline(il2cpp_rgctx_data(method->rgctx_data, 2));
		AsyncUniTask_1_SetStateMachine_mC01AE78C304544C088858602E092DF11755B2926(L_1, L_2, il2cpp_rgctx_method(method->rgctx_data, 1));
	}

IL_0014:
	{
		Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* L_3 = ___0_awaiter;
		RuntimeObject* L_4 = __this->___runnerPromise;
		NullCheck(L_4);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_5;
		L_5 = InterfaceFuncInvoker0< Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* >::Invoke(0, IStateMachineRunnerPromise_t5CE1FDF2F619E55F7DCFBE22E2B97835C6781C7B_il2cpp_TypeInfo_var, L_4);
		Awaiter_UnsafeOnCompleted_m6C4775F8F4F2BEFB8118EBBA1EA621440CE84D62_inline(L_3, L_5, il2cpp_rgctx_method(method->rgctx_data, 5));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->____current;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_m809450298141D527D3A4FFAF77AE69D9B08CC17F_gshared_inline (List_1_tDA4D291C60B1EFA9EA50BBA3367C657CC9410576* __this, int32_t ___0_item, const RuntimeMethod* method) 
{
	Int32EnumU5BU5D_t87B7DB802810C38016332669039EF42C487A081F* V_0 = NULL;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
		Int32EnumU5BU5D_t87B7DB802810C38016332669039EF42C487A081F* L_1 = __this->____items;
		V_0 = L_1;
		int32_t L_2 = __this->____size;
		V_1 = L_2;
		int32_t L_3 = V_1;
		Int32EnumU5BU5D_t87B7DB802810C38016332669039EF42C487A081F* L_4 = V_0;
		NullCheck(L_4);
		if ((!(((uint32_t)L_3) < ((uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length))))))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = V_1;
		__this->____size = ((int32_t)il2cpp_codegen_add(L_5, 1));
		Int32EnumU5BU5D_t87B7DB802810C38016332669039EF42C487A081F* L_6 = V_0;
		int32_t L_7 = V_1;
		int32_t L_8 = ___0_item;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (int32_t)L_8);
		return;
	}

IL_0034:
	{
		int32_t L_9 = ___0_item;
		List_1_AddWithResize_mAD159B09F28BD7914562E219CA52E2D4BDCF5530(__this, L_9, il2cpp_rgctx_method(method->klass->rgctx_data, 14));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_2_Invoke_m3BE7DD18C4D59E8D0ECBBA5ED30F084F842415DD_gshared_inline (Action_2_t7B1DA294236CEB03E01D057BD5D4E8DCFCBF1811* __this, int32_t ___0_arg1, int32_t ___1_arg2, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, int32_t, int32_t, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_arg1, ___1_arg2, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mBF7DDBCD230E9D28EDF45D3E65F907DE1AE0CCBC_gshared_inline (Action_1_tC926860F20D428DA3E93D6FBA36420E904DD903B* __this, int32_t ___0_obj, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, int32_t, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_obj, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) 
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_0 = NULL;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = __this->____items;
		V_0 = L_1;
		int32_t L_2 = __this->____size;
		V_1 = L_2;
		int32_t L_3 = V_1;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_4 = V_0;
		NullCheck(L_4);
		if ((!(((uint32_t)L_3) < ((uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length))))))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = V_1;
		__this->____size = ((int32_t)il2cpp_codegen_add(L_5, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = V_0;
		int32_t L_7 = V_1;
		RuntimeObject* L_8 = ___0_item;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (RuntimeObject*)L_8);
		return;
	}

IL_0034:
	{
		RuntimeObject* L_9 = ___0_item;
		List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4(__this, L_9, il2cpp_rgctx_method(method->klass->rgctx_data, 14));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_obj, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_2_Invoke_mAFC1A6B82DBD3B488FF63EE80C4D280D6979260F_gshared_inline (Action_2_t4A5313D1C1FEF099C0E5969104BDE957CD82CF22* __this, RuntimeObject* ___0_arg1, float ___1_arg2, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, RuntimeObject*, float, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_arg1, ___1_arg2, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Awaiter__ctor_m4154A3A6D62BB1657D17A8106633CD9E1CE51F04_inline (Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* __this, UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* ___0_task, const RuntimeMethod* method) 
{
	{
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* L_0 = ___0_task;
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 L_1 = (*(UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270*)L_0);
		__this->___task = L_1;
		Il2CppCodeGenWriteBarrier((void**)&(((&__this->___task))->___source), (void*)NULL);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t UniTask_get_Status_mA15B0F13DE3CE36730357CF50F65AE99ADF564DA_inline (UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUniTaskSource_t5AF6C202B2616C72E989C90B6E76DFEC4118174D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->___source;
		if (L_0)
		{
			goto IL_000a;
		}
	}
	{
		return (int32_t)(1);
	}

IL_000a:
	{
		RuntimeObject* L_1 = __this->___source;
		int16_t L_2 = __this->___token;
		NullCheck(L_1);
		int32_t L_3;
		L_3 = InterfaceFuncInvoker1< int32_t, int16_t >::Invoke(0, IUniTaskSource_t5AF6C202B2616C72E989C90B6E76DFEC4118174D_il2cpp_TypeInfo_var, L_1, L_2);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool UniTaskStatusExtensions_IsCompleted_mF43C41C9CEB640E381D1F7A8B40142843AED87AE_inline (int32_t ___0_status, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_status;
		return (bool)((!(((uint32_t)L_0) <= ((uint32_t)0)))? 1 : 0);
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Awaiter_UnsafeOnCompleted_m6C4775F8F4F2BEFB8118EBBA1EA621440CE84D62_inline (Awaiter_tFACD37B3B2ACD03C61DB949FD0E4FAB189A1D956* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_continuation, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AwaiterActions_t5D05CAC006FDEBCF6B65E2B9224BC4B44783BBE5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUniTaskSource_t5AF6C202B2616C72E989C90B6E76DFEC4118174D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* L_0 = (UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270*)(&__this->___task);
		RuntimeObject* L_1 = L_0->___source;
		if (L_1)
		{
			goto IL_0014;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_2 = ___0_continuation;
		NullCheck(L_2);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_2, NULL);
		return;
	}

IL_0014:
	{
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* L_3 = (UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270*)(&__this->___task);
		RuntimeObject* L_4 = L_3->___source;
		il2cpp_codegen_runtime_class_init_inline(AwaiterActions_t5D05CAC006FDEBCF6B65E2B9224BC4B44783BBE5_il2cpp_TypeInfo_var);
		Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* L_5 = ((AwaiterActions_t5D05CAC006FDEBCF6B65E2B9224BC4B44783BBE5_StaticFields*)il2cpp_codegen_static_fields_for(AwaiterActions_t5D05CAC006FDEBCF6B65E2B9224BC4B44783BBE5_il2cpp_TypeInfo_var))->___InvokeContinuationDelegate;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = ___0_continuation;
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270* L_7 = (UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270*)(&__this->___task);
		int16_t L_8 = L_7->___token;
		NullCheck(L_4);
		InterfaceActionInvoker3< Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87*, RuntimeObject*, int16_t >::Invoke(1, IUniTaskSource_t5AF6C202B2616C72E989C90B6E76DFEC4118174D_il2cpp_TypeInfo_var, L_4, L_5, L_6, L_8);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
