1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.PhantomTeam.PufflandAdventure"
4    android:installLocation="preferExternal"
5    android:versionCode="7"
6    android:versionName="0.2.01" >
7
8    <uses-sdk
9        android:minSdkVersion="23"
10        android:targetSdkVersion="36" />
11
12    <supports-screens
12-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:3-163
13        android:anyDensity="true"
13-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:135-160
14        android:largeScreens="true"
14-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:78-105
15        android:normalScreens="true"
15-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:49-77
16        android:smallScreens="true"
16-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:21-48
17        android:xlargeScreens="true" />
17-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:106-134
18
19    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
19-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-79
19-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-76
20    <uses-permission android:name="android.permission.VIBRATE" />
20-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-66
20-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:22-63
21    <uses-permission android:name="android.permission.INTERNET" />
21-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-67
21-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:22-64
22
23    <uses-feature android:glEsVersion="0x00030000" />
23-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-54
23-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:19-51
24    <uses-feature
24-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:5-15:36
25        android:name="android.hardware.touchscreen"
25-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:9-52
26        android:required="false" />
26-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-33
27    <uses-feature
27-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:5-18:36
28        android:name="android.hardware.touchscreen.multitouch"
28-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:9-63
29        android:required="false" />
29-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-33
30    <uses-feature
30-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:5-21:36
31        android:name="android.hardware.touchscreen.multitouch.distinct"
31-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:9-72
32        android:required="false" /> <!-- Permission will be merged into the manifest of the hosting app. -->
32-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-33
33    <!-- Is required to launch foreground extraction service for targetSdkVersion 28+. -->
34    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- Is required to launch foreground extraction service for targetSdkVersion 34+. -->
34-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:13:5-77
34-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:13:22-74
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
35-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:15:5-87
35-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:15:22-84
36
37    <queries>
37-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:8:5-12:15
38        <intent>
38-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:9:9-11:18
39            <action android:name="com.attribution.REFERRAL_PROVIDER" />
39-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:10:13-72
39-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:10:21-69
40        </intent>
41        <!-- For browser content -->
42        <intent>
42-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:38:9-44:18
43            <action android:name="android.intent.action.VIEW" />
43-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:39:13-65
43-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:39:21-62
44
45            <category android:name="android.intent.category.BROWSABLE" />
45-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:41:13-74
45-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:41:23-71
46
47            <data android:scheme="https" />
47-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:43:13-44
47-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:43:19-41
48        </intent> <!-- End of browser content -->
49        <!-- For CustomTabsService -->
50        <intent>
50-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:47:9-49:18
51            <action android:name="android.support.customtabs.action.CustomTabsService" />
51-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:48:13-90
51-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:48:21-87
52        </intent> <!-- End of CustomTabsService -->
53        <!-- For MRAID capabilities -->
54        <intent>
54-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:52:9-56:18
55            <action android:name="android.intent.action.INSERT" />
55-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:53:13-67
55-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:53:21-64
56
57            <data android:mimeType="vnd.android.cursor.dir/event" />
57-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:43:13-44
58        </intent>
59        <intent>
59-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:57:9-61:18
60            <action android:name="android.intent.action.VIEW" />
60-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:39:13-65
60-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:39:21-62
61
62            <data android:scheme="sms" />
62-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:43:13-44
62-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:43:19-41
63        </intent>
64        <intent>
64-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:62:9-66:18
65            <action android:name="android.intent.action.DIAL" />
65-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:63:13-65
65-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:63:21-62
66
67            <data android:path="tel:" />
67-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:43:13-44
68        </intent>
69
70        <package android:name="com.google.android.gms" />
70-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:8:9-58
70-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:8:18-55
71        <package android:name="com.android.vending" />
71-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:9:9-55
71-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:9:18-52
72    </queries>
73
74    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
74-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:14:5-79
74-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:14:22-76
75    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
75-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:17:5-83
75-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:17:22-80
76    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
76-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:18:5-88
76-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:18:22-85
77    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
77-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:27:5-82
77-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:27:22-79
78    <uses-permission android:name="android.permission.WAKE_LOCK" />
78-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\792437666b45f157dc6abd83a2eb64ab\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
78-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\792437666b45f157dc6abd83a2eb64ab\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
79
80    <permission
80-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
81        android:name="com.PhantomTeam.PufflandAdventure.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
81-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
82        android:protectionLevel="signature" />
82-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
83
84    <uses-permission android:name="com.PhantomTeam.PufflandAdventure.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
84-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
84-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
85
86    <application
86-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:3-126
87        android:allowBackup="false"
87-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:9-36
88        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
88-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
89        android:enableOnBackInvokedCallback="false"
89-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:9-52
90        android:extractNativeLibs="true"
91        android:icon="@mipmap/app_icon"
91-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:16-47
92        android:label="@string/app_name"
92-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:48-80
93        android:roundIcon="@mipmap/app_icon_round"
93-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:81-123
94        android:usesCleartextTraffic="false" >
94-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:9-45
95        <meta-data
95-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:9-30:33
96            android:name="unity.splash-mode"
96-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-45
97            android:value="0" />
97-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-30
98        <meta-data
98-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:9-33:36
99            android:name="unity.splash-enable"
99-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:32:13-47
100            android:value="True" />
100-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:13-33
101        <meta-data
101-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:9-36:36
102            android:name="unity.launch-fullscreen"
102-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-51
103            android:value="True" />
103-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-33
104        <meta-data
104-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:9-39:36
105            android:name="unity.render-outside-safearea"
105-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:13-57
106            android:value="True" />
106-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:13-33
107        <meta-data
107-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:9-42:50
108            android:name="notch.config"
108-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:13-40
109            android:value="portrait|landscape" />
109-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:13-47
110        <meta-data
110-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:9-45:36
111            android:name="unity.auto-report-fully-drawn"
111-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-57
112            android:value="true" />
112-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-33
113        <meta-data
113-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:46:9-48:36
114            android:name="unity.auto-set-game-state"
114-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:47:13-53
115            android:value="true" />
115-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:48:13-33
116        <meta-data
116-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:49:9-51:36
117            android:name="unity.strip-engine-code"
117-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:50:13-51
118            android:value="true" />
118-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:51:13-33
119
120        <activity
120-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:53:9-87:20
121            android:name="com.unity3d.player.appui.AppUIGameActivity"
121-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:54:13-70
122            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
122-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:55:13-194
123            android:exported="true"
123-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:56:13-36
124            android:hardwareAccelerated="false"
124-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:57:13-48
125            android:launchMode="singleTask"
125-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:58:13-44
126            android:resizeableActivity="true"
126-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:59:13-46
127            android:screenOrientation="userPortrait"
127-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:60:13-53
128            android:theme="@style/BaseUnityGameActivityTheme" >
128-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:61:13-62
129            <intent-filter>
129-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:62:13-66:29
130                <category android:name="android.intent.category.LAUNCHER" />
130-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:63:17-77
130-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:63:27-74
131
132                <action android:name="android.intent.action.MAIN" />
132-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:65:17-69
132-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:65:25-66
133            </intent-filter>
134
135            <meta-data
135-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:68:13-70:40
136                android:name="unityplayer.UnityActivity"
136-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:69:17-57
137                android:value="true" />
137-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:70:17-37
138            <meta-data
138-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:71:13-73:40
139                android:name="android.app.lib_name"
139-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:72:17-52
140                android:value="game" />
140-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:73:17-37
141            <meta-data
141-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:74:13-76:71
142                android:name="WindowManagerPreference:FreeformWindowSize"
142-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:75:17-74
143                android:value="@string/FreeformWindowSize_maximize" />
143-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:76:17-68
144            <meta-data
144-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:77:13-79:78
145                android:name="WindowManagerPreference:FreeformWindowOrientation"
145-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:78:17-81
146                android:value="@string/FreeformWindowOrientation_portrait" />
146-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:79:17-75
147            <meta-data
147-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:80:13-82:40
148                android:name="notch_support"
148-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:81:17-45
149                android:value="true" />
149-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:82:17-37
150
151            <layout
151-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:84:13-86:44
152                android:minHeight="300px"
152-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:85:17-42
153                android:minWidth="400px" />
153-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:86:17-41
154        </activity> <!-- The services will be merged into the manifest of the hosting app. -->
155        <service
155-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:20:9-27:19
156            android:name="com.google.android.play.core.assetpacks.AssetPackExtractionService"
156-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:21:13-94
157            android:enabled="false"
157-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:22:13-36
158            android:exported="true" >
158-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:23:13-36
159            <meta-data
159-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:24:13-26:41
160                android:name="com.google.android.play.core.assetpacks.versionCode"
160-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:25:17-83
161                android:value="20100" />
161-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:26:17-38
162        </service>
163        <service
163-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:28:9-32:56
164            android:name="com.google.android.play.core.assetpacks.ExtractionForegroundService"
164-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:29:13-95
165            android:enabled="false"
165-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:30:13-36
166            android:exported="false"
166-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:31:13-37
167            android:foregroundServiceType="dataSync" /> <!-- The activities will be merged into the manifest of the hosting app. -->
167-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:32:13-53
168        <activity
168-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
169            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
169-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
170            android:exported="false"
170-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
171            android:stateNotNeeded="true"
171-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
172            android:theme="@style/Theme.PlayCore.Transparent" />
172-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
173        <activity
173-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:9:9-17:20
174            android:name="com.google.games.bridge.GenericResolutionActivity"
174-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:10:13-77
175            android:exported="false"
175-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:11:13-37
176            android:label="GenericResolutionActivity"
176-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:12:13-54
177            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" >
177-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:13:13-83
178            <meta-data
178-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:14:13-16:40
179                android:name="instantapps.clients.allowed"
179-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:15:17-59
180                android:value="true" />
180-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:16:17-37
181        </activity>
182        <activity
182-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:21:9-26:74
183            android:name="com.unity3d.services.ads.adunit.AdUnitActivity"
183-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:22:13-74
184            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
184-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:23:13-170
185            android:exported="false"
185-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:24:13-37
186            android:hardwareAccelerated="true"
186-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:25:13-47
187            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
187-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:26:13-71
188        <activity
188-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:27:9-32:86
189            android:name="com.unity3d.services.ads.adunit.AdUnitTransparentActivity"
189-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:28:13-85
190            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
190-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:29:13-170
191            android:exported="false"
191-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:30:13-37
192            android:hardwareAccelerated="true"
192-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:31:13-47
193            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
193-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:32:13-83
194        <activity
194-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:33:9-38:86
195            android:name="com.unity3d.services.ads.adunit.AdUnitTransparentSoftwareActivity"
195-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:34:13-93
196            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
196-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:35:13-170
197            android:exported="false"
197-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:36:13-37
198            android:hardwareAccelerated="false"
198-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:37:13-48
199            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
199-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:38:13-83
200        <activity
200-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:39:9-44:74
201            android:name="com.unity3d.services.ads.adunit.AdUnitSoftwareActivity"
201-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:40:13-82
202            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
202-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:41:13-170
203            android:exported="false"
203-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:42:13-37
204            android:hardwareAccelerated="false"
204-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:43:13-48
205            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
205-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:44:13-71
206        <activity
206-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:45:9-50:74
207            android:name="com.unity3d.ads.adplayer.FullScreenWebViewDisplay"
207-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:46:13-77
208            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
208-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:47:13-170
209            android:exported="false"
209-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:48:13-37
210            android:hardwareAccelerated="true"
210-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:49:13-47
211            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
211-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:50:13-71
212
213        <provider
213-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:52:9-62:20
214            android:name="androidx.startup.InitializationProvider"
214-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:53:13-67
215            android:authorities="com.PhantomTeam.PufflandAdventure.androidx-startup"
215-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:54:13-68
216            android:exported="false" >
216-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
217            <meta-data
217-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:56:13-58:52
218                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
218-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:57:17-78
219                android:value="androidx.startup" />
219-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:58:17-49
220            <meta-data
220-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:59:13-61:52
221                android:name="com.unity3d.services.core.configuration.AdsSdkInitializer"
221-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:60:17-89
222                android:value="androidx.startup" />
222-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:61:17-49
223            <meta-data
223-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
224                android:name="androidx.emoji2.text.EmojiCompatInitializer"
224-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
225                android:value="androidx.startup" />
225-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
226            <meta-data
226-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
227                android:name="androidx.work.WorkManagerInitializer"
227-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
228                android:value="androidx.startup" />
228-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
229            <meta-data
229-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
230                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
230-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
231                android:value="androidx.startup" />
231-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
232        </provider>
233
234        <activity
234-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:12:9-16:63
235            android:name="com.ironsource.sdk.controller.ControllerActivity"
235-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:13:13-76
236            android:configChanges="orientation|screenSize"
236-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:14:13-59
237            android:hardwareAccelerated="true"
237-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:15:13-47
238            android:theme="@android:style/Theme.NoTitleBar" />
238-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:16:13-60
239        <activity
239-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:17:9-21:75
240            android:name="com.ironsource.sdk.controller.InterstitialActivity"
240-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:18:13-78
241            android:configChanges="orientation|screenSize"
241-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:19:13-59
242            android:hardwareAccelerated="true"
242-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:20:13-47
243            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
243-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:21:13-72
244        <activity
244-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:22:9-26:75
245            android:name="com.ironsource.sdk.controller.OpenUrlActivity"
245-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:23:13-73
246            android:configChanges="orientation|screenSize"
246-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:24:13-59
247            android:hardwareAccelerated="true"
247-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:25:13-47
248            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
248-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:26:13-72
249        <activity
249-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:27:9-36:20
250            android:name="com.ironsource.mediationsdk.testSuite.TestSuiteActivity"
250-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:28:13-83
251            android:configChanges="orientation|screenSize"
251-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:29:13-59
252            android:exported="false"
252-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:30:13-37
253            android:hardwareAccelerated="true"
253-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:31:13-47
254            android:theme="@android:style/Theme.NoTitleBar" >
254-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:32:13-60
255            <meta-data
255-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:33:13-35:40
256                android:name="android.webkit.WebView.EnableSafeBrowsing"
256-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:34:17-73
257                android:value="true" />
257-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:35:17-37
258        </activity>
259
260        <provider
260-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:38:9-41:40
261            android:name="com.ironsource.lifecycle.IronsourceLifecycleProvider"
261-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:39:13-80
262            android:authorities="com.PhantomTeam.PufflandAdventure.IronsourceLifecycleProvider"
262-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:40:13-79
263            android:exported="false" />
263-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:41:13-37
264        <provider
264-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:42:9-45:40
265            android:name="com.ironsource.lifecycle.LevelPlayActivityLifecycleProvider"
265-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:43:13-87
266            android:authorities="com.PhantomTeam.PufflandAdventure.LevelPlayActivityLifecycleProvider"
266-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:44:13-86
267            android:exported="false" /> <!-- AdMob -->
267-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:45:13-37
268        <!-- As Requiered By Admob please add your App ID -->
269        <!-- <meta-data -->
270        <!-- android:name="com.google.android.gms.ads.APPLICATION_ID" -->
271        <!-- android:value="YOUR_ADMOB_APP_ID"/> -->
272        <meta-data
272-->[:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-20:70
273            android:name="com.google.android.gms.ads.APPLICATION_ID"
273-->[:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-69
274            android:value="ca-app-pub-5894990702008734~3511741656" /> <!-- The space in these forces it to be interpreted as a string vs. int -->
274-->[:unityLibrary:IronSource.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-67
275        <meta-data
275-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:9-18:48
276            android:name="com.google.android.gms.games.APP_ID"
276-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-63
277            android:value="\u00327067140321" /> <!-- Keep track of which plugin is being used -->
277-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-45
278        <meta-data
278-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-23:42
279            android:name="com.google.android.gms.games.unityVersion"
279-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-69
280            android:value="\u0032.0.0" />
280-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-39
281
282        <activity
282-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:9-27:86
283            android:name="com.google.games.bridge.NativeBridgeActivity"
283-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:13-72
284            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
284-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-83
285        <activity
285-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:73:9-78:43
286            android:name="com.google.android.gms.ads.AdActivity"
286-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:74:13-65
287            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
287-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:75:13-122
288            android:exported="false"
288-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:76:13-37
289            android:theme="@android:style/Theme.Translucent" />
289-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:77:13-61
290
291        <provider
291-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:80:9-85:43
292            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
292-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:81:13-76
293            android:authorities="com.PhantomTeam.PufflandAdventure.mobileadsinitprovider"
293-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:82:13-73
294            android:exported="false"
294-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:83:13-37
295            android:initOrder="100" />
295-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:84:13-36
296
297        <service
297-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:87:9-91:43
298            android:name="com.google.android.gms.ads.AdService"
298-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:88:13-64
299            android:enabled="true"
299-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:89:13-35
300            android:exported="false" />
300-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:90:13-37
301
302        <activity
302-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:93:9-97:43
303            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
303-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:94:13-82
304            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
304-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:95:13-122
305            android:exported="false" />
305-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:96:13-37
306        <activity
306-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:98:9-105:43
307            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
307-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:99:13-82
308            android:excludeFromRecents="true"
308-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:100:13-46
309            android:exported="false"
309-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:101:13-37
310            android:launchMode="singleTask"
310-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:102:13-44
311            android:taskAffinity=""
311-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:103:13-36
312            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
312-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:104:13-72
313
314        <meta-data
314-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:107:9-109:36
315            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
315-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:108:13-79
316            android:value="true" />
316-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:109:13-33
317        <meta-data
317-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:110:9-112:36
318            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
318-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:111:13-83
319            android:value="true" />
319-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c0b17fbb70774091137ec51e7399cbb4\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:112:13-33
320
321        <provider
321-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:13:9-17:38
322            android:name="com.google.android.gms.games.provider.PlayGamesInitProvider"
322-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:14:13-87
323            android:authorities="com.PhantomTeam.PufflandAdventure.playgamesinitprovider"
323-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:15:13-73
324            android:exported="false"
324-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:16:13-37
325            android:initOrder="99" />
325-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:17:13-35
326
327        <activity
327-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:19:9-22:62
328            android:name="com.google.android.gms.games.internal.v2.resolution.GamesResolutionActivity"
328-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:20:13-103
329            android:exported="false"
329-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:21:13-37
330            android:theme="@style/Theme.Games.Transparent" />
330-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:22:13-59
331        <activity
331-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:23:9-26:62
332            android:name="com.google.android.gms.games.internal.v2.appshortcuts.PlayGamesAppShortcutsActivity"
332-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:24:13-111
333            android:exported="true"
333-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:25:13-36
334            android:theme="@style/Theme.Games.Transparent" />
334-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:26:13-59
335
336        <meta-data
336-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:28:9-30:62
337            android:name="com.google.android.gms.games.version"
337-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:29:13-64
338            android:value="@string/play_games_sdk_version" />
338-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:30:13-59
339
340        <service
340-->[com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:10:9-13:105
341            android:name="com.google.android.gms.nearby.exposurenotification.WakeUpService"
341-->[com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:11:13-92
342            android:exported="true"
342-->[com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:12:13-36
343            android:permission="com.google.android.gms.nearby.exposurenotification.EXPOSURE_CALLBACK" />
343-->[com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:13:13-102
344
345        <activity
345-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
346            android:name="com.google.android.gms.common.api.GoogleApiActivity"
346-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
347            android:exported="false"
347-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
348            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
348-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
349
350        <meta-data
350-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
351            android:name="com.google.android.gms.version"
351-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
352            android:value="@integer/google_play_services_version" />
352-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
353
354        <uses-library
354-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\d45fed3a22ef80b0d59961fd3893403c\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
355            android:name="android.ext.adservices"
355-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\d45fed3a22ef80b0d59961fd3893403c\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
356            android:required="false" />
356-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\d45fed3a22ef80b0d59961fd3893403c\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
357
358        <service
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
359            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
360            android:directBootAware="false"
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
361            android:enabled="@bool/enable_system_alarm_service_default"
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
362            android:exported="false" />
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
363        <service
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
364            android:name="androidx.work.impl.background.systemjob.SystemJobService"
364-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
365            android:directBootAware="false"
365-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
366            android:enabled="@bool/enable_system_job_service_default"
366-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
367            android:exported="true"
367-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
368            android:permission="android.permission.BIND_JOB_SERVICE" />
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
369        <service
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
370            android:name="androidx.work.impl.foreground.SystemForegroundService"
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
371            android:directBootAware="false"
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
372            android:enabled="@bool/enable_system_foreground_service_default"
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
373            android:exported="false" />
373-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
374
375        <receiver
375-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
376            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
377            android:directBootAware="false"
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
378            android:enabled="true"
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
379            android:exported="false" />
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
380        <receiver
380-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
381            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
381-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
382            android:directBootAware="false"
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
383            android:enabled="false"
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
384            android:exported="false" >
384-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
385            <intent-filter>
385-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
386                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
387                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
388            </intent-filter>
389        </receiver>
390        <receiver
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
391            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
391-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
392            android:directBootAware="false"
392-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
393            android:enabled="false"
393-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
394            android:exported="false" >
394-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
395            <intent-filter>
395-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
396                <action android:name="android.intent.action.BATTERY_OKAY" />
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
397                <action android:name="android.intent.action.BATTERY_LOW" />
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
398            </intent-filter>
399        </receiver>
400        <receiver
400-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
401            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
401-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
402            android:directBootAware="false"
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
403            android:enabled="false"
403-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
404            android:exported="false" >
404-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
405            <intent-filter>
405-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
406                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
407                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
408            </intent-filter>
409        </receiver>
410        <receiver
410-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
411            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
411-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
412            android:directBootAware="false"
412-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
413            android:enabled="false"
413-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
414            android:exported="false" >
414-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
415            <intent-filter>
415-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
416                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
416-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
416-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
417            </intent-filter>
418        </receiver>
419        <receiver
419-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
420            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
420-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
421            android:directBootAware="false"
421-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
422            android:enabled="false"
422-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
423            android:exported="false" >
423-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
424            <intent-filter>
424-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
425                <action android:name="android.intent.action.BOOT_COMPLETED" />
425-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
425-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
426                <action android:name="android.intent.action.TIME_SET" />
426-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
426-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
427                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
428            </intent-filter>
429        </receiver>
430        <receiver
430-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
431            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
431-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
432            android:directBootAware="false"
432-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
433            android:enabled="@bool/enable_system_alarm_service_default"
433-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
434            android:exported="false" >
434-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
435            <intent-filter>
435-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
436                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
437            </intent-filter>
438        </receiver>
439        <receiver
439-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
440            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
440-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
441            android:directBootAware="false"
441-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
442            android:enabled="true"
442-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
443            android:exported="true"
443-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
444            android:permission="android.permission.DUMP" >
444-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
445            <intent-filter>
445-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
446                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
446-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
446-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
447            </intent-filter>
448        </receiver>
449        <receiver
449-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
450            android:name="androidx.profileinstaller.ProfileInstallReceiver"
450-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
451            android:directBootAware="false"
451-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
452            android:enabled="true"
452-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
453            android:exported="true"
453-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
454            android:permission="android.permission.DUMP" >
454-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
455            <intent-filter>
455-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
456                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
456-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
456-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
457            </intent-filter>
458            <intent-filter>
458-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
459                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
459-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
459-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
460            </intent-filter>
461            <intent-filter>
461-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
462                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
462-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
462-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
463            </intent-filter>
464            <intent-filter>
464-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
465                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
465-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
465-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
466            </intent-filter>
467        </receiver>
468
469        <service
469-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
470            android:name="androidx.room.MultiInstanceInvalidationService"
470-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
471            android:directBootAware="true"
471-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
472            android:exported="false" />
472-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
473    </application>
474
475</manifest>
