﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void ITilemap__ctor_m3281F6903F18F9B867E6B81E18BCCD0828084258 (void);
extern void ITilemap_GetTile_mA6D6CD833634FA3B4A4F38D3904AB5BE80C31E65 (void);
extern void ITilemap_RefreshTile_m4C4B0A062A13E986BD20AA87F056982D67FAF69D (void);
extern void ITilemap_CreateInstance_m63D3D1EDDCA86A1F1A116A2205D798AD2BAF3E96 (void);
extern void ITilemap_FindAllRefreshPositions_m681FAC77C526640B18549097C961C85EA5846CCC (void);
extern void ITilemap_GetAllTileData_m3B6CF200F925322F951314FE8336C581A782F804 (void);
extern void Tile_get_sprite_m3324CBA00505C3C95DA57FC3A6F8B0D5FA2EF553 (void);
extern void Tile_set_sprite_mD9F351775FDFDFFA0FCC40121B4C54D566052D18 (void);
extern void Tile_get_color_mD50E790F486A1E64757E9471D48BA42FC9ECCE4C (void);
extern void Tile_set_color_m9D76C21865CA89F39FF56C112CB13AFD45CD8B69 (void);
extern void Tile_get_transform_mFA119A0C353E4E75C92C8BE829C6BDFA40F17643 (void);
extern void Tile_set_transform_m2E46927D29823DBDC3B7B36E013845006075EB02 (void);
extern void Tile_get_gameObject_m8B1B09FD1B6B5A0402D63D3AFF139C6078754077 (void);
extern void Tile_set_gameObject_mD4C82AFCA4B96D44BE5549CFF9E0F36218A4ECE9 (void);
extern void Tile_get_flags_m4AC2E9F8CF43DB83E9F8389EFDF7E6111E5A9806 (void);
extern void Tile_set_flags_mE221D85F2B767EC5C3D473266CB7AABD66674DEA (void);
extern void Tile_get_colliderType_mDB7A2E3BEF055617F6AC198841938B79C289E967 (void);
extern void Tile_set_colliderType_m21E434F55E4CC8AEB867E7FCF88821EFFC9CEB3F (void);
extern void Tile_GetTileData_m187B4A0A655AAB70CC8EC203F78E4777ABB96D4E (void);
extern void Tile__ctor_m1680C25E80E5ACCB156133C14199BD5BFE00EA5E (void);
extern void TileBase_RefreshTile_m7302220B588658247D635871B92DBFF7708E2224 (void);
extern void TileBase_GetTileData_m04B3B474F4DBF88997FF29ABA115A2FFB91BAF81 (void);
extern void TileBase_GetTileDataNoRef_m657510B6853906E397D8FC7E6F1A8B2DC4B34397 (void);
extern void TileBase_GetTileAnimationData_m8E1C84B8BC0B38E978ECEE6C7AD50D7D8BF810FE (void);
extern void TileBase_GetTileAnimationDataNoRef_m061D2FB92E28E5C2379385827F78C22719287D97 (void);
extern void TileBase_GetTileAnimationDataRef_m10D856F55369986224F166E8EEF5633EB8EBA5C3 (void);
extern void TileBase_StartUp_mBAF37DBB4DCC7BDB384352D93AB609CEB0E2E78B (void);
extern void TileBase_StartUpRef_mB00DB38868F87645811DE4784F57278388FAEEF9 (void);
extern void TileBase__ctor_mBFD0A0ACF9DB1F08783B9F3F35D4E61C9205D4A2 (void);
extern void Tilemap_get_bufferSyncTile_m5506F240CC262FD454CFF9B547F16530F9506B1D (void);
extern void Tilemap_HasLoopEndedForTileAnimationCallback_m6A4AA954E8521E7BFC71B0A44B60DFDE7F7F51BD (void);
extern void Tilemap_HandleLoopEndedForTileAnimationCallback_m7BA8FF295BA812311BF3854DB2978EAE03B2F9C1 (void);
extern void Tilemap_SendLoopEndedForTileAnimationCallback_m8D1E624097F94B218E9675EC3AA121C0F22CB6BA (void);
extern void Tilemap_HasSyncTileCallback_m522AE13C1DEBDDA7EBC7C9BAF1302EB75EF3A0EB (void);
extern void Tilemap_HasPositionsChangedCallback_mD02A9A567086C4F60CA7B4733EAFD173289857FF (void);
extern void Tilemap_HandleSyncTileCallback_mF1D8059E6F8ED90041313259D5DCFC3DBEB8750A (void);
extern void Tilemap_HandlePositionsChangedCallback_mCEC3B01A5328F6C83163C25CE9EDCD87E5895CD0 (void);
extern void Tilemap_SendTilemapTileChangedCallback_m66E5D12B134C48E57EF4C1B29658CD61B75366EF (void);
extern void Tilemap_SendTilemapPositionsChangedCallback_m8F1D0E0F18A797349A83465F5E68DF01972D75D4 (void);
extern void Tilemap_GetCellCenterWorld_m567FBE8E0822C9A75681D3B95AD9FDDF3C43A4F3 (void);
extern void Tilemap_get_cellBounds_m2C1EDCFFD145175A83457B4F7A88CEA037DF8EB9 (void);
extern void Tilemap_get_animationFrameRate_m391ACF664A9239DCB5A3344AE6A27A6D924234AE (void);
extern void Tilemap_get_origin_mB5E10582CFAA76144BB44DECAADB84E904D02E55 (void);
extern void Tilemap_get_size_m8B9F0C2CC3CD37626AE921047DA5DC239B3F00EA (void);
extern void Tilemap_get_tileAnchor_mD3C7F2A024DC43019CEB93682307ED41EC3329E4 (void);
extern void Tilemap_get_orientationMatrix_mF63DF1E4FC7E4B7DE10CE67DFDBB130262784F24 (void);
extern void Tilemap_GetTileAsset_m3B9C96C2E2488141C4F6EBD52C6D807C801C6922 (void);
extern void Tilemap_GetTile_m8500FBFF853C9E813810929BD29D7A866B516225 (void);
extern void Tilemap_SetTileAsset_m88D70B08B3D291F99EB34F01136C2D4EEBE45D4B (void);
extern void Tilemap_SetTile_m880BD0CC6B69A4B15495C4FBD2CBEA50D1BE23BA (void);
extern void Tilemap_RefreshTile_mEF4F94212FD9B311431DFFAFE092A4A6EBA580DF (void);
extern void Tilemap_RefreshTilesNative_mD73E77DFD7C808A3665CA8389F728CBC08A52232 (void);
extern void Tilemap_RefreshAllTiles_mA64BB2AFE77727C6358ACDA467A7B082A0034A9E (void);
extern void Tilemap_GetUsedTilesCount_mF75EB807D49AEF1AA1748984D238B55946A4DD4A (void);
extern void Tilemap_GetUsedTilesNonAlloc_m4FEDE5F12A7A1333A4A2082637CFEC76B7903E14 (void);
extern void Tilemap_Internal_GetUsedTilesNonAlloc_m79F745C755C26362833B0F0CC8C5846547EF5C4F (void);
extern void Tilemap_ClearAllTiles_m440B00506358103B65F7A2FE3B3AC44F621FE5B6 (void);
extern void Tilemap_GetLoopEndedForTileAnimationCallbackSettings_mB19F0933D4E0BCBD4EC9BE836B064D25997FDDC8 (void);
extern void Tilemap_DoLoopEndedForTileAnimationCallback_m30B429EAB1964CD0CD09E3043AA926DE715B2763 (void);
extern void Tilemap_GetSyncTileCallbackSettings_m1630BBFA37F85D2E29E73EA92DB13C700CC86B29 (void);
extern void Tilemap_DoSyncTileCallback_m7BF07E7C678E7A55BDF116FA7C5BEF29963402A2 (void);
extern void Tilemap_DoPositionsChangedCallback_mCD3C79A37783BB7DD22454981E0B51394B7990F4 (void);
extern void Tilemap__ctor_m1D0F3884F418FC0D39DE07F85E356B9A733F138C (void);
extern void Tilemap_get_animationFrameRate_Injected_m853E4D792F977520F9AF25B875050F0E160CCF45 (void);
extern void Tilemap_get_origin_Injected_mE1614FE27564626B3726F24F6CC284204A1BDEA2 (void);
extern void Tilemap_get_size_Injected_mE4A4FAFA70C3B5BA87CF8E64CD0BCCB8E11323EA (void);
extern void Tilemap_get_tileAnchor_Injected_m7D8067BA5CD69E8B8807AFD721E9BD732E5B8187 (void);
extern void Tilemap_get_orientationMatrix_Injected_mD54025C0654F2E07D74364E44A287136074F8646 (void);
extern void Tilemap_GetTileAsset_Injected_mC4D3F18DDF3496564747BCD4E56631E21456093C (void);
extern void Tilemap_SetTileAsset_Injected_mC24A398B065AFB28E88A51F38DA4762DD5DCBD4E (void);
extern void Tilemap_RefreshTile_Injected_m9EF6875C16B8F425284A6E681CD466C09B753643 (void);
extern void Tilemap_RefreshTilesNative_Injected_m1CA76F21817AE189F616BA8585D219973222FAF6 (void);
extern void Tilemap_RefreshAllTiles_Injected_m5F81411F270FA246D9A5664AD077E338FA20A538 (void);
extern void Tilemap_GetUsedTilesCount_Injected_mF9233C510EFA2441B71C2648A0FB4EF03D07E8D1 (void);
extern void Tilemap_Internal_GetUsedTilesNonAlloc_Injected_m56C0D831A7D75EAF3FC3B3360D82EF7921C947C2 (void);
extern void Tilemap_ClearAllTiles_Injected_mA1907919D0EAE3FCD14370073669922F0944E530 (void);
extern void TilemapRenderer_RegisterSpriteAtlasRegistered_m5D7676A05B0B16ABCCF4CEE57BA9E28FA4D171BC (void);
extern void TilemapRenderer_UnregisterSpriteAtlasRegistered_mFE33C972CF738A50A631203D0DD7E325AADFCB08 (void);
extern void TilemapRenderer_OnSpriteAtlasRegistered_m4348D78754045C8B10CEA76195A313790F412ED1 (void);
extern void TilemapRenderer_OnSpriteAtlasRegistered_Injected_mEA941D5042FADDC2E5457247D2DF55324C143D9A (void);
extern void TileData_set_sprite_m3566544847F9C9C27EDB154324B6FBDB446EFE94 (void);
extern void TileData_set_color_m5E759823878243A226EF46419FAD7C0CC3D5F40A (void);
extern void TileData_set_transform_m71074A780C066292F940002A7165658E9CC01F9F (void);
extern void TileData_set_gameObject_m1CE5B2AAAB5BF5AEF36EBAF2BCE23E4D2E5A9E09 (void);
extern void TileData_set_flags_mEB46B1364D6DB7F77C2E1E43AFD31381B291BD30 (void);
extern void TileData_set_colliderType_mE12359ADEF5F42CC0B635DCBAEC3035F0526FA96 (void);
extern void TileData_CreateDefault_m13A39001A47B60635B10FFD06AD65082CBB7D12D (void);
extern void TileData__cctor_mE2F5A802075C68DE978E46092DC8BF465182934A (void);
extern void TileAnimationData_set_animatedSprites_m315FE8DAB5071E1FA594AEA74B1B66BBF6A5C3E1 (void);
extern void TileAnimationData_set_animationSpeed_mE1DB382A9D7F0385D70248A93B998405890D4611 (void);
extern void TileAnimationData_set_animationStartTime_mBC2F61289403253C6B43C12576A98654B94A9B40 (void);
extern void TileAnimationData_set_flags_m5C1157264844D313EB81E33FFD5CB7EAE165603D (void);
static Il2CppMethodPointer s_methodPointers[93] = 
{
	ITilemap__ctor_m3281F6903F18F9B867E6B81E18BCCD0828084258,
	ITilemap_GetTile_mA6D6CD833634FA3B4A4F38D3904AB5BE80C31E65,
	ITilemap_RefreshTile_m4C4B0A062A13E986BD20AA87F056982D67FAF69D,
	NULL,
	ITilemap_CreateInstance_m63D3D1EDDCA86A1F1A116A2205D798AD2BAF3E96,
	ITilemap_FindAllRefreshPositions_m681FAC77C526640B18549097C961C85EA5846CCC,
	ITilemap_GetAllTileData_m3B6CF200F925322F951314FE8336C581A782F804,
	Tile_get_sprite_m3324CBA00505C3C95DA57FC3A6F8B0D5FA2EF553,
	Tile_set_sprite_mD9F351775FDFDFFA0FCC40121B4C54D566052D18,
	Tile_get_color_mD50E790F486A1E64757E9471D48BA42FC9ECCE4C,
	Tile_set_color_m9D76C21865CA89F39FF56C112CB13AFD45CD8B69,
	Tile_get_transform_mFA119A0C353E4E75C92C8BE829C6BDFA40F17643,
	Tile_set_transform_m2E46927D29823DBDC3B7B36E013845006075EB02,
	Tile_get_gameObject_m8B1B09FD1B6B5A0402D63D3AFF139C6078754077,
	Tile_set_gameObject_mD4C82AFCA4B96D44BE5549CFF9E0F36218A4ECE9,
	Tile_get_flags_m4AC2E9F8CF43DB83E9F8389EFDF7E6111E5A9806,
	Tile_set_flags_mE221D85F2B767EC5C3D473266CB7AABD66674DEA,
	Tile_get_colliderType_mDB7A2E3BEF055617F6AC198841938B79C289E967,
	Tile_set_colliderType_m21E434F55E4CC8AEB867E7FCF88821EFFC9CEB3F,
	Tile_GetTileData_m187B4A0A655AAB70CC8EC203F78E4777ABB96D4E,
	Tile__ctor_m1680C25E80E5ACCB156133C14199BD5BFE00EA5E,
	TileBase_RefreshTile_m7302220B588658247D635871B92DBFF7708E2224,
	TileBase_GetTileData_m04B3B474F4DBF88997FF29ABA115A2FFB91BAF81,
	TileBase_GetTileDataNoRef_m657510B6853906E397D8FC7E6F1A8B2DC4B34397,
	TileBase_GetTileAnimationData_m8E1C84B8BC0B38E978ECEE6C7AD50D7D8BF810FE,
	TileBase_GetTileAnimationDataNoRef_m061D2FB92E28E5C2379385827F78C22719287D97,
	TileBase_GetTileAnimationDataRef_m10D856F55369986224F166E8EEF5633EB8EBA5C3,
	TileBase_StartUp_mBAF37DBB4DCC7BDB384352D93AB609CEB0E2E78B,
	TileBase_StartUpRef_mB00DB38868F87645811DE4784F57278388FAEEF9,
	TileBase__ctor_mBFD0A0ACF9DB1F08783B9F3F35D4E61C9205D4A2,
	Tilemap_get_bufferSyncTile_m5506F240CC262FD454CFF9B547F16530F9506B1D,
	Tilemap_HasLoopEndedForTileAnimationCallback_m6A4AA954E8521E7BFC71B0A44B60DFDE7F7F51BD,
	Tilemap_HandleLoopEndedForTileAnimationCallback_m7BA8FF295BA812311BF3854DB2978EAE03B2F9C1,
	Tilemap_SendLoopEndedForTileAnimationCallback_m8D1E624097F94B218E9675EC3AA121C0F22CB6BA,
	Tilemap_HasSyncTileCallback_m522AE13C1DEBDDA7EBC7C9BAF1302EB75EF3A0EB,
	Tilemap_HasPositionsChangedCallback_mD02A9A567086C4F60CA7B4733EAFD173289857FF,
	Tilemap_HandleSyncTileCallback_mF1D8059E6F8ED90041313259D5DCFC3DBEB8750A,
	Tilemap_HandlePositionsChangedCallback_mCEC3B01A5328F6C83163C25CE9EDCD87E5895CD0,
	Tilemap_SendTilemapTileChangedCallback_m66E5D12B134C48E57EF4C1B29658CD61B75366EF,
	Tilemap_SendTilemapPositionsChangedCallback_m8F1D0E0F18A797349A83465F5E68DF01972D75D4,
	Tilemap_GetCellCenterWorld_m567FBE8E0822C9A75681D3B95AD9FDDF3C43A4F3,
	Tilemap_get_cellBounds_m2C1EDCFFD145175A83457B4F7A88CEA037DF8EB9,
	Tilemap_get_animationFrameRate_m391ACF664A9239DCB5A3344AE6A27A6D924234AE,
	Tilemap_get_origin_mB5E10582CFAA76144BB44DECAADB84E904D02E55,
	Tilemap_get_size_m8B9F0C2CC3CD37626AE921047DA5DC239B3F00EA,
	Tilemap_get_tileAnchor_mD3C7F2A024DC43019CEB93682307ED41EC3329E4,
	Tilemap_get_orientationMatrix_mF63DF1E4FC7E4B7DE10CE67DFDBB130262784F24,
	Tilemap_GetTileAsset_m3B9C96C2E2488141C4F6EBD52C6D807C801C6922,
	Tilemap_GetTile_m8500FBFF853C9E813810929BD29D7A866B516225,
	Tilemap_SetTileAsset_m88D70B08B3D291F99EB34F01136C2D4EEBE45D4B,
	Tilemap_SetTile_m880BD0CC6B69A4B15495C4FBD2CBEA50D1BE23BA,
	Tilemap_RefreshTile_mEF4F94212FD9B311431DFFAFE092A4A6EBA580DF,
	Tilemap_RefreshTilesNative_mD73E77DFD7C808A3665CA8389F728CBC08A52232,
	Tilemap_RefreshAllTiles_mA64BB2AFE77727C6358ACDA467A7B082A0034A9E,
	Tilemap_GetUsedTilesCount_mF75EB807D49AEF1AA1748984D238B55946A4DD4A,
	Tilemap_GetUsedTilesNonAlloc_m4FEDE5F12A7A1333A4A2082637CFEC76B7903E14,
	Tilemap_Internal_GetUsedTilesNonAlloc_m79F745C755C26362833B0F0CC8C5846547EF5C4F,
	Tilemap_ClearAllTiles_m440B00506358103B65F7A2FE3B3AC44F621FE5B6,
	Tilemap_GetLoopEndedForTileAnimationCallbackSettings_mB19F0933D4E0BCBD4EC9BE836B064D25997FDDC8,
	Tilemap_DoLoopEndedForTileAnimationCallback_m30B429EAB1964CD0CD09E3043AA926DE715B2763,
	Tilemap_GetSyncTileCallbackSettings_m1630BBFA37F85D2E29E73EA92DB13C700CC86B29,
	Tilemap_DoSyncTileCallback_m7BF07E7C678E7A55BDF116FA7C5BEF29963402A2,
	Tilemap_DoPositionsChangedCallback_mCD3C79A37783BB7DD22454981E0B51394B7990F4,
	Tilemap__ctor_m1D0F3884F418FC0D39DE07F85E356B9A733F138C,
	Tilemap_get_animationFrameRate_Injected_m853E4D792F977520F9AF25B875050F0E160CCF45,
	Tilemap_get_origin_Injected_mE1614FE27564626B3726F24F6CC284204A1BDEA2,
	Tilemap_get_size_Injected_mE4A4FAFA70C3B5BA87CF8E64CD0BCCB8E11323EA,
	Tilemap_get_tileAnchor_Injected_m7D8067BA5CD69E8B8807AFD721E9BD732E5B8187,
	Tilemap_get_orientationMatrix_Injected_mD54025C0654F2E07D74364E44A287136074F8646,
	Tilemap_GetTileAsset_Injected_mC4D3F18DDF3496564747BCD4E56631E21456093C,
	Tilemap_SetTileAsset_Injected_mC24A398B065AFB28E88A51F38DA4762DD5DCBD4E,
	Tilemap_RefreshTile_Injected_m9EF6875C16B8F425284A6E681CD466C09B753643,
	Tilemap_RefreshTilesNative_Injected_m1CA76F21817AE189F616BA8585D219973222FAF6,
	Tilemap_RefreshAllTiles_Injected_m5F81411F270FA246D9A5664AD077E338FA20A538,
	Tilemap_GetUsedTilesCount_Injected_mF9233C510EFA2441B71C2648A0FB4EF03D07E8D1,
	Tilemap_Internal_GetUsedTilesNonAlloc_Injected_m56C0D831A7D75EAF3FC3B3360D82EF7921C947C2,
	Tilemap_ClearAllTiles_Injected_mA1907919D0EAE3FCD14370073669922F0944E530,
	TilemapRenderer_RegisterSpriteAtlasRegistered_m5D7676A05B0B16ABCCF4CEE57BA9E28FA4D171BC,
	TilemapRenderer_UnregisterSpriteAtlasRegistered_mFE33C972CF738A50A631203D0DD7E325AADFCB08,
	TilemapRenderer_OnSpriteAtlasRegistered_m4348D78754045C8B10CEA76195A313790F412ED1,
	TilemapRenderer_OnSpriteAtlasRegistered_Injected_mEA941D5042FADDC2E5457247D2DF55324C143D9A,
	TileData_set_sprite_m3566544847F9C9C27EDB154324B6FBDB446EFE94,
	TileData_set_color_m5E759823878243A226EF46419FAD7C0CC3D5F40A,
	TileData_set_transform_m71074A780C066292F940002A7165658E9CC01F9F,
	TileData_set_gameObject_m1CE5B2AAAB5BF5AEF36EBAF2BCE23E4D2E5A9E09,
	TileData_set_flags_mEB46B1364D6DB7F77C2E1E43AFD31381B291BD30,
	TileData_set_colliderType_mE12359ADEF5F42CC0B635DCBAEC3035F0526FA96,
	TileData_CreateDefault_m13A39001A47B60635B10FFD06AD65082CBB7D12D,
	TileData__cctor_mE2F5A802075C68DE978E46092DC8BF465182934A,
	TileAnimationData_set_animatedSprites_m315FE8DAB5071E1FA594AEA74B1B66BBF6A5C3E1,
	TileAnimationData_set_animationSpeed_mE1DB382A9D7F0385D70248A93B998405890D4611,
	TileAnimationData_set_animationStartTime_mBC2F61289403253C6B43C12576A98654B94A9B40,
	TileAnimationData_set_flags_m5C1157264844D313EB81E33FFD5CB7EAE165603D,
};
extern void TileData_set_sprite_m3566544847F9C9C27EDB154324B6FBDB446EFE94_AdjustorThunk (void);
extern void TileData_set_color_m5E759823878243A226EF46419FAD7C0CC3D5F40A_AdjustorThunk (void);
extern void TileData_set_transform_m71074A780C066292F940002A7165658E9CC01F9F_AdjustorThunk (void);
extern void TileData_set_gameObject_m1CE5B2AAAB5BF5AEF36EBAF2BCE23E4D2E5A9E09_AdjustorThunk (void);
extern void TileData_set_flags_mEB46B1364D6DB7F77C2E1E43AFD31381B291BD30_AdjustorThunk (void);
extern void TileData_set_colliderType_mE12359ADEF5F42CC0B635DCBAEC3035F0526FA96_AdjustorThunk (void);
extern void TileAnimationData_set_animatedSprites_m315FE8DAB5071E1FA594AEA74B1B66BBF6A5C3E1_AdjustorThunk (void);
extern void TileAnimationData_set_animationSpeed_mE1DB382A9D7F0385D70248A93B998405890D4611_AdjustorThunk (void);
extern void TileAnimationData_set_animationStartTime_mBC2F61289403253C6B43C12576A98654B94A9B40_AdjustorThunk (void);
extern void TileAnimationData_set_flags_m5C1157264844D313EB81E33FFD5CB7EAE165603D_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[10] = 
{
	{ 0x06000052, TileData_set_sprite_m3566544847F9C9C27EDB154324B6FBDB446EFE94_AdjustorThunk },
	{ 0x06000053, TileData_set_color_m5E759823878243A226EF46419FAD7C0CC3D5F40A_AdjustorThunk },
	{ 0x06000054, TileData_set_transform_m71074A780C066292F940002A7165658E9CC01F9F_AdjustorThunk },
	{ 0x06000055, TileData_set_gameObject_m1CE5B2AAAB5BF5AEF36EBAF2BCE23E4D2E5A9E09_AdjustorThunk },
	{ 0x06000056, TileData_set_flags_mEB46B1364D6DB7F77C2E1E43AFD31381B291BD30_AdjustorThunk },
	{ 0x06000057, TileData_set_colliderType_mE12359ADEF5F42CC0B635DCBAEC3035F0526FA96_AdjustorThunk },
	{ 0x0600005A, TileAnimationData_set_animatedSprites_m315FE8DAB5071E1FA594AEA74B1B66BBF6A5C3E1_AdjustorThunk },
	{ 0x0600005B, TileAnimationData_set_animationSpeed_mE1DB382A9D7F0385D70248A93B998405890D4611_AdjustorThunk },
	{ 0x0600005C, TileAnimationData_set_animationStartTime_mBC2F61289403253C6B43C12576A98654B94A9B40_AdjustorThunk },
	{ 0x0600005D, TileAnimationData_set_flags_m5C1157264844D313EB81E33FFD5CB7EAE165603D_AdjustorThunk },
};
static const int32_t s_InvokerIndices[93] = 
{
	21016,
	13843,
	16200,
	-1,
	34156,
	23514,
	23514,
	20761,
	15968,
	20556,
	15762,
	20743,
	15949,
	20761,
	15968,
	20694,
	15903,
	20694,
	15903,
	3899,
	21016,
	8204,
	3899,
	6201,
	3163,
	6200,
	2925,
	3168,
	2926,
	21016,
	20550,
	34103,
	7408,
	14895,
	34103,
	34103,
	15968,
	7408,
	15968,
	14895,
	14280,
	20548,
	20873,
	21005,
	21005,
	21004,
	20743,
	13843,
	13843,
	8204,
	8204,
	16200,
	6517,
	21016,
	20694,
	13212,
	13212,
	21016,
	15721,
	7408,
	15721,
	15968,
	7408,
	21016,
	32260,
	28958,
	28958,
	28958,
	28958,
	27919,
	26484,
	28958,
	26483,
	32762,
	31784,
	27848,
	32762,
	21016,
	21016,
	15968,
	28963,
	15968,
	15762,
	15949,
	15968,
	15903,
	15903,
	34232,
	34252,
	15968,
	16071,
	16071,
	15903,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x06000004, { 0, 3 } },
};
extern const uint32_t g_rgctx_T_t5861EDE6EE239F7CB8B7041B5D5F5178632F4831;
extern const uint32_t g_rgctx_T_t5861EDE6EE239F7CB8B7041B5D5F5178632F4831;
extern const uint32_t g_rgctx_Component_GetComponent_TisT_t5861EDE6EE239F7CB8B7041B5D5F5178632F4831_m69FA7CD01EDFF57DFB8E90A49F6A1C8CBF91C628;
static const Il2CppRGCTXDefinition s_rgctxValues[3] = 
{
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t5861EDE6EE239F7CB8B7041B5D5F5178632F4831 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5861EDE6EE239F7CB8B7041B5D5F5178632F4831 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponent_TisT_t5861EDE6EE239F7CB8B7041B5D5F5178632F4831_m69FA7CD01EDFF57DFB8E90A49F6A1C8CBF91C628 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TilemapModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TilemapModule_CodeGenModule = 
{
	"UnityEngine.TilemapModule.dll",
	93,
	s_methodPointers,
	10,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	3,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
