# C/C++ build system timings
generate_cxx_metadata
  [gap of 34ms]
  create-invalidation-state 129ms
  generate-prefab-packages
    exec-prefab 613ms
    [gap of 26ms]
  generate-prefab-packages completed in 647ms
  execute-generate-process
    exec-configure 691ms
    [gap of 125ms]
  execute-generate-process completed in 816ms
  [gap of 81ms]
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 1729ms

