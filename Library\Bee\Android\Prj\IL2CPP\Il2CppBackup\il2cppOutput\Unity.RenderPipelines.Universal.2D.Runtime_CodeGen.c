﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* ShadowUtility_CalculateEdgesFromLines_m95B59AC1CE430C8952290FAC5C4DFEAD0EF53008_RuntimeMethod_var;
extern const RuntimeMethod* ShadowUtility_CalculateEdgesFromTriangles_m1F08E1F50422891DCB5A4A70D57ABD9F1354CB63_RuntimeMethod_var;
extern const RuntimeMethod* ShadowUtility_CalculateLocalBounds_mCD7AB254F81081483F03BEF504C2B2918777D32D_RuntimeMethod_var;
extern const RuntimeMethod* ShadowUtility_CalculateProjectionInfo_m952E6AB7BDD578374B99ED119819090E81F81067_RuntimeMethod_var;
extern const RuntimeMethod* ShadowUtility_CalculateTriangles_m9D055B9D0E3B41D00713797D5074A7BD42A4D216_RuntimeMethod_var;
extern const RuntimeMethod* ShadowUtility_CalculateVertices_m1087DC5C13D021D10BB58FA9EAD2D99416D2974E_RuntimeMethod_var;
extern const RuntimeMethod* ShadowUtility_ClipEdges_m104D8B0F86040657D28698C6FEDD40210FC710FB_RuntimeMethod_var;
extern const RuntimeMethod* ShadowUtility_GenerateInteriorMesh_mBF198238681A38CB0ECED75E112BBEAE45F2B362_RuntimeMethod_var;
extern const RuntimeMethod* ShadowUtility_GetVertexReferenceStats_m1CB3AC1703058D94FF1211F9BA9430600C57B106_RuntimeMethod_var;
extern const RuntimeMethod* ShadowUtility_ReverseWindingOrder_m9F8C8065FA5294F22C51C1552E34416B3AED38DE_RuntimeMethod_var;



extern void ShadowShapeProvider2DUtility_GetTrimEdgeFromBounds_m6C6B9FACDACF3342E20B3892A13E930FE64F0820 (void);
extern void ShadowShapeProvider2DUtility_IsUsingGpuDeformation_m8C590AC63075427436DD23B0B3E704374CD3E6EF (void);
extern void VertexDictionary_GetIndexRemap_m0E5260046A68AA40445328A6A22F21EB718AA143 (void);
extern void VertexDictionary__cctor_mB58E922FDCBC9C102CCF0A439CF913F8DD1F91B3 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m050A9CAA08705EC022F15BE62FE419CF3D6E2C76 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m28F5DE7E6798EB10AB1BD26513A5C7E15F3096A8 (void);
extern void CinemachineUniversalPixelPerfect_OnEnable_mE985C3B2D6154244E4D1586771D7F4CCD7420F94 (void);
extern void CinemachineUniversalPixelPerfect__ctor_mC7A1AE72895E88A136FA7BD8421DBC2C0A710BFC (void);
extern void DoublePoint__ctor_m29F8204086F248FC19FB9593452E8EECF612563A (void);
extern void DoublePoint__ctor_m52141CF0CACA19656612BC12A2C4F7664763F1B2 (void);
extern void DoublePoint__ctor_m41A0279EEC0F9151AE7450FB7897C28252712545 (void);
extern void PolyTree_Clear_m4EE08CDE266753595E9B4A79A6B363EEE4CC9C43 (void);
extern void PolyTree_GetFirst_m0B5384CBBF4424101DED5DF85ACD3429571B0D7B (void);
extern void PolyTree_get_Total_m2C24746F50527209FE394C6CF52CD40BB95B48C7 (void);
extern void PolyTree__ctor_m1994B16073578DFA1EEA2842DB0F445B50757AF7 (void);
extern void PolyNode_IsHoleNode_m7BE6DBE07C8A7B4C61D0D3A9FFA4F71FE0B47EE5 (void);
extern void PolyNode_get_ChildCount_m0C56A91C4A8A62998B9AA4D3ABC143B511B66A89 (void);
extern void PolyNode_get_Contour_m9AB3A0595689E2DB122E050B8725FD4F56FAC521 (void);
extern void PolyNode_AddChild_mB8507A9D14D146FFB323B4428B46CFAB27ACD9BF (void);
extern void PolyNode_GetNext_m1D7A502BB5D76DECC88DC4D6621D6C3FA590206F (void);
extern void PolyNode_GetNextSiblingUp_m85E45FDAFBB96E6FE37F9EE1C146D9ACC09D4F3B (void);
extern void PolyNode_get_Childs_m9504B6F5165F6AD581F63D3A80A5FAA7B472BC79 (void);
extern void PolyNode_get_Parent_mF7E2484447DB849847A1F57387E4B676DDF9E0E5 (void);
extern void PolyNode_get_IsHole_m59B43B31ED19DD2E0CB22EDFD3627004A46E33E9 (void);
extern void PolyNode_get_IsOpen_m92C56CD9B1E64B260B7C09FC2A90D5D834292447 (void);
extern void PolyNode_set_IsOpen_m2242B21BDCDB2E105ED4EBCD63F89F602047F629 (void);
extern void PolyNode__ctor_mC3D70892D4FF7F6870DA40C31CDC5826588606CF (void);
extern void Int128__ctor_m2BB0A4CF9E48910476A58754D2AE1250D3C12966 (void);
extern void Int128__ctor_mBC356544A97CDF5EACE12C41CBB3C286CC68B723 (void);
extern void Int128__ctor_mE8A298C41D274843EE69B61FA4505EEE3D2C1054 (void);
extern void Int128_IsNegative_m70EBBF367872796AFF9BEA2D5B0C44B391B2FD91 (void);
extern void Int128_op_Equality_mDC0CD599A09D6D85CFFB4A3F9E03BB32F5289CC9 (void);
extern void Int128_op_Inequality_m34C85F43A0A9C5A3D064C0FA631D8B2473AA97B9 (void);
extern void Int128_Equals_m9E0BF4D381BD6AB4201A7F1C8D6DE2E76FCA7CDE (void);
extern void Int128_GetHashCode_m4D04D0D584560C74DC48A4931C0FEFC434233CA2 (void);
extern void Int128_op_GreaterThan_mE7DE40923FEBDAAA9ABF80C43237AF39362B8434 (void);
extern void Int128_op_LessThan_m80BA06A74741A1A873BC0223843EA0E337A87B8E (void);
extern void Int128_op_Addition_m2026A4247863AA78F3A004E6405CE72837B85A8C (void);
extern void Int128_op_Subtraction_m9518B4A9B0C14D87B451F43A881AFC43631416B8 (void);
extern void Int128_op_UnaryNegation_m141B38A5611CF59437D561CD625B9E38788CE257 (void);
extern void Int128_op_Explicit_m73D92CA9FB1D3FD008EADA7B99E26F4C47A69466 (void);
extern void Int128_Int128Mul_m1A760A293E9FEB7A1395EF901CD1A2AA3FF4C61A (void);
extern void IntPoint__ctor_m975E8CAF60B3C05EA5C947E1E5A5D46A6991C9F7 (void);
extern void IntPoint__ctor_m1C17D5437492194E95980214849A1BA4FEC919B0 (void);
extern void IntPoint__ctor_mA6DA01E8192EAB09DAEE3C900A83D4A2E87AA8FE (void);
extern void IntPoint_op_Equality_m19418B21E6FE3EB92A59BF3E7E2881C21CF9755C (void);
extern void IntPoint_op_Inequality_m567150C592985BE0233C9E7FEA6D526F681864D3 (void);
extern void IntPoint_Equals_mB95B6A612FF6F33DAFB3AF701DF7554CFB414833 (void);
extern void IntPoint_GetHashCode_mCC421CBDCDE3C6A291D3CAA06D787AF76B6F8EA9 (void);
extern void IntRect__ctor_m2679B769D75424420E84A8348164D92437FD003A (void);
extern void IntRect__ctor_m61FE9261A36D5EE88EB7CFDF926045C308753D8E (void);
extern void TEdge__ctor_m610F3E67203EB46AF16C214956F390FFFA84DE7C (void);
extern void IntersectNode__ctor_m67DEB85303E6FF915F173B73C8F41E1B4AEA54FC (void);
extern void MyIntersectNodeSort_Compare_mC6F12D968F6F4536E1C45EF6E36D5FBB294853FB (void);
extern void MyIntersectNodeSort__ctor_mB8C56DB3FAED9391DFBABE28026911599CEFAC33 (void);
extern void LocalMinima__ctor_mA494B9C91045BD7B788A7FD2985648D9859F36E0 (void);
extern void Scanbeam__ctor_mD24C1558F845142F670925E66F753D37439CCFF0 (void);
extern void Maxima__ctor_mC32025C99D8EA87503C8E97448C0D14B5646FC07 (void);
extern void OutRec__ctor_m3C5E6760414D602107B4D98A0F050F561C249C74 (void);
extern void OutPt__ctor_mCE53B6D8295199ABEC9AB5648E09C9F365A12F27 (void);
extern void Join__ctor_m88EEDAF536FE700D923B4AB9AE7ECCCD5FD1FAB6 (void);
extern void ClipperBase_near_zero_m8B80BDA7A141F620A243DFED5559F9A549C0B1D4 (void);
extern void ClipperBase_get_PreserveCollinear_m9D2B1A1F052450CACBF3905CA51A50A1BE37B4C2 (void);
extern void ClipperBase_set_PreserveCollinear_mC58976CDF7BAF261FABB75AFCB9EAFA07E24F78A (void);
extern void ClipperBase_Swap_m2EEA14EBC5B1D1D8C61D5446D9EFE46A318B6B4D (void);
extern void ClipperBase_IsHorizontal_m6BAD44CE7EDD2316867C067D25B16BC929CE2674 (void);
extern void ClipperBase_PointIsVertex_m8ADAF8684696049E6A3682C82192D9CC7FE9C177 (void);
extern void ClipperBase_PointOnLineSegment_m6D701248A671BAA1DECAF77E62F840FE06F54F15 (void);
extern void ClipperBase_PointOnPolygon_mD693C741F82F0B3ACC8836413DE7C578DD9B6508 (void);
extern void ClipperBase_SlopesEqual_m00AF12F96769255CE798C4E74DE3C5996F461C8A (void);
extern void ClipperBase_SlopesEqual_mEB17161B9ECC29DFF57B793C164DB8C00029CC94 (void);
extern void ClipperBase_SlopesEqual_m589B45B1AAFE029C0360B6A8EC802C8AFD6ED1EB (void);
extern void ClipperBase__ctor_m0894488439AE46B592AE18030F09235C1FDC693F (void);
extern void ClipperBase_Clear_m0B93D231A51B4F6B1B019E45834124B1E09E7069 (void);
extern void ClipperBase_DisposeLocalMinimaList_m7D4312016822E530E1D4E7FBBB46FA8CBEF5A6E7 (void);
extern void ClipperBase_RangeTest_mA0B6D833FDEFCF7EF73C25ADDC9A93AE49917081 (void);
extern void ClipperBase_InitEdge_m4D917102532B87E80607086BD42F811F66B4171D (void);
extern void ClipperBase_InitEdge2_m5B567E5C9D56734F0DE68206F659D26F9043F669 (void);
extern void ClipperBase_FindNextLocMin_m271E984508D670BE99476E553DBA25FEAE3CCDBB (void);
extern void ClipperBase_ProcessBound_m49923BA5E64AF4EB19CF8EAF5F731A100A919A53 (void);
extern void ClipperBase_AddPath_mA946CF9B6011042421DDEE21266D25E70AA4FB68 (void);
extern void ClipperBase_AddPaths_m27ACCC4640137E92BE1FF9BAF3382FB70CFBEBB1 (void);
extern void ClipperBase_Pt2IsBetweenPt1AndPt3_m13DFE616832125372998174FEBBF95284BCBAC0B (void);
extern void ClipperBase_RemoveEdge_m7D4A0FFE739E0A8C43C2B5CDE98CF7C8D51C8E13 (void);
extern void ClipperBase_SetDx_m26594DD05B93158C408F0AF7E4525BACD3C9233B (void);
extern void ClipperBase_InsertLocalMinima_m0E794BEC70E81A9BE1FA0C0A1D8A40A08718CE23 (void);
extern void ClipperBase_PopLocalMinima_m24AFAE8B70455538DCD5922163EF5C11D6E980BA (void);
extern void ClipperBase_ReverseHorizontal_mA3AD14B2BCBE97985D26C06B255390CB0C7CAF23 (void);
extern void ClipperBase_Reset_mFF927BD6DE091885EB0051B3EAC2B7768CB7AA1F (void);
extern void ClipperBase_GetBounds_m382A82FC696016C3D0484AB1115E2146F415E793 (void);
extern void ClipperBase_InsertScanbeam_mE98355A33D03DD9ECB3755B1F73D17C2A7E0DE34 (void);
extern void ClipperBase_PopScanbeam_m30D6EC26308049F1C1C71E4276D5EE684D2E2A10 (void);
extern void ClipperBase_LocalMinimaPending_m811369BA00B320F43BAD38139AC65FAF37641F75 (void);
extern void ClipperBase_CreateOutRec_m8385CCD504D822268DDC5D9174253A80E76C2122 (void);
extern void ClipperBase_DisposeOutRec_m3F29081230A27D799F601FFBE9760A12ADE74A1C (void);
extern void ClipperBase_UpdateEdgeIntoAEL_m4C4C629344AE2E4072183668434761BF7E4D92DC (void);
extern void ClipperBase_SwapPositionsInAEL_m8115E667C69A638634397D13A4D0C34EA25DCB07 (void);
extern void ClipperBase_DeleteFromAEL_mBDD7A633D67B4FE528EF0E1BC18E2997B9ADD996 (void);
extern void Clipper__ctor_m79876B5EC9228EEAAF0564D8AED96823013A9378 (void);
extern void Clipper_InsertMaxima_mB6D3CCC041F1854241C01396BD5D18FCC312AA1C (void);
extern void Clipper_get_LastIndex_mAC6074E04AB6F8042F2D853F146D20735F096B26 (void);
extern void Clipper_set_LastIndex_m6F599F61C577B01788A268086273E2A63838C40E (void);
extern void Clipper_get_ReverseSolution_mDA8487BE5689AC1DD8FA0AECA1687EDFC05442BE (void);
extern void Clipper_set_ReverseSolution_m0D892BFC128ED8F617128C1247B128DA98046355 (void);
extern void Clipper_get_StrictlySimple_m0668EAFA6CBFB92C4BA987D31B968956E406DA90 (void);
extern void Clipper_set_StrictlySimple_mBAA38534C91283F371B0BC5847702C29E674A00C (void);
extern void Clipper_Execute_m70516548117FA1F84C2D3CFF961D2A9EFB3A7621 (void);
extern void Clipper_Execute_mA5A3F3FC444587617F075E17180654E802A7E4B0 (void);
extern void Clipper_Execute_m6BFF7174B7680F350A69BB8CD2B7997C27A78BEA (void);
extern void Clipper_Execute_mEE13FA88C6CC6871D5D66D8C0652A99F2E4BD0E5 (void);
extern void Clipper_FixHoleLinkage_m4B62F6906FD04FECBCE2F457400B2CC33694F834 (void);
extern void Clipper_ExecuteInternal_m7D08783296C9BD61B5370C0DA00FA3E053DFE98B (void);
extern void Clipper_DisposeAllPolyPts_m6C8F88CA62B0782EFFF78A998A8131D0451ED47F (void);
extern void Clipper_AddJoin_mA87C379712044EED79BF51767A4C79D7D0351EC5 (void);
extern void Clipper_AddGhostJoin_m612C3ADB4C32701F537DA35DAED2193BD80CE609 (void);
extern void Clipper_InsertLocalMinimaIntoAEL_mA82B704FD997BB615616F8CF16F0ACE970706B52 (void);
extern void Clipper_InsertEdgeIntoAEL_m7584619E65629469C02AC98B40B0CDD00922D617 (void);
extern void Clipper_E2InsertsBeforeE1_m0009EE4EFDDD65FBCFAA6DBB68CF69EECD7BC653 (void);
extern void Clipper_IsEvenOddFillType_m9F2B05C69F279E0C7F9C1C5F2E748E6979321A3F (void);
extern void Clipper_IsEvenOddAltFillType_m5D259419A5C33724F372EE937C3469AA34FC199B (void);
extern void Clipper_IsContributing_mDFF5C7D0EE49D4D958D774BE48C5598FE69F2FB9 (void);
extern void Clipper_SetWindingCount_mD42016432E8FEF29D8ED729408F9E24C3BFC80DD (void);
extern void Clipper_AddEdgeToSEL_m3B43222EF1A254812F2A7D61AE72959852A7A4A1 (void);
extern void Clipper_PopEdgeFromSEL_m45006B3EB7CA7B77EB866606E3FA7C1D3A7BDEE9 (void);
extern void Clipper_CopyAELToSEL_mD4D878B45AD029FFA43E1B3F681D089B99058CFD (void);
extern void Clipper_SwapPositionsInSEL_m5CC2DCF327486C0BE07121830577CB74CECDC207 (void);
extern void Clipper_AddLocalMaxPoly_m375B6F89A77EC9D1B0EFDBEA8312F9FE249C807E (void);
extern void Clipper_AddLocalMinPoly_m7C4A79390680D0A5C5F09A341F67FC64331C5A2B (void);
extern void Clipper_AddOutPt_mC0A2C10B5A7FCCFF77CABD850B17859164EDBBBF (void);
extern void Clipper_GetLastOutPt_mE511905B91CC0BC041D55B614DE1A5ABACDEF5E1 (void);
extern void Clipper_SwapPoints_mC61CBEED84CE6DCC505247E0C8692FCB7CBAD043 (void);
extern void Clipper_HorzSegmentsOverlap_mB0882EB0BA3FE027C2066A9FCE3176BA2144F0E0 (void);
extern void Clipper_SetHoleState_mCF984127BF470390DCFE98A155E9D112320F08BE (void);
extern void Clipper_GetDx_mE8200E866BD7A7DF76D24765377C40BCD0FAD709 (void);
extern void Clipper_FirstIsBottomPt_m736C3F4AF75269DF0920BEC50F0D49E9FEE27204 (void);
extern void Clipper_GetBottomPt_m840521EDC1D46AFC372FEEC7156A972401BAFDF6 (void);
extern void Clipper_GetLowermostRec_mBF0DB1688D4B7C020685B70A1CE2873868ADFDF9 (void);
extern void Clipper_OutRec1RightOfOutRec2_mDF89A4E48E77D7F36B6712E32F0D2DC538405779 (void);
extern void Clipper_GetOutRec_mE6B40CDC55D028755FF06F961E56EED4B46A970E (void);
extern void Clipper_AppendPolygon_m8A44054101EDA2291F3CE49082EDDE792ED3B9B9 (void);
extern void Clipper_ReversePolyPtLinks_mEA503CD7054E111843825753767C860123935328 (void);
extern void Clipper_SwapSides_m9FC00D418F5135E48F2CC1EE5EC7013E42D34488 (void);
extern void Clipper_SwapPolyIndexes_mC557D0FE42F84FB0618EC3753ABFF452DD61127B (void);
extern void Clipper_IntersectEdges_mD054D64A65188A152D241B4A66A9F4DAF61A5E97 (void);
extern void Clipper_DeleteFromSEL_m24617A4CCCF62EFE7D57818C59FD6BA322826E2D (void);
extern void Clipper_ProcessHorizontals_m4B595F375B5DA3AD1DEE1227A0B49CE6C63EAD46 (void);
extern void Clipper_GetHorzDirection_m6A3C4247B1BB901651DD505DB7FE5DB156B7E715 (void);
extern void Clipper_ProcessHorizontal_mECF545528976041E2CD3C08E9D6031B379ACD772 (void);
extern void Clipper_GetNextInAEL_m11BE83210097750DBC0BE637744F6E0465391C24 (void);
extern void Clipper_IsMinima_mA7F1B87150CF93342F8B66335DB3256ACEB2A616 (void);
extern void Clipper_IsMaxima_mE0DBD58A52930B956A04EEC33CE7444683D560E0 (void);
extern void Clipper_IsIntermediate_m470B2D9FB187245F625D3823186F77471A7E424B (void);
extern void Clipper_GetMaximaPair_m0220542A90C48A7028925F730724CF15CDEE1FA8 (void);
extern void Clipper_GetMaximaPairEx_mA4F0D5DBDF57152DA6CB784F96FEA4A1E8BBBCB6 (void);
extern void Clipper_ProcessIntersections_m4A76591615621FC7F31C0D2A7344E1BAACE50BDD (void);
extern void Clipper_BuildIntersectList_m2CB9D558A5A064D89485CC6D3DB2172EEDF6F70D (void);
extern void Clipper_EdgesAdjacent_mBCFFF6E516FEE06E1BB61B87630C092C2C6B0A7A (void);
extern void Clipper_IntersectNodeSort_m3AD2B566799D815D66E4A0B5BF38FAA541EDF86A (void);
extern void Clipper_FixupIntersectionOrder_m34743E697F79F00B31E1B46FBD29C5BD418B4F92 (void);
extern void Clipper_ProcessIntersectList_m914E8AA216C8EC1142AB3908727F0A151E487A66 (void);
extern void Clipper_Round_mA77D2621C05B82882ED72BB6E948D007CEC926B2 (void);
extern void Clipper_TopX_m8DE68B1C33B15035D802240A279626C5A0AE14B3 (void);
extern void Clipper_IntersectPoint_mA466BDB8128485DCB73CA65F7942409B8F81E4CE (void);
extern void Clipper_ProcessEdgesAtTopOfScanbeam_m6AD0668AC23EEFE993E18C5C190DA39612F2B8BC (void);
extern void Clipper_DoMaxima_m74C5B15D72E63AB46F7AD86BA78D81A825C8C753 (void);
extern void Clipper_ReversePaths_m523FCFF78EF4BEBF609D318B0366178AED833CAB (void);
extern void Clipper_Orientation_mAA2E77E8CFB335296C5F6DDF07DB62A401674FB6 (void);
extern void Clipper_PointCount_m88CCAAEDFE45055530FB8008192EA3ECF7E3FBD2 (void);
extern void Clipper_BuildResult_mFDFB8C6E1880CA19175F3CE89A95BA8C86DFE10C (void);
extern void Clipper_BuildResult2_m7367CEEAB529E9B4CA1E7F8888910375F44F8E63 (void);
extern void Clipper_FixupOutPolyline_mD67B3527E6C50388E6E4A5610072C0340209E483 (void);
extern void Clipper_FixupOutPolygon_m85287817B16BDE7470C3BC58DD1C203298F6D299 (void);
extern void Clipper_DupOutPt_m3BA477F43003B7EBEA4691B4F1EAAA6264F559FE (void);
extern void Clipper_GetOverlap_m42026FC20CD4FBF2C537B7829B968504C9986E56 (void);
extern void Clipper_JoinHorz_mB4DCBF754F6D3F2FA0E3E1226E8830846B0FA549 (void);
extern void Clipper_JoinPoints_m423275BB6DCB14C4DF436FB1EFC114FD6D1B3A80 (void);
extern void Clipper_PointInPolygon_mF11506965411DFD9662DDA49A16CFA3209BB5C60 (void);
extern void Clipper_PointInPolygon_mCF19505D43AE084A8BEA8860FCF134F5652524B2 (void);
extern void Clipper_Poly2ContainsPoly1_mEC43E8E8547F0CADADE1DB80D45B6B67B29E7BBD (void);
extern void Clipper_FixupFirstLefts1_m955754CF5788136720A5C049B4C4CF761DC73AED (void);
extern void Clipper_FixupFirstLefts2_m34CD287CAA95AA183535EAFFD64B45CDD8847752 (void);
extern void Clipper_FixupFirstLefts3_m764B2D88C1CA5437B2014EB8EBF1A3A0BB4E9AC4 (void);
extern void Clipper_ParseFirstLeft_m7D1AE9300F78210F45EB132427731BEF24C952B9 (void);
extern void Clipper_JoinCommonEdges_m2E6FD6E964AEE899F4725C3A930460926009DD02 (void);
extern void Clipper_UpdateOutPtIdxs_mD7AA527B468A83E10A722589DA7DC6EDC74266B0 (void);
extern void Clipper_DoSimplePolygons_mBEF68D5DED381C48823A211E5891E71A01E5A30C (void);
extern void Clipper_Area_m0C8F6E265FB3A731A4BDEA4FAAC2BD11E691BDCF (void);
extern void Clipper_Area_m22B30EDCF6C7DDE5B7DE2F892A3410DA26EACE13 (void);
extern void Clipper_Area_mD211BB43D4AE983C8EEAE1BCC9B4DE6E653B8299 (void);
extern void Clipper_SimplifyPolygon_m8AABE4A176C6DF388D644D478577211E86B69C06 (void);
extern void Clipper_SimplifyPolygons_mE5D7A0917BBC6067C2F79050721BBF40599BA20A (void);
extern void Clipper_DistanceSqrd_mDBED06ECBD73F8D0638A7DD69BA523AE596D9B5C (void);
extern void Clipper_DistanceFromLineSqrd_m1D0734740503B55AB703355431F34B24778AA4B0 (void);
extern void Clipper_SlopesNearCollinear_mB1DB109DE0E73203B2C85CB98037ED6B32874496 (void);
extern void Clipper_PointsAreClose_m55D44A9DC9B3367E5844C6AEFA406A6FC0CF2531 (void);
extern void Clipper_ExcludeOp_mF68B2639A94B551C633013C7DC1F6DAA19F1AA2F (void);
extern void Clipper_CleanPolygon_m50945A9225FE118C7E5AF61CAE3FFFFB8DC2F364 (void);
extern void Clipper_CleanPolygons_m5AE84E4C1A0BF4F1B337EA16675674CBD7F6789F (void);
extern void Clipper_Minkowski_mEF5E1EFBB32800CC6FD0A31E9BFB98EA2E7F6AC3 (void);
extern void Clipper_MinkowskiSum_m52E28149418FBC2DA0BD6CDC7CD51DE0C0914B12 (void);
extern void Clipper_TranslatePath_m989F5B720466DE87DD7ED7222A81B819BD7EC735 (void);
extern void Clipper_MinkowskiSum_mC50F9511EB58CAEB4AF2E6A7893123D75B43A3DA (void);
extern void Clipper_MinkowskiDiff_mD3BAC64A9C19F98526FB081E775427F4E500A921 (void);
extern void Clipper_PolyTreeToPaths_m7ACCF8C1783D5D638EB2473756F5DA78F82CEDE9 (void);
extern void Clipper_AddPolyNodeToPaths_m8285754304395D43033E500A6FB193A0130135B8 (void);
extern void Clipper_OpenPathsFromPolyTree_m1B293AF32A608083F8EFBB54D78F453E3C7BC599 (void);
extern void Clipper_ClosedPathsFromPolyTree_m2C78F9D0CF438791FAA3DDEC26D6C17D470BAF38 (void);
extern void ClipperOffset_get_ArcTolerance_m789189DA990071CC699A4D73945042326A03F139 (void);
extern void ClipperOffset_set_ArcTolerance_m3DFE7D76F298F4C80B7A2BADF84B7D07A317A8D4 (void);
extern void ClipperOffset__ctor_m1A52E0DF9798A533D30D12A8C03CF993264B0CE9 (void);
extern void ClipperOffset_Clear_m07D7E1B2791E98189A9ABCBA9026A47B4A53BD60 (void);
extern void ClipperOffset_Round_m47BA7B3F751BD03A0D2500800DF21CB981969446 (void);
extern void ClipperOffset_AddPath_m78D7148CD786EAF6275B46B9F743188249398988 (void);
extern void ClipperOffset_AddPaths_mFCD6516C336C117E7A9DF086ADE5ABD05467182A (void);
extern void ClipperOffset_FixOrientations_m5C6E3E9E9FE03BE2A56F5289691F013324725616 (void);
extern void ClipperOffset_GetUnitNormal_mE3D4295EFBF7F39A2BA158C99C204C1152E2DC98 (void);
extern void ClipperOffset_DoOffset_mC6DA30C8D896E0DC750E098754A9F1E4F110CBB0 (void);
extern void ClipperOffset_Execute_m9F9D16406B43192D18A02CED1C3D6D849FF42706 (void);
extern void ClipperOffset_Execute_m3FC3FE69829108FE13A2D922DA67998C72602A04 (void);
extern void ClipperOffset_OffsetPoint_m4CB6DFFEC35012C732B8C229D2427814F05729BD (void);
extern void ClipperOffset_DoSquare_m69ADF6B140584C61444F6CD35F47F00A6EB30068 (void);
extern void ClipperOffset_DoMiter_m993E5CF14E91A145B9614A82E1B2C5A90966DBF2 (void);
extern void ClipperOffset_DoRound_m9E49F085B9B848B70E9EB640943FEE5FF928A591 (void);
extern void ClipperException__ctor_mF1E22950760B1F1C7D5ADE536EACE305D09B8651 (void);
extern void Light2D_get_vertices_m02D080A78BC48FF5B02844C79789F40BE4284188 (void);
extern void Light2D_set_vertices_m93A93ADF722B7BD7894D12FC890CC1955CBCE2DA (void);
extern void Light2D_get_indices_m06336E2856756DB8649979C77BC7FBBB92395974 (void);
extern void Light2D_set_indices_m432850D252D6CDEC08AF20376CB95F7C838D134F (void);
extern void Light2D_get_batchSlotIndex_m0B51B331D7A794B939B3EE8BF76F6A9006917430 (void);
extern void Light2D_set_batchSlotIndex_m5E5B20E26B90F83E91FF2D2E955AC8DB400B2B6E (void);
extern void Light2D_get_lightCookieSpriteInstanceID_m30FF418E8659E7255A08B5957A24F0467535F6A1 (void);
extern void Light2D_get_useCookieSprite_m2C52603A7FB008F4E89836CFD36018D42E03901B (void);
extern void Light2D_get_boundingSphere_m1B78AF6A3981BE032C1EE459C109948160B307E0 (void);
extern void Light2D_set_boundingSphere_mE3B40CF5BFAD77846DE6ED73621E594B3F9F7433 (void);
extern void Light2D_get_lightMesh_m36653EA7D3AC78C63952000C029ADB16AB28FE8F (void);
extern void Light2D_get_hasCachedMesh_m9311EC77B9CA1860E01D25270896723FA35FAF35 (void);
extern void Light2D_get_lightType_m4B3DFEF0B821E6EBE24337820CC87E4AAC589097 (void);
extern void Light2D_set_lightType_mB964E077A445576A182B68079590474D31FF0FDE (void);
extern void Light2D_get_blendStyleIndex_m5022FCE517D15BC317F335AD485068ED849CDA36 (void);
extern void Light2D_set_blendStyleIndex_m2431A3E3B2BA2C13C1FF017C0CC9C85E68043741 (void);
extern void Light2D_get_shadowIntensity_m650F97C7B5E81434800E92D266DCD7D7DEC0663E (void);
extern void Light2D_set_shadowIntensity_mBCAB002DA5D4F92A6958610CAB1005C311B63B9B (void);
extern void Light2D_get_shadowSoftness_m5127B43BDA9A3C5CD99CF8976D3CE9A0F5A79683 (void);
extern void Light2D_set_shadowSoftness_mD9DDE3B82CB473A329096C818E8923DF1FB6633E (void);
extern void Light2D_get_shadowsEnabled_mDDAE3B6F8129E4DF72E3E91669FAA262733D33B3 (void);
extern void Light2D_set_shadowsEnabled_m69ED92B90AC2082A4CBBA1E62D6A8EC6F4A12FE8 (void);
extern void Light2D_get_shadowVolumeIntensity_m4BAF625CC5844F53C81DCCED0C02EDDCDC36325A (void);
extern void Light2D_set_shadowVolumeIntensity_mC342B1C841A107B6A14BC00EB8529C06C682CB7A (void);
extern void Light2D_get_volumetricShadowsEnabled_mC92883629048CC425D3356B5A5121BEAA37F7471 (void);
extern void Light2D_set_volumetricShadowsEnabled_m49F2AE1B923D6B4A358A41D56AE9F4CFD45012EC (void);
extern void Light2D_get_color_m6BBF205EE29D52B7C5CFFD1273B85680A02700A2 (void);
extern void Light2D_set_color_mA33647407CCA9F5B3121B0518D71BDD996E6EA91 (void);
extern void Light2D_get_intensity_m314E2E858289C7B01C8E9B7265ECF8193522D2BD (void);
extern void Light2D_set_intensity_mE28C237751EDE1E6F5D78F5B0019FC6735957359 (void);
extern void Light2D_get_volumeOpacity_m15E306E958F493BBF63A43EC689B3BF16D8616B0 (void);
extern void Light2D_get_volumeIntensity_m50854F673179630BC2AD1E1BA9A6F68ACEFD58EC (void);
extern void Light2D_set_volumeIntensity_m502D7E022C07B64C6C4A7CE71B130587F416C964 (void);
extern void Light2D_get_volumeIntensityEnabled_m625749FC7CC12D09107667494FF028022BB7EB78 (void);
extern void Light2D_set_volumeIntensityEnabled_m40C9B8AFF340D30A35D99925E95330A166701ECD (void);
extern void Light2D_get_volumetricEnabled_mD0D08DCF54B1C42CF9F16EE0EF643BD23CB781DD (void);
extern void Light2D_set_volumetricEnabled_m84F441C40CCEA738E194E26FF685377F62F0601C (void);
extern void Light2D_get_lightCookieSprite_m269D43B5E09727578FA6EB80DA190D157C7CEE19 (void);
extern void Light2D_set_lightCookieSprite_mB3EFF2456B7B4E70736CA42A08AF6731CE67FB13 (void);
extern void Light2D_get_falloffIntensity_m22D6E6A3165A26A787D8CE0B16A031041C52B485 (void);
extern void Light2D_set_falloffIntensity_mAFE26BE3643CE03ED76CDA29B9B460846A3D5C3E (void);
extern void Light2D_get_shadowSoftnessFalloffIntensity_m46A7A9E2EC93A73206E76EDC333B62AFB4919326 (void);
extern void Light2D_set_shadowSoftnessFalloffIntensity_m8FA05074C7AE8B9DD4CF195573E3BCE5265B0D38 (void);
extern void Light2D_get_alphaBlendOnOverlap_m8B0141E8FA9F3A31518D3265B032F927FC0D2642 (void);
extern void Light2D_get_overlapOperation_m551ED37C2B318C89C1D9CA170EBD771FFEF97E38 (void);
extern void Light2D_set_overlapOperation_m49B1B0F9083ADE10F6CEE76B9DDA6264543F0460 (void);
extern void Light2D_get_lightOrder_m98564FE88A359A61FA48E3A8CB86AE5E577EFF8E (void);
extern void Light2D_set_lightOrder_mEEDABA50C7406B8993909F078D965B862F48B016 (void);
extern void Light2D_get_normalMapDistance_m4062B2799F333EF3E949FF3D7DE65DC2691D60B3 (void);
extern void Light2D_get_normalMapQuality_mD1DC839DD41E9468A432EE08203F642B6CEB558F (void);
extern void Light2D_get_renderVolumetricShadows_m64B99C9CF0366EDB1E7C8A61B83FEFEC144309CA (void);
extern void Light2D_get_targetSortingLayers_m507993B01B1260EFEACE93CC90558062C31AB825 (void);
extern void Light2D_set_targetSortingLayers_m27E926A40BCF90A8835E06B2B686106B71D40345 (void);
extern void Light2D_IsValidLayer_mD8358AF68A650551AD681E85CB315C1D473EDDC3 (void);
extern void Light2D_AddTargetSortingLayer_m965A1AC1DA1DC591E449B3C7E21B25B8E0260896 (void);
extern void Light2D_AddTargetSortingLayer_mF682B63ED6C64F54E1512F984D8CC3FCAA1A6F14 (void);
extern void Light2D_RemoveTargetSortingLayer_mE3915F9C28AC691C2DBAB85B9024A9D49B2A742E (void);
extern void Light2D_RemoveTargetSortingLayer_m90B70D16E02D7C234ED19DC65EED3C9F730857BD (void);
extern void Light2D_MarkForUpdate_m5BB2181B870C167B8F63066F25DD7C4287C177CC (void);
extern void Light2D_CacheValues_m24A4089B58DBE8FE07DC5C7358BF953690749564 (void);
extern void Light2D_GetTopMostLitLayer_m303645F98C379D3B5ECB3D2AD0C9F7968D4D6EA5 (void);
extern void Light2D_UpdateSpriteMesh_mC2B147782628BF45D0285A9E987789AD8DDAD3BA (void);
extern void Light2D_UpdateBatchSlotIndex_m08FC5148F1F8460C6B146BEDBB11E3D4300E7A46 (void);
extern void Light2D_NeedsColorIndexBaking_m7FC2F8AFC1DB91154EAD30878F6DC84362F6EF0E (void);
extern void Light2D_UpdateCookieSpriteTexture_m4EFEA55D249D5CE52598F69333AB6C7527AA40FF (void);
extern void Light2D_UpdateMesh_m7BF8080C85FD793D22637AE363E24ACFF9E86B96 (void);
extern void Light2D_UpdateBoundingSphere_m4FA46F8CDF8D684F9878DCDEE4ADE97E12667365 (void);
extern void Light2D_IsLitLayer_m4D56AA7A4749D4CCE3E8AFED8D47AA6506F3B5D0 (void);
extern void Light2D_GetMatrix_m72971588F4FD378CDAFF5CC261C86F14AAED65A5 (void);
extern void Light2D_Awake_m68478B72028919B7F2875EF70F8CD4B91B9249D9 (void);
extern void Light2D_OnEnable_mE1D3021C29C009A55D524234A8FD443427857CFC (void);
extern void Light2D_OnDisable_m9990C78B1657A0889E9F17F4D6F775D2FAD9BFA7 (void);
extern void Light2D_LateUpdate_m37642B2F400EFA4407E6A055D231A6F685223C22 (void);
extern void Light2D_OnBeforeSerialize_m866376CDEAE8B560BCD3B5984E6CF93F62D72EFB (void);
extern void Light2D_OnAfterDeserialize_mB21C7A863E878839DC4381DDEC85667434DC21F2 (void);
extern void Light2D_get_pointLightInnerAngle_m9081D25FA7367194C6BB668640439588CB5ACEE8 (void);
extern void Light2D_set_pointLightInnerAngle_m55AF6A0B3E79FA451763D2712BE6417A141877FB (void);
extern void Light2D_get_pointLightOuterAngle_mE25C3D35226DB71C0DBFF2737E4F33D8F240FEC1 (void);
extern void Light2D_set_pointLightOuterAngle_m79DBA1BDBFEDCD89F19E5FDC782B70D36E883481 (void);
extern void Light2D_get_pointLightInnerRadius_m3A82695BC7D7817665CFDA1524B9FA6F81D390D5 (void);
extern void Light2D_set_pointLightInnerRadius_m7F3013BB1EBF3B1DBBCB1B856B69FE1C7FAE8C60 (void);
extern void Light2D_get_pointLightOuterRadius_m9DC18E51091F3977F2F49BD4DAC7A42E379B3FA4 (void);
extern void Light2D_set_pointLightOuterRadius_mADBCDB215684B1EC3C08FF3C5704D3B74C68F26A (void);
extern void Light2D_get_pointLightDistance_mC14DA7B2C9FF402C9E92973614370A286DA5D26C (void);
extern void Light2D_get_pointLightQuality_m8CA8D328F163D35494D945DEF4C73057427E5844 (void);
extern void Light2D_get_isPointLight_mCAAB187BBB52B8A631122279D2349D6F598505E6 (void);
extern void Light2D_get_shapeLightParametricSides_mC35FEE23894A8FAA0F7D0BD061A7B90F0EED1C4E (void);
extern void Light2D_get_shapeLightParametricAngleOffset_m2E8D6EB3B554B19F4E721C661030BF81CCC318A1 (void);
extern void Light2D_get_shapeLightParametricRadius_mF68CA9032F11C90DC1EBDDA8225D3A2D7294CA4E (void);
extern void Light2D_set_shapeLightParametricRadius_mF4DCB86DFC4543C658231C8C0058675C0BBE50C0 (void);
extern void Light2D_get_shapeLightFalloffSize_m7721FEC3F1107EFC4D6588414B08C54A0050150A (void);
extern void Light2D_set_shapeLightFalloffSize_mAD3C488B2F8C365B89A9B6C876AA8B34F3C450F0 (void);
extern void Light2D_get_shapePath_mBB6B7B49C59F4EA01CEC08C3B1C869AE921AEAED (void);
extern void Light2D_set_shapePath_m5B5BD8F64481A0C87635834682D57C9F90C701E4 (void);
extern void Light2D_SetShapePath_mA62CB7D32E7EDBD594B19FEF491F0921FCEFA7DE (void);
extern void Light2D__ctor_mAC4A9EAB4D0F7E8D9CACE0DAC7D2F217F9B82798 (void);
extern void Light2DBlendStyle_get_blendFactors_mC60D457EC148FAD9DF648D8744E8FD1739428D76 (void);
extern void Light2DBlendStyle_get_maskTextureChannelFilter_m0BAA8F7231AC36BBD698EF17ADA929551CBEA5D1 (void);
extern void Light2DBlendStyle_get_isDirty_mBAE482E03B7BBAF92D33250196D9572CBEE943DA (void);
extern void Light2DBlendStyle_set_isDirty_m9E685039DACBFC96AD6BFE2B0159F8D48650DE37 (void);
extern void Light2DBlendStyle_get_hasRenderTarget_m1165817ABB896D6794DD535B31F43253F0C77326 (void);
extern void Light2DBlendStyle_set_hasRenderTarget_m8C3590F7559A6A200B2A07395275A10E4BB453FA (void);
extern void MaskChannelFilter_get_mask_mE84FC5DE03D3B8FC30FCDD09098E5BA108FEE550 (void);
extern void MaskChannelFilter_set_mask_mBA4F50BB0878FF53E2BCB044D4AE6056EBF6E15B (void);
extern void MaskChannelFilter_get_inverted_m4F955DAACAED39F771A5D442479390BA5B87D623 (void);
extern void MaskChannelFilter_set_inverted_mD1CD0612835F5757B16C099121FA8CD2B6E42FEF (void);
extern void MaskChannelFilter__ctor_m0EB791DE0052FF44F5E9FEC4F927F7459CAFD7D2 (void);
extern void LightStats_get_useLights_m5D283F1EE777C5125CBCF130FCBD5E65606B9685 (void);
extern void LightStats_get_useShadows_mD182F3CEE2ADAACB4E5621B0B095BBDCBFD6300C (void);
extern void LightStats_get_useVolumetricLights_m24B97C84F76DD41D6D9D6A1AB1CB65AD08333D92 (void);
extern void LightStats_get_useVolumetricShadowLights_m4F51EC079FF136ED8D217328333FBCAA990C8324 (void);
extern void LightStats_get_useNormalMap_m62161A14013BBDB018B9DA75376A1864199FCDED (void);
extern void Light2DCullResult_get_visibleLights_mB8E2057748258CD9EB430AFEC401B213FA6E4145 (void);
extern void Light2DCullResult_get_visibleShadows_m25A1CC24120F4C5ADA60E68A2D6E701DBCAA4085 (void);
extern void Light2DCullResult_IsSceneLit_m6D24A4E77358390F0AC33FC0BA64EFDE2ED93BCC (void);
extern void Light2DCullResult_GetLightStatsByLayer_m9B6FBE09E3321D791A246F98DB61C7B0A7102C42 (void);
extern void Light2DCullResult_SetupCulling_m5B926BF3D30A11B90EB2F54FE4F9D9E0D5E403FB (void);
extern void Light2DCullResult__ctor_mC609816D7818C069F1791B5AC354A2F81A57349A (void);
extern void U3CU3Ec__cctor_m5C5FCFE7B39B50262F61E9F07686630015CEE020 (void);
extern void U3CU3Ec__ctor_mDBA5983FBEEB4E8494D3CBC320A3839EF7392A33 (void);
extern void U3CU3Ec_U3CSetupCullingU3Eb__8_0_mDEFDD320D9E8E54B0CEA64D28E2BA2496FCF9C76 (void);
extern void Light2DManager_get_lights_mFB510837AFAAF5DB9DA1E017531AACB670BD7DAB (void);
extern void Light2DManager_Initialize_m6532C2F2F8E3B49E3ED9B37E810CAAC263D648C4 (void);
extern void Light2DManager_Dispose_mE976706C09DE563755418A1930B32AEBBCB21F13 (void);
extern void Light2DManager_RegisterLight_m99273E83E7AEEBEE04C019DAFEF468B3D0C11613 (void);
extern void Light2DManager_DeregisterLight_m9348E3ADAF93EA79BECC28A28D5EEA0B855193B4 (void);
extern void Light2DManager_ErrorIfDuplicateGlobalLight_mEF25755359B8589F953E3425634C5B9505406B76 (void);
extern void Light2DManager_GetGlobalColor_m7B7BDD801A82E47F014F065FCA9B2CB673308A22 (void);
extern void Light2DManager_ContainsDuplicateGlobalLight_mA1B36C42CAD7B2CBA22CEA1B2E9B07EAD2AE289E (void);
extern void Light2DManager_GetCachedSortingLayer_m764C670FCEB4F029DB74C2B0D98CEE94B248173B (void);
extern void Light2DManager__cctor_m84CD663BA5581E4C8A1CC0DD2F5272E668CF4EE7 (void);
extern void LightUtility_CheckForChange_mB65C2BCC30014B662DEF8F099B86234FE654E5D7 (void);
extern void LightUtility_CheckForChange_m57245EDC04F9C7C58CADC1146C1B3E09675B3AFA (void);
extern void LightUtility_CheckForChange_mDF8E1A2DEB5B99CA49ADB27F8083449114CF25FD (void);
extern void LightUtility_CheckForChange_mB49921527380387C972B55BF35CCD192D86C8EDB (void);
extern void LightUtility_CheckForChange_mD5C8C73254BBE688F9DEF3502F6F8D8211018C94 (void);
extern void LightUtility_TestPivot_m0ADA4D05343C7D68A724CB6F54337D41B9DE5C1E (void);
extern void LightUtility_DegeneratePivots_m678E91731D80F748B6A1A90AD285916812BDBD59 (void);
extern void LightUtility_SortPivots_mE32B6077F82E1D69C0C50D7E575C3880EDD252FF (void);
extern void LightUtility_FixPivots_mCBA9F5F5CDD094A370A315C5EF4E203FCB6210BC (void);
extern void LightUtility_GetOutlinePath_m589B9C90B523CC24B0C82F6F0199E9AA9CF8E8FA (void);
extern void LightUtility_TransferToMesh_m71367E6605D8D36FB118BAF1E83F79316C657556 (void);
extern void LightUtility_GenerateShapeMesh_m11E9ED4523584976B57033E5125BE7CA2D749E86 (void);
extern void LightUtility_GenerateParametricMesh_m66406BC7B72696E3EB71FD2DAC6DBE0662EE1409 (void);
extern void LightUtility_GenerateSpriteMesh_m62A264A6CB484FD49890FD3E92FDFA13E30C7AFA (void);
extern void LightUtility_GetShapePathHash_mE74777FD3CFEE93074C35B4A82E8B77B61EEA31D (void);
extern void LightMeshVertex__cctor_m535187D6EA25C79BF942E2545F937DB924CF09BC (void);
extern void PixelPerfectBackgroundPass__ctor_m86A0236C3536B437EE8583523A1B39209186FE88 (void);
extern void PixelPerfectBackgroundPass_Execute_m8740C7CA194D1FDEFA40BCF0B62D62FE412434BA (void);
extern void PixelPerfectBackgroundPass__cctor_m51A08FCDEC8045C7D997043F0BECF786AA64FF88 (void);
extern void Render2DLightingPass__ctor_m26264190053C29ACEE89A5E978858D57AEDE365D (void);
extern void Render2DLightingPass_Setup_m3FBC7898206307E8806EA0DAB34858D9CBB7093C (void);
extern void Render2DLightingPass_CopyCameraSortingLayerRenderTexture_m99CEA96622C89738D23B8CABC4542C415801A1D4 (void);
extern void Render2DLightingPass_GetCameraSortingLayerBoundsIndex_m01AA8FA01196C4DA9EF63DA66622C00632838785 (void);
extern void Render2DLightingPass_DetermineWhenToResolve_m7E6E2643CBADED1922AB40FCFE273B30E664E657 (void);
extern void Render2DLightingPass_Render_mBA45ECE41798777E6CE9A9E25BA469DDA226C230 (void);
extern void Render2DLightingPass_DrawLayerBatches_mB46F2DC778EF51C79DC061C4AE794558D0C61EDB (void);
extern void Render2DLightingPass_Execute_mE7EF30C9B985249CBCF623653F2228BB4CE4964B (void);
extern void Render2DLightingPass_UnityEngine_Rendering_Universal_IRenderPass2D_get_rendererData_m10FAA8B0DF4E1F03CF576EDF27F4312AA9512278 (void);
extern void Render2DLightingPass_Dispose_mA6539778FF349A2FA439F95876DC380386517317 (void);
extern void Render2DLightingPass__cctor_m8AF4838112C375B2ABDDC71B34B73F3236DF2630 (void);
extern void LayerBatch_InitRTIds_mC15F5BAF8B993D83A36EC1220A772C86C6C3935D (void);
extern void LayerBatch_GetRTId_mD844336931BE54E9203E890BFAB139DF9F713266 (void);
extern void LayerBatch_ReleaseRT_mD2C253FB4B36C42D7DFE559872AFD91CFA7E5024 (void);
extern void LayerUtility_get_maxTextureCount_m0954302D62496ED6F17F322C286969822856B2A6 (void);
extern void LayerUtility_set_maxTextureCount_m088362A56E478B589DF8E3FF3C7F764670EFCB15 (void);
extern void LayerUtility_InitializeBudget_m48BD2B2D16A4C7AA7692146F1A92F8E31EAAE7F7 (void);
extern void LayerUtility_CanBatchLightsInLayer_mEBCDDD3A3D16D256A58778B1F685613EC8EF289C (void);
extern void LayerUtility_CanBatchCameraSortingLayer_mF59A650337B6A95FF7FFE7E58E6D79B13A056068 (void);
extern void LayerUtility_FindUpperBoundInBatch_mA364B9A4A86A29D313996CAD19109CFB69348BA5 (void);
extern void LayerUtility_InitializeBatchInfos_m8A3B081E3B668FB1707A0EE9D1E560EF48EBA839 (void);
extern void LayerUtility_CalculateBatches_m60B7E1AE0A4BF35AD520351DB9E46A99E2B647C2 (void);
extern void LayerUtility_GetFilterSettings_mEC82CC59AE688E41AEED3E128ADBB9ECAF154EB2 (void);
extern void LayerUtility_SetupActiveBlendStyles_mA3A4563344BAE12344466651E0CFE2454B392E43 (void);
extern void Light2DLookupTexture_GetLightLookupTexture_Rendergraph_mFD299D2C9AC6F1A9946E2922DBF90AFE0D7C20A2 (void);
extern void Light2DLookupTexture_GetFallOffLookupTexture_Rendergraph_mBF4C976304B9C96240014BD3A9C6D7CA6694DACA (void);
extern void Light2DLookupTexture_Release_m32563DD10E4BC167F4A49C3D4B69AB18D8AD8102 (void);
extern void Light2DLookupTexture_GetLightLookupTexture_mBD4AB58BAEFD9E69EE37735A83D08BF9A830FFFF (void);
extern void Light2DLookupTexture_GetFalloffLookupTexture_m370A351FF1675270196D6BE4612A1BD3F2FF105A (void);
extern void Light2DLookupTexture_CreatePointLightLookupTexture_m95160038940FEF512EF7E3128BE979CFABEA210B (void);
extern void Light2DLookupTexture_CreateFalloffLookupTexture_m2E830891B0926BC7427F81647420073B422AD151 (void);
extern void Light2DLookupTexture__cctor_m1583B83BBAFB4C72A057BB913EC26FD063A3E6C4 (void);
extern void LightBuffer_get_graphicsBuffer_m78B4581311CD4DA3A65EBD56E7B03905F3EE8B8F (void);
extern void LightBuffer_get_lightMarkers_m9A678AA9F780CF5879BC6548AAF674E2D73CD96A (void);
extern void LightBuffer_get_nativeBuffer_m2F20F7BE4C63025BF1043EB7A430EBC3CE7451C5 (void);
extern void LightBuffer_Release_mDCB2E543DA8A81D799C34F98B5F5795C1C1A7A84 (void);
extern void LightBuffer_Reset_m288E71C0BD5B2F9302451502AA81EAC4F290ABDB (void);
extern void LightBuffer__ctor_mB824945998E943542F68A78309DEBCCBE2EB31B7 (void);
extern void LightBuffer__cctor_mDA7305E6FB4313A66D6E1E28878DA0DAE771C9A0 (void);
extern void LightBatch_get_batchLightMod_m56B5FEDF5AA2A01781E1BD25EF12AC3B79B148AD (void);
extern void LightBatch_get_batchRunningIndex_m04AD40332816E83886B47F651097EAE1A911B9E0 (void);
extern void LightBatch_get_isBatchingSupported_mBAC6C205C6E2EDB1089FB9B2202339D382F6943C (void);
extern void LightBatch_get_nativeBuffer_mAA551A379DD5B0E072A01B9AB46DB9183C6DE557 (void);
extern void LightBatch_get_graphicsBuffer_m3445577419D6248C1EFDC0E1CCD33B5EC67204D0 (void);
extern void LightBatch_get_lightMarker_mAE455AFA7EBF68DD52F8E0D86D6CA8D488095626 (void);
extern void LightBatch_GetLight_mE76BDDAD1500687E8648244B966B1E8C2FA06A30 (void);
extern void LightBatch_get_batchSlotIndex_m992B374107A15AFAFF7D8B02A2EE8586E788BA42 (void);
extern void LightBatch_SetLight_m4A73C022698DA833A16A2F8E71ED804E86CA7069 (void);
extern void LightBatch_GetBatchColor_m3F361FD7BC467A4718E4130E739DF7B6E7568493 (void);
extern void LightBatch_GetBatchSlotIndex_m41C6D6BADBE271C57C905770AEB9E3E11D09E13A (void);
extern void LightBatch_Hash_m8D1AF6C85DCCBD5EA77B9CC83CA8D6878C7E3350 (void);
extern void LightBatch_Validate_m299E5FE2BA84F4E439E51881D4BCA3D1E83BF5FB (void);
extern void LightBatch_OnAssemblyReload_m2586E10B8E6D0B026E7718830AE3D144B171E1D5 (void);
extern void LightBatch_ResetInternals_m494376221916E69CEEFEF6720D642CAE126B8C7D (void);
extern void LightBatch_SetBuffer_m6952E68B3E54047ACC7E32A7C7C0DDD3F88AA666 (void);
extern void LightBatch_SlotIndex_mB701097B2FB44229BCF6404AEB67CBD22E2DF85F (void);
extern void LightBatch_Reset_m5A1ADCA53BE2CDA777644526A1437BE5437099C3 (void);
extern void LightBatch_CanBatch_m7A43AC6E93A11702616ACF648DEB1D8950B0709E (void);
extern void LightBatch_AddBatch_mCF836429D32A838D30210F3FA1FE18AA0A429121 (void);
extern void LightBatch_Flush_m4EAD3228E1F1E0D8BEFE24CF8258230DA9C14E38 (void);
extern void LightBatch__ctor_mF10B3F2F99C2E0721F9D14D633A5C8C490FD218A (void);
extern void LightBatch__cctor_m6C6633B82EF139F947B9B4F213F46175607F26AE (void);
extern void RendererLighting_GetRenderTextureFormat_mEDBD21AF251A0E99A12DE7A7A99905E0D78C61F8 (void);
extern void RendererLighting_CreateNormalMapRenderTexture_m932AF3AB045A92113107500CFF4E974EF18BF9E2 (void);
extern void RendererLighting_GetBlendStyleRenderTextureDesc_m86C721468AF33D420C2F47C73B05E0FBD60164EA (void);
extern void RendererLighting_CreateCameraSortingLayerRenderTexture_mF66525AF6AB1DAFF475FAFA430C8CA23CE5186A0 (void);
extern void RendererLighting_EnableBlendStyle_mFE530D1333F0F80ECD69C51DB35E8FA5A5DEBFBE (void);
extern void RendererLighting_DisableAllKeywords_mAD6D8BCA9DDEA324040D8E9A17DE7C1BFB49C743 (void);
extern void RendererLighting_GetTransparencySortingMode_m463D291DD520F6A129519CF69F715FBF8EB8FD67 (void);
extern void RendererLighting_CanRenderLight_m69470E4D13FCEA7052E2F5965642FA6AB3E8BCA4 (void);
extern void RendererLighting_CanCastShadows_m007FCFBC1F5FE7C190417065A619DF5614FD055D (void);
extern void RendererLighting_CanCastVolumetricShadows_mF09D3A010A68D9DB27D57D64A54B6FCAE6FB58B6 (void);
extern void RendererLighting_RenderLight_m2E2042C9D20DF75D6FFBCE0D29DAF2C17CF3BBB3 (void);
extern void RendererLighting_RenderLightSet_mD634C1448213EDFE58CD71499477EA62DC1F4E71 (void);
extern void RendererLighting_RenderLightVolumes_mABDC85F23459F139A180FD16AC919DA05C241D49 (void);
extern void RendererLighting_SetLightShaderGlobals_m2CB3DC3E167851D00A77A9C4E9E9FC940C039693 (void);
extern void RendererLighting_SetLightShaderGlobals_m85DCE18286830A1814FF5F2FA6AB4202CD732ACD (void);
extern void RendererLighting_GetNormalizedInnerRadius_m1BBA02BC5A10A3472C03651FC511A6B10FC0ECF6 (void);
extern void RendererLighting_GetNormalizedAngle_mEBAA7923DB8FD29DB91D8CD26F97F49C5A5AC258 (void);
extern void RendererLighting_GetScaledLightInvMatrix_m5CB21F61E5DF1A2CAF4B010C90BFAA7A9EF184D2 (void);
extern void RendererLighting_SetPerLightShaderGlobals_m26403ADAD43320131345474537D1B4EE16A4C200 (void);
extern void RendererLighting_SetPerPointLightShaderGlobals_mD9A7BAC692944F6F7E3489F91333CCD313A80892 (void);
extern void RendererLighting_SetCookieShaderGlobals_m1A84AD0A5AC9F1B8A4050FC1D947F27EC8F73F68 (void);
extern void RendererLighting_SetCookieShaderProperties_m1D75FDC98B17FAC9E4C7D92C7ED895BFD0C8CEC2 (void);
extern void RendererLighting_ClearDirtyLighting_m9E939AE0122DA564504C6B3A8D6D5998DE5722AC (void);
extern void RendererLighting_RenderNormals_m9F0998883AF753E5E9FFA2C9781F6B2F318525E9 (void);
extern void RendererLighting_RenderLights_m774C14F01003219B9E127D1B6D24B4CC9A6AFEA4 (void);
extern void RendererLighting_SetBlendModes_mC61B8F5126573E133D96670E9DEA5342855D4F87 (void);
extern void RendererLighting_GetLightMaterialIndex_mC205E701C0DD0359B0AD8423659EEFBCBE3D56CF (void);
extern void RendererLighting_CreateLightMaterial_m6D63176168EADDB2441BC7198C8F787CA0332884 (void);
extern void RendererLighting_GetLightMaterial_mF962F96AAECFF774B9B61393D64E11E433660110 (void);
extern void RendererLighting__cctor_m058F06ABA7017A23C218D93599FB7175E12CB074 (void);
extern void PixelPerfectCamera_get_cropFrame_m049E2C02AED3C1C244B3AFD1E0B6104AFCC33A60 (void);
extern void PixelPerfectCamera_set_cropFrame_m6CE5030A1594EE99DA8FAE0B032182DD88033A2E (void);
extern void PixelPerfectCamera_get_gridSnapping_mEF34B7A6CFF739935B11FF478B1F096C5D321A80 (void);
extern void PixelPerfectCamera_set_gridSnapping_m54000C548DDFE0CF863166F82D79E55F5D338796 (void);
extern void PixelPerfectCamera_get_orthographicSize_m64808DF017F8BE75E1581746CB142F22A151A1A2 (void);
extern void PixelPerfectCamera_get_assetsPPU_mDF5D73D22D07FE0CAA7761673527FF30BD10EB4D (void);
extern void PixelPerfectCamera_set_assetsPPU_m592A5390903DCB8D20122E25694705CA311B9CCE (void);
extern void PixelPerfectCamera_get_refResolutionX_m5523E53A067744D8A32B721DE936B800B43790C0 (void);
extern void PixelPerfectCamera_set_refResolutionX_mE3F8317F6E4D4D8D355EF85CF8751C77A655DEB9 (void);
extern void PixelPerfectCamera_get_refResolutionY_m2E7C84EA248898609CA5ADD30E211976A44CD521 (void);
extern void PixelPerfectCamera_set_refResolutionY_m6440FB4E82E6E04091CDC83C8CF76E46CBF39370 (void);
extern void PixelPerfectCamera_get_upscaleRT_mE8E62C676679B7D8E889C9CC73996E36A677A7A5 (void);
extern void PixelPerfectCamera_set_upscaleRT_mBF56C58F57DF202E220A51A6A0BCF447987ED9E8 (void);
extern void PixelPerfectCamera_get_pixelSnapping_m016C9FFAF9D3A769188F842CC09060AD3B12E593 (void);
extern void PixelPerfectCamera_set_pixelSnapping_mD5038B0AACF71EE91A9223E753091FD5E1BC756B (void);
extern void PixelPerfectCamera_get_cropFrameX_m1E3DA844B064A4A258919FD41AA39AA819CAB405 (void);
extern void PixelPerfectCamera_set_cropFrameX_m3C84FDF660EC143C9D15EA0F2EBA529E8C29BB60 (void);
extern void PixelPerfectCamera_get_cropFrameY_mB6069E68FF57C4F9B5C5D45F434AA4E23DF6C166 (void);
extern void PixelPerfectCamera_set_cropFrameY_mF679DFACB003DAB2B21740E21CECD739C9754B6D (void);
extern void PixelPerfectCamera_get_stretchFill_mE5BAB5E287F73A850CC9870D84FA176F7560BC44 (void);
extern void PixelPerfectCamera_set_stretchFill_m0D6DA91446635FA665C3D405875B8237B3E1E2FD (void);
extern void PixelPerfectCamera_get_pixelRatio_m43A1ECE99E8FD38158D1AC65011DD98B50BD3A60 (void);
extern void PixelPerfectCamera_get_requiresUpscalePass_mB8C4EA7518270939C98E1868DCF65DE8385A23FB (void);
extern void PixelPerfectCamera_RoundToPixel_mDAE91343C303FF75DDCDBFF55A44675ADF34A919 (void);
extern void PixelPerfectCamera_CorrectCinemachineOrthoSize_m980A6969D95EA0F73DD16E9E3DA8E22565D94FB8 (void);
extern void PixelPerfectCamera_get_finalBlitFilterMode_m7DE0B801BB4BD1B1CD41AB0EF6CB9A2741B006AF (void);
extern void PixelPerfectCamera_get_offscreenRTSize_mB23A1DC33EC0A8422CF1B4D618D291E2A3BD2F5B (void);
extern void PixelPerfectCamera_get_cameraRTSize_mE0CDEFF078B313BDA170E6EDC66A8A1C8F8B0656 (void);
extern void PixelPerfectCamera_PixelSnap_mB4A7DDD3EC5A6BEE7086125E61E3A716CD25BFD7 (void);
extern void PixelPerfectCamera_Awake_m25F30BF98810839D5A735CCAE9F57469B0398A99 (void);
extern void PixelPerfectCamera_UpdateCameraProperties_mDD3B992B0A44231F2FD60075821015FDDD625E99 (void);
extern void PixelPerfectCamera_OnBeginCameraRendering_m587FA327280AC017B622DB1B4706FF6D113D279A (void);
extern void PixelPerfectCamera_OnEndCameraRendering_m5D4D2899F818CFB9566FA00DB9734B19FE4F5172 (void);
extern void PixelPerfectCamera_OnEnable_m1FC65FDDB4482827D6111686FA435CDCBA163CC1 (void);
extern void PixelPerfectCamera_OnDisable_m3F6D16F4423CD5622C68EA0138A37E3947D120A9 (void);
extern void PixelPerfectCamera_OnBeforeSerialize_mED54D4AD64682B1314D338134ECF4790E0062546 (void);
extern void PixelPerfectCamera_OnAfterDeserialize_m4C38B4CC1F67277C234831D0F0F6608DBD8F818B (void);
extern void PixelPerfectCamera__ctor_mCF6FB2072357E5CB9F7AA1EB4E09E5BA84BC54C7 (void);
extern void PixelPerfectCameraInternal__ctor_m6BC5985512637F4B2AAD903E5D411B954CB8E795 (void);
extern void PixelPerfectCameraInternal_OnBeforeSerialize_m4AE0DDB47BB97880367D494025A2CD9B0EDCCB45 (void);
extern void PixelPerfectCameraInternal_OnAfterDeserialize_mEA652F30B205113EED7848CE7948EB8F7709ED3B (void);
extern void PixelPerfectCameraInternal_CalculateCameraProperties_mB201DE82608102113237D5509D085C7ED74BB9FE (void);
extern void PixelPerfectCameraInternal_CalculateFinalBlitPixelRect_mDBD399AEAA750ACC3B03C21110E361758DBC0C82 (void);
extern void PixelPerfectCameraInternal_CorrectCinemachineOrthoSize_m885222B60F8A525214DE5D945448F245E8C9A4D4 (void);
extern void Renderer2D_get_createColorTexture_m24FE1C199854F11A89066AC3E5BB58A00FD53BDB (void);
extern void Renderer2D_get_createDepthTexture_mDBD88B4419A0E76C156E6401C90FA47A3415684C (void);
extern void Renderer2D_get_colorGradingLutPass_m6A765DDECE48E4861DA23D8ACCE592585E419CF0 (void);
extern void Renderer2D_get_postProcessPass_mECF6708B2BBA3FCC65FC760A548F852E280FE1F1 (void);
extern void Renderer2D_get_finalPostProcessPass_m65C46BE4C556A8C15728944D1791A62197AEC04D (void);
extern void Renderer2D_get_afterPostProcessColorHandle_m9DA17585615C88AF653F0CED7EF417865AB89BBD (void);
extern void Renderer2D_get_colorGradingLutHandle_m26F4A7E5FDFC6A4E1A54B006EF97C60CBE0157E8 (void);
extern void Renderer2D_SupportedCameraStackingTypes_mD8EE833AF318A1A0D7416DD70E0BB95BE1F80A4C (void);
extern void Renderer2D__ctor_m4B5E6E72241B763D6C6B0BB52F0336E4BC90B3F9 (void);
extern void Renderer2D_Dispose_m641D39C52711232ED5E4FFC8D6D5F00D2302E62D (void);
extern void Renderer2D_ReleaseRenderTargets_m2FB434B00ED7FF63A3BDDED2DA4E174AF705B48E (void);
extern void Renderer2D_GetRenderer2DData_m1C19D051822EC98D12A95431E896A4153EA7CE69 (void);
extern void Renderer2D_GetRenderPassInputs_mA879518FAE9BDAF8E01C4D199BC4A11FEC0F1D4D (void);
extern void Renderer2D_CreateRenderTextures_m1FF4B66860212106DFA96CBC0AF2E1B3F23A9A3C (void);
extern void Renderer2D_Setup_m44EA4521E611DCDB7DA1EE926433974EE28FE332 (void);
extern void Renderer2D_SetupCullingParameters_m22BF0D637F9B0F6300A47F3E68FB3192C71F1556 (void);
extern void Renderer2D_SwapColorBuffer_m4B9C2623930435CC20B25662EDAA596D4306332B (void);
extern void Renderer2D_GetCameraColorFrontBuffer_mF3BF31412BDA85A25E6D8C70F1D9F6434655E036 (void);
extern void Renderer2D_GetCameraColorBackBuffer_m262FA1E93C4FE49599975A9C9022EE16F03ACF0C (void);
extern void Renderer2D_EnableSwapBufferMSAA_m85629DFFFE0A76ECACA671DD571F6B6D06CFD755 (void);
extern void Renderer2D_IsGLESDevice_m784A2B7E1F93D4D6FEE13B6BAD70A61B058C55CE (void);
extern void Renderer2D_get_supportsNativeRenderPassRendergraphCompiler_mE21C328C01DC63AE638EEF00D37E682DAC06167F (void);
extern void Renderer2D_get_currentRenderGraphCameraColorHandle_mE02065704413CACA7EA6B4ED9230038B1C10ECF3 (void);
extern void Renderer2D_get_nextRenderGraphCameraColorHandle_m4624775CC789302B64F7A060EF79EE0A6EC20912 (void);
extern void Renderer2D_IsPixelPerfectCameraEnabled_m9A48A3502D9BC27EFF7DCBA574F82C574F1EBA38 (void);
extern void Renderer2D_GetImportResourceSummary_mB0D0D64AF8D8E61E918D4D72D5289DD47E59B94E (void);
extern void Renderer2D_InitializeLayerBatches_m2425AA5DC7B42EEF042C9365D5A88985FE953ECA (void);
extern void Renderer2D_CreateResources_mC3BA123FDDA55BB098C002571C413084BAED9E50 (void);
extern void Renderer2D_CreateCameraNormalsTextures_mEF0C77F0CCE1542066C1C1A9984FBC475D5A2918 (void);
extern void Renderer2D_CreateLightTextures_mA3E16F64DB53CA3E6D927EA861EE35C7FBDC09E7 (void);
extern void Renderer2D_CreateShadowTextures_m025418376AE331C0F71E4CD1F08491E17EA10C03 (void);
extern void Renderer2D_CreateCameraSortingLayerTexture_m0C97B5CFA263C6DFE47DA968730F9A4430C3A44F (void);
extern void Renderer2D_RequiresDepthCopyPass_mB1261174566202F51233B0047886918B720FCA2E (void);
extern void Renderer2D_CreateCameraDepthCopyTexture_m48DF1D159858D7600733C9B3A9D3CDB87722E5A1 (void);
extern void Renderer2D_OnBeginRenderGraphFrame_m4701B5EE01040ED936C27AE5CDA5CDA621D95E65 (void);
extern void Renderer2D_RecordCustomRenderGraphPasses_m431274D940FA9D1391FD61D3030CAE1890D0B4EA (void);
extern void Renderer2D_OnRecordRenderGraph_m0ED9BBECB2A1205AD162D01FB6D45CA02F0D5E5E (void);
extern void Renderer2D_OnEndRenderGraphFrame_m47825DA20D08C77F60A4426C3AD7AE66AB618DE2 (void);
extern void Renderer2D_OnFinishRenderGraphRendering_m769EEDDD73EC5D2B237D64B67E871002F4860C2F (void);
extern void Renderer2D_OnBeforeRendering_mD99EC5307BC1C7A2A40D46E12928DF68B8BA2283 (void);
extern void Renderer2D_OnMainRendering_m7A40EC178991304026B01021D8BE8BA70706CAB3 (void);
extern void Renderer2D_OnAfterRendering_m3B3A0F4839EE95EA2D7FDB23F10BA5ECC09E5B6C (void);
extern void Renderer2D_CleanupRenderGraphResources_m8686E2A1F660754FF1E0B3E59B0C6E27F7B07966 (void);
extern void Renderer2D__cctor_m76CE69DEF4C4416F17613FE6D966E690D417D5A9 (void);
extern void U3CU3Ec__cctor_mD716CFE365EB51A827E6334430023CF8006184E6 (void);
extern void U3CU3Ec__ctor_m60886C74A7CC3A4EB396318D6A9497A487C54EEF (void);
extern void U3CU3Ec_U3CSetupU3Eb__44_1_m17013F6E689B41B9583F092CC46370358578D4E2 (void);
extern void U3CU3Ec_U3CSetupU3Eb__44_0_mDE87966701F599B6212B00EC85A1881E638BDB62 (void);
extern void U3CU3Ec_U3COnAfterRenderingU3Eb__88_0_mA4D0BBA40519006CB5CE2BE44E6E18CD55737DCE (void);
extern void Renderer2DData_get_hdrEmulationScale_m0A25B71CBF5B1482C129A0085DB78544EE7388BE (void);
extern void Renderer2DData_get_lightRenderTextureScale_m6FD4326249B00A168A36C2B314D4C035FEE7CB8F (void);
extern void Renderer2DData_get_lightBlendStyles_m5564525FC5E476F8B8B709FEE780729C0812D6A4 (void);
extern void Renderer2DData_get_useDepthStencilBuffer_m44AA62569D6155425AA7F1B669CB5AEFCC690BC4 (void);
extern void Renderer2DData_get_postProcessData_m4575F93D637FE88BDA13DFB57241E889A2E74D42 (void);
extern void Renderer2DData_set_postProcessData_mC663B5A3BD7313211AEE2251D72028087F63F516 (void);
extern void Renderer2DData_get_transparencySortMode_m5ADF4D78A34280993A7390E378593E8528ADE164 (void);
extern void Renderer2DData_get_transparencySortAxis_mD5ACC931C88DE8FBBC411F5BB16CD913CB3FAC6A (void);
extern void Renderer2DData_get_lightRenderTextureMemoryBudget_mFC1D055D5C96B4B25C66F853794141BAE440525C (void);
extern void Renderer2DData_get_shadowRenderTextureMemoryBudget_m77314E3103EF456B4F749A2398140395D626143B (void);
extern void Renderer2DData_get_useCameraSortingLayerTexture_mACAC0745A72D88C39DB1C9C0C2AC65B00EBBF574 (void);
extern void Renderer2DData_get_cameraSortingLayerTextureBound_m5947FD913CE5F264AC4353A189AAD19CBDED7706 (void);
extern void Renderer2DData_get_cameraSortingLayerDownsamplingMethod_m23F835318EF3DD5972097BAF42FFAC63B99B2AB4 (void);
extern void Renderer2DData_get_layerMask_m7B31D1269152C8E272EF718A9AF897B31FFCB14F (void);
extern void Renderer2DData_Create_m2F6E03C7561BA30ECF1186BA5EE691889AF67D58 (void);
extern void Renderer2DData_Dispose_m80832EF5A9C286413D89236184E101EA44A74EBF (void);
extern void Renderer2DData_OnEnable_m80F93198CEBE0B32B0BF020C36ED1F9EDEBE7841 (void);
extern void Renderer2DData_get_lightMaterials_mC074C05B230126E1DFB606C6FAFFBAEC99E3EADC (void);
extern void Renderer2DData_get_spriteSelfShadowMaterial_m7436BA5A575F6DF3AA3732E7C7C44ABFD63CDD20 (void);
extern void Renderer2DData_set_spriteSelfShadowMaterial_mB2B00B1F4E2DEE264AA79B0C2F22BA329095AECD (void);
extern void Renderer2DData_get_spriteUnshadowMaterial_m4415A748869AAA64EAC66ED6E1588639DEC52F2F (void);
extern void Renderer2DData_set_spriteUnshadowMaterial_mD8C9E9C343D025813148B3379D8F1828A423EE2E (void);
extern void Renderer2DData_get_geometrySelfShadowMaterial_mF219386663094A2C9BDC3B4E69EF8112827DC86A (void);
extern void Renderer2DData_set_geometrySelfShadowMaterial_mBCF9BB3E6AD7C14085F5B23ED646430D9CE7B590 (void);
extern void Renderer2DData_get_geometryUnshadowMaterial_mCBF3F176611E39E5D20A4917440AE0FC5B54E950 (void);
extern void Renderer2DData_set_geometryUnshadowMaterial_m533C4F9582B0C6E1270E153C9311D139858EC233 (void);
extern void Renderer2DData_get_projectedShadowMaterial_m2B41EE220BB6CF8F6AFDB4D6B9D5CEE0FB6A1532 (void);
extern void Renderer2DData_set_projectedShadowMaterial_m92F2FF910697148A41C6AEDC5600838FF0F97E79 (void);
extern void Renderer2DData_get_projectedUnshadowMaterial_m850DE00788FCD51337CA3A1B3DF818CED804A93B (void);
extern void Renderer2DData_set_projectedUnshadowMaterial_m6A37BE29BC05D7A3FBFC915F43D5E4BCEB43FF85 (void);
extern void Renderer2DData_get_lightCullResult_mEF0BB47B5B239E5736AEC45418B0BF9A41B4EE8F (void);
extern void Renderer2DData_set_lightCullResult_m62468050A0621073A84F29ACF962BB484BD518D3 (void);
extern void Renderer2DData__ctor_mA659713BB22B6FE0ACDD49CC1E0E1516802CC8D4 (void);
extern void ScriptableRenderPass2DExtension_GetInjectionPoint2D_mFA788D173427FAF328C861DF434DEAA3728A8491 (void);
extern void CopyCameraSortingLayerPass__ctor_mED6EC102125AF1570562831609D156696EE93C88 (void);
extern void CopyCameraSortingLayerPass_Execute_mC8C162963E5AF8105475B43617E22A88778D417D (void);
extern void CopyCameraSortingLayerPass_ConfigureDescriptor_m0A46E598DD77A6B72F84D00D20158491CEFC7778 (void);
extern void CopyCameraSortingLayerPass_Execute_m492B9B89C02CA4E79782A64639F4A0EA5CACADFC (void);
extern void CopyCameraSortingLayerPass_Render_m82672971BA4C765CEF93CE291D64A1955586E165 (void);
extern void CopyCameraSortingLayerPass__cctor_m72F21A0CBA1B505918B4BB72E1690B1480BFE5D4 (void);
extern void PassData__ctor_mD997610E5107FED77DD16C89D4A51804D2EFCCB7 (void);
extern void U3CU3Ec__cctor_mC7C67DF491D96912E544F500FCFA7EF2BD9FD76B (void);
extern void U3CU3Ec__ctor_m2FB7104470538C2E476C62573495F75ED05879C3 (void);
extern void U3CU3Ec_U3CRenderU3Eb__11_0_m96456D6CF6B1AEF48757F051518643B982B5A62C (void);
extern void DrawLight2DPass_Setup_m35EEC25BE39ABAA6040661E992438F7F1BE85CB1 (void);
extern void DrawLight2DPass_TryGetShadowIndex_mDA17DB1FE068F6A32FDB03A0CEA85766C14AF3EC (void);
extern void DrawLight2DPass_Execute_mEC6764D1C34A2A74A5F276B0096F8FEA1DB5E0F8 (void);
extern void DrawLight2DPass_Execute_m4A4A91917E220F021A9ECBC6CF4D135C7B4C8427 (void);
extern void DrawLight2DPass_Render_m7BB5D9A611AF0A77DB6D5D774024E0F38E3CD663 (void);
extern void DrawLight2DPass__ctor_m3D3BDF9772395119C09B44F99CF796387071FFE9 (void);
extern void DrawLight2DPass__cctor_m6CBDF5C5CF7B0C118075FD7F71FFD251AFDF2867 (void);
extern void PassData__ctor_m7F72C6A60504133FE239B30B21FC6AC5DB9E2D09 (void);
extern void U3CU3Ec__cctor_mC8386A9B25591BCFD4A537D26E74B1D2DA274E1B (void);
extern void U3CU3Ec__ctor_mE0FF665208C32A7E114E76FADE1D6CA53D08CFCC (void);
extern void U3CU3Ec_U3CRenderU3Eb__14_0_m1EDA35DD60317044CDC944DDE4123DD07B43B2F9 (void);
extern void DrawNormal2DPass_Execute_m30D5D86F00E92F89A86E6BB6CA3291B99995256B (void);
extern void DrawNormal2DPass_Execute_m3148A1BAD04EE7DDBABD030B95EE0FCC2C87035F (void);
extern void DrawNormal2DPass_Render_m1E4BAF20829CA5BD44EB7DFE6B3000C1F929C273 (void);
extern void DrawNormal2DPass__ctor_m568EA66D37D33BAADB5A1E96F063EEE8638B4BA2 (void);
extern void DrawNormal2DPass__cctor_mE7320BB314DEE83B891BB6ADDB8688ED17B57B3D (void);
extern void PassData__ctor_mE158AEFC4E7C134772877F81EE8FD8E31BA49CCE (void);
extern void U3CU3Ec__cctor_mFF47CEBF6A8510D1D4CFB0EC6DA07464867CDDD7 (void);
extern void U3CU3Ec__ctor_mCE24387C5806B003DDB03FA84853DE9086859410 (void);
extern void U3CU3Ec_U3CRenderU3Eb__6_0_mD746022E4248A894CB87047D3C2FA2DA7BE758A0 (void);
extern void DrawRenderer2DPass_Execute_m7130CAC027A9827B9E62A169E20DC4FA5557C3D1 (void);
extern void DrawRenderer2DPass_Execute_mB61335AC51212D54B27DF11342A4135ADC5B82B3 (void);
extern void DrawRenderer2DPass_Render_mC61B669FCFC9F83B867F99ECDD102FACC58A32A2 (void);
extern void DrawRenderer2DPass_SetGlobalLightTextures_m3AECF91E79FD41B7E78D76A7E6C2BE2CC3CAABEC (void);
extern void DrawRenderer2DPass__ctor_m4DB878CD10F2BEA503F17E314D9C4BE29D63C0A4 (void);
extern void DrawRenderer2DPass__cctor_m7B09578064656B3AD405EF2F1067AACCA7D33269 (void);
extern void SetGlobalPassData__ctor_mC85DCEFD6F707694ED86F3A365B11A2CD20AAEB1 (void);
extern void PassData__ctor_m96914E1FDB7E70ECAD1B5C74A778580091038A96 (void);
extern void U3CU3Ec__cctor_m137C88E3FE8060FDF72A592944BB5AA039CAA4D5 (void);
extern void U3CU3Ec__ctor_m93AF8CAF19BBC9BC37F655C973113459C437858C (void);
extern void U3CU3Ec_U3CRenderU3Eb__13_0_mFB43EF72AF3B344103B8EE74FA432CF18BB0F9D0 (void);
extern void U3CU3Ec_U3CRenderU3Eb__13_1_m70B9E0579218A27A1EBB23C795048DDC4EE2A825 (void);
extern void DrawShadow2DPass_Execute_m1D4260EEC7BE1CED5D0289E73052EC64B11FE488 (void);
extern void DrawShadow2DPass_ExecuteShadowPass_m689668EF493100A7BB9A76E3B77DD7B94BF7DA00 (void);
extern void DrawShadow2DPass_Render_mE51A12E83BD4E9E019DA5BD74233BCC00C970479 (void);
extern void DrawShadow2DPass__ctor_m4245DF8F5DD32496C01CEFAFEE195424AA9832E4 (void);
extern void DrawShadow2DPass__cctor_mFF79C7CBDF449C8B0756951D29911BCE2F5DD448 (void);
extern void PassData__ctor_m10D63718FB19937FE3B17DA4F1D6148CBB9F120C (void);
extern void U3CU3Ec__cctor_mB272CDF2EAE141C160D8D01FB864892F8454A964 (void);
extern void U3CU3Ec__ctor_m2EFBE1E3290F51983E5A977E3972962E4A0A40E3 (void);
extern void U3CU3Ec_U3CRenderU3Eb__7_0_m4784C8A334352870F304502BCA9D0B360595F399 (void);
extern void GlobalPropertiesPass_Setup_m7F161605AFD3E836610360B508CFF77624115DA1 (void);
extern void GlobalPropertiesPass__ctor_m97AF39BDE3A7DCC33F556F349220AE5D047A9DAF (void);
extern void GlobalPropertiesPass__cctor_mBACC65593AAAC2FFBA59673D5D3B60320B862DBA (void);
extern void PassData__ctor_m89CA203AE517A3FF347230DA13B7BFBBD86C0AEA (void);
extern void U3CU3Ec__cctor_m316252F7CB8BEA4442C36076F828FAD0702073D9 (void);
extern void U3CU3Ec__ctor_m331F10886D9DB150D337B9B079FD596018BF274C (void);
extern void U3CU3Ec_U3CSetupU3Eb__3_0_mE33D763E49C2E35A2727CBF40B9E9DA10973A649 (void);
extern void UpscalePass__ctor_m04FF134CB5F0BF0772080565721BE2519C4FFE14 (void);
extern void UpscalePass_Setup_mFCF16DCFA7283D9C22657A76D211F7545FB834DB (void);
extern void UpscalePass_Dispose_m4CAB01CBC95FDD232CF2862D7C9D887B0646BF1E (void);
extern void UpscalePass_Execute_m99194CAEE26DE24830EF13FF07ED83E0FD922CFE (void);
extern void UpscalePass_ExecutePass_m77ED665004E72B3CBB132B1DEA45D2E53EC0C83E (void);
extern void UpscalePass_Render_mF4A57DB5B46AB8D0439BDDC4517387EC40B4C0A9 (void);
extern void UpscalePass__cctor_mEF5427392FE235DE309B5104883329D903AEAE66 (void);
extern void PassData__ctor_m65E56911530CE69F154AF3A98713DB054F3895DF (void);
extern void U3CU3Ec__cctor_m3E912DFAC5EA20E0A91F421E4D2C6A607B7E71F0 (void);
extern void U3CU3Ec__ctor_m20846050203F7EFF4F82375F475ED9182BB1D874 (void);
extern void U3CU3Ec_U3CRenderU3Eb__12_0_mDD8F33683D7EA74046F03249C7015A1D202E4F36 (void);
extern void CompositeShadowCaster2D_OnEnable_m1D1BA15633A4AA5A677015692BB23121DB8D79FB (void);
extern void CompositeShadowCaster2D_OnDisable_m9B5D0C8C98B8805DFA560CA150C645FE6D9DD336 (void);
extern void CompositeShadowCaster2D__ctor_mB93048F65ADF0C49CFB021DDE81E14FE5C2FA807 (void);
extern void ShadowCaster2D_get_edgeProcessing_m60499748789A90315F9B94656F8C68D937E35D97 (void);
extern void ShadowCaster2D_set_edgeProcessing_m1451C019791C81C5885B87D098C8485F5EA598AF (void);
extern void ShadowCaster2D_get_mesh_m487088118BA14D1E81CACC9820703372075A8CD0 (void);
extern void ShadowCaster2D_get_boundingSphere_mB4512E20FC461F45DE40A37D118DA6A5395D8B18 (void);
extern void ShadowCaster2D_get_trimEdge_m3E133B9631F634C782E323204196FF536ABBD51F (void);
extern void ShadowCaster2D_set_trimEdge_m5E92365536558F94AC99A6B17D30ACB096F94A43 (void);
extern void ShadowCaster2D_get_alphaCutoff_mFAF68B32010B7ACF0D2C0E084D60EBA59BEC7834 (void);
extern void ShadowCaster2D_set_alphaCutoff_mC1C551B7C6F9AC41479F32A182A17C983531D337 (void);
extern void ShadowCaster2D_get_shapePath_mC13A25686164C42EAE3DDB81F8AE0563BE8F4AA9 (void);
extern void ShadowCaster2D_get_shapePathHash_mE1E759DC596D4301F247BA06901419262CEB3187 (void);
extern void ShadowCaster2D_set_shapePathHash_m38728C49239E936F5EBA8E36596E516D6FC5E102 (void);
extern void ShadowCaster2D_get_shadowCastingSource_mE28EEAF8CC7C2E234C6CDE2C9B3688189D0F1F7E (void);
extern void ShadowCaster2D_set_shadowCastingSource_m560DE5F59C4008CD2CDC8637523B402C7254A291 (void);
extern void ShadowCaster2D_get_shadowShape2DComponent_mE86BE25189D62590E996611DB23634FD253E7901 (void);
extern void ShadowCaster2D_set_shadowShape2DComponent_m4AA23FC17D0FB2BDB72171CF158C8F4B2F05AAB4 (void);
extern void ShadowCaster2D_get_shadowShape2DProvider_mAF6299D6D50E4B1FC94464BDA5667FBAB2552525 (void);
extern void ShadowCaster2D_set_shadowShape2DProvider_mDA38AAC7B3E1DEE2DE56CB8220C3A189AB479283 (void);
extern void ShadowCaster2D_get_spriteMaterialCount_m203A04545F355886EE50BFD034BEDEF7377FF021 (void);
extern void ShadowCaster2D_CacheValues_mCD794C74DB2595598D06DFA3C00A69981E839074 (void);
extern void ShadowCaster2D_set_castingOption_m75E57418A4B3CC61D5C81A5F825A484BA9D7E464 (void);
extern void ShadowCaster2D_get_castingOption_m11F5F86721E7CABA77996CDFCC3741F3C9A8F9DA (void);
extern void ShadowCaster2D_set_useRendererSilhouette_m04135E639BADCAB86C8BAFC23F2936AB7BF57A00 (void);
extern void ShadowCaster2D_get_useRendererSilhouette_mB7B8DCFC5478D297D696ADF346006E3112BB9A0E (void);
extern void ShadowCaster2D_set_selfShadows_m8D8A2A79F306B265284AB6212A631AC4474437B8 (void);
extern void ShadowCaster2D_get_selfShadows_m40E1BF40EEC25DF7538D6E6CACC6FFF86A113475 (void);
extern void ShadowCaster2D_set_castsShadows_m6F53705A974B0EE373F6174D58370E8775607A3C (void);
extern void ShadowCaster2D_get_castsShadows_m4D2E2254C8C21E8EACF99F26ECCB10E33EBAC234 (void);
extern void ShadowCaster2D_SetDefaultSortingLayers_m50FE472B68EC21CEAD03A6EF3E033993AF6153C3 (void);
extern void ShadowCaster2D_IsLit_m84045950AA9DE345C719C1A1F0AA4A0944FF71B7 (void);
extern void ShadowCaster2D_IsShadowedLayer_mB707B6F9E7F3AE915122E6B6D9684435668FDFF1 (void);
extern void ShadowCaster2D_SetShadowShape_m062B2E3C3CFFE7A81018B7FD2FA44755FD6C320D (void);
extern void ShadowCaster2D_Awake_m52BAB618C714732348D03CDAA412B3A079CDE4F1 (void);
extern void ShadowCaster2D_OnEnable_mB25C4DCE54980CCD65049F1FE14D134113A4B0B4 (void);
extern void ShadowCaster2D_OnDisable_mD438090275DC53244CC002CE2F453BC6E4952A5E (void);
extern void ShadowCaster2D_Update_mBBDA5909706AC0A79C88E02099242B149C4C60DC (void);
extern void ShadowCaster2D_OnBeforeSerialize_m8B240D66D9C8555CE7ABCE3BFD6151B74CE144B0 (void);
extern void ShadowCaster2D_OnAfterDeserialize_m0404319D0C2D7B2DF040AB6BBF04BD1CDF67788D (void);
extern void ShadowCaster2D__ctor_m50E27C1D91323382AFEEBD79F9C6179E2616AF0E (void);
extern void ShadowCasterGroup2D_CacheValues_mB461294C285A98D76A2F889CF19CC58FCC930E14 (void);
extern void ShadowCasterGroup2D_GetShadowCasters_mDCD74C94A151D61CAC8F53DE3750AD40CBFE51BA (void);
extern void ShadowCasterGroup2D_GetShadowGroup_mB6700A5E166ED6122ED38D71832EF82A526C3A99 (void);
extern void ShadowCasterGroup2D_RegisterShadowCaster2D_mC26BBCA52C510C4877AA853672C51D42C153B4A4 (void);
extern void ShadowCasterGroup2D_UnregisterShadowCaster2D_m3BEB2488CA7FBCC8769C4A244D3AA22DD277E5B2 (void);
extern void ShadowCasterGroup2D__ctor_m1E3787BD91B5E1712215CB902D57FC8173CB5584 (void);
extern void ShadowCasterGroup2DManager_get_shadowCasterGroups_mD2D34E88683CFFBE42142BC775F83689D96ADCC7 (void);
extern void ShadowCasterGroup2DManager_CacheValues_m7FB246220BE8D9A2CE5116B7A063C7418AE3F121 (void);
extern void ShadowCasterGroup2DManager_AddShadowCasterGroupToList_mC64E3E9C4D1A719BC850B7C227A55B4FDC58E1C2 (void);
extern void ShadowCasterGroup2DManager_RemoveShadowCasterGroupFromList_mBA201346FD60EB8374E87E48ED91E91DF6252081 (void);
extern void ShadowCasterGroup2DManager_FindTopMostCompositeShadowCaster_mD6B6696DF6EC83EEF4B1759E87BF77BEB2275189 (void);
extern void ShadowCasterGroup2DManager_GetRendereringPriority_m496B18814E92635A028C7FD89040DFB29A4651EB (void);
extern void ShadowCasterGroup2DManager_AddToShadowCasterGroup_m22CAC0B7B1DADE0CFF9BC08148065F671F911EEF (void);
extern void ShadowCasterGroup2DManager_RemoveFromShadowCasterGroup_m61E355382D0C4778303CF692F69C7D260E194F0F (void);
extern void ShadowCasterGroup2DManager_AddGroup_m942049A2176C9FA18311CF6FF2574F905EBE43B8 (void);
extern void ShadowCasterGroup2DManager_RemoveGroup_m976D7E3D5A893B9E3899F0B5ABD52E42509E3031 (void);
extern void ShadowCasterGroup2DManager__ctor_m774B2F78A3B18B34CB141CF9CCB560A0C1D08F7B (void);
extern void EdgeDictionary_GetOutsideEdges_m02DF9E4693CDEA1D5D5FB07DF069F31C0966CFBC (void);
extern void EdgeDictionary__cctor_m38F4BB0B42EE6F67D3588C0E84AE767FA6FEB6F6 (void);
extern void EdgeComparer_Equals_mFD5D9B24B13605CF165B53219FABADF1EFB46116 (void);
extern void EdgeComparer_GetHashCode_mE851ADE38E1762FFFCCA1581750ACD0EB00CC89C (void);
extern void EdgeComparer__ctor_m150453B6D728B16CE072AE418D1C6D3665E298A1 (void);
extern void ShadowShape2DProvider_Collider2D_CompareApproximately_m594778E96095AA90E72363AF8C524DA1AC2DA7B2 (void);
extern void ShadowShape2DProvider_Collider2D_TransformBounds2D_mC80021EAA7760A5E347141DAB886048A800FFF0D (void);
extern void ShadowShape2DProvider_Collider2D_ClearShapes_mBF8C6993482152D7657BABA7743C7E575CCF974B (void);
extern void ShadowShape2DProvider_Collider2D_CalculateShadows_m9BE3A6BCF84B7EEB24F6F089D858F99A7D7CCC7B (void);
extern void ShadowShape2DProvider_Collider2D_IsShapeSource_mCD042B9A4628E2193544D283906DE306A3E22CE9 (void);
extern void ShadowShape2DProvider_Collider2D_OnPersistantDataCreated_mD1B6820FD5F8F9665353E23E7DCDDAAB71A81704 (void);
extern void ShadowShape2DProvider_Collider2D_OnBeforeRender_m9444BCE0AC5C43D059B7D8AD5362F5FCF8DF0000 (void);
extern void ShadowShape2DProvider_Collider2D__ctor_m5C93C2D9491011944B0022C4B807AE4C64DF1958 (void);
extern void MinMaxBounds_Intersects_mB283C28F81335E978846E325A9482892D24644C1 (void);
extern void MinMaxBounds__ctor_m3D75999E612D51C1A02E2768D6291255386D9B40 (void);
extern void ShadowShape2DProvider_SpriteRenderer_SetFullRectShapeData_m417FBA4F9C083FEAD0DA36936EFFF81AA93F5894 (void);
extern void ShadowShape2DProvider_SpriteRenderer_SetPersistantShapeData_mF3C6353F1D76219CB302BC59BDC3217D3C039001 (void);
extern void ShadowShape2DProvider_SpriteRenderer_TryToSetPersistantShapeData_m50BF40899EDD2F2316C02E4E7707F17E744148C3 (void);
extern void ShadowShape2DProvider_SpriteRenderer_UpdatePersistantShapeData_m38DA83F754494DDD0890D02CCD10EAEE81E00F8C (void);
extern void ShadowShape2DProvider_SpriteRenderer_Priority_mA5D69BEB07B8061F1A552B191052501A3EED741F (void);
extern void ShadowShape2DProvider_SpriteRenderer_IsShapeSource_mB32B096484EBD2AAAD1FAF3EA19A4022E19620CD (void);
extern void ShadowShape2DProvider_SpriteRenderer_OnPersistantDataCreated_mA3303B4A89577E0206656A7817803E7E14D4110C (void);
extern void ShadowShape2DProvider_SpriteRenderer_OnBeforeRender_mE11597B28DD27666F6A8B6191BF0310027679010 (void);
extern void ShadowShape2DProvider_SpriteRenderer_Enabled_mA6A4E5EF5C2641C021F3DEBCB097510CE481A4D4 (void);
extern void ShadowShape2DProvider_SpriteRenderer_Disabled_m84EA2BBDEAC6145161710351C2FA35D267A2AAAB (void);
extern void ShadowShape2DProvider_SpriteRenderer__ctor_m201B9882887CFB0BF7CD787009A9BA63B30901D7 (void);
extern void ShadowShape2DProvider_SpriteShape_UpdateShadows_m6F863810C0A44FB0408F2EBEE495E888B397D122 (void);
extern void ShadowShape2DProvider_SpriteShape_Priority_m061733966F82A556E8CE1716983A40B43280D8E3 (void);
extern void ShadowShape2DProvider_SpriteShape_Enabled_m9F56522618F243BD96B888200416777338A6AC5E (void);
extern void ShadowShape2DProvider_SpriteShape_Disabled_m7EA95D9EB30FD3D22DBAFE00348126BCBF1BFE5E (void);
extern void ShadowShape2DProvider_SpriteShape_IsShapeSource_mFAE1A267E1436FAB9F76AC6211C238085661191D (void);
extern void ShadowShape2DProvider_SpriteShape_OnPersistantDataCreated_mD10C4A9BABD45192D2F6D88D3A7DE6B591934822 (void);
extern void ShadowShape2DProvider_SpriteShape_OnBeforeRender_m7C9117F3DC6AEA8761C196298986F4309605C1D8 (void);
extern void ShadowShape2DProvider_SpriteShape__ctor_m66328576B2D1A01EE98A73B1ADE10DEA6092445B (void);
extern void ShadowShape2DProvider_SpriteSkin_TryToSetPersistantShapeData_mAB34AA58BEF387CFF5638ED53E8244220AC8F2E3 (void);
extern void ShadowShape2DProvider_SpriteSkin_UpdatePersistantShapeData_mA55D950808853EC547B291B5DAC3F231A09D4692 (void);
extern void ShadowShape2DProvider_SpriteSkin_Priority_m7C203AF2B70F3206D954EF0805EC27DFD6AFDAC4 (void);
extern void ShadowShape2DProvider_SpriteSkin_IsShapeSource_mDB470F88C1B880A1A647B38D8834AD18C8698DBF (void);
extern void ShadowShape2DProvider_SpriteSkin_OnPersistantDataCreated_mBD9B40CED8AD3E6015245F18BFCFDB856EFA1768 (void);
extern void ShadowShape2DProvider_SpriteSkin_OnBeforeRender_m017E4B5A32CF3565F56D89C7D489FE221AC39FA0 (void);
extern void ShadowShape2DProvider_SpriteSkin__ctor_m21A466B235BD996F4687342558DEE0334196FB8E (void);
extern void ShadowEdge__ctor_m92189DE1A34AA7F4D481C640A7415EE57584C537 (void);
extern void ShadowEdge_Reverse_m267B65507DE000FD5359CAEB003472B65D26A890 (void);
extern void ShadowMesh2D_get_mesh_m77225D909CD28099B6E8E109DC00111881EC1698 (void);
extern void ShadowMesh2D_get_boundingSphere_m16F11181C4232CA8715D1DF55B86BDC234B11F99 (void);
extern void ShadowMesh2D_get_edgeProcessing_m1FC6B8E5D138E26E63CF74FF085106FCEDDDB9CA (void);
extern void ShadowMesh2D_set_edgeProcessing_m99A5E225F5461F6FAA2E9A9BF1C1ED0E3D2B57E1 (void);
extern void ShadowMesh2D_get_trimEdge_m1DD051939E61F267AF4D1E02272759AC81146C44 (void);
extern void ShadowMesh2D_set_trimEdge_m279F62FA0EE7B5C55916FE2AA46B8BB26AD85B45 (void);
extern void ShadowMesh2D_DuplicateShadowMesh_m152BE15B41DA6DCCDC9353D95DD92DD95CE12E12 (void);
extern void ShadowMesh2D_CopyFrom_m386F95D086D78F1ED08DD19C1AAB994695D24FC9 (void);
extern void ShadowMesh2D_AddCircle_m**************************************** (void);
extern void ShadowMesh2D_AddCapsuleCap_m6E27D17295F0AF3A2AFF3C8D0ABD384FDFA5E34E (void);
extern void ShadowMesh2D_AddCapsule_m1FED2B1D4E42BACC30C0CCAB04D6261D67BFB2A7 (void);
extern void ShadowMesh2D_AddShape_m3DE259B1B291B5C7D80DE9DFEF410561455FAABC (void);
extern void ShadowMesh2D_SetShape_m9B0FA8637CC785DA26F2BEBD3DCBB444A4FC76C7 (void);
extern void ShadowMesh2D_AreDegenerateVertices_m0AFEF1902699BE42F46D1E62901106CD623AD3D1 (void);
extern void ShadowMesh2D_SetShape_mB28DAC3876C7AA3BAED7AAADA7FEA355277A6672 (void);
extern void ShadowMesh2D_SetShapeWithLines_m880CA371C3BC648E4159B948209AAE9131139DB4 (void);
extern void ShadowMesh2D_SetFlip_m549820939EF0D42BDD7A8417153B4C0376C2AB71 (void);
extern void ShadowMesh2D_GetFlip_mA3ADC6C8A69E861DA5842B68039E51CD125499E5 (void);
extern void ShadowMesh2D_SetDefaultTrim_m8C41DFD50DB8D3688D2139B7D87BFBFF5FA94A8C (void);
extern void ShadowMesh2D_UpdateBoundingSphere_m5839E98BBE0945D13C3681D3A813496C774C0126 (void);
extern void ShadowMesh2D__ctor_mC428FDAD0852DF646213FC84F829E4538DD3E60C (void);
extern void ShadowShape2D__ctor_mC5F751194FE6DACECC36384A29B80557AB872454 (void);
extern void ShadowShape2DProvider_ProviderName_m4C762B5B61880D7EA151C7983909C309165D1A7A (void);
extern void ShadowShape2DProvider_Priority_mDAD57677B632CE66D162F0115DB67D60835CE90F (void);
extern void ShadowShape2DProvider_Enabled_mADA55FE15163605B4CA90D7C44712071D99294CB (void);
extern void ShadowShape2DProvider_Disabled_m6BCBDEE73B4A2A43D978D7F0DD2D1877301CF282 (void);
extern void ShadowShape2DProvider_OnPersistantDataCreated_m6904CDCD6B83E6A7ACB0D743B14F8E2A2AAC8A65 (void);
extern void ShadowShape2DProvider_OnBeforeRender_mC98BADB92A71863CB07ED607AA84A36E673B341F (void);
extern void ShadowShape2DProvider__ctor_m4CAFF1D4FC5B03E88FF979403B48CF506F3A7F21 (void);
extern void ShadowUtility_GetNextShapeStart_m968CBCD3C6880218101543B0119593D4250875C9 (void);
extern void ShadowUtility_CalculateProjectionInfo_m952E6AB7BDD578374B99ED119819090E81F81067 (void);
extern void ShadowUtility_CalculateVertices_m1087DC5C13D021D10BB58FA9EAD2D99416D2974E (void);
extern void ShadowUtility_CalculateTriangles_m9D055B9D0E3B41D00713797D5074A7BD42A4D216 (void);
extern void ShadowUtility_CalculateLocalBounds_mCD7AB254F81081483F03BEF504C2B2918777D32D (void);
extern void ShadowUtility_GenerateInteriorMesh_mBF198238681A38CB0ECED75E112BBEAE45F2B362 (void);
extern void ShadowUtility_GenerateShadowMesh_m0F248BD62ED797CABF037EECC47B99EB0A70675C (void);
extern void ShadowUtility_CalculateEdgesFromLines_m95B59AC1CE430C8952290FAC5C4DFEAD0EF53008 (void);
extern void ShadowUtility_GetVertexReferenceStats_m1CB3AC1703058D94FF1211F9BA9430600C57B106 (void);
extern void ShadowUtility_IsTriangleReversed_mB4A626328D15027B593D407B78CBF1ADA82CC6BF (void);
extern void ShadowUtility_CalculateEdgesFromTriangles_m1F08E1F50422891DCB5A4A70D57ABD9F1354CB63 (void);
extern void ShadowUtility_ReverseWindingOrder_m9F8C8065FA5294F22C51C1552E34416B3AED38DE (void);
extern void ShadowUtility_GetClosedPathCount_m53BF1D0206FF7ECEA8CED8661B9EB22D0D4596F4 (void);
extern void ShadowUtility_GetPathInfo_m1DFC3F2D9122A5B75C81913410C9F7848C4252B4 (void);
extern void ShadowUtility_ClipEdges_m104D8B0F86040657D28698C6FEDD40210FC710FB (void);
extern void ShadowUtility__ctor_mD10F21907FF856828CDC4738FF5B9F077F6834B1 (void);
extern void ShadowUtility__cctor_mAA00C5F2C0393822FE9348110491FADD67B4A46F (void);
extern void ShadowUtility_CalculateProjectionInfoU24BurstManaged_mE98E80DEAEC78B4B97D19EEC66155059FA1FBB5C (void);
extern void ShadowUtility_CalculateVerticesU24BurstManaged_m036FA3D23DA0DB1D9E2D6EA66D88847A9CF3351D (void);
extern void ShadowUtility_CalculateTrianglesU24BurstManaged_m16F548D18B5A2B0F8FC2C1935EFE67830B4D78D4 (void);
extern void ShadowUtility_CalculateLocalBoundsU24BurstManaged_m1BB0B91C0858FCF73002FCE86500763214C72262 (void);
extern void ShadowUtility_GenerateInteriorMeshU24BurstManaged_mDBCFF562718DFA354F2C9AEC84D421C4A84FBF1D (void);
extern void ShadowUtility_CalculateEdgesFromLinesU24BurstManaged_mE27595FD4F2C77C938E7AE97B9BF2F366A6AC2A3 (void);
extern void ShadowUtility_GetVertexReferenceStatsU24BurstManaged_m55F620268F290E792364229451024085EFF72216 (void);
extern void ShadowUtility_CalculateEdgesFromTrianglesU24BurstManaged_m7E4C36F5690123320F892747336A579E0BD67BEC (void);
extern void ShadowUtility_ReverseWindingOrderU24BurstManaged_m90722ACCF040FB77E3FBA68CA69E0A97AA8E1BD1 (void);
extern void ShadowUtility_ClipEdgesU24BurstManaged_mADDE5B936F1D8B62C4E8FEACDB7CABE823FC9C8F (void);
extern void ShadowMeshVertex__ctor_m2AA5F11678057369D90EC50FB7617477A9E909C9 (void);
extern void RemappingInfo_Initialize_mC79F12A1E91E25F931C9F858473E38216719C497 (void);
extern void CalculateProjectionInfo_00000305U24PostfixBurstDelegate__ctor_m91F31CC6130B02B0F1D7E7CBAC00E1D6B51CF24C (void);
extern void CalculateProjectionInfo_00000305U24PostfixBurstDelegate_Invoke_m7DBBFC8E6B4DC2B3F02A24D43E6A61A044BA45B0 (void);
extern void CalculateProjectionInfo_00000305U24PostfixBurstDelegate_BeginInvoke_m55A0998B57C52AFBD38879EA0751063705A16674 (void);
extern void CalculateProjectionInfo_00000305U24PostfixBurstDelegate_EndInvoke_m6A135FED02001BCA080C85431D156362EC15CB12 (void);
extern void CalculateProjectionInfo_00000305U24BurstDirectCall_GetFunctionPointerDiscard_mA24383FF30620961D80DD375F0BA154B8663F77A (void);
extern void CalculateProjectionInfo_00000305U24BurstDirectCall_GetFunctionPointer_m74A2CF091EE133861A8CB0A46F6BC1EB67959A21 (void);
extern void CalculateProjectionInfo_00000305U24BurstDirectCall_Invoke_m3EF35319BD0B3BAC250D45F2D28D5F069E0688FD (void);
extern void CalculateVertices_00000306U24PostfixBurstDelegate__ctor_mE1D6D159CB8776F10E8BE374CAAF23D81F088388 (void);
extern void CalculateVertices_00000306U24PostfixBurstDelegate_Invoke_m91073D96146937BD1B4A2E1A4C9C1C775EA7B7F9 (void);
extern void CalculateVertices_00000306U24PostfixBurstDelegate_BeginInvoke_m09B68CBB00A010A7086ABAACB7F8B4559C038A0B (void);
extern void CalculateVertices_00000306U24PostfixBurstDelegate_EndInvoke_m1E742D9CB01279C16154F44619A9B19340318D64 (void);
extern void CalculateVertices_00000306U24BurstDirectCall_GetFunctionPointerDiscard_mA50ACE0C6B2E336BE30A03CA5D18953A554340F6 (void);
extern void CalculateVertices_00000306U24BurstDirectCall_GetFunctionPointer_m12A747D745BE8ABFF7DE572FE4A9AB051D2E144B (void);
extern void CalculateVertices_00000306U24BurstDirectCall_Invoke_mB643496F6EAA3506796BB5D51506717ED67412A9 (void);
extern void CalculateTriangles_00000307U24PostfixBurstDelegate__ctor_m17EF157E80D37E6A406D7AC9FA0E3874054C2E87 (void);
extern void CalculateTriangles_00000307U24PostfixBurstDelegate_Invoke_mE4CAB4C589B8620F2BA866AA2C09F2E61481AB8F (void);
extern void CalculateTriangles_00000307U24PostfixBurstDelegate_BeginInvoke_mF89BE1A16DE4C3CD12C492D34DA254A073E890F3 (void);
extern void CalculateTriangles_00000307U24PostfixBurstDelegate_EndInvoke_mD14435C2BC9F045B74799A02C90FEDDD4FC74A3D (void);
extern void CalculateTriangles_00000307U24BurstDirectCall_GetFunctionPointerDiscard_mC5CC848E357520585C3E84C4E3475982A817B2C7 (void);
extern void CalculateTriangles_00000307U24BurstDirectCall_GetFunctionPointer_mDC29D38E04C75AD36127CCDF294DF04C08F76E90 (void);
extern void CalculateTriangles_00000307U24BurstDirectCall_Invoke_m49269F6FABF74FEE776D2B8648A5A8A14129405F (void);
extern void CalculateLocalBounds_00000308U24PostfixBurstDelegate__ctor_mC4FE6F7F78234EE82243F72B6B86D6FAB7D0ACAE (void);
extern void CalculateLocalBounds_00000308U24PostfixBurstDelegate_Invoke_m2DEF4F5A390ABAFD148769E7144A3FEB9B681141 (void);
extern void CalculateLocalBounds_00000308U24PostfixBurstDelegate_BeginInvoke_m33C51EAD1F1C94B9AD8396BED3EDAE8B78E684EE (void);
extern void CalculateLocalBounds_00000308U24PostfixBurstDelegate_EndInvoke_m83F25BB6A83DD5EF1E501ACB66E16BA0E97F43BC (void);
extern void CalculateLocalBounds_00000308U24BurstDirectCall_GetFunctionPointerDiscard_m5B262DEA989827D5DF9A896750E8D2D98364F9B8 (void);
extern void CalculateLocalBounds_00000308U24BurstDirectCall_GetFunctionPointer_m4AA846C6E282E38E3A19DF69C5AF64E0A1B8DC70 (void);
extern void CalculateLocalBounds_00000308U24BurstDirectCall_Invoke_m1AA07A8F99F9CFA90FFAEF1B934699CB6029B725 (void);
extern void GenerateInteriorMesh_00000309U24PostfixBurstDelegate__ctor_mD44EA028A07347F7EE95D699524233B83FFDF298 (void);
extern void GenerateInteriorMesh_00000309U24PostfixBurstDelegate_Invoke_mD38B0BBEE54CF5493DB5DBBFAA42296B9FF67C78 (void);
extern void GenerateInteriorMesh_00000309U24PostfixBurstDelegate_BeginInvoke_m2F04A12024365685E4155D3DE167319A7697951A (void);
extern void GenerateInteriorMesh_00000309U24PostfixBurstDelegate_EndInvoke_m27B682DC36FAA7F9893FB985C4C14E5521E1B8A2 (void);
extern void GenerateInteriorMesh_00000309U24BurstDirectCall_GetFunctionPointerDiscard_m0E92F08E25D0D968A75EC97A0565CB5256610F3D (void);
extern void GenerateInteriorMesh_00000309U24BurstDirectCall_GetFunctionPointer_m2A0B4B792589567C641DBBCF4FB59BE9FFE3E774 (void);
extern void GenerateInteriorMesh_00000309U24BurstDirectCall_Invoke_mF4F75E49B795F0C5FB96E7B2571B49AEAB925AEB (void);
extern void CalculateEdgesFromLines_0000030BU24PostfixBurstDelegate__ctor_m677FF2FFB6EC8132C3E82B6DC8E9734063498871 (void);
extern void CalculateEdgesFromLines_0000030BU24PostfixBurstDelegate_Invoke_m17ABC4C54CCEDD82AA356D45C770DFC2C6ABD642 (void);
extern void CalculateEdgesFromLines_0000030BU24PostfixBurstDelegate_BeginInvoke_mEEC910F2719B2780F970B74634085A471DA53993 (void);
extern void CalculateEdgesFromLines_0000030BU24PostfixBurstDelegate_EndInvoke_mFEDBF2930A08B90A9AD013E2EE3C5DFB00D9F11B (void);
extern void CalculateEdgesFromLines_0000030BU24BurstDirectCall_GetFunctionPointerDiscard_m1A614F8DCF8C9235C4A76C0394D5EE038D08FCF6 (void);
extern void CalculateEdgesFromLines_0000030BU24BurstDirectCall_GetFunctionPointer_m93E3D13466D29AAF8B7D411B56D5C4CF31C9766A (void);
extern void CalculateEdgesFromLines_0000030BU24BurstDirectCall_Invoke_m20BA44B4ACCC532A2CEAA882ED295379BC2DD420 (void);
extern void GetVertexReferenceStats_0000030CU24PostfixBurstDelegate__ctor_m2F91BDFD4B15A538D6E6BD679B00377C5BC132D6 (void);
extern void GetVertexReferenceStats_0000030CU24PostfixBurstDelegate_Invoke_m519FE040B5068E0834CFD3311B002F7768ACD568 (void);
extern void GetVertexReferenceStats_0000030CU24PostfixBurstDelegate_BeginInvoke_m910D589CF05146C5EB03CA04906D69CBC4655D4D (void);
extern void GetVertexReferenceStats_0000030CU24PostfixBurstDelegate_EndInvoke_m4A14553CAED48C23F13A98767705B23D681A624D (void);
extern void GetVertexReferenceStats_0000030CU24BurstDirectCall_GetFunctionPointerDiscard_m9326FEA8DF22F3BE371A611486A6BE47D8171ED4 (void);
extern void GetVertexReferenceStats_0000030CU24BurstDirectCall_GetFunctionPointer_m3F715874C9BC72635B3F637615194FA43D98618E (void);
extern void GetVertexReferenceStats_0000030CU24BurstDirectCall_Invoke_mC7F6F632C594B5770FE20454A54925454E0B8F0B (void);
extern void CalculateEdgesFromTriangles_0000030EU24PostfixBurstDelegate__ctor_m77CE4EA41987F567E0A762D024B38F0914C7E795 (void);
extern void CalculateEdgesFromTriangles_0000030EU24PostfixBurstDelegate_Invoke_m4DE337096BA41499148C7656BF638AE4EEE6D685 (void);
extern void CalculateEdgesFromTriangles_0000030EU24PostfixBurstDelegate_BeginInvoke_mACA60E87CF55563D4D85D46D8D58BE238F0FF06C (void);
extern void CalculateEdgesFromTriangles_0000030EU24PostfixBurstDelegate_EndInvoke_m21744D2A27833C4C100A20339174C08D12AF1816 (void);
extern void CalculateEdgesFromTriangles_0000030EU24BurstDirectCall_GetFunctionPointerDiscard_m70F9138FBC0F2F4A2069CC01634ABE04D324368C (void);
extern void CalculateEdgesFromTriangles_0000030EU24BurstDirectCall_GetFunctionPointer_mD21B9D1F00B0DBC2BC9D5E6920136A718A71D915 (void);
extern void CalculateEdgesFromTriangles_0000030EU24BurstDirectCall_Invoke_m1DD893BC82D09DAE2E81481C394A00E9BDEE6108 (void);
extern void ReverseWindingOrder_0000030FU24PostfixBurstDelegate__ctor_m92E77F7CB24D2FB49B839723E2344594709FD6B9 (void);
extern void ReverseWindingOrder_0000030FU24PostfixBurstDelegate_Invoke_m7AF4C6672B5E2D619E3605F8FB916D07159A5180 (void);
extern void ReverseWindingOrder_0000030FU24PostfixBurstDelegate_BeginInvoke_mC79281FB1ACAB00C043A86B7A0908E8098DDBDD8 (void);
extern void ReverseWindingOrder_0000030FU24PostfixBurstDelegate_EndInvoke_mBA39070482EB30D09730B17266E27A6C75B9443F (void);
extern void ReverseWindingOrder_0000030FU24BurstDirectCall_GetFunctionPointerDiscard_m4AA7CD7CF013D090F2E1DBA3873F9529DED422EA (void);
extern void ReverseWindingOrder_0000030FU24BurstDirectCall_GetFunctionPointer_mE899AA1409BEB052ACCAA5652518BDCF62724914 (void);
extern void ReverseWindingOrder_0000030FU24BurstDirectCall_Invoke_m77AA98A7164A96FA31669950E57C9D229936488A (void);
extern void ClipEdges_00000312U24PostfixBurstDelegate__ctor_m3DB42A7AFB7FF44649061DDEE8FF4A3A8227BBFE (void);
extern void ClipEdges_00000312U24PostfixBurstDelegate_Invoke_mC2D9CD27A15AD761818455E0D391DF029E552A1C (void);
extern void ClipEdges_00000312U24PostfixBurstDelegate_BeginInvoke_m1A9CB4A4C739C9099E54CA43787EFA444FCD84AF (void);
extern void ClipEdges_00000312U24PostfixBurstDelegate_EndInvoke_mE783C2711A7DF4D21DAA4F2774534FF19501BD83 (void);
extern void ClipEdges_00000312U24BurstDirectCall_GetFunctionPointerDiscard_mD2392F1788D67B2E7E8CBBCEC5A1847D75B68F8C (void);
extern void ClipEdges_00000312U24BurstDirectCall_GetFunctionPointer_m8B92B9B18002E9F99327332330680488B29ABC07 (void);
extern void ClipEdges_00000312U24BurstDirectCall_Invoke_m3D4C6089F6D8F6871341073FA5F429B9D113C3EF (void);
extern void ShapeProviderUtility_CallOnBeforeRender_m7C51462DD5E927D3EF32E4948DB40644A7B245C2 (void);
extern void ShapeProviderUtility_PersistantDataCreated_m435BC95D833768183FCB85269612D96AF54B7791 (void);
extern void ShapeProviderUtility__ctor_mBFDC150C86C11364B9B1650E4A63BB80A8EE7D0C (void);
extern void ShadowRendering_get_maxTextureCount_m1BC0EA9B75B149E2604389A7FBF8A1CD6589D72A (void);
extern void ShadowRendering_set_maxTextureCount_m0E7989731298A22E597B948BD4DDDE84139EFE71 (void);
extern void ShadowRendering_get_lightInputTextures_mE4AFC2A828A8BE487069D55E4500992BDDD44FA4 (void);
extern void ShadowRendering_InitializeBudget_mF4924A4929E5A768A8EC050D901BBDAA87CF572E (void);
extern void ShadowRendering_CreateMaterial_m8234BC4D52AD07B733DD6BC003A4DC0C9D646AAD (void);
extern void ShadowRendering_GetProjectedShadowMaterial_mA1090A2C989FE965B7BEF55AF55DCE8F984B1D23 (void);
extern void ShadowRendering_GetProjectedShadowMaterial_mF79827ED39DF40998A911656E1C198D25BF4D0C9 (void);
extern void ShadowRendering_GetProjectedUnshadowMaterial_mFAF66A404023EC26ACB31CB946BDEAB1F6450EE9 (void);
extern void ShadowRendering_GetSpriteShadowMaterial_mC47BECEDD6453FAE2E49E2223816F278083CB42E (void);
extern void ShadowRendering_GetSpriteUnshadowMaterial_mD7C249A243DDDFED43E48D97B20AC32266D778B3 (void);
extern void ShadowRendering_GetGeometryShadowMaterial_mA143EE752E3247C63FF820BABA58AA12AA3E15F2 (void);
extern void ShadowRendering_GetGeometryUnshadowMaterial_m300ECB92E475135406D7ACD1524823F00FB6AC18 (void);
extern void ShadowRendering_CalculateFrustumCornersPerspective_mA6985BEDB971D8E85C79442820EEC60B084C69D5 (void);
extern void ShadowRendering_CalculateFrustumCornersOrthographic_m22EA13A775F8325A4CF1480DFAE06991A687277F (void);
extern void ShadowRendering_CalculateWorldSpaceBounds_mFC268ED4AD9EE7410AF79C2CDEB1B067D1371CB9 (void);
extern void ShadowRendering_CallOnBeforeRender_m87C1AD9258CD9ABC9212779776097EC0F7A277BC (void);
extern void ShadowRendering_CreateShadowRenderTexture_m646D41B6763426609E002132D260B479E7948D2D (void);
extern void ShadowRendering_PrerenderShadows_mF16E1075806863694F4CF9A8B3EFD9B608F827C8 (void);
extern void ShadowRendering_PrerenderShadows_m38E3EE0127295FB65248CB39C70812814253FD8C (void);
extern void ShadowRendering_CreateShadowRenderTexture_mF1678878C9EA31F8AA3DA7342A60DD8CD66C2EB1 (void);
extern void ShadowRendering_ReleaseShadowRenderTexture_m58B1B092C38DD47EE161B78861EA26BF7128651B (void);
extern void ShadowRendering_SetShadowProjectionGlobals_m800F872BFD9A718F314E39976CC96581551DF988 (void);
extern void ShadowRendering_SetGlobalShadowTexture_m57F5462AE413508271DCC7BC1E6BA9E24227FCAE (void);
extern void ShadowRendering_SetGlobalShadowProp_mBC6B4620F9F40D782E77395E71BADB14E4DF89F5 (void);
extern void ShadowRendering_ShadowCasterIsVisible_m901BA428CB526E0ECB849A88BBD9A1F49D5B036F (void);
extern void ShadowRendering_GetRendererFromCaster_m6D7D8BE57FD015AD6E17AC71ABC06B4EBEBDCD68 (void);
extern void ShadowRendering_RenderProjectedShadows_m7DC895C1D5B3FB27A71E0004B9F156647CBC9ABF (void);
extern void ShadowRendering_GetRendererSubmeshes_m89AC52C66C3E91562D51094BC3F28F383D770B89 (void);
extern void ShadowRendering_RenderSpriteShadow_m1916F963D5E90B5719A5B1A80123B3781AE62EFF (void);
extern void ShadowRendering_ShadowTest_mFD03AFC4E36ADB38D8183815B717A1936FAC816F (void);
extern void ShadowRendering_RenderShadows_m8597106EAD21509138AD38E17681064DADF957B7 (void);
extern void ShadowRendering__cctor_m3284B7B1564FF36D44700C39029479777FC9A48E (void);
extern void U3CU3Ec__cctor_mA0CA5E31DCC8E36F7B28797566B3B3250766F007 (void);
extern void U3CU3Ec__ctor_m016A055FA79AD7FDD7A1D02AA6977CEA8F886813 (void);
extern void U3CU3Ec_U3CGetProjectedShadowMaterialU3Eb__34_0_m0EE4A885D48D01B62D6AD2FD0E99A1EED6353FB8 (void);
extern void U3CU3Ec_U3CGetProjectedUnshadowMaterialU3Eb__35_0_mC6A06AF99AD0CD680B88EA54FE6A31E0AAECF85A (void);
extern void U3CU3Ec_U3CGetSpriteShadowMaterialU3Eb__36_0_m4E0A684740BB69A5501256B8951AADEA3139810D (void);
extern void U3CU3Ec_U3CGetSpriteUnshadowMaterialU3Eb__37_0_m1B668EEFEAB91A0664F5D02B16D3742C7DEC2A2F (void);
extern void U3CU3Ec_U3CGetGeometryShadowMaterialU3Eb__38_0_m247F5EB28B9F6FC6CF159492B61A4AE58151F057 (void);
extern void U3CU3Ec_U3CGetGeometryUnshadowMaterialU3Eb__39_0_m95FE3770DF377169A723D401F354E48BB514C8A1 (void);
extern void PlanarGraph_RemoveDuplicateEdges_m02B7AF8FAF00D6029D902205B836E6E7B9B9FD43 (void);
extern void PlanarGraph_CheckCollinear_m2DDF994C13C20490A8520E11CDA42958EA13E026 (void);
extern void PlanarGraph_LineLineIntersection_m17350F15EBC948F829EB7C392AD18A7AFD241284 (void);
extern void PlanarGraph_LineLineIntersection_mE3D6BA5A3BEAFD6CF5168142443F49E1B4A086ED (void);
extern void PlanarGraph_CalculateEdgeIntersections_mD706558D362051701D8FBF2757B7C4A2EFB04D54 (void);
extern void PlanarGraph_CalculateTJunctions_mEE0C82242AAEC33550DF9992A5F259762D5CFD44 (void);
extern void PlanarGraph_CutEdges_mD2C22A6E61E85C92EFE1EE79E687F67B3FCE01BF (void);
extern void PlanarGraph_RemoveDuplicatePoints_m0E4EA60CACDB36682410B04FD3DDB7A92C31A0EA (void);
extern void PlanarGraph_Validate_m55816BC6804855F94178B5FDEC284BB63C0ACC88 (void);
extern void PlanarGraph__cctor_m41BAFC16976D7CC16BE6F8C0863A6FE6EC3BD336 (void);
extern void Tessellator_FindSplit_m10B801FE843957B0D014E7867373EAF6AA2CF1FD (void);
extern void Tessellator_SetAllocator_m66AFC39E8CFBDC82638D7D7BC3BA70CB4B818C96 (void);
extern void Tessellator_AddPoint_m1B8B2A1C5A2F7742F1E5AF1D7618E65C1B75C70B (void);
extern void Tessellator_InsertHull_mC363D2CABF716C07C41BB4209A4BCBCC50F1EFC1 (void);
extern void Tessellator_EraseHull_mC63AB321BFF1D86A25F33DE2180CD685D138069C (void);
extern void Tessellator_SplitHulls_mC558F30B7A383F1169945126EF5FF34B2E9B54C9 (void);
extern void Tessellator_MergeHulls_m9A96C241CD12A18A2CF1FAC88978385323F57DFE (void);
extern void Tessellator_InsertUniqueEdge_m05BA0DE1A1B7A7392EA482F6AFD23CCE325D4625 (void);
extern void Tessellator_PrepareDelaunay_mB942127F1C81668537200C7D5BEBEAB81A8E8396 (void);
extern void Tessellator_OppositeOf_m22FD730F3F313FD1E8DD6FCA4ECA99883B92F38C (void);
extern void Tessellator_FindConstraint_m6913A7E958CC2DF88EF0FF08E6651728B26C547A (void);
extern void Tessellator_AddTriangle_m007B2BB6D24FC02369BD0777488E181BF3D18544 (void);
extern void Tessellator_RemovePair_mFCFB259D9EB11DF42B6D598B2577324DEB458C45 (void);
extern void Tessellator_RemoveTriangle_m34E53EFC4378A920679A5FD8C30C02630CD78968 (void);
extern void Tessellator_EdgeFlip_m1C6D8B63CCA7B3A8183D14680904C72FBA89C843 (void);
extern void Tessellator_Flip_m6DA43B217DF80D40A990220077BB66AF23D04A8A (void);
extern void Tessellator_GetCells_m67140E0BC8D71B44D99C55F76B7FB4160F853854 (void);
extern void Tessellator_ApplyDelaunay_m39B60F4E8FE33D874D762C034A2D1157F28FCF49 (void);
extern void Tessellator_FindNeighbor_m3BD0F869CABD9603D3EAF91D591931C8BF79C2A1 (void);
extern void Tessellator_Constrain_mF9C988B9CE9F1C8E4F518FE3850D5FE9577D954F (void);
extern void Tessellator_RemoveExterior_m63CD3C9CFC589841E8071F8095AE44C9DAC4F4F3 (void);
extern void Tessellator_RemoveInterior_m613C8F301ABDA9F6C96049E2F1675539E2F56756 (void);
extern void Tessellator_Triangulate_mE55C698C207EA1A8DA592191AB17C3FB7688989E (void);
extern void Tessellator_Tessellate_mE45146D6B03B63F8D23C19B8B3FAF0047300B1AC (void);
extern void Tessellator_Cleanup_m54EFF571CA8971F236A791FC880C8046286D2A37 (void);
extern void TestHullPointL_Test_mD67B5326AED143E216C7DC238637E8E0B3131ECC (void);
extern void TestHullPointU_Test_m3AF0A2CD6162B20C77771850BF94DC7A2D8ED9C2 (void);
extern void TestHullEventLe_Test_m4FE651114100DE7E07DBADE520E75FC168A97CB3 (void);
extern void TestHullEventE_Test_mD7344227B61F43A6A023EBC2F1C13140FD082F49 (void);
extern void TestEdgePointE_Test_m1405C8998CF580F46817D5D0F33BBC98B4CC240B (void);
extern void TestCellE_Test_m41B697E52090E8C53B5FF1ABE7981AAFB2E48C3E (void);
extern void XCompare_Compare_mC591D9D097659ECE784A849856E80AB4D5E2C11A (void);
extern void IntersectionCompare_Compare_m47AF21131D95CE78ACD63B1848834162EE5FB582 (void);
extern void TessEventCompare_Compare_mCB358637173C49F5385C5C10E6EF7FB2998FEF40 (void);
extern void TessEdgeCompare_Compare_mE3BC27F4E0F907DADA5C2B96C0826122EBB34E97 (void);
extern void TessCellCompare_Compare_m33DEA3199CA382144A3B0376035D2F1A789B4108 (void);
extern void TessJunctionCompare_Compare_m28B7673AAA0AD8D3EAFE0DD9289AB335D4350F13 (void);
extern void DelaEdgeCompare_Compare_m4AE4388767DAE729E5F497C66009C5DEB796B61A (void);
extern void TessLink_CreateLink_m29A2928ED0ECDA9EE29A29E2EC92D6678F10FF05 (void);
extern void TessLink_DestroyLink_m8AE3E6F07F5695531C7712DC0777B62A7494AF1F (void);
extern void TessLink_Find_mEFBB6D30AF8D5CCDD565E36A2EAD62534AC52D08 (void);
extern void TessLink_Link_m371C8A216AFD9AC8BCD5F015C59DD926F8088B8B (void);
extern void ModuleHandle_OrientFast_m5AE5B276A51F924CB72CC43E05BED8D0C7FF6A06 (void);
extern void ModuleHandle_OrientFastDouble_m22FA5D345F2DE849281B3A4FD34A094C241FAC2F (void);
extern void ModuleHandle_CircumCircle_m**************************************** (void);
extern void ModuleHandle_IsInsideCircle_m**************************************** (void);
extern void ModuleHandle_TriangleArea_mB975E204C8246F28F6D0FFC2816EA77071094CD4 (void);
extern void ModuleHandle_Sign_m2A3FE8C13958F68CA76C2F1A510C4B90ED9168F1 (void);
extern void ModuleHandle_IsInsideTriangle_mE5E0727E61408F6660F8523E486B325CD14D028A (void);
extern void ModuleHandle_IsInsideTriangleApproximate_m857F51A8532FE7F8F81A1439E79F3A11D5BF0CD6 (void);
extern void ModuleHandle_IsInsideCircle_m**************************************** (void);
extern void ModuleHandle_BuildTriangles_mD6A98AB9B2DD595D042196B675EFBD00A8CA2DFC (void);
extern void ModuleHandle_BuildTriangles_m6234B1C0C2B49C85551535F8CD0805845C03CAAC (void);
extern void ModuleHandle_BuildTrianglesAndEdges_m27E981150B7C706432B2B0289D171292848DD602 (void);
extern void ModuleHandle_CopyGraph_mD2417E59077CE579091AC05BEC315E4FBD802054 (void);
extern void ModuleHandle_CopyGeometry_mBB391F8676ADD633B9B0A4F64001B689F055B3D0 (void);
extern void ModuleHandle_TransferOutput_mE769E946634074513DB462A6E244FD843ED2135C (void);
extern void ModuleHandle_GraphConditioner_m322541485AFCB09F1FDF3609BECCB093D0DA138E (void);
extern void ModuleHandle_Reorder_m40FFFB135A6763AE1ED04125D4AC161FFE65ABFF (void);
extern void ModuleHandle_VertexCleanupConditioner_m6AC0853FEBD2826F41C6C3115F33D927867676A6 (void);
extern void ModuleHandle_ConvexQuad_m7D9BC4D23333081D14A8044E77AED5FBF01DDFF4 (void);
extern void ModuleHandle_Tessellate_m6AE06111AB2B0B09EC2C846DEF23C1AAA61B968E (void);
extern void ModuleHandle__cctor_mBE35D3D252C3BA83B20D8EC431BE1589A144E03D (void);
extern void U24BurstDirectCallInitializer_Initialize_m15347CE4997B95D8D58B24CEC10BDB0ACB562281 (void);
static Il2CppMethodPointer s_methodPointers[1059] = 
{
	ShadowShapeProvider2DUtility_GetTrimEdgeFromBounds_m6C6B9FACDACF3342E20B3892A13E930FE64F0820,
	ShadowShapeProvider2DUtility_IsUsingGpuDeformation_m8C590AC63075427436DD23B0B3E704374CD3E6EF,
	VertexDictionary_GetIndexRemap_m0E5260046A68AA40445328A6A22F21EB718AA143,
	VertexDictionary__cctor_mB58E922FDCBC9C102CCF0A439CF913F8DD1F91B3,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m050A9CAA08705EC022F15BE62FE419CF3D6E2C76,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m28F5DE7E6798EB10AB1BD26513A5C7E15F3096A8,
	CinemachineUniversalPixelPerfect_OnEnable_mE985C3B2D6154244E4D1586771D7F4CCD7420F94,
	CinemachineUniversalPixelPerfect__ctor_mC7A1AE72895E88A136FA7BD8421DBC2C0A710BFC,
	DoublePoint__ctor_m29F8204086F248FC19FB9593452E8EECF612563A,
	DoublePoint__ctor_m52141CF0CACA19656612BC12A2C4F7664763F1B2,
	DoublePoint__ctor_m41A0279EEC0F9151AE7450FB7897C28252712545,
	PolyTree_Clear_m4EE08CDE266753595E9B4A79A6B363EEE4CC9C43,
	PolyTree_GetFirst_m0B5384CBBF4424101DED5DF85ACD3429571B0D7B,
	PolyTree_get_Total_m2C24746F50527209FE394C6CF52CD40BB95B48C7,
	PolyTree__ctor_m1994B16073578DFA1EEA2842DB0F445B50757AF7,
	PolyNode_IsHoleNode_m7BE6DBE07C8A7B4C61D0D3A9FFA4F71FE0B47EE5,
	PolyNode_get_ChildCount_m0C56A91C4A8A62998B9AA4D3ABC143B511B66A89,
	PolyNode_get_Contour_m9AB3A0595689E2DB122E050B8725FD4F56FAC521,
	PolyNode_AddChild_mB8507A9D14D146FFB323B4428B46CFAB27ACD9BF,
	PolyNode_GetNext_m1D7A502BB5D76DECC88DC4D6621D6C3FA590206F,
	PolyNode_GetNextSiblingUp_m85E45FDAFBB96E6FE37F9EE1C146D9ACC09D4F3B,
	PolyNode_get_Childs_m9504B6F5165F6AD581F63D3A80A5FAA7B472BC79,
	PolyNode_get_Parent_mF7E2484447DB849847A1F57387E4B676DDF9E0E5,
	PolyNode_get_IsHole_m59B43B31ED19DD2E0CB22EDFD3627004A46E33E9,
	PolyNode_get_IsOpen_m92C56CD9B1E64B260B7C09FC2A90D5D834292447,
	PolyNode_set_IsOpen_m2242B21BDCDB2E105ED4EBCD63F89F602047F629,
	PolyNode__ctor_mC3D70892D4FF7F6870DA40C31CDC5826588606CF,
	Int128__ctor_m2BB0A4CF9E48910476A58754D2AE1250D3C12966,
	Int128__ctor_mBC356544A97CDF5EACE12C41CBB3C286CC68B723,
	Int128__ctor_mE8A298C41D274843EE69B61FA4505EEE3D2C1054,
	Int128_IsNegative_m70EBBF367872796AFF9BEA2D5B0C44B391B2FD91,
	Int128_op_Equality_mDC0CD599A09D6D85CFFB4A3F9E03BB32F5289CC9,
	Int128_op_Inequality_m34C85F43A0A9C5A3D064C0FA631D8B2473AA97B9,
	Int128_Equals_m9E0BF4D381BD6AB4201A7F1C8D6DE2E76FCA7CDE,
	Int128_GetHashCode_m4D04D0D584560C74DC48A4931C0FEFC434233CA2,
	Int128_op_GreaterThan_mE7DE40923FEBDAAA9ABF80C43237AF39362B8434,
	Int128_op_LessThan_m80BA06A74741A1A873BC0223843EA0E337A87B8E,
	Int128_op_Addition_m2026A4247863AA78F3A004E6405CE72837B85A8C,
	Int128_op_Subtraction_m9518B4A9B0C14D87B451F43A881AFC43631416B8,
	Int128_op_UnaryNegation_m141B38A5611CF59437D561CD625B9E38788CE257,
	Int128_op_Explicit_m73D92CA9FB1D3FD008EADA7B99E26F4C47A69466,
	Int128_Int128Mul_m1A760A293E9FEB7A1395EF901CD1A2AA3FF4C61A,
	IntPoint__ctor_m975E8CAF60B3C05EA5C947E1E5A5D46A6991C9F7,
	IntPoint__ctor_m1C17D5437492194E95980214849A1BA4FEC919B0,
	IntPoint__ctor_mA6DA01E8192EAB09DAEE3C900A83D4A2E87AA8FE,
	IntPoint_op_Equality_m19418B21E6FE3EB92A59BF3E7E2881C21CF9755C,
	IntPoint_op_Inequality_m567150C592985BE0233C9E7FEA6D526F681864D3,
	IntPoint_Equals_mB95B6A612FF6F33DAFB3AF701DF7554CFB414833,
	IntPoint_GetHashCode_mCC421CBDCDE3C6A291D3CAA06D787AF76B6F8EA9,
	IntRect__ctor_m2679B769D75424420E84A8348164D92437FD003A,
	IntRect__ctor_m61FE9261A36D5EE88EB7CFDF926045C308753D8E,
	TEdge__ctor_m610F3E67203EB46AF16C214956F390FFFA84DE7C,
	IntersectNode__ctor_m67DEB85303E6FF915F173B73C8F41E1B4AEA54FC,
	MyIntersectNodeSort_Compare_mC6F12D968F6F4536E1C45EF6E36D5FBB294853FB,
	MyIntersectNodeSort__ctor_mB8C56DB3FAED9391DFBABE28026911599CEFAC33,
	LocalMinima__ctor_mA494B9C91045BD7B788A7FD2985648D9859F36E0,
	Scanbeam__ctor_mD24C1558F845142F670925E66F753D37439CCFF0,
	Maxima__ctor_mC32025C99D8EA87503C8E97448C0D14B5646FC07,
	OutRec__ctor_m3C5E6760414D602107B4D98A0F050F561C249C74,
	OutPt__ctor_mCE53B6D8295199ABEC9AB5648E09C9F365A12F27,
	Join__ctor_m88EEDAF536FE700D923B4AB9AE7ECCCD5FD1FAB6,
	ClipperBase_near_zero_m8B80BDA7A141F620A243DFED5559F9A549C0B1D4,
	ClipperBase_get_PreserveCollinear_m9D2B1A1F052450CACBF3905CA51A50A1BE37B4C2,
	ClipperBase_set_PreserveCollinear_mC58976CDF7BAF261FABB75AFCB9EAFA07E24F78A,
	ClipperBase_Swap_m2EEA14EBC5B1D1D8C61D5446D9EFE46A318B6B4D,
	ClipperBase_IsHorizontal_m6BAD44CE7EDD2316867C067D25B16BC929CE2674,
	ClipperBase_PointIsVertex_m8ADAF8684696049E6A3682C82192D9CC7FE9C177,
	ClipperBase_PointOnLineSegment_m6D701248A671BAA1DECAF77E62F840FE06F54F15,
	ClipperBase_PointOnPolygon_mD693C741F82F0B3ACC8836413DE7C578DD9B6508,
	ClipperBase_SlopesEqual_m00AF12F96769255CE798C4E74DE3C5996F461C8A,
	ClipperBase_SlopesEqual_mEB17161B9ECC29DFF57B793C164DB8C00029CC94,
	ClipperBase_SlopesEqual_m589B45B1AAFE029C0360B6A8EC802C8AFD6ED1EB,
	ClipperBase__ctor_m0894488439AE46B592AE18030F09235C1FDC693F,
	ClipperBase_Clear_m0B93D231A51B4F6B1B019E45834124B1E09E7069,
	ClipperBase_DisposeLocalMinimaList_m7D4312016822E530E1D4E7FBBB46FA8CBEF5A6E7,
	ClipperBase_RangeTest_mA0B6D833FDEFCF7EF73C25ADDC9A93AE49917081,
	ClipperBase_InitEdge_m4D917102532B87E80607086BD42F811F66B4171D,
	ClipperBase_InitEdge2_m5B567E5C9D56734F0DE68206F659D26F9043F669,
	ClipperBase_FindNextLocMin_m271E984508D670BE99476E553DBA25FEAE3CCDBB,
	ClipperBase_ProcessBound_m49923BA5E64AF4EB19CF8EAF5F731A100A919A53,
	ClipperBase_AddPath_mA946CF9B6011042421DDEE21266D25E70AA4FB68,
	ClipperBase_AddPaths_m27ACCC4640137E92BE1FF9BAF3382FB70CFBEBB1,
	ClipperBase_Pt2IsBetweenPt1AndPt3_m13DFE616832125372998174FEBBF95284BCBAC0B,
	ClipperBase_RemoveEdge_m7D4A0FFE739E0A8C43C2B5CDE98CF7C8D51C8E13,
	ClipperBase_SetDx_m26594DD05B93158C408F0AF7E4525BACD3C9233B,
	ClipperBase_InsertLocalMinima_m0E794BEC70E81A9BE1FA0C0A1D8A40A08718CE23,
	ClipperBase_PopLocalMinima_m24AFAE8B70455538DCD5922163EF5C11D6E980BA,
	ClipperBase_ReverseHorizontal_mA3AD14B2BCBE97985D26C06B255390CB0C7CAF23,
	ClipperBase_Reset_mFF927BD6DE091885EB0051B3EAC2B7768CB7AA1F,
	ClipperBase_GetBounds_m382A82FC696016C3D0484AB1115E2146F415E793,
	ClipperBase_InsertScanbeam_mE98355A33D03DD9ECB3755B1F73D17C2A7E0DE34,
	ClipperBase_PopScanbeam_m30D6EC26308049F1C1C71E4276D5EE684D2E2A10,
	ClipperBase_LocalMinimaPending_m811369BA00B320F43BAD38139AC65FAF37641F75,
	ClipperBase_CreateOutRec_m8385CCD504D822268DDC5D9174253A80E76C2122,
	ClipperBase_DisposeOutRec_m3F29081230A27D799F601FFBE9760A12ADE74A1C,
	ClipperBase_UpdateEdgeIntoAEL_m4C4C629344AE2E4072183668434761BF7E4D92DC,
	ClipperBase_SwapPositionsInAEL_m8115E667C69A638634397D13A4D0C34EA25DCB07,
	ClipperBase_DeleteFromAEL_mBDD7A633D67B4FE528EF0E1BC18E2997B9ADD996,
	Clipper__ctor_m79876B5EC9228EEAAF0564D8AED96823013A9378,
	Clipper_InsertMaxima_mB6D3CCC041F1854241C01396BD5D18FCC312AA1C,
	Clipper_get_LastIndex_mAC6074E04AB6F8042F2D853F146D20735F096B26,
	Clipper_set_LastIndex_m6F599F61C577B01788A268086273E2A63838C40E,
	Clipper_get_ReverseSolution_mDA8487BE5689AC1DD8FA0AECA1687EDFC05442BE,
	Clipper_set_ReverseSolution_m0D892BFC128ED8F617128C1247B128DA98046355,
	Clipper_get_StrictlySimple_m0668EAFA6CBFB92C4BA987D31B968956E406DA90,
	Clipper_set_StrictlySimple_mBAA38534C91283F371B0BC5847702C29E674A00C,
	Clipper_Execute_m70516548117FA1F84C2D3CFF961D2A9EFB3A7621,
	Clipper_Execute_mA5A3F3FC444587617F075E17180654E802A7E4B0,
	Clipper_Execute_m6BFF7174B7680F350A69BB8CD2B7997C27A78BEA,
	Clipper_Execute_mEE13FA88C6CC6871D5D66D8C0652A99F2E4BD0E5,
	Clipper_FixHoleLinkage_m4B62F6906FD04FECBCE2F457400B2CC33694F834,
	Clipper_ExecuteInternal_m7D08783296C9BD61B5370C0DA00FA3E053DFE98B,
	Clipper_DisposeAllPolyPts_m6C8F88CA62B0782EFFF78A998A8131D0451ED47F,
	Clipper_AddJoin_mA87C379712044EED79BF51767A4C79D7D0351EC5,
	Clipper_AddGhostJoin_m612C3ADB4C32701F537DA35DAED2193BD80CE609,
	Clipper_InsertLocalMinimaIntoAEL_mA82B704FD997BB615616F8CF16F0ACE970706B52,
	Clipper_InsertEdgeIntoAEL_m7584619E65629469C02AC98B40B0CDD00922D617,
	Clipper_E2InsertsBeforeE1_m0009EE4EFDDD65FBCFAA6DBB68CF69EECD7BC653,
	Clipper_IsEvenOddFillType_m9F2B05C69F279E0C7F9C1C5F2E748E6979321A3F,
	Clipper_IsEvenOddAltFillType_m5D259419A5C33724F372EE937C3469AA34FC199B,
	Clipper_IsContributing_mDFF5C7D0EE49D4D958D774BE48C5598FE69F2FB9,
	Clipper_SetWindingCount_mD42016432E8FEF29D8ED729408F9E24C3BFC80DD,
	Clipper_AddEdgeToSEL_m3B43222EF1A254812F2A7D61AE72959852A7A4A1,
	Clipper_PopEdgeFromSEL_m45006B3EB7CA7B77EB866606E3FA7C1D3A7BDEE9,
	Clipper_CopyAELToSEL_mD4D878B45AD029FFA43E1B3F681D089B99058CFD,
	Clipper_SwapPositionsInSEL_m5CC2DCF327486C0BE07121830577CB74CECDC207,
	Clipper_AddLocalMaxPoly_m375B6F89A77EC9D1B0EFDBEA8312F9FE249C807E,
	Clipper_AddLocalMinPoly_m7C4A79390680D0A5C5F09A341F67FC64331C5A2B,
	Clipper_AddOutPt_mC0A2C10B5A7FCCFF77CABD850B17859164EDBBBF,
	Clipper_GetLastOutPt_mE511905B91CC0BC041D55B614DE1A5ABACDEF5E1,
	Clipper_SwapPoints_mC61CBEED84CE6DCC505247E0C8692FCB7CBAD043,
	Clipper_HorzSegmentsOverlap_mB0882EB0BA3FE027C2066A9FCE3176BA2144F0E0,
	Clipper_SetHoleState_mCF984127BF470390DCFE98A155E9D112320F08BE,
	Clipper_GetDx_mE8200E866BD7A7DF76D24765377C40BCD0FAD709,
	Clipper_FirstIsBottomPt_m736C3F4AF75269DF0920BEC50F0D49E9FEE27204,
	Clipper_GetBottomPt_m840521EDC1D46AFC372FEEC7156A972401BAFDF6,
	Clipper_GetLowermostRec_mBF0DB1688D4B7C020685B70A1CE2873868ADFDF9,
	Clipper_OutRec1RightOfOutRec2_mDF89A4E48E77D7F36B6712E32F0D2DC538405779,
	Clipper_GetOutRec_mE6B40CDC55D028755FF06F961E56EED4B46A970E,
	Clipper_AppendPolygon_m8A44054101EDA2291F3CE49082EDDE792ED3B9B9,
	Clipper_ReversePolyPtLinks_mEA503CD7054E111843825753767C860123935328,
	Clipper_SwapSides_m9FC00D418F5135E48F2CC1EE5EC7013E42D34488,
	Clipper_SwapPolyIndexes_mC557D0FE42F84FB0618EC3753ABFF452DD61127B,
	Clipper_IntersectEdges_mD054D64A65188A152D241B4A66A9F4DAF61A5E97,
	Clipper_DeleteFromSEL_m24617A4CCCF62EFE7D57818C59FD6BA322826E2D,
	Clipper_ProcessHorizontals_m4B595F375B5DA3AD1DEE1227A0B49CE6C63EAD46,
	Clipper_GetHorzDirection_m6A3C4247B1BB901651DD505DB7FE5DB156B7E715,
	Clipper_ProcessHorizontal_mECF545528976041E2CD3C08E9D6031B379ACD772,
	Clipper_GetNextInAEL_m11BE83210097750DBC0BE637744F6E0465391C24,
	Clipper_IsMinima_mA7F1B87150CF93342F8B66335DB3256ACEB2A616,
	Clipper_IsMaxima_mE0DBD58A52930B956A04EEC33CE7444683D560E0,
	Clipper_IsIntermediate_m470B2D9FB187245F625D3823186F77471A7E424B,
	Clipper_GetMaximaPair_m0220542A90C48A7028925F730724CF15CDEE1FA8,
	Clipper_GetMaximaPairEx_mA4F0D5DBDF57152DA6CB784F96FEA4A1E8BBBCB6,
	Clipper_ProcessIntersections_m4A76591615621FC7F31C0D2A7344E1BAACE50BDD,
	Clipper_BuildIntersectList_m2CB9D558A5A064D89485CC6D3DB2172EEDF6F70D,
	Clipper_EdgesAdjacent_mBCFFF6E516FEE06E1BB61B87630C092C2C6B0A7A,
	Clipper_IntersectNodeSort_m3AD2B566799D815D66E4A0B5BF38FAA541EDF86A,
	Clipper_FixupIntersectionOrder_m34743E697F79F00B31E1B46FBD29C5BD418B4F92,
	Clipper_ProcessIntersectList_m914E8AA216C8EC1142AB3908727F0A151E487A66,
	Clipper_Round_mA77D2621C05B82882ED72BB6E948D007CEC926B2,
	Clipper_TopX_m8DE68B1C33B15035D802240A279626C5A0AE14B3,
	Clipper_IntersectPoint_mA466BDB8128485DCB73CA65F7942409B8F81E4CE,
	Clipper_ProcessEdgesAtTopOfScanbeam_m6AD0668AC23EEFE993E18C5C190DA39612F2B8BC,
	Clipper_DoMaxima_m74C5B15D72E63AB46F7AD86BA78D81A825C8C753,
	Clipper_ReversePaths_m523FCFF78EF4BEBF609D318B0366178AED833CAB,
	Clipper_Orientation_mAA2E77E8CFB335296C5F6DDF07DB62A401674FB6,
	Clipper_PointCount_m88CCAAEDFE45055530FB8008192EA3ECF7E3FBD2,
	Clipper_BuildResult_mFDFB8C6E1880CA19175F3CE89A95BA8C86DFE10C,
	Clipper_BuildResult2_m7367CEEAB529E9B4CA1E7F8888910375F44F8E63,
	Clipper_FixupOutPolyline_mD67B3527E6C50388E6E4A5610072C0340209E483,
	Clipper_FixupOutPolygon_m85287817B16BDE7470C3BC58DD1C203298F6D299,
	Clipper_DupOutPt_m3BA477F43003B7EBEA4691B4F1EAAA6264F559FE,
	Clipper_GetOverlap_m42026FC20CD4FBF2C537B7829B968504C9986E56,
	Clipper_JoinHorz_mB4DCBF754F6D3F2FA0E3E1226E8830846B0FA549,
	Clipper_JoinPoints_m423275BB6DCB14C4DF436FB1EFC114FD6D1B3A80,
	Clipper_PointInPolygon_mF11506965411DFD9662DDA49A16CFA3209BB5C60,
	Clipper_PointInPolygon_mCF19505D43AE084A8BEA8860FCF134F5652524B2,
	Clipper_Poly2ContainsPoly1_mEC43E8E8547F0CADADE1DB80D45B6B67B29E7BBD,
	Clipper_FixupFirstLefts1_m955754CF5788136720A5C049B4C4CF761DC73AED,
	Clipper_FixupFirstLefts2_m34CD287CAA95AA183535EAFFD64B45CDD8847752,
	Clipper_FixupFirstLefts3_m764B2D88C1CA5437B2014EB8EBF1A3A0BB4E9AC4,
	Clipper_ParseFirstLeft_m7D1AE9300F78210F45EB132427731BEF24C952B9,
	Clipper_JoinCommonEdges_m2E6FD6E964AEE899F4725C3A930460926009DD02,
	Clipper_UpdateOutPtIdxs_mD7AA527B468A83E10A722589DA7DC6EDC74266B0,
	Clipper_DoSimplePolygons_mBEF68D5DED381C48823A211E5891E71A01E5A30C,
	Clipper_Area_m0C8F6E265FB3A731A4BDEA4FAAC2BD11E691BDCF,
	Clipper_Area_m22B30EDCF6C7DDE5B7DE2F892A3410DA26EACE13,
	Clipper_Area_mD211BB43D4AE983C8EEAE1BCC9B4DE6E653B8299,
	Clipper_SimplifyPolygon_m8AABE4A176C6DF388D644D478577211E86B69C06,
	Clipper_SimplifyPolygons_mE5D7A0917BBC6067C2F79050721BBF40599BA20A,
	Clipper_DistanceSqrd_mDBED06ECBD73F8D0638A7DD69BA523AE596D9B5C,
	Clipper_DistanceFromLineSqrd_m1D0734740503B55AB703355431F34B24778AA4B0,
	Clipper_SlopesNearCollinear_mB1DB109DE0E73203B2C85CB98037ED6B32874496,
	Clipper_PointsAreClose_m55D44A9DC9B3367E5844C6AEFA406A6FC0CF2531,
	Clipper_ExcludeOp_mF68B2639A94B551C633013C7DC1F6DAA19F1AA2F,
	Clipper_CleanPolygon_m50945A9225FE118C7E5AF61CAE3FFFFB8DC2F364,
	Clipper_CleanPolygons_m5AE84E4C1A0BF4F1B337EA16675674CBD7F6789F,
	Clipper_Minkowski_mEF5E1EFBB32800CC6FD0A31E9BFB98EA2E7F6AC3,
	Clipper_MinkowskiSum_m52E28149418FBC2DA0BD6CDC7CD51DE0C0914B12,
	Clipper_TranslatePath_m989F5B720466DE87DD7ED7222A81B819BD7EC735,
	Clipper_MinkowskiSum_mC50F9511EB58CAEB4AF2E6A7893123D75B43A3DA,
	Clipper_MinkowskiDiff_mD3BAC64A9C19F98526FB081E775427F4E500A921,
	Clipper_PolyTreeToPaths_m7ACCF8C1783D5D638EB2473756F5DA78F82CEDE9,
	Clipper_AddPolyNodeToPaths_m8285754304395D43033E500A6FB193A0130135B8,
	Clipper_OpenPathsFromPolyTree_m1B293AF32A608083F8EFBB54D78F453E3C7BC599,
	Clipper_ClosedPathsFromPolyTree_m2C78F9D0CF438791FAA3DDEC26D6C17D470BAF38,
	ClipperOffset_get_ArcTolerance_m789189DA990071CC699A4D73945042326A03F139,
	ClipperOffset_set_ArcTolerance_m3DFE7D76F298F4C80B7A2BADF84B7D07A317A8D4,
	ClipperOffset__ctor_m1A52E0DF9798A533D30D12A8C03CF993264B0CE9,
	ClipperOffset_Clear_m07D7E1B2791E98189A9ABCBA9026A47B4A53BD60,
	ClipperOffset_Round_m47BA7B3F751BD03A0D2500800DF21CB981969446,
	ClipperOffset_AddPath_m78D7148CD786EAF6275B46B9F743188249398988,
	ClipperOffset_AddPaths_mFCD6516C336C117E7A9DF086ADE5ABD05467182A,
	ClipperOffset_FixOrientations_m5C6E3E9E9FE03BE2A56F5289691F013324725616,
	ClipperOffset_GetUnitNormal_mE3D4295EFBF7F39A2BA158C99C204C1152E2DC98,
	ClipperOffset_DoOffset_mC6DA30C8D896E0DC750E098754A9F1E4F110CBB0,
	ClipperOffset_Execute_m9F9D16406B43192D18A02CED1C3D6D849FF42706,
	ClipperOffset_Execute_m3FC3FE69829108FE13A2D922DA67998C72602A04,
	ClipperOffset_OffsetPoint_m4CB6DFFEC35012C732B8C229D2427814F05729BD,
	ClipperOffset_DoSquare_m69ADF6B140584C61444F6CD35F47F00A6EB30068,
	ClipperOffset_DoMiter_m993E5CF14E91A145B9614A82E1B2C5A90966DBF2,
	ClipperOffset_DoRound_m9E49F085B9B848B70E9EB640943FEE5FF928A591,
	ClipperException__ctor_mF1E22950760B1F1C7D5ADE536EACE305D09B8651,
	Light2D_get_vertices_m02D080A78BC48FF5B02844C79789F40BE4284188,
	Light2D_set_vertices_m93A93ADF722B7BD7894D12FC890CC1955CBCE2DA,
	Light2D_get_indices_m06336E2856756DB8649979C77BC7FBBB92395974,
	Light2D_set_indices_m432850D252D6CDEC08AF20376CB95F7C838D134F,
	Light2D_get_batchSlotIndex_m0B51B331D7A794B939B3EE8BF76F6A9006917430,
	Light2D_set_batchSlotIndex_m5E5B20E26B90F83E91FF2D2E955AC8DB400B2B6E,
	Light2D_get_lightCookieSpriteInstanceID_m30FF418E8659E7255A08B5957A24F0467535F6A1,
	Light2D_get_useCookieSprite_m2C52603A7FB008F4E89836CFD36018D42E03901B,
	Light2D_get_boundingSphere_m1B78AF6A3981BE032C1EE459C109948160B307E0,
	Light2D_set_boundingSphere_mE3B40CF5BFAD77846DE6ED73621E594B3F9F7433,
	Light2D_get_lightMesh_m36653EA7D3AC78C63952000C029ADB16AB28FE8F,
	Light2D_get_hasCachedMesh_m9311EC77B9CA1860E01D25270896723FA35FAF35,
	Light2D_get_lightType_m4B3DFEF0B821E6EBE24337820CC87E4AAC589097,
	Light2D_set_lightType_mB964E077A445576A182B68079590474D31FF0FDE,
	Light2D_get_blendStyleIndex_m5022FCE517D15BC317F335AD485068ED849CDA36,
	Light2D_set_blendStyleIndex_m2431A3E3B2BA2C13C1FF017C0CC9C85E68043741,
	Light2D_get_shadowIntensity_m650F97C7B5E81434800E92D266DCD7D7DEC0663E,
	Light2D_set_shadowIntensity_mBCAB002DA5D4F92A6958610CAB1005C311B63B9B,
	Light2D_get_shadowSoftness_m5127B43BDA9A3C5CD99CF8976D3CE9A0F5A79683,
	Light2D_set_shadowSoftness_mD9DDE3B82CB473A329096C818E8923DF1FB6633E,
	Light2D_get_shadowsEnabled_mDDAE3B6F8129E4DF72E3E91669FAA262733D33B3,
	Light2D_set_shadowsEnabled_m69ED92B90AC2082A4CBBA1E62D6A8EC6F4A12FE8,
	Light2D_get_shadowVolumeIntensity_m4BAF625CC5844F53C81DCCED0C02EDDCDC36325A,
	Light2D_set_shadowVolumeIntensity_mC342B1C841A107B6A14BC00EB8529C06C682CB7A,
	Light2D_get_volumetricShadowsEnabled_mC92883629048CC425D3356B5A5121BEAA37F7471,
	Light2D_set_volumetricShadowsEnabled_m49F2AE1B923D6B4A358A41D56AE9F4CFD45012EC,
	Light2D_get_color_m6BBF205EE29D52B7C5CFFD1273B85680A02700A2,
	Light2D_set_color_mA33647407CCA9F5B3121B0518D71BDD996E6EA91,
	Light2D_get_intensity_m314E2E858289C7B01C8E9B7265ECF8193522D2BD,
	Light2D_set_intensity_mE28C237751EDE1E6F5D78F5B0019FC6735957359,
	Light2D_get_volumeOpacity_m15E306E958F493BBF63A43EC689B3BF16D8616B0,
	Light2D_get_volumeIntensity_m50854F673179630BC2AD1E1BA9A6F68ACEFD58EC,
	Light2D_set_volumeIntensity_m502D7E022C07B64C6C4A7CE71B130587F416C964,
	Light2D_get_volumeIntensityEnabled_m625749FC7CC12D09107667494FF028022BB7EB78,
	Light2D_set_volumeIntensityEnabled_m40C9B8AFF340D30A35D99925E95330A166701ECD,
	Light2D_get_volumetricEnabled_mD0D08DCF54B1C42CF9F16EE0EF643BD23CB781DD,
	Light2D_set_volumetricEnabled_m84F441C40CCEA738E194E26FF685377F62F0601C,
	Light2D_get_lightCookieSprite_m269D43B5E09727578FA6EB80DA190D157C7CEE19,
	Light2D_set_lightCookieSprite_mB3EFF2456B7B4E70736CA42A08AF6731CE67FB13,
	Light2D_get_falloffIntensity_m22D6E6A3165A26A787D8CE0B16A031041C52B485,
	Light2D_set_falloffIntensity_mAFE26BE3643CE03ED76CDA29B9B460846A3D5C3E,
	Light2D_get_shadowSoftnessFalloffIntensity_m46A7A9E2EC93A73206E76EDC333B62AFB4919326,
	Light2D_set_shadowSoftnessFalloffIntensity_m8FA05074C7AE8B9DD4CF195573E3BCE5265B0D38,
	Light2D_get_alphaBlendOnOverlap_m8B0141E8FA9F3A31518D3265B032F927FC0D2642,
	Light2D_get_overlapOperation_m551ED37C2B318C89C1D9CA170EBD771FFEF97E38,
	Light2D_set_overlapOperation_m49B1B0F9083ADE10F6CEE76B9DDA6264543F0460,
	Light2D_get_lightOrder_m98564FE88A359A61FA48E3A8CB86AE5E577EFF8E,
	Light2D_set_lightOrder_mEEDABA50C7406B8993909F078D965B862F48B016,
	Light2D_get_normalMapDistance_m4062B2799F333EF3E949FF3D7DE65DC2691D60B3,
	Light2D_get_normalMapQuality_mD1DC839DD41E9468A432EE08203F642B6CEB558F,
	Light2D_get_renderVolumetricShadows_m64B99C9CF0366EDB1E7C8A61B83FEFEC144309CA,
	Light2D_get_targetSortingLayers_m507993B01B1260EFEACE93CC90558062C31AB825,
	Light2D_set_targetSortingLayers_m27E926A40BCF90A8835E06B2B686106B71D40345,
	Light2D_IsValidLayer_mD8358AF68A650551AD681E85CB315C1D473EDDC3,
	Light2D_AddTargetSortingLayer_m965A1AC1DA1DC591E449B3C7E21B25B8E0260896,
	Light2D_AddTargetSortingLayer_mF682B63ED6C64F54E1512F984D8CC3FCAA1A6F14,
	Light2D_RemoveTargetSortingLayer_mE3915F9C28AC691C2DBAB85B9024A9D49B2A742E,
	Light2D_RemoveTargetSortingLayer_m90B70D16E02D7C234ED19DC65EED3C9F730857BD,
	Light2D_MarkForUpdate_m5BB2181B870C167B8F63066F25DD7C4287C177CC,
	Light2D_CacheValues_m24A4089B58DBE8FE07DC5C7358BF953690749564,
	Light2D_GetTopMostLitLayer_m303645F98C379D3B5ECB3D2AD0C9F7968D4D6EA5,
	Light2D_UpdateSpriteMesh_mC2B147782628BF45D0285A9E987789AD8DDAD3BA,
	Light2D_UpdateBatchSlotIndex_m08FC5148F1F8460C6B146BEDBB11E3D4300E7A46,
	Light2D_NeedsColorIndexBaking_m7FC2F8AFC1DB91154EAD30878F6DC84362F6EF0E,
	Light2D_UpdateCookieSpriteTexture_m4EFEA55D249D5CE52598F69333AB6C7527AA40FF,
	Light2D_UpdateMesh_m7BF8080C85FD793D22637AE363E24ACFF9E86B96,
	Light2D_UpdateBoundingSphere_m4FA46F8CDF8D684F9878DCDEE4ADE97E12667365,
	Light2D_IsLitLayer_m4D56AA7A4749D4CCE3E8AFED8D47AA6506F3B5D0,
	Light2D_GetMatrix_m72971588F4FD378CDAFF5CC261C86F14AAED65A5,
	Light2D_Awake_m68478B72028919B7F2875EF70F8CD4B91B9249D9,
	Light2D_OnEnable_mE1D3021C29C009A55D524234A8FD443427857CFC,
	Light2D_OnDisable_m9990C78B1657A0889E9F17F4D6F775D2FAD9BFA7,
	Light2D_LateUpdate_m37642B2F400EFA4407E6A055D231A6F685223C22,
	Light2D_OnBeforeSerialize_m866376CDEAE8B560BCD3B5984E6CF93F62D72EFB,
	Light2D_OnAfterDeserialize_mB21C7A863E878839DC4381DDEC85667434DC21F2,
	Light2D_get_pointLightInnerAngle_m9081D25FA7367194C6BB668640439588CB5ACEE8,
	Light2D_set_pointLightInnerAngle_m55AF6A0B3E79FA451763D2712BE6417A141877FB,
	Light2D_get_pointLightOuterAngle_mE25C3D35226DB71C0DBFF2737E4F33D8F240FEC1,
	Light2D_set_pointLightOuterAngle_m79DBA1BDBFEDCD89F19E5FDC782B70D36E883481,
	Light2D_get_pointLightInnerRadius_m3A82695BC7D7817665CFDA1524B9FA6F81D390D5,
	Light2D_set_pointLightInnerRadius_m7F3013BB1EBF3B1DBBCB1B856B69FE1C7FAE8C60,
	Light2D_get_pointLightOuterRadius_m9DC18E51091F3977F2F49BD4DAC7A42E379B3FA4,
	Light2D_set_pointLightOuterRadius_mADBCDB215684B1EC3C08FF3C5704D3B74C68F26A,
	Light2D_get_pointLightDistance_mC14DA7B2C9FF402C9E92973614370A286DA5D26C,
	Light2D_get_pointLightQuality_m8CA8D328F163D35494D945DEF4C73057427E5844,
	Light2D_get_isPointLight_mCAAB187BBB52B8A631122279D2349D6F598505E6,
	Light2D_get_shapeLightParametricSides_mC35FEE23894A8FAA0F7D0BD061A7B90F0EED1C4E,
	Light2D_get_shapeLightParametricAngleOffset_m2E8D6EB3B554B19F4E721C661030BF81CCC318A1,
	Light2D_get_shapeLightParametricRadius_mF68CA9032F11C90DC1EBDDA8225D3A2D7294CA4E,
	Light2D_set_shapeLightParametricRadius_mF4DCB86DFC4543C658231C8C0058675C0BBE50C0,
	Light2D_get_shapeLightFalloffSize_m7721FEC3F1107EFC4D6588414B08C54A0050150A,
	Light2D_set_shapeLightFalloffSize_mAD3C488B2F8C365B89A9B6C876AA8B34F3C450F0,
	Light2D_get_shapePath_mBB6B7B49C59F4EA01CEC08C3B1C869AE921AEAED,
	Light2D_set_shapePath_m5B5BD8F64481A0C87635834682D57C9F90C701E4,
	Light2D_SetShapePath_mA62CB7D32E7EDBD594B19FEF491F0921FCEFA7DE,
	Light2D__ctor_mAC4A9EAB4D0F7E8D9CACE0DAC7D2F217F9B82798,
	Light2DBlendStyle_get_blendFactors_mC60D457EC148FAD9DF648D8744E8FD1739428D76,
	Light2DBlendStyle_get_maskTextureChannelFilter_m0BAA8F7231AC36BBD698EF17ADA929551CBEA5D1,
	Light2DBlendStyle_get_isDirty_mBAE482E03B7BBAF92D33250196D9572CBEE943DA,
	Light2DBlendStyle_set_isDirty_m9E685039DACBFC96AD6BFE2B0159F8D48650DE37,
	Light2DBlendStyle_get_hasRenderTarget_m1165817ABB896D6794DD535B31F43253F0C77326,
	Light2DBlendStyle_set_hasRenderTarget_m8C3590F7559A6A200B2A07395275A10E4BB453FA,
	MaskChannelFilter_get_mask_mE84FC5DE03D3B8FC30FCDD09098E5BA108FEE550,
	MaskChannelFilter_set_mask_mBA4F50BB0878FF53E2BCB044D4AE6056EBF6E15B,
	MaskChannelFilter_get_inverted_m4F955DAACAED39F771A5D442479390BA5B87D623,
	MaskChannelFilter_set_inverted_mD1CD0612835F5757B16C099121FA8CD2B6E42FEF,
	MaskChannelFilter__ctor_m0EB791DE0052FF44F5E9FEC4F927F7459CAFD7D2,
	LightStats_get_useLights_m5D283F1EE777C5125CBCF130FCBD5E65606B9685,
	LightStats_get_useShadows_mD182F3CEE2ADAACB4E5621B0B095BBDCBFD6300C,
	LightStats_get_useVolumetricLights_m24B97C84F76DD41D6D9D6A1AB1CB65AD08333D92,
	LightStats_get_useVolumetricShadowLights_m4F51EC079FF136ED8D217328333FBCAA990C8324,
	LightStats_get_useNormalMap_m62161A14013BBDB018B9DA75376A1864199FCDED,
	NULL,
	NULL,
	NULL,
	NULL,
	Light2DCullResult_get_visibleLights_mB8E2057748258CD9EB430AFEC401B213FA6E4145,
	Light2DCullResult_get_visibleShadows_m25A1CC24120F4C5ADA60E68A2D6E701DBCAA4085,
	Light2DCullResult_IsSceneLit_m6D24A4E77358390F0AC33FC0BA64EFDE2ED93BCC,
	Light2DCullResult_GetLightStatsByLayer_m9B6FBE09E3321D791A246F98DB61C7B0A7102C42,
	Light2DCullResult_SetupCulling_m5B926BF3D30A11B90EB2F54FE4F9D9E0D5E403FB,
	Light2DCullResult__ctor_mC609816D7818C069F1791B5AC354A2F81A57349A,
	U3CU3Ec__cctor_m5C5FCFE7B39B50262F61E9F07686630015CEE020,
	U3CU3Ec__ctor_mDBA5983FBEEB4E8494D3CBC320A3839EF7392A33,
	U3CU3Ec_U3CSetupCullingU3Eb__8_0_mDEFDD320D9E8E54B0CEA64D28E2BA2496FCF9C76,
	Light2DManager_get_lights_mFB510837AFAAF5DB9DA1E017531AACB670BD7DAB,
	Light2DManager_Initialize_m6532C2F2F8E3B49E3ED9B37E810CAAC263D648C4,
	Light2DManager_Dispose_mE976706C09DE563755418A1930B32AEBBCB21F13,
	Light2DManager_RegisterLight_m99273E83E7AEEBEE04C019DAFEF468B3D0C11613,
	Light2DManager_DeregisterLight_m9348E3ADAF93EA79BECC28A28D5EEA0B855193B4,
	Light2DManager_ErrorIfDuplicateGlobalLight_mEF25755359B8589F953E3425634C5B9505406B76,
	Light2DManager_GetGlobalColor_m7B7BDD801A82E47F014F065FCA9B2CB673308A22,
	Light2DManager_ContainsDuplicateGlobalLight_mA1B36C42CAD7B2CBA22CEA1B2E9B07EAD2AE289E,
	Light2DManager_GetCachedSortingLayer_m764C670FCEB4F029DB74C2B0D98CEE94B248173B,
	Light2DManager__cctor_m84CD663BA5581E4C8A1CC0DD2F5272E668CF4EE7,
	LightUtility_CheckForChange_mB65C2BCC30014B662DEF8F099B86234FE654E5D7,
	LightUtility_CheckForChange_m57245EDC04F9C7C58CADC1146C1B3E09675B3AFA,
	LightUtility_CheckForChange_mDF8E1A2DEB5B99CA49ADB27F8083449114CF25FD,
	LightUtility_CheckForChange_mB49921527380387C972B55BF35CCD192D86C8EDB,
	LightUtility_CheckForChange_mD5C8C73254BBE688F9DEF3502F6F8D8211018C94,
	LightUtility_TestPivot_m0ADA4D05343C7D68A724CB6F54337D41B9DE5C1E,
	LightUtility_DegeneratePivots_m678E91731D80F748B6A1A90AD285916812BDBD59,
	LightUtility_SortPivots_mE32B6077F82E1D69C0C50D7E575C3880EDD252FF,
	LightUtility_FixPivots_mCBA9F5F5CDD094A370A315C5EF4E203FCB6210BC,
	LightUtility_GetOutlinePath_m589B9C90B523CC24B0C82F6F0199E9AA9CF8E8FA,
	LightUtility_TransferToMesh_m71367E6605D8D36FB118BAF1E83F79316C657556,
	LightUtility_GenerateShapeMesh_m11E9ED4523584976B57033E5125BE7CA2D749E86,
	LightUtility_GenerateParametricMesh_m66406BC7B72696E3EB71FD2DAC6DBE0662EE1409,
	LightUtility_GenerateSpriteMesh_m62A264A6CB484FD49890FD3E92FDFA13E30C7AFA,
	LightUtility_GetShapePathHash_mE74777FD3CFEE93074C35B4A82E8B77B61EEA31D,
	LightMeshVertex__cctor_m535187D6EA25C79BF942E2545F937DB924CF09BC,
	NULL,
	PixelPerfectBackgroundPass__ctor_m86A0236C3536B437EE8583523A1B39209186FE88,
	PixelPerfectBackgroundPass_Execute_m8740C7CA194D1FDEFA40BCF0B62D62FE412434BA,
	PixelPerfectBackgroundPass__cctor_m51A08FCDEC8045C7D997043F0BECF786AA64FF88,
	Render2DLightingPass__ctor_m26264190053C29ACEE89A5E978858D57AEDE365D,
	Render2DLightingPass_Setup_m3FBC7898206307E8806EA0DAB34858D9CBB7093C,
	Render2DLightingPass_CopyCameraSortingLayerRenderTexture_m99CEA96622C89738D23B8CABC4542C415801A1D4,
	Render2DLightingPass_GetCameraSortingLayerBoundsIndex_m01AA8FA01196C4DA9EF63DA66622C00632838785,
	Render2DLightingPass_DetermineWhenToResolve_m7E6E2643CBADED1922AB40FCFE273B30E664E657,
	Render2DLightingPass_Render_mBA45ECE41798777E6CE9A9E25BA469DDA226C230,
	Render2DLightingPass_DrawLayerBatches_mB46F2DC778EF51C79DC061C4AE794558D0C61EDB,
	Render2DLightingPass_Execute_mE7EF30C9B985249CBCF623653F2228BB4CE4964B,
	Render2DLightingPass_UnityEngine_Rendering_Universal_IRenderPass2D_get_rendererData_m10FAA8B0DF4E1F03CF576EDF27F4312AA9512278,
	Render2DLightingPass_Dispose_mA6539778FF349A2FA439F95876DC380386517317,
	Render2DLightingPass__cctor_m8AF4838112C375B2ABDDC71B34B73F3236DF2630,
	LayerBatch_InitRTIds_mC15F5BAF8B993D83A36EC1220A772C86C6C3935D,
	LayerBatch_GetRTId_mD844336931BE54E9203E890BFAB139DF9F713266,
	LayerBatch_ReleaseRT_mD2C253FB4B36C42D7DFE559872AFD91CFA7E5024,
	LayerUtility_get_maxTextureCount_m0954302D62496ED6F17F322C286969822856B2A6,
	LayerUtility_set_maxTextureCount_m088362A56E478B589DF8E3FF3C7F764670EFCB15,
	LayerUtility_InitializeBudget_m48BD2B2D16A4C7AA7692146F1A92F8E31EAAE7F7,
	LayerUtility_CanBatchLightsInLayer_mEBCDDD3A3D16D256A58778B1F685613EC8EF289C,
	LayerUtility_CanBatchCameraSortingLayer_mF59A650337B6A95FF7FFE7E58E6D79B13A056068,
	LayerUtility_FindUpperBoundInBatch_mA364B9A4A86A29D313996CAD19109CFB69348BA5,
	LayerUtility_InitializeBatchInfos_m8A3B081E3B668FB1707A0EE9D1E560EF48EBA839,
	LayerUtility_CalculateBatches_m60B7E1AE0A4BF35AD520351DB9E46A99E2B647C2,
	LayerUtility_GetFilterSettings_mEC82CC59AE688E41AEED3E128ADBB9ECAF154EB2,
	LayerUtility_SetupActiveBlendStyles_mA3A4563344BAE12344466651E0CFE2454B392E43,
	Light2DLookupTexture_GetLightLookupTexture_Rendergraph_mFD299D2C9AC6F1A9946E2922DBF90AFE0D7C20A2,
	Light2DLookupTexture_GetFallOffLookupTexture_Rendergraph_mBF4C976304B9C96240014BD3A9C6D7CA6694DACA,
	Light2DLookupTexture_Release_m32563DD10E4BC167F4A49C3D4B69AB18D8AD8102,
	Light2DLookupTexture_GetLightLookupTexture_mBD4AB58BAEFD9E69EE37735A83D08BF9A830FFFF,
	Light2DLookupTexture_GetFalloffLookupTexture_m370A351FF1675270196D6BE4612A1BD3F2FF105A,
	Light2DLookupTexture_CreatePointLightLookupTexture_m95160038940FEF512EF7E3128BE979CFABEA210B,
	Light2DLookupTexture_CreateFalloffLookupTexture_m2E830891B0926BC7427F81647420073B422AD151,
	Light2DLookupTexture__cctor_m1583B83BBAFB4C72A057BB913EC26FD063A3E6C4,
	LightBuffer_get_graphicsBuffer_m78B4581311CD4DA3A65EBD56E7B03905F3EE8B8F,
	LightBuffer_get_lightMarkers_m9A678AA9F780CF5879BC6548AAF674E2D73CD96A,
	LightBuffer_get_nativeBuffer_m2F20F7BE4C63025BF1043EB7A430EBC3CE7451C5,
	LightBuffer_Release_mDCB2E543DA8A81D799C34F98B5F5795C1C1A7A84,
	LightBuffer_Reset_m288E71C0BD5B2F9302451502AA81EAC4F290ABDB,
	LightBuffer__ctor_mB824945998E943542F68A78309DEBCCBE2EB31B7,
	LightBuffer__cctor_mDA7305E6FB4313A66D6E1E28878DA0DAE771C9A0,
	LightBatch_get_batchLightMod_m56B5FEDF5AA2A01781E1BD25EF12AC3B79B148AD,
	LightBatch_get_batchRunningIndex_m04AD40332816E83886B47F651097EAE1A911B9E0,
	LightBatch_get_isBatchingSupported_mBAC6C205C6E2EDB1089FB9B2202339D382F6943C,
	LightBatch_get_nativeBuffer_mAA551A379DD5B0E072A01B9AB46DB9183C6DE557,
	LightBatch_get_graphicsBuffer_m3445577419D6248C1EFDC0E1CCD33B5EC67204D0,
	LightBatch_get_lightMarker_mAE455AFA7EBF68DD52F8E0D86D6CA8D488095626,
	LightBatch_GetLight_mE76BDDAD1500687E8648244B966B1E8C2FA06A30,
	LightBatch_get_batchSlotIndex_m992B374107A15AFAFF7D8B02A2EE8586E788BA42,
	LightBatch_SetLight_m4A73C022698DA833A16A2F8E71ED804E86CA7069,
	LightBatch_GetBatchColor_m3F361FD7BC467A4718E4130E739DF7B6E7568493,
	LightBatch_GetBatchSlotIndex_m41C6D6BADBE271C57C905770AEB9E3E11D09E13A,
	LightBatch_Hash_m8D1AF6C85DCCBD5EA77B9CC83CA8D6878C7E3350,
	LightBatch_Validate_m299E5FE2BA84F4E439E51881D4BCA3D1E83BF5FB,
	LightBatch_OnAssemblyReload_m2586E10B8E6D0B026E7718830AE3D144B171E1D5,
	LightBatch_ResetInternals_m494376221916E69CEEFEF6720D642CAE126B8C7D,
	LightBatch_SetBuffer_m6952E68B3E54047ACC7E32A7C7C0DDD3F88AA666,
	LightBatch_SlotIndex_mB701097B2FB44229BCF6404AEB67CBD22E2DF85F,
	LightBatch_Reset_m5A1ADCA53BE2CDA777644526A1437BE5437099C3,
	LightBatch_CanBatch_m7A43AC6E93A11702616ACF648DEB1D8950B0709E,
	LightBatch_AddBatch_mCF836429D32A838D30210F3FA1FE18AA0A429121,
	LightBatch_Flush_m4EAD3228E1F1E0D8BEFE24CF8258230DA9C14E38,
	LightBatch__ctor_mF10B3F2F99C2E0721F9D14D633A5C8C490FD218A,
	LightBatch__cctor_m6C6633B82EF139F947B9B4F213F46175607F26AE,
	RendererLighting_GetRenderTextureFormat_mEDBD21AF251A0E99A12DE7A7A99905E0D78C61F8,
	RendererLighting_CreateNormalMapRenderTexture_m932AF3AB045A92113107500CFF4E974EF18BF9E2,
	RendererLighting_GetBlendStyleRenderTextureDesc_m86C721468AF33D420C2F47C73B05E0FBD60164EA,
	RendererLighting_CreateCameraSortingLayerRenderTexture_mF66525AF6AB1DAFF475FAFA430C8CA23CE5186A0,
	RendererLighting_EnableBlendStyle_mFE530D1333F0F80ECD69C51DB35E8FA5A5DEBFBE,
	RendererLighting_DisableAllKeywords_mAD6D8BCA9DDEA324040D8E9A17DE7C1BFB49C743,
	RendererLighting_GetTransparencySortingMode_m463D291DD520F6A129519CF69F715FBF8EB8FD67,
	RendererLighting_CanRenderLight_m69470E4D13FCEA7052E2F5965642FA6AB3E8BCA4,
	RendererLighting_CanCastShadows_m007FCFBC1F5FE7C190417065A619DF5614FD055D,
	RendererLighting_CanCastVolumetricShadows_mF09D3A010A68D9DB27D57D64A54B6FCAE6FB58B6,
	RendererLighting_RenderLight_m2E2042C9D20DF75D6FFBCE0D29DAF2C17CF3BBB3,
	RendererLighting_RenderLightSet_mD634C1448213EDFE58CD71499477EA62DC1F4E71,
	RendererLighting_RenderLightVolumes_mABDC85F23459F139A180FD16AC919DA05C241D49,
	RendererLighting_SetLightShaderGlobals_m2CB3DC3E167851D00A77A9C4E9E9FC940C039693,
	RendererLighting_SetLightShaderGlobals_m85DCE18286830A1814FF5F2FA6AB4202CD732ACD,
	RendererLighting_GetNormalizedInnerRadius_m1BBA02BC5A10A3472C03651FC511A6B10FC0ECF6,
	RendererLighting_GetNormalizedAngle_mEBAA7923DB8FD29DB91D8CD26F97F49C5A5AC258,
	RendererLighting_GetScaledLightInvMatrix_m5CB21F61E5DF1A2CAF4B010C90BFAA7A9EF184D2,
	RendererLighting_SetPerLightShaderGlobals_m26403ADAD43320131345474537D1B4EE16A4C200,
	RendererLighting_SetPerPointLightShaderGlobals_mD9A7BAC692944F6F7E3489F91333CCD313A80892,
	RendererLighting_SetCookieShaderGlobals_m1A84AD0A5AC9F1B8A4050FC1D947F27EC8F73F68,
	RendererLighting_SetCookieShaderProperties_m1D75FDC98B17FAC9E4C7D92C7ED895BFD0C8CEC2,
	RendererLighting_ClearDirtyLighting_m9E939AE0122DA564504C6B3A8D6D5998DE5722AC,
	RendererLighting_RenderNormals_m9F0998883AF753E5E9FFA2C9781F6B2F318525E9,
	RendererLighting_RenderLights_m774C14F01003219B9E127D1B6D24B4CC9A6AFEA4,
	RendererLighting_SetBlendModes_mC61B8F5126573E133D96670E9DEA5342855D4F87,
	RendererLighting_GetLightMaterialIndex_mC205E701C0DD0359B0AD8423659EEFBCBE3D56CF,
	RendererLighting_CreateLightMaterial_m6D63176168EADDB2441BC7198C8F787CA0332884,
	RendererLighting_GetLightMaterial_mF962F96AAECFF774B9B61393D64E11E433660110,
	RendererLighting__cctor_m058F06ABA7017A23C218D93599FB7175E12CB074,
	PixelPerfectCamera_get_cropFrame_m049E2C02AED3C1C244B3AFD1E0B6104AFCC33A60,
	PixelPerfectCamera_set_cropFrame_m6CE5030A1594EE99DA8FAE0B032182DD88033A2E,
	PixelPerfectCamera_get_gridSnapping_mEF34B7A6CFF739935B11FF478B1F096C5D321A80,
	PixelPerfectCamera_set_gridSnapping_m54000C548DDFE0CF863166F82D79E55F5D338796,
	PixelPerfectCamera_get_orthographicSize_m64808DF017F8BE75E1581746CB142F22A151A1A2,
	PixelPerfectCamera_get_assetsPPU_mDF5D73D22D07FE0CAA7761673527FF30BD10EB4D,
	PixelPerfectCamera_set_assetsPPU_m592A5390903DCB8D20122E25694705CA311B9CCE,
	PixelPerfectCamera_get_refResolutionX_m5523E53A067744D8A32B721DE936B800B43790C0,
	PixelPerfectCamera_set_refResolutionX_mE3F8317F6E4D4D8D355EF85CF8751C77A655DEB9,
	PixelPerfectCamera_get_refResolutionY_m2E7C84EA248898609CA5ADD30E211976A44CD521,
	PixelPerfectCamera_set_refResolutionY_m6440FB4E82E6E04091CDC83C8CF76E46CBF39370,
	PixelPerfectCamera_get_upscaleRT_mE8E62C676679B7D8E889C9CC73996E36A677A7A5,
	PixelPerfectCamera_set_upscaleRT_mBF56C58F57DF202E220A51A6A0BCF447987ED9E8,
	PixelPerfectCamera_get_pixelSnapping_m016C9FFAF9D3A769188F842CC09060AD3B12E593,
	PixelPerfectCamera_set_pixelSnapping_mD5038B0AACF71EE91A9223E753091FD5E1BC756B,
	PixelPerfectCamera_get_cropFrameX_m1E3DA844B064A4A258919FD41AA39AA819CAB405,
	PixelPerfectCamera_set_cropFrameX_m3C84FDF660EC143C9D15EA0F2EBA529E8C29BB60,
	PixelPerfectCamera_get_cropFrameY_mB6069E68FF57C4F9B5C5D45F434AA4E23DF6C166,
	PixelPerfectCamera_set_cropFrameY_mF679DFACB003DAB2B21740E21CECD739C9754B6D,
	PixelPerfectCamera_get_stretchFill_mE5BAB5E287F73A850CC9870D84FA176F7560BC44,
	PixelPerfectCamera_set_stretchFill_m0D6DA91446635FA665C3D405875B8237B3E1E2FD,
	PixelPerfectCamera_get_pixelRatio_m43A1ECE99E8FD38158D1AC65011DD98B50BD3A60,
	PixelPerfectCamera_get_requiresUpscalePass_mB8C4EA7518270939C98E1868DCF65DE8385A23FB,
	PixelPerfectCamera_RoundToPixel_mDAE91343C303FF75DDCDBFF55A44675ADF34A919,
	PixelPerfectCamera_CorrectCinemachineOrthoSize_m980A6969D95EA0F73DD16E9E3DA8E22565D94FB8,
	PixelPerfectCamera_get_finalBlitFilterMode_m7DE0B801BB4BD1B1CD41AB0EF6CB9A2741B006AF,
	PixelPerfectCamera_get_offscreenRTSize_mB23A1DC33EC0A8422CF1B4D618D291E2A3BD2F5B,
	PixelPerfectCamera_get_cameraRTSize_mE0CDEFF078B313BDA170E6EDC66A8A1C8F8B0656,
	PixelPerfectCamera_PixelSnap_mB4A7DDD3EC5A6BEE7086125E61E3A716CD25BFD7,
	PixelPerfectCamera_Awake_m25F30BF98810839D5A735CCAE9F57469B0398A99,
	PixelPerfectCamera_UpdateCameraProperties_mDD3B992B0A44231F2FD60075821015FDDD625E99,
	PixelPerfectCamera_OnBeginCameraRendering_m587FA327280AC017B622DB1B4706FF6D113D279A,
	PixelPerfectCamera_OnEndCameraRendering_m5D4D2899F818CFB9566FA00DB9734B19FE4F5172,
	PixelPerfectCamera_OnEnable_m1FC65FDDB4482827D6111686FA435CDCBA163CC1,
	PixelPerfectCamera_OnDisable_m3F6D16F4423CD5622C68EA0138A37E3947D120A9,
	PixelPerfectCamera_OnBeforeSerialize_mED54D4AD64682B1314D338134ECF4790E0062546,
	PixelPerfectCamera_OnAfterDeserialize_m4C38B4CC1F67277C234831D0F0F6608DBD8F818B,
	PixelPerfectCamera__ctor_mCF6FB2072357E5CB9F7AA1EB4E09E5BA84BC54C7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PixelPerfectCameraInternal__ctor_m6BC5985512637F4B2AAD903E5D411B954CB8E795,
	PixelPerfectCameraInternal_OnBeforeSerialize_m4AE0DDB47BB97880367D494025A2CD9B0EDCCB45,
	PixelPerfectCameraInternal_OnAfterDeserialize_mEA652F30B205113EED7848CE7948EB8F7709ED3B,
	PixelPerfectCameraInternal_CalculateCameraProperties_mB201DE82608102113237D5509D085C7ED74BB9FE,
	PixelPerfectCameraInternal_CalculateFinalBlitPixelRect_mDBD399AEAA750ACC3B03C21110E361758DBC0C82,
	PixelPerfectCameraInternal_CorrectCinemachineOrthoSize_m885222B60F8A525214DE5D945448F245E8C9A4D4,
	Renderer2D_get_createColorTexture_m24FE1C199854F11A89066AC3E5BB58A00FD53BDB,
	Renderer2D_get_createDepthTexture_mDBD88B4419A0E76C156E6401C90FA47A3415684C,
	Renderer2D_get_colorGradingLutPass_m6A765DDECE48E4861DA23D8ACCE592585E419CF0,
	Renderer2D_get_postProcessPass_mECF6708B2BBA3FCC65FC760A548F852E280FE1F1,
	Renderer2D_get_finalPostProcessPass_m65C46BE4C556A8C15728944D1791A62197AEC04D,
	Renderer2D_get_afterPostProcessColorHandle_m9DA17585615C88AF653F0CED7EF417865AB89BBD,
	Renderer2D_get_colorGradingLutHandle_m26F4A7E5FDFC6A4E1A54B006EF97C60CBE0157E8,
	Renderer2D_SupportedCameraStackingTypes_mD8EE833AF318A1A0D7416DD70E0BB95BE1F80A4C,
	Renderer2D__ctor_m4B5E6E72241B763D6C6B0BB52F0336E4BC90B3F9,
	Renderer2D_Dispose_m641D39C52711232ED5E4FFC8D6D5F00D2302E62D,
	Renderer2D_ReleaseRenderTargets_m2FB434B00ED7FF63A3BDDED2DA4E174AF705B48E,
	Renderer2D_GetRenderer2DData_m1C19D051822EC98D12A95431E896A4153EA7CE69,
	Renderer2D_GetRenderPassInputs_mA879518FAE9BDAF8E01C4D199BC4A11FEC0F1D4D,
	Renderer2D_CreateRenderTextures_m1FF4B66860212106DFA96CBC0AF2E1B3F23A9A3C,
	Renderer2D_Setup_m44EA4521E611DCDB7DA1EE926433974EE28FE332,
	Renderer2D_SetupCullingParameters_m22BF0D637F9B0F6300A47F3E68FB3192C71F1556,
	Renderer2D_SwapColorBuffer_m4B9C2623930435CC20B25662EDAA596D4306332B,
	Renderer2D_GetCameraColorFrontBuffer_mF3BF31412BDA85A25E6D8C70F1D9F6434655E036,
	Renderer2D_GetCameraColorBackBuffer_m262FA1E93C4FE49599975A9C9022EE16F03ACF0C,
	Renderer2D_EnableSwapBufferMSAA_m85629DFFFE0A76ECACA671DD571F6B6D06CFD755,
	Renderer2D_IsGLESDevice_m784A2B7E1F93D4D6FEE13B6BAD70A61B058C55CE,
	Renderer2D_get_supportsNativeRenderPassRendergraphCompiler_mE21C328C01DC63AE638EEF00D37E682DAC06167F,
	Renderer2D_get_currentRenderGraphCameraColorHandle_mE02065704413CACA7EA6B4ED9230038B1C10ECF3,
	Renderer2D_get_nextRenderGraphCameraColorHandle_m4624775CC789302B64F7A060EF79EE0A6EC20912,
	Renderer2D_IsPixelPerfectCameraEnabled_m9A48A3502D9BC27EFF7DCBA574F82C574F1EBA38,
	Renderer2D_GetImportResourceSummary_mB0D0D64AF8D8E61E918D4D72D5289DD47E59B94E,
	Renderer2D_InitializeLayerBatches_m2425AA5DC7B42EEF042C9365D5A88985FE953ECA,
	Renderer2D_CreateResources_mC3BA123FDDA55BB098C002571C413084BAED9E50,
	Renderer2D_CreateCameraNormalsTextures_mEF0C77F0CCE1542066C1C1A9984FBC475D5A2918,
	Renderer2D_CreateLightTextures_mA3E16F64DB53CA3E6D927EA861EE35C7FBDC09E7,
	Renderer2D_CreateShadowTextures_m025418376AE331C0F71E4CD1F08491E17EA10C03,
	Renderer2D_CreateCameraSortingLayerTexture_m0C97B5CFA263C6DFE47DA968730F9A4430C3A44F,
	Renderer2D_RequiresDepthCopyPass_mB1261174566202F51233B0047886918B720FCA2E,
	Renderer2D_CreateCameraDepthCopyTexture_m48DF1D159858D7600733C9B3A9D3CDB87722E5A1,
	Renderer2D_OnBeginRenderGraphFrame_m4701B5EE01040ED936C27AE5CDA5CDA621D95E65,
	Renderer2D_RecordCustomRenderGraphPasses_m431274D940FA9D1391FD61D3030CAE1890D0B4EA,
	Renderer2D_OnRecordRenderGraph_m0ED9BBECB2A1205AD162D01FB6D45CA02F0D5E5E,
	Renderer2D_OnEndRenderGraphFrame_m47825DA20D08C77F60A4426C3AD7AE66AB618DE2,
	Renderer2D_OnFinishRenderGraphRendering_m769EEDDD73EC5D2B237D64B67E871002F4860C2F,
	Renderer2D_OnBeforeRendering_mD99EC5307BC1C7A2A40D46E12928DF68B8BA2283,
	Renderer2D_OnMainRendering_m7A40EC178991304026B01021D8BE8BA70706CAB3,
	Renderer2D_OnAfterRendering_m3B3A0F4839EE95EA2D7FDB23F10BA5ECC09E5B6C,
	Renderer2D_CleanupRenderGraphResources_m8686E2A1F660754FF1E0B3E59B0C6E27F7B07966,
	Renderer2D__cctor_m76CE69DEF4C4416F17613FE6D966E690D417D5A9,
	U3CU3Ec__cctor_mD716CFE365EB51A827E6334430023CF8006184E6,
	U3CU3Ec__ctor_m60886C74A7CC3A4EB396318D6A9497A487C54EEF,
	U3CU3Ec_U3CSetupU3Eb__44_1_m17013F6E689B41B9583F092CC46370358578D4E2,
	U3CU3Ec_U3CSetupU3Eb__44_0_mDE87966701F599B6212B00EC85A1881E638BDB62,
	U3CU3Ec_U3COnAfterRenderingU3Eb__88_0_mA4D0BBA40519006CB5CE2BE44E6E18CD55737DCE,
	Renderer2DData_get_hdrEmulationScale_m0A25B71CBF5B1482C129A0085DB78544EE7388BE,
	Renderer2DData_get_lightRenderTextureScale_m6FD4326249B00A168A36C2B314D4C035FEE7CB8F,
	Renderer2DData_get_lightBlendStyles_m5564525FC5E476F8B8B709FEE780729C0812D6A4,
	Renderer2DData_get_useDepthStencilBuffer_m44AA62569D6155425AA7F1B669CB5AEFCC690BC4,
	Renderer2DData_get_postProcessData_m4575F93D637FE88BDA13DFB57241E889A2E74D42,
	Renderer2DData_set_postProcessData_mC663B5A3BD7313211AEE2251D72028087F63F516,
	Renderer2DData_get_transparencySortMode_m5ADF4D78A34280993A7390E378593E8528ADE164,
	Renderer2DData_get_transparencySortAxis_mD5ACC931C88DE8FBBC411F5BB16CD913CB3FAC6A,
	Renderer2DData_get_lightRenderTextureMemoryBudget_mFC1D055D5C96B4B25C66F853794141BAE440525C,
	Renderer2DData_get_shadowRenderTextureMemoryBudget_m77314E3103EF456B4F749A2398140395D626143B,
	Renderer2DData_get_useCameraSortingLayerTexture_mACAC0745A72D88C39DB1C9C0C2AC65B00EBBF574,
	Renderer2DData_get_cameraSortingLayerTextureBound_m5947FD913CE5F264AC4353A189AAD19CBDED7706,
	Renderer2DData_get_cameraSortingLayerDownsamplingMethod_m23F835318EF3DD5972097BAF42FFAC63B99B2AB4,
	Renderer2DData_get_layerMask_m7B31D1269152C8E272EF718A9AF897B31FFCB14F,
	Renderer2DData_Create_m2F6E03C7561BA30ECF1186BA5EE691889AF67D58,
	Renderer2DData_Dispose_m80832EF5A9C286413D89236184E101EA44A74EBF,
	Renderer2DData_OnEnable_m80F93198CEBE0B32B0BF020C36ED1F9EDEBE7841,
	Renderer2DData_get_lightMaterials_mC074C05B230126E1DFB606C6FAFFBAEC99E3EADC,
	Renderer2DData_get_spriteSelfShadowMaterial_m7436BA5A575F6DF3AA3732E7C7C44ABFD63CDD20,
	Renderer2DData_set_spriteSelfShadowMaterial_mB2B00B1F4E2DEE264AA79B0C2F22BA329095AECD,
	Renderer2DData_get_spriteUnshadowMaterial_m4415A748869AAA64EAC66ED6E1588639DEC52F2F,
	Renderer2DData_set_spriteUnshadowMaterial_mD8C9E9C343D025813148B3379D8F1828A423EE2E,
	Renderer2DData_get_geometrySelfShadowMaterial_mF219386663094A2C9BDC3B4E69EF8112827DC86A,
	Renderer2DData_set_geometrySelfShadowMaterial_mBCF9BB3E6AD7C14085F5B23ED646430D9CE7B590,
	Renderer2DData_get_geometryUnshadowMaterial_mCBF3F176611E39E5D20A4917440AE0FC5B54E950,
	Renderer2DData_set_geometryUnshadowMaterial_m533C4F9582B0C6E1270E153C9311D139858EC233,
	Renderer2DData_get_projectedShadowMaterial_m2B41EE220BB6CF8F6AFDB4D6B9D5CEE0FB6A1532,
	Renderer2DData_set_projectedShadowMaterial_m92F2FF910697148A41C6AEDC5600838FF0F97E79,
	Renderer2DData_get_projectedUnshadowMaterial_m850DE00788FCD51337CA3A1B3DF818CED804A93B,
	Renderer2DData_set_projectedUnshadowMaterial_m6A37BE29BC05D7A3FBFC915F43D5E4BCEB43FF85,
	Renderer2DData_get_lightCullResult_mEF0BB47B5B239E5736AEC45418B0BF9A41B4EE8F,
	Renderer2DData_set_lightCullResult_m62468050A0621073A84F29ACF962BB484BD518D3,
	Renderer2DData__ctor_mA659713BB22B6FE0ACDD49CC1E0E1516802CC8D4,
	ScriptableRenderPass2DExtension_GetInjectionPoint2D_mFA788D173427FAF328C861DF434DEAA3728A8491,
	CopyCameraSortingLayerPass__ctor_mED6EC102125AF1570562831609D156696EE93C88,
	CopyCameraSortingLayerPass_Execute_mC8C162963E5AF8105475B43617E22A88778D417D,
	CopyCameraSortingLayerPass_ConfigureDescriptor_m0A46E598DD77A6B72F84D00D20158491CEFC7778,
	CopyCameraSortingLayerPass_Execute_m492B9B89C02CA4E79782A64639F4A0EA5CACADFC,
	CopyCameraSortingLayerPass_Render_m82672971BA4C765CEF93CE291D64A1955586E165,
	CopyCameraSortingLayerPass__cctor_m72F21A0CBA1B505918B4BB72E1690B1480BFE5D4,
	PassData__ctor_mD997610E5107FED77DD16C89D4A51804D2EFCCB7,
	U3CU3Ec__cctor_mC7C67DF491D96912E544F500FCFA7EF2BD9FD76B,
	U3CU3Ec__ctor_m2FB7104470538C2E476C62573495F75ED05879C3,
	U3CU3Ec_U3CRenderU3Eb__11_0_m96456D6CF6B1AEF48757F051518643B982B5A62C,
	DrawLight2DPass_Setup_m35EEC25BE39ABAA6040661E992438F7F1BE85CB1,
	DrawLight2DPass_TryGetShadowIndex_mDA17DB1FE068F6A32FDB03A0CEA85766C14AF3EC,
	DrawLight2DPass_Execute_mEC6764D1C34A2A74A5F276B0096F8FEA1DB5E0F8,
	DrawLight2DPass_Execute_m4A4A91917E220F021A9ECBC6CF4D135C7B4C8427,
	DrawLight2DPass_Render_m7BB5D9A611AF0A77DB6D5D774024E0F38E3CD663,
	DrawLight2DPass__ctor_m3D3BDF9772395119C09B44F99CF796387071FFE9,
	DrawLight2DPass__cctor_m6CBDF5C5CF7B0C118075FD7F71FFD251AFDF2867,
	PassData__ctor_m7F72C6A60504133FE239B30B21FC6AC5DB9E2D09,
	U3CU3Ec__cctor_mC8386A9B25591BCFD4A537D26E74B1D2DA274E1B,
	U3CU3Ec__ctor_mE0FF665208C32A7E114E76FADE1D6CA53D08CFCC,
	U3CU3Ec_U3CRenderU3Eb__14_0_m1EDA35DD60317044CDC944DDE4123DD07B43B2F9,
	DrawNormal2DPass_Execute_m30D5D86F00E92F89A86E6BB6CA3291B99995256B,
	DrawNormal2DPass_Execute_m3148A1BAD04EE7DDBABD030B95EE0FCC2C87035F,
	DrawNormal2DPass_Render_m1E4BAF20829CA5BD44EB7DFE6B3000C1F929C273,
	DrawNormal2DPass__ctor_m568EA66D37D33BAADB5A1E96F063EEE8638B4BA2,
	DrawNormal2DPass__cctor_mE7320BB314DEE83B891BB6ADDB8688ED17B57B3D,
	PassData__ctor_mE158AEFC4E7C134772877F81EE8FD8E31BA49CCE,
	U3CU3Ec__cctor_mFF47CEBF6A8510D1D4CFB0EC6DA07464867CDDD7,
	U3CU3Ec__ctor_mCE24387C5806B003DDB03FA84853DE9086859410,
	U3CU3Ec_U3CRenderU3Eb__6_0_mD746022E4248A894CB87047D3C2FA2DA7BE758A0,
	DrawRenderer2DPass_Execute_m7130CAC027A9827B9E62A169E20DC4FA5557C3D1,
	DrawRenderer2DPass_Execute_mB61335AC51212D54B27DF11342A4135ADC5B82B3,
	DrawRenderer2DPass_Render_mC61B669FCFC9F83B867F99ECDD102FACC58A32A2,
	DrawRenderer2DPass_SetGlobalLightTextures_m3AECF91E79FD41B7E78D76A7E6C2BE2CC3CAABEC,
	DrawRenderer2DPass__ctor_m4DB878CD10F2BEA503F17E314D9C4BE29D63C0A4,
	DrawRenderer2DPass__cctor_m7B09578064656B3AD405EF2F1067AACCA7D33269,
	SetGlobalPassData__ctor_mC85DCEFD6F707694ED86F3A365B11A2CD20AAEB1,
	PassData__ctor_m96914E1FDB7E70ECAD1B5C74A778580091038A96,
	U3CU3Ec__cctor_m137C88E3FE8060FDF72A592944BB5AA039CAA4D5,
	U3CU3Ec__ctor_m93AF8CAF19BBC9BC37F655C973113459C437858C,
	U3CU3Ec_U3CRenderU3Eb__13_0_mFB43EF72AF3B344103B8EE74FA432CF18BB0F9D0,
	U3CU3Ec_U3CRenderU3Eb__13_1_m70B9E0579218A27A1EBB23C795048DDC4EE2A825,
	DrawShadow2DPass_Execute_m1D4260EEC7BE1CED5D0289E73052EC64B11FE488,
	DrawShadow2DPass_ExecuteShadowPass_m689668EF493100A7BB9A76E3B77DD7B94BF7DA00,
	DrawShadow2DPass_Render_mE51A12E83BD4E9E019DA5BD74233BCC00C970479,
	DrawShadow2DPass__ctor_m4245DF8F5DD32496C01CEFAFEE195424AA9832E4,
	DrawShadow2DPass__cctor_mFF79C7CBDF449C8B0756951D29911BCE2F5DD448,
	PassData__ctor_m10D63718FB19937FE3B17DA4F1D6148CBB9F120C,
	U3CU3Ec__cctor_mB272CDF2EAE141C160D8D01FB864892F8454A964,
	U3CU3Ec__ctor_m2EFBE1E3290F51983E5A977E3972962E4A0A40E3,
	U3CU3Ec_U3CRenderU3Eb__7_0_m4784C8A334352870F304502BCA9D0B360595F399,
	GlobalPropertiesPass_Setup_m7F161605AFD3E836610360B508CFF77624115DA1,
	GlobalPropertiesPass__ctor_m97AF39BDE3A7DCC33F556F349220AE5D047A9DAF,
	GlobalPropertiesPass__cctor_mBACC65593AAAC2FFBA59673D5D3B60320B862DBA,
	PassData__ctor_m89CA203AE517A3FF347230DA13B7BFBBD86C0AEA,
	U3CU3Ec__cctor_m316252F7CB8BEA4442C36076F828FAD0702073D9,
	U3CU3Ec__ctor_m331F10886D9DB150D337B9B079FD596018BF274C,
	U3CU3Ec_U3CSetupU3Eb__3_0_mE33D763E49C2E35A2727CBF40B9E9DA10973A649,
	UpscalePass__ctor_m04FF134CB5F0BF0772080565721BE2519C4FFE14,
	UpscalePass_Setup_mFCF16DCFA7283D9C22657A76D211F7545FB834DB,
	UpscalePass_Dispose_m4CAB01CBC95FDD232CF2862D7C9D887B0646BF1E,
	UpscalePass_Execute_m99194CAEE26DE24830EF13FF07ED83E0FD922CFE,
	UpscalePass_ExecutePass_m77ED665004E72B3CBB132B1DEA45D2E53EC0C83E,
	UpscalePass_Render_mF4A57DB5B46AB8D0439BDDC4517387EC40B4C0A9,
	UpscalePass__cctor_mEF5427392FE235DE309B5104883329D903AEAE66,
	PassData__ctor_m65E56911530CE69F154AF3A98713DB054F3895DF,
	U3CU3Ec__cctor_m3E912DFAC5EA20E0A91F421E4D2C6A607B7E71F0,
	U3CU3Ec__ctor_m20846050203F7EFF4F82375F475ED9182BB1D874,
	U3CU3Ec_U3CRenderU3Eb__12_0_mDD8F33683D7EA74046F03249C7015A1D202E4F36,
	CompositeShadowCaster2D_OnEnable_m1D1BA15633A4AA5A677015692BB23121DB8D79FB,
	CompositeShadowCaster2D_OnDisable_m9B5D0C8C98B8805DFA560CA150C645FE6D9DD336,
	CompositeShadowCaster2D__ctor_mB93048F65ADF0C49CFB021DDE81E14FE5C2FA807,
	ShadowCaster2D_get_edgeProcessing_m60499748789A90315F9B94656F8C68D937E35D97,
	ShadowCaster2D_set_edgeProcessing_m1451C019791C81C5885B87D098C8485F5EA598AF,
	ShadowCaster2D_get_mesh_m487088118BA14D1E81CACC9820703372075A8CD0,
	ShadowCaster2D_get_boundingSphere_mB4512E20FC461F45DE40A37D118DA6A5395D8B18,
	ShadowCaster2D_get_trimEdge_m3E133B9631F634C782E323204196FF536ABBD51F,
	ShadowCaster2D_set_trimEdge_m5E92365536558F94AC99A6B17D30ACB096F94A43,
	ShadowCaster2D_get_alphaCutoff_mFAF68B32010B7ACF0D2C0E084D60EBA59BEC7834,
	ShadowCaster2D_set_alphaCutoff_mC1C551B7C6F9AC41479F32A182A17C983531D337,
	ShadowCaster2D_get_shapePath_mC13A25686164C42EAE3DDB81F8AE0563BE8F4AA9,
	ShadowCaster2D_get_shapePathHash_mE1E759DC596D4301F247BA06901419262CEB3187,
	ShadowCaster2D_set_shapePathHash_m38728C49239E936F5EBA8E36596E516D6FC5E102,
	ShadowCaster2D_get_shadowCastingSource_mE28EEAF8CC7C2E234C6CDE2C9B3688189D0F1F7E,
	ShadowCaster2D_set_shadowCastingSource_m560DE5F59C4008CD2CDC8637523B402C7254A291,
	ShadowCaster2D_get_shadowShape2DComponent_mE86BE25189D62590E996611DB23634FD253E7901,
	ShadowCaster2D_set_shadowShape2DComponent_m4AA23FC17D0FB2BDB72171CF158C8F4B2F05AAB4,
	ShadowCaster2D_get_shadowShape2DProvider_mAF6299D6D50E4B1FC94464BDA5667FBAB2552525,
	ShadowCaster2D_set_shadowShape2DProvider_mDA38AAC7B3E1DEE2DE56CB8220C3A189AB479283,
	ShadowCaster2D_get_spriteMaterialCount_m203A04545F355886EE50BFD034BEDEF7377FF021,
	ShadowCaster2D_CacheValues_mCD794C74DB2595598D06DFA3C00A69981E839074,
	ShadowCaster2D_set_castingOption_m75E57418A4B3CC61D5C81A5F825A484BA9D7E464,
	ShadowCaster2D_get_castingOption_m11F5F86721E7CABA77996CDFCC3741F3C9A8F9DA,
	ShadowCaster2D_set_useRendererSilhouette_m04135E639BADCAB86C8BAFC23F2936AB7BF57A00,
	ShadowCaster2D_get_useRendererSilhouette_mB7B8DCFC5478D297D696ADF346006E3112BB9A0E,
	ShadowCaster2D_set_selfShadows_m8D8A2A79F306B265284AB6212A631AC4474437B8,
	ShadowCaster2D_get_selfShadows_m40E1BF40EEC25DF7538D6E6CACC6FFF86A113475,
	ShadowCaster2D_set_castsShadows_m6F53705A974B0EE373F6174D58370E8775607A3C,
	ShadowCaster2D_get_castsShadows_m4D2E2254C8C21E8EACF99F26ECCB10E33EBAC234,
	ShadowCaster2D_SetDefaultSortingLayers_m50FE472B68EC21CEAD03A6EF3E033993AF6153C3,
	ShadowCaster2D_IsLit_m84045950AA9DE345C719C1A1F0AA4A0944FF71B7,
	ShadowCaster2D_IsShadowedLayer_mB707B6F9E7F3AE915122E6B6D9684435668FDFF1,
	ShadowCaster2D_SetShadowShape_m062B2E3C3CFFE7A81018B7FD2FA44755FD6C320D,
	ShadowCaster2D_Awake_m52BAB618C714732348D03CDAA412B3A079CDE4F1,
	ShadowCaster2D_OnEnable_mB25C4DCE54980CCD65049F1FE14D134113A4B0B4,
	ShadowCaster2D_OnDisable_mD438090275DC53244CC002CE2F453BC6E4952A5E,
	ShadowCaster2D_Update_mBBDA5909706AC0A79C88E02099242B149C4C60DC,
	ShadowCaster2D_OnBeforeSerialize_m8B240D66D9C8555CE7ABCE3BFD6151B74CE144B0,
	ShadowCaster2D_OnAfterDeserialize_m0404319D0C2D7B2DF040AB6BBF04BD1CDF67788D,
	ShadowCaster2D__ctor_m50E27C1D91323382AFEEBD79F9C6179E2616AF0E,
	ShadowCasterGroup2D_CacheValues_mB461294C285A98D76A2F889CF19CC58FCC930E14,
	ShadowCasterGroup2D_GetShadowCasters_mDCD74C94A151D61CAC8F53DE3750AD40CBFE51BA,
	ShadowCasterGroup2D_GetShadowGroup_mB6700A5E166ED6122ED38D71832EF82A526C3A99,
	ShadowCasterGroup2D_RegisterShadowCaster2D_mC26BBCA52C510C4877AA853672C51D42C153B4A4,
	ShadowCasterGroup2D_UnregisterShadowCaster2D_m3BEB2488CA7FBCC8769C4A244D3AA22DD277E5B2,
	ShadowCasterGroup2D__ctor_m1E3787BD91B5E1712215CB902D57FC8173CB5584,
	ShadowCasterGroup2DManager_get_shadowCasterGroups_mD2D34E88683CFFBE42142BC775F83689D96ADCC7,
	ShadowCasterGroup2DManager_CacheValues_m7FB246220BE8D9A2CE5116B7A063C7418AE3F121,
	ShadowCasterGroup2DManager_AddShadowCasterGroupToList_mC64E3E9C4D1A719BC850B7C227A55B4FDC58E1C2,
	ShadowCasterGroup2DManager_RemoveShadowCasterGroupFromList_mBA201346FD60EB8374E87E48ED91E91DF6252081,
	ShadowCasterGroup2DManager_FindTopMostCompositeShadowCaster_mD6B6696DF6EC83EEF4B1759E87BF77BEB2275189,
	ShadowCasterGroup2DManager_GetRendereringPriority_m496B18814E92635A028C7FD89040DFB29A4651EB,
	ShadowCasterGroup2DManager_AddToShadowCasterGroup_m22CAC0B7B1DADE0CFF9BC08148065F671F911EEF,
	ShadowCasterGroup2DManager_RemoveFromShadowCasterGroup_m61E355382D0C4778303CF692F69C7D260E194F0F,
	ShadowCasterGroup2DManager_AddGroup_m942049A2176C9FA18311CF6FF2574F905EBE43B8,
	ShadowCasterGroup2DManager_RemoveGroup_m976D7E3D5A893B9E3899F0B5ABD52E42509E3031,
	ShadowCasterGroup2DManager__ctor_m774B2F78A3B18B34CB141CF9CCB560A0C1D08F7B,
	EdgeDictionary_GetOutsideEdges_m02DF9E4693CDEA1D5D5FB07DF069F31C0966CFBC,
	EdgeDictionary__cctor_m38F4BB0B42EE6F67D3588C0E84AE767FA6FEB6F6,
	EdgeComparer_Equals_mFD5D9B24B13605CF165B53219FABADF1EFB46116,
	EdgeComparer_GetHashCode_mE851ADE38E1762FFFCCA1581750ACD0EB00CC89C,
	EdgeComparer__ctor_m150453B6D728B16CE072AE418D1C6D3665E298A1,
	NULL,
	ShadowShape2DProvider_Collider2D_CompareApproximately_m594778E96095AA90E72363AF8C524DA1AC2DA7B2,
	ShadowShape2DProvider_Collider2D_TransformBounds2D_mC80021EAA7760A5E347141DAB886048A800FFF0D,
	ShadowShape2DProvider_Collider2D_ClearShapes_mBF8C6993482152D7657BABA7743C7E575CCF974B,
	ShadowShape2DProvider_Collider2D_CalculateShadows_m9BE3A6BCF84B7EEB24F6F089D858F99A7D7CCC7B,
	ShadowShape2DProvider_Collider2D_IsShapeSource_mCD042B9A4628E2193544D283906DE306A3E22CE9,
	ShadowShape2DProvider_Collider2D_OnPersistantDataCreated_mD1B6820FD5F8F9665353E23E7DCDDAAB71A81704,
	ShadowShape2DProvider_Collider2D_OnBeforeRender_m9444BCE0AC5C43D059B7D8AD5362F5FCF8DF0000,
	ShadowShape2DProvider_Collider2D__ctor_m5C93C2D9491011944B0022C4B807AE4C64DF1958,
	MinMaxBounds_Intersects_mB283C28F81335E978846E325A9482892D24644C1,
	MinMaxBounds__ctor_m3D75999E612D51C1A02E2768D6291255386D9B40,
	ShadowShape2DProvider_SpriteRenderer_SetFullRectShapeData_m417FBA4F9C083FEAD0DA36936EFFF81AA93F5894,
	ShadowShape2DProvider_SpriteRenderer_SetPersistantShapeData_mF3C6353F1D76219CB302BC59BDC3217D3C039001,
	ShadowShape2DProvider_SpriteRenderer_TryToSetPersistantShapeData_m50BF40899EDD2F2316C02E4E7707F17E744148C3,
	ShadowShape2DProvider_SpriteRenderer_UpdatePersistantShapeData_m38DA83F754494DDD0890D02CCD10EAEE81E00F8C,
	ShadowShape2DProvider_SpriteRenderer_Priority_mA5D69BEB07B8061F1A552B191052501A3EED741F,
	ShadowShape2DProvider_SpriteRenderer_IsShapeSource_mB32B096484EBD2AAAD1FAF3EA19A4022E19620CD,
	ShadowShape2DProvider_SpriteRenderer_OnPersistantDataCreated_mA3303B4A89577E0206656A7817803E7E14D4110C,
	ShadowShape2DProvider_SpriteRenderer_OnBeforeRender_mE11597B28DD27666F6A8B6191BF0310027679010,
	ShadowShape2DProvider_SpriteRenderer_Enabled_mA6A4E5EF5C2641C021F3DEBCB097510CE481A4D4,
	ShadowShape2DProvider_SpriteRenderer_Disabled_m84EA2BBDEAC6145161710351C2FA35D267A2AAAB,
	ShadowShape2DProvider_SpriteRenderer__ctor_m201B9882887CFB0BF7CD787009A9BA63B30901D7,
	ShadowShape2DProvider_SpriteShape_UpdateShadows_m6F863810C0A44FB0408F2EBEE495E888B397D122,
	ShadowShape2DProvider_SpriteShape_Priority_m061733966F82A556E8CE1716983A40B43280D8E3,
	ShadowShape2DProvider_SpriteShape_Enabled_m9F56522618F243BD96B888200416777338A6AC5E,
	ShadowShape2DProvider_SpriteShape_Disabled_m7EA95D9EB30FD3D22DBAFE00348126BCBF1BFE5E,
	ShadowShape2DProvider_SpriteShape_IsShapeSource_mFAE1A267E1436FAB9F76AC6211C238085661191D,
	ShadowShape2DProvider_SpriteShape_OnPersistantDataCreated_mD10C4A9BABD45192D2F6D88D3A7DE6B591934822,
	ShadowShape2DProvider_SpriteShape_OnBeforeRender_m7C9117F3DC6AEA8761C196298986F4309605C1D8,
	ShadowShape2DProvider_SpriteShape__ctor_m66328576B2D1A01EE98A73B1ADE10DEA6092445B,
	ShadowShape2DProvider_SpriteSkin_TryToSetPersistantShapeData_mAB34AA58BEF387CFF5638ED53E8244220AC8F2E3,
	ShadowShape2DProvider_SpriteSkin_UpdatePersistantShapeData_mA55D950808853EC547B291B5DAC3F231A09D4692,
	ShadowShape2DProvider_SpriteSkin_Priority_m7C203AF2B70F3206D954EF0805EC27DFD6AFDAC4,
	ShadowShape2DProvider_SpriteSkin_IsShapeSource_mDB470F88C1B880A1A647B38D8834AD18C8698DBF,
	ShadowShape2DProvider_SpriteSkin_OnPersistantDataCreated_mBD9B40CED8AD3E6015245F18BFCFDB856EFA1768,
	ShadowShape2DProvider_SpriteSkin_OnBeforeRender_m017E4B5A32CF3565F56D89C7D489FE221AC39FA0,
	ShadowShape2DProvider_SpriteSkin__ctor_m21A466B235BD996F4687342558DEE0334196FB8E,
	ShadowEdge__ctor_m92189DE1A34AA7F4D481C640A7415EE57584C537,
	ShadowEdge_Reverse_m267B65507DE000FD5359CAEB003472B65D26A890,
	ShadowMesh2D_get_mesh_m77225D909CD28099B6E8E109DC00111881EC1698,
	ShadowMesh2D_get_boundingSphere_m16F11181C4232CA8715D1DF55B86BDC234B11F99,
	ShadowMesh2D_get_edgeProcessing_m1FC6B8E5D138E26E63CF74FF085106FCEDDDB9CA,
	ShadowMesh2D_set_edgeProcessing_m99A5E225F5461F6FAA2E9A9BF1C1ED0E3D2B57E1,
	ShadowMesh2D_get_trimEdge_m1DD051939E61F267AF4D1E02272759AC81146C44,
	ShadowMesh2D_set_trimEdge_m279F62FA0EE7B5C55916FE2AA46B8BB26AD85B45,
	ShadowMesh2D_DuplicateShadowMesh_m152BE15B41DA6DCCDC9353D95DD92DD95CE12E12,
	ShadowMesh2D_CopyFrom_m386F95D086D78F1ED08DD19C1AAB994695D24FC9,
	ShadowMesh2D_AddCircle_m****************************************,
	ShadowMesh2D_AddCapsuleCap_m6E27D17295F0AF3A2AFF3C8D0ABD384FDFA5E34E,
	ShadowMesh2D_AddCapsule_m1FED2B1D4E42BACC30C0CCAB04D6261D67BFB2A7,
	ShadowMesh2D_AddShape_m3DE259B1B291B5C7D80DE9DFEF410561455FAABC,
	ShadowMesh2D_SetShape_m9B0FA8637CC785DA26F2BEBD3DCBB444A4FC76C7,
	ShadowMesh2D_AreDegenerateVertices_m0AFEF1902699BE42F46D1E62901106CD623AD3D1,
	ShadowMesh2D_SetShape_mB28DAC3876C7AA3BAED7AAADA7FEA355277A6672,
	ShadowMesh2D_SetShapeWithLines_m880CA371C3BC648E4159B948209AAE9131139DB4,
	ShadowMesh2D_SetFlip_m549820939EF0D42BDD7A8417153B4C0376C2AB71,
	ShadowMesh2D_GetFlip_mA3ADC6C8A69E861DA5842B68039E51CD125499E5,
	ShadowMesh2D_SetDefaultTrim_m8C41DFD50DB8D3688D2139B7D87BFBFF5FA94A8C,
	ShadowMesh2D_UpdateBoundingSphere_m5839E98BBE0945D13C3681D3A813496C774C0126,
	ShadowMesh2D__ctor_mC428FDAD0852DF646213FC84F829E4538DD3E60C,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ShadowShape2D__ctor_mC5F751194FE6DACECC36384A29B80557AB872454,
	ShadowShape2DProvider_ProviderName_m4C762B5B61880D7EA151C7983909C309165D1A7A,
	ShadowShape2DProvider_Priority_mDAD57677B632CE66D162F0115DB67D60835CE90F,
	ShadowShape2DProvider_Enabled_mADA55FE15163605B4CA90D7C44712071D99294CB,
	ShadowShape2DProvider_Disabled_m6BCBDEE73B4A2A43D978D7F0DD2D1877301CF282,
	NULL,
	ShadowShape2DProvider_OnPersistantDataCreated_m6904CDCD6B83E6A7ACB0D743B14F8E2A2AAC8A65,
	ShadowShape2DProvider_OnBeforeRender_mC98BADB92A71863CB07ED607AA84A36E673B341F,
	ShadowShape2DProvider__ctor_m4CAFF1D4FC5B03E88FF979403B48CF506F3A7F21,
	ShadowUtility_GetNextShapeStart_m968CBCD3C6880218101543B0119593D4250875C9,
	ShadowUtility_CalculateProjectionInfo_m952E6AB7BDD578374B99ED119819090E81F81067,
	ShadowUtility_CalculateVertices_m1087DC5C13D021D10BB58FA9EAD2D99416D2974E,
	ShadowUtility_CalculateTriangles_m9D055B9D0E3B41D00713797D5074A7BD42A4D216,
	ShadowUtility_CalculateLocalBounds_mCD7AB254F81081483F03BEF504C2B2918777D32D,
	ShadowUtility_GenerateInteriorMesh_mBF198238681A38CB0ECED75E112BBEAE45F2B362,
	ShadowUtility_GenerateShadowMesh_m0F248BD62ED797CABF037EECC47B99EB0A70675C,
	ShadowUtility_CalculateEdgesFromLines_m95B59AC1CE430C8952290FAC5C4DFEAD0EF53008,
	ShadowUtility_GetVertexReferenceStats_m1CB3AC1703058D94FF1211F9BA9430600C57B106,
	ShadowUtility_IsTriangleReversed_mB4A626328D15027B593D407B78CBF1ADA82CC6BF,
	ShadowUtility_CalculateEdgesFromTriangles_m1F08E1F50422891DCB5A4A70D57ABD9F1354CB63,
	ShadowUtility_ReverseWindingOrder_m9F8C8065FA5294F22C51C1552E34416B3AED38DE,
	ShadowUtility_GetClosedPathCount_m53BF1D0206FF7ECEA8CED8661B9EB22D0D4596F4,
	ShadowUtility_GetPathInfo_m1DFC3F2D9122A5B75C81913410C9F7848C4252B4,
	ShadowUtility_ClipEdges_m104D8B0F86040657D28698C6FEDD40210FC710FB,
	ShadowUtility__ctor_mD10F21907FF856828CDC4738FF5B9F077F6834B1,
	ShadowUtility__cctor_mAA00C5F2C0393822FE9348110491FADD67B4A46F,
	ShadowUtility_CalculateProjectionInfoU24BurstManaged_mE98E80DEAEC78B4B97D19EEC66155059FA1FBB5C,
	ShadowUtility_CalculateVerticesU24BurstManaged_m036FA3D23DA0DB1D9E2D6EA66D88847A9CF3351D,
	ShadowUtility_CalculateTrianglesU24BurstManaged_m16F548D18B5A2B0F8FC2C1935EFE67830B4D78D4,
	ShadowUtility_CalculateLocalBoundsU24BurstManaged_m1BB0B91C0858FCF73002FCE86500763214C72262,
	ShadowUtility_GenerateInteriorMeshU24BurstManaged_mDBCFF562718DFA354F2C9AEC84D421C4A84FBF1D,
	ShadowUtility_CalculateEdgesFromLinesU24BurstManaged_mE27595FD4F2C77C938E7AE97B9BF2F366A6AC2A3,
	ShadowUtility_GetVertexReferenceStatsU24BurstManaged_m55F620268F290E792364229451024085EFF72216,
	ShadowUtility_CalculateEdgesFromTrianglesU24BurstManaged_m7E4C36F5690123320F892747336A579E0BD67BEC,
	ShadowUtility_ReverseWindingOrderU24BurstManaged_m90722ACCF040FB77E3FBA68CA69E0A97AA8E1BD1,
	ShadowUtility_ClipEdgesU24BurstManaged_mADDE5B936F1D8B62C4E8FEACDB7CABE823FC9C8F,
	ShadowMeshVertex__ctor_m2AA5F11678057369D90EC50FB7617477A9E909C9,
	RemappingInfo_Initialize_mC79F12A1E91E25F931C9F858473E38216719C497,
	CalculateProjectionInfo_00000305U24PostfixBurstDelegate__ctor_m91F31CC6130B02B0F1D7E7CBAC00E1D6B51CF24C,
	CalculateProjectionInfo_00000305U24PostfixBurstDelegate_Invoke_m7DBBFC8E6B4DC2B3F02A24D43E6A61A044BA45B0,
	CalculateProjectionInfo_00000305U24PostfixBurstDelegate_BeginInvoke_m55A0998B57C52AFBD38879EA0751063705A16674,
	CalculateProjectionInfo_00000305U24PostfixBurstDelegate_EndInvoke_m6A135FED02001BCA080C85431D156362EC15CB12,
	CalculateProjectionInfo_00000305U24BurstDirectCall_GetFunctionPointerDiscard_mA24383FF30620961D80DD375F0BA154B8663F77A,
	CalculateProjectionInfo_00000305U24BurstDirectCall_GetFunctionPointer_m74A2CF091EE133861A8CB0A46F6BC1EB67959A21,
	CalculateProjectionInfo_00000305U24BurstDirectCall_Invoke_m3EF35319BD0B3BAC250D45F2D28D5F069E0688FD,
	CalculateVertices_00000306U24PostfixBurstDelegate__ctor_mE1D6D159CB8776F10E8BE374CAAF23D81F088388,
	CalculateVertices_00000306U24PostfixBurstDelegate_Invoke_m91073D96146937BD1B4A2E1A4C9C1C775EA7B7F9,
	CalculateVertices_00000306U24PostfixBurstDelegate_BeginInvoke_m09B68CBB00A010A7086ABAACB7F8B4559C038A0B,
	CalculateVertices_00000306U24PostfixBurstDelegate_EndInvoke_m1E742D9CB01279C16154F44619A9B19340318D64,
	CalculateVertices_00000306U24BurstDirectCall_GetFunctionPointerDiscard_mA50ACE0C6B2E336BE30A03CA5D18953A554340F6,
	CalculateVertices_00000306U24BurstDirectCall_GetFunctionPointer_m12A747D745BE8ABFF7DE572FE4A9AB051D2E144B,
	CalculateVertices_00000306U24BurstDirectCall_Invoke_mB643496F6EAA3506796BB5D51506717ED67412A9,
	CalculateTriangles_00000307U24PostfixBurstDelegate__ctor_m17EF157E80D37E6A406D7AC9FA0E3874054C2E87,
	CalculateTriangles_00000307U24PostfixBurstDelegate_Invoke_mE4CAB4C589B8620F2BA866AA2C09F2E61481AB8F,
	CalculateTriangles_00000307U24PostfixBurstDelegate_BeginInvoke_mF89BE1A16DE4C3CD12C492D34DA254A073E890F3,
	CalculateTriangles_00000307U24PostfixBurstDelegate_EndInvoke_mD14435C2BC9F045B74799A02C90FEDDD4FC74A3D,
	CalculateTriangles_00000307U24BurstDirectCall_GetFunctionPointerDiscard_mC5CC848E357520585C3E84C4E3475982A817B2C7,
	CalculateTriangles_00000307U24BurstDirectCall_GetFunctionPointer_mDC29D38E04C75AD36127CCDF294DF04C08F76E90,
	CalculateTriangles_00000307U24BurstDirectCall_Invoke_m49269F6FABF74FEE776D2B8648A5A8A14129405F,
	CalculateLocalBounds_00000308U24PostfixBurstDelegate__ctor_mC4FE6F7F78234EE82243F72B6B86D6FAB7D0ACAE,
	CalculateLocalBounds_00000308U24PostfixBurstDelegate_Invoke_m2DEF4F5A390ABAFD148769E7144A3FEB9B681141,
	CalculateLocalBounds_00000308U24PostfixBurstDelegate_BeginInvoke_m33C51EAD1F1C94B9AD8396BED3EDAE8B78E684EE,
	CalculateLocalBounds_00000308U24PostfixBurstDelegate_EndInvoke_m83F25BB6A83DD5EF1E501ACB66E16BA0E97F43BC,
	CalculateLocalBounds_00000308U24BurstDirectCall_GetFunctionPointerDiscard_m5B262DEA989827D5DF9A896750E8D2D98364F9B8,
	CalculateLocalBounds_00000308U24BurstDirectCall_GetFunctionPointer_m4AA846C6E282E38E3A19DF69C5AF64E0A1B8DC70,
	CalculateLocalBounds_00000308U24BurstDirectCall_Invoke_m1AA07A8F99F9CFA90FFAEF1B934699CB6029B725,
	GenerateInteriorMesh_00000309U24PostfixBurstDelegate__ctor_mD44EA028A07347F7EE95D699524233B83FFDF298,
	GenerateInteriorMesh_00000309U24PostfixBurstDelegate_Invoke_mD38B0BBEE54CF5493DB5DBBFAA42296B9FF67C78,
	GenerateInteriorMesh_00000309U24PostfixBurstDelegate_BeginInvoke_m2F04A12024365685E4155D3DE167319A7697951A,
	GenerateInteriorMesh_00000309U24PostfixBurstDelegate_EndInvoke_m27B682DC36FAA7F9893FB985C4C14E5521E1B8A2,
	GenerateInteriorMesh_00000309U24BurstDirectCall_GetFunctionPointerDiscard_m0E92F08E25D0D968A75EC97A0565CB5256610F3D,
	GenerateInteriorMesh_00000309U24BurstDirectCall_GetFunctionPointer_m2A0B4B792589567C641DBBCF4FB59BE9FFE3E774,
	GenerateInteriorMesh_00000309U24BurstDirectCall_Invoke_mF4F75E49B795F0C5FB96E7B2571B49AEAB925AEB,
	CalculateEdgesFromLines_0000030BU24PostfixBurstDelegate__ctor_m677FF2FFB6EC8132C3E82B6DC8E9734063498871,
	CalculateEdgesFromLines_0000030BU24PostfixBurstDelegate_Invoke_m17ABC4C54CCEDD82AA356D45C770DFC2C6ABD642,
	CalculateEdgesFromLines_0000030BU24PostfixBurstDelegate_BeginInvoke_mEEC910F2719B2780F970B74634085A471DA53993,
	CalculateEdgesFromLines_0000030BU24PostfixBurstDelegate_EndInvoke_mFEDBF2930A08B90A9AD013E2EE3C5DFB00D9F11B,
	CalculateEdgesFromLines_0000030BU24BurstDirectCall_GetFunctionPointerDiscard_m1A614F8DCF8C9235C4A76C0394D5EE038D08FCF6,
	CalculateEdgesFromLines_0000030BU24BurstDirectCall_GetFunctionPointer_m93E3D13466D29AAF8B7D411B56D5C4CF31C9766A,
	CalculateEdgesFromLines_0000030BU24BurstDirectCall_Invoke_m20BA44B4ACCC532A2CEAA882ED295379BC2DD420,
	GetVertexReferenceStats_0000030CU24PostfixBurstDelegate__ctor_m2F91BDFD4B15A538D6E6BD679B00377C5BC132D6,
	GetVertexReferenceStats_0000030CU24PostfixBurstDelegate_Invoke_m519FE040B5068E0834CFD3311B002F7768ACD568,
	GetVertexReferenceStats_0000030CU24PostfixBurstDelegate_BeginInvoke_m910D589CF05146C5EB03CA04906D69CBC4655D4D,
	GetVertexReferenceStats_0000030CU24PostfixBurstDelegate_EndInvoke_m4A14553CAED48C23F13A98767705B23D681A624D,
	GetVertexReferenceStats_0000030CU24BurstDirectCall_GetFunctionPointerDiscard_m9326FEA8DF22F3BE371A611486A6BE47D8171ED4,
	GetVertexReferenceStats_0000030CU24BurstDirectCall_GetFunctionPointer_m3F715874C9BC72635B3F637615194FA43D98618E,
	GetVertexReferenceStats_0000030CU24BurstDirectCall_Invoke_mC7F6F632C594B5770FE20454A54925454E0B8F0B,
	CalculateEdgesFromTriangles_0000030EU24PostfixBurstDelegate__ctor_m77CE4EA41987F567E0A762D024B38F0914C7E795,
	CalculateEdgesFromTriangles_0000030EU24PostfixBurstDelegate_Invoke_m4DE337096BA41499148C7656BF638AE4EEE6D685,
	CalculateEdgesFromTriangles_0000030EU24PostfixBurstDelegate_BeginInvoke_mACA60E87CF55563D4D85D46D8D58BE238F0FF06C,
	CalculateEdgesFromTriangles_0000030EU24PostfixBurstDelegate_EndInvoke_m21744D2A27833C4C100A20339174C08D12AF1816,
	CalculateEdgesFromTriangles_0000030EU24BurstDirectCall_GetFunctionPointerDiscard_m70F9138FBC0F2F4A2069CC01634ABE04D324368C,
	CalculateEdgesFromTriangles_0000030EU24BurstDirectCall_GetFunctionPointer_mD21B9D1F00B0DBC2BC9D5E6920136A718A71D915,
	CalculateEdgesFromTriangles_0000030EU24BurstDirectCall_Invoke_m1DD893BC82D09DAE2E81481C394A00E9BDEE6108,
	ReverseWindingOrder_0000030FU24PostfixBurstDelegate__ctor_m92E77F7CB24D2FB49B839723E2344594709FD6B9,
	ReverseWindingOrder_0000030FU24PostfixBurstDelegate_Invoke_m7AF4C6672B5E2D619E3605F8FB916D07159A5180,
	ReverseWindingOrder_0000030FU24PostfixBurstDelegate_BeginInvoke_mC79281FB1ACAB00C043A86B7A0908E8098DDBDD8,
	ReverseWindingOrder_0000030FU24PostfixBurstDelegate_EndInvoke_mBA39070482EB30D09730B17266E27A6C75B9443F,
	ReverseWindingOrder_0000030FU24BurstDirectCall_GetFunctionPointerDiscard_m4AA7CD7CF013D090F2E1DBA3873F9529DED422EA,
	ReverseWindingOrder_0000030FU24BurstDirectCall_GetFunctionPointer_mE899AA1409BEB052ACCAA5652518BDCF62724914,
	ReverseWindingOrder_0000030FU24BurstDirectCall_Invoke_m77AA98A7164A96FA31669950E57C9D229936488A,
	ClipEdges_00000312U24PostfixBurstDelegate__ctor_m3DB42A7AFB7FF44649061DDEE8FF4A3A8227BBFE,
	ClipEdges_00000312U24PostfixBurstDelegate_Invoke_mC2D9CD27A15AD761818455E0D391DF029E552A1C,
	ClipEdges_00000312U24PostfixBurstDelegate_BeginInvoke_m1A9CB4A4C739C9099E54CA43787EFA444FCD84AF,
	ClipEdges_00000312U24PostfixBurstDelegate_EndInvoke_mE783C2711A7DF4D21DAA4F2774534FF19501BD83,
	ClipEdges_00000312U24BurstDirectCall_GetFunctionPointerDiscard_mD2392F1788D67B2E7E8CBBCEC5A1847D75B68F8C,
	ClipEdges_00000312U24BurstDirectCall_GetFunctionPointer_m8B92B9B18002E9F99327332330680488B29ABC07,
	ClipEdges_00000312U24BurstDirectCall_Invoke_m3D4C6089F6D8F6871341073FA5F429B9D113C3EF,
	ShapeProviderUtility_CallOnBeforeRender_m7C51462DD5E927D3EF32E4948DB40644A7B245C2,
	ShapeProviderUtility_PersistantDataCreated_m435BC95D833768183FCB85269612D96AF54B7791,
	ShapeProviderUtility__ctor_mBFDC150C86C11364B9B1650E4A63BB80A8EE7D0C,
	ShadowRendering_get_maxTextureCount_m1BC0EA9B75B149E2604389A7FBF8A1CD6589D72A,
	ShadowRendering_set_maxTextureCount_m0E7989731298A22E597B948BD4DDDE84139EFE71,
	ShadowRendering_get_lightInputTextures_mE4AFC2A828A8BE487069D55E4500992BDDD44FA4,
	ShadowRendering_InitializeBudget_mF4924A4929E5A768A8EC050D901BBDAA87CF572E,
	ShadowRendering_CreateMaterial_m8234BC4D52AD07B733DD6BC003A4DC0C9D646AAD,
	ShadowRendering_GetProjectedShadowMaterial_mA1090A2C989FE965B7BEF55AF55DCE8F984B1D23,
	ShadowRendering_GetProjectedShadowMaterial_mF79827ED39DF40998A911656E1C198D25BF4D0C9,
	ShadowRendering_GetProjectedUnshadowMaterial_mFAF66A404023EC26ACB31CB946BDEAB1F6450EE9,
	ShadowRendering_GetSpriteShadowMaterial_mC47BECEDD6453FAE2E49E2223816F278083CB42E,
	ShadowRendering_GetSpriteUnshadowMaterial_mD7C249A243DDDFED43E48D97B20AC32266D778B3,
	ShadowRendering_GetGeometryShadowMaterial_mA143EE752E3247C63FF820BABA58AA12AA3E15F2,
	ShadowRendering_GetGeometryUnshadowMaterial_m300ECB92E475135406D7ACD1524823F00FB6AC18,
	ShadowRendering_CalculateFrustumCornersPerspective_mA6985BEDB971D8E85C79442820EEC60B084C69D5,
	ShadowRendering_CalculateFrustumCornersOrthographic_m22EA13A775F8325A4CF1480DFAE06991A687277F,
	ShadowRendering_CalculateWorldSpaceBounds_mFC268ED4AD9EE7410AF79C2CDEB1B067D1371CB9,
	ShadowRendering_CallOnBeforeRender_m87C1AD9258CD9ABC9212779776097EC0F7A277BC,
	ShadowRendering_CreateShadowRenderTexture_m646D41B6763426609E002132D260B479E7948D2D,
	ShadowRendering_PrerenderShadows_mF16E1075806863694F4CF9A8B3EFD9B608F827C8,
	ShadowRendering_PrerenderShadows_m38E3EE0127295FB65248CB39C70812814253FD8C,
	ShadowRendering_CreateShadowRenderTexture_mF1678878C9EA31F8AA3DA7342A60DD8CD66C2EB1,
	ShadowRendering_ReleaseShadowRenderTexture_m58B1B092C38DD47EE161B78861EA26BF7128651B,
	ShadowRendering_SetShadowProjectionGlobals_m800F872BFD9A718F314E39976CC96581551DF988,
	ShadowRendering_SetGlobalShadowTexture_m57F5462AE413508271DCC7BC1E6BA9E24227FCAE,
	ShadowRendering_SetGlobalShadowProp_mBC6B4620F9F40D782E77395E71BADB14E4DF89F5,
	ShadowRendering_ShadowCasterIsVisible_m901BA428CB526E0ECB849A88BBD9A1F49D5B036F,
	ShadowRendering_GetRendererFromCaster_m6D7D8BE57FD015AD6E17AC71ABC06B4EBEBDCD68,
	ShadowRendering_RenderProjectedShadows_m7DC895C1D5B3FB27A71E0004B9F156647CBC9ABF,
	ShadowRendering_GetRendererSubmeshes_m89AC52C66C3E91562D51094BC3F28F383D770B89,
	ShadowRendering_RenderSpriteShadow_m1916F963D5E90B5719A5B1A80123B3781AE62EFF,
	ShadowRendering_ShadowTest_mFD03AFC4E36ADB38D8183815B717A1936FAC816F,
	ShadowRendering_RenderShadows_m8597106EAD21509138AD38E17681064DADF957B7,
	ShadowRendering__cctor_m3284B7B1564FF36D44700C39029479777FC9A48E,
	U3CU3Ec__cctor_mA0CA5E31DCC8E36F7B28797566B3B3250766F007,
	U3CU3Ec__ctor_m016A055FA79AD7FDD7A1D02AA6977CEA8F886813,
	U3CU3Ec_U3CGetProjectedShadowMaterialU3Eb__34_0_m0EE4A885D48D01B62D6AD2FD0E99A1EED6353FB8,
	U3CU3Ec_U3CGetProjectedUnshadowMaterialU3Eb__35_0_mC6A06AF99AD0CD680B88EA54FE6A31E0AAECF85A,
	U3CU3Ec_U3CGetSpriteShadowMaterialU3Eb__36_0_m4E0A684740BB69A5501256B8951AADEA3139810D,
	U3CU3Ec_U3CGetSpriteUnshadowMaterialU3Eb__37_0_m1B668EEFEAB91A0664F5D02B16D3742C7DEC2A2F,
	U3CU3Ec_U3CGetGeometryShadowMaterialU3Eb__38_0_m247F5EB28B9F6FC6CF159492B61A4AE58151F057,
	U3CU3Ec_U3CGetGeometryUnshadowMaterialU3Eb__39_0_m95FE3770DF377169A723D401F354E48BB514C8A1,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PlanarGraph_RemoveDuplicateEdges_m02B7AF8FAF00D6029D902205B836E6E7B9B9FD43,
	PlanarGraph_CheckCollinear_m2DDF994C13C20490A8520E11CDA42958EA13E026,
	PlanarGraph_LineLineIntersection_m17350F15EBC948F829EB7C392AD18A7AFD241284,
	PlanarGraph_LineLineIntersection_mE3D6BA5A3BEAFD6CF5168142443F49E1B4A086ED,
	PlanarGraph_CalculateEdgeIntersections_mD706558D362051701D8FBF2757B7C4A2EFB04D54,
	PlanarGraph_CalculateTJunctions_mEE0C82242AAEC33550DF9992A5F259762D5CFD44,
	PlanarGraph_CutEdges_mD2C22A6E61E85C92EFE1EE79E687F67B3FCE01BF,
	PlanarGraph_RemoveDuplicatePoints_m0E4EA60CACDB36682410B04FD3DDB7A92C31A0EA,
	PlanarGraph_Validate_m55816BC6804855F94178B5FDEC284BB63C0ACC88,
	PlanarGraph__cctor_m41BAFC16976D7CC16BE6F8C0863A6FE6EC3BD336,
	Tessellator_FindSplit_m10B801FE843957B0D014E7867373EAF6AA2CF1FD,
	Tessellator_SetAllocator_m66AFC39E8CFBDC82638D7D7BC3BA70CB4B818C96,
	Tessellator_AddPoint_m1B8B2A1C5A2F7742F1E5AF1D7618E65C1B75C70B,
	Tessellator_InsertHull_mC363D2CABF716C07C41BB4209A4BCBCC50F1EFC1,
	Tessellator_EraseHull_mC63AB321BFF1D86A25F33DE2180CD685D138069C,
	Tessellator_SplitHulls_mC558F30B7A383F1169945126EF5FF34B2E9B54C9,
	Tessellator_MergeHulls_m9A96C241CD12A18A2CF1FAC88978385323F57DFE,
	Tessellator_InsertUniqueEdge_m05BA0DE1A1B7A7392EA482F6AFD23CCE325D4625,
	Tessellator_PrepareDelaunay_mB942127F1C81668537200C7D5BEBEAB81A8E8396,
	Tessellator_OppositeOf_m22FD730F3F313FD1E8DD6FCA4ECA99883B92F38C,
	Tessellator_FindConstraint_m6913A7E958CC2DF88EF0FF08E6651728B26C547A,
	Tessellator_AddTriangle_m007B2BB6D24FC02369BD0777488E181BF3D18544,
	Tessellator_RemovePair_mFCFB259D9EB11DF42B6D598B2577324DEB458C45,
	Tessellator_RemoveTriangle_m34E53EFC4378A920679A5FD8C30C02630CD78968,
	Tessellator_EdgeFlip_m1C6D8B63CCA7B3A8183D14680904C72FBA89C843,
	Tessellator_Flip_m6DA43B217DF80D40A990220077BB66AF23D04A8A,
	Tessellator_GetCells_m67140E0BC8D71B44D99C55F76B7FB4160F853854,
	Tessellator_ApplyDelaunay_m39B60F4E8FE33D874D762C034A2D1157F28FCF49,
	Tessellator_FindNeighbor_m3BD0F869CABD9603D3EAF91D591931C8BF79C2A1,
	Tessellator_Constrain_mF9C988B9CE9F1C8E4F518FE3850D5FE9577D954F,
	Tessellator_RemoveExterior_m63CD3C9CFC589841E8071F8095AE44C9DAC4F4F3,
	Tessellator_RemoveInterior_m613C8F301ABDA9F6C96049E2F1675539E2F56756,
	Tessellator_Triangulate_mE55C698C207EA1A8DA592191AB17C3FB7688989E,
	Tessellator_Tessellate_mE45146D6B03B63F8D23C19B8B3FAF0047300B1AC,
	Tessellator_Cleanup_m54EFF571CA8971F236A791FC880C8046286D2A37,
	TestHullPointL_Test_mD67B5326AED143E216C7DC238637E8E0B3131ECC,
	TestHullPointU_Test_m3AF0A2CD6162B20C77771850BF94DC7A2D8ED9C2,
	TestHullEventLe_Test_m4FE651114100DE7E07DBADE520E75FC168A97CB3,
	TestHullEventE_Test_mD7344227B61F43A6A023EBC2F1C13140FD082F49,
	TestEdgePointE_Test_m1405C8998CF580F46817D5D0F33BBC98B4CC240B,
	TestCellE_Test_m41B697E52090E8C53B5FF1ABE7981AAFB2E48C3E,
	NULL,
	XCompare_Compare_mC591D9D097659ECE784A849856E80AB4D5E2C11A,
	IntersectionCompare_Compare_m47AF21131D95CE78ACD63B1848834162EE5FB582,
	TessEventCompare_Compare_mCB358637173C49F5385C5C10E6EF7FB2998FEF40,
	TessEdgeCompare_Compare_mE3BC27F4E0F907DADA5C2B96C0826122EBB34E97,
	TessCellCompare_Compare_m33DEA3199CA382144A3B0376035D2F1A789B4108,
	TessJunctionCompare_Compare_m28B7673AAA0AD8D3EAFE0DD9289AB335D4350F13,
	DelaEdgeCompare_Compare_m4AE4388767DAE729E5F497C66009C5DEB796B61A,
	TessLink_CreateLink_m29A2928ED0ECDA9EE29A29E2EC92D6678F10FF05,
	TessLink_DestroyLink_m8AE3E6F07F5695531C7712DC0777B62A7494AF1F,
	TessLink_Find_mEFBB6D30AF8D5CCDD565E36A2EAD62534AC52D08,
	TessLink_Link_m371C8A216AFD9AC8BCD5F015C59DD926F8088B8B,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ModuleHandle_OrientFast_m5AE5B276A51F924CB72CC43E05BED8D0C7FF6A06,
	ModuleHandle_OrientFastDouble_m22FA5D345F2DE849281B3A4FD34A094C241FAC2F,
	ModuleHandle_CircumCircle_m****************************************,
	ModuleHandle_IsInsideCircle_m****************************************,
	ModuleHandle_TriangleArea_mB975E204C8246F28F6D0FFC2816EA77071094CD4,
	ModuleHandle_Sign_m2A3FE8C13958F68CA76C2F1A510C4B90ED9168F1,
	ModuleHandle_IsInsideTriangle_mE5E0727E61408F6660F8523E486B325CD14D028A,
	ModuleHandle_IsInsideTriangleApproximate_m857F51A8532FE7F8F81A1439E79F3A11D5BF0CD6,
	ModuleHandle_IsInsideCircle_m****************************************,
	ModuleHandle_BuildTriangles_mD6A98AB9B2DD595D042196B675EFBD00A8CA2DFC,
	ModuleHandle_BuildTriangles_m6234B1C0C2B49C85551535F8CD0805845C03CAAC,
	ModuleHandle_BuildTrianglesAndEdges_m27E981150B7C706432B2B0289D171292848DD602,
	ModuleHandle_CopyGraph_mD2417E59077CE579091AC05BEC315E4FBD802054,
	ModuleHandle_CopyGeometry_mBB391F8676ADD633B9B0A4F64001B689F055B3D0,
	ModuleHandle_TransferOutput_mE769E946634074513DB462A6E244FD843ED2135C,
	ModuleHandle_GraphConditioner_m322541485AFCB09F1FDF3609BECCB093D0DA138E,
	ModuleHandle_Reorder_m40FFFB135A6763AE1ED04125D4AC161FFE65ABFF,
	ModuleHandle_VertexCleanupConditioner_m6AC0853FEBD2826F41C6C3115F33D927867676A6,
	ModuleHandle_ConvexQuad_m7D9BC4D23333081D14A8044E77AED5FBF01DDFF4,
	ModuleHandle_Tessellate_m6AE06111AB2B0B09EC2C846DEF23C1AAA61B968E,
	ModuleHandle__cctor_mBE35D3D252C3BA83B20D8EC431BE1589A144E03D,
	U24BurstDirectCallInitializer_Initialize_m15347CE4997B95D8D58B24CEC10BDB0ACB562281,
};
extern void VertexDictionary_GetIndexRemap_m0E5260046A68AA40445328A6A22F21EB718AA143_AdjustorThunk (void);
extern void DoublePoint__ctor_m29F8204086F248FC19FB9593452E8EECF612563A_AdjustorThunk (void);
extern void DoublePoint__ctor_m52141CF0CACA19656612BC12A2C4F7664763F1B2_AdjustorThunk (void);
extern void DoublePoint__ctor_m41A0279EEC0F9151AE7450FB7897C28252712545_AdjustorThunk (void);
extern void Int128__ctor_m2BB0A4CF9E48910476A58754D2AE1250D3C12966_AdjustorThunk (void);
extern void Int128__ctor_mBC356544A97CDF5EACE12C41CBB3C286CC68B723_AdjustorThunk (void);
extern void Int128__ctor_mE8A298C41D274843EE69B61FA4505EEE3D2C1054_AdjustorThunk (void);
extern void Int128_IsNegative_m70EBBF367872796AFF9BEA2D5B0C44B391B2FD91_AdjustorThunk (void);
extern void Int128_Equals_m9E0BF4D381BD6AB4201A7F1C8D6DE2E76FCA7CDE_AdjustorThunk (void);
extern void Int128_GetHashCode_m4D04D0D584560C74DC48A4931C0FEFC434233CA2_AdjustorThunk (void);
extern void IntPoint__ctor_m975E8CAF60B3C05EA5C947E1E5A5D46A6991C9F7_AdjustorThunk (void);
extern void IntPoint__ctor_m1C17D5437492194E95980214849A1BA4FEC919B0_AdjustorThunk (void);
extern void IntPoint__ctor_mA6DA01E8192EAB09DAEE3C900A83D4A2E87AA8FE_AdjustorThunk (void);
extern void IntPoint_Equals_mB95B6A612FF6F33DAFB3AF701DF7554CFB414833_AdjustorThunk (void);
extern void IntPoint_GetHashCode_mCC421CBDCDE3C6A291D3CAA06D787AF76B6F8EA9_AdjustorThunk (void);
extern void IntRect__ctor_m2679B769D75424420E84A8348164D92437FD003A_AdjustorThunk (void);
extern void IntRect__ctor_m61FE9261A36D5EE88EB7CFDF926045C308753D8E_AdjustorThunk (void);
extern void Light2DBlendStyle_get_blendFactors_mC60D457EC148FAD9DF648D8744E8FD1739428D76_AdjustorThunk (void);
extern void Light2DBlendStyle_get_maskTextureChannelFilter_m0BAA8F7231AC36BBD698EF17ADA929551CBEA5D1_AdjustorThunk (void);
extern void Light2DBlendStyle_get_isDirty_mBAE482E03B7BBAF92D33250196D9572CBEE943DA_AdjustorThunk (void);
extern void Light2DBlendStyle_set_isDirty_m9E685039DACBFC96AD6BFE2B0159F8D48650DE37_AdjustorThunk (void);
extern void Light2DBlendStyle_get_hasRenderTarget_m1165817ABB896D6794DD535B31F43253F0C77326_AdjustorThunk (void);
extern void Light2DBlendStyle_set_hasRenderTarget_m8C3590F7559A6A200B2A07395275A10E4BB453FA_AdjustorThunk (void);
extern void MaskChannelFilter_get_mask_mE84FC5DE03D3B8FC30FCDD09098E5BA108FEE550_AdjustorThunk (void);
extern void MaskChannelFilter_set_mask_mBA4F50BB0878FF53E2BCB044D4AE6056EBF6E15B_AdjustorThunk (void);
extern void MaskChannelFilter_get_inverted_m4F955DAACAED39F771A5D442479390BA5B87D623_AdjustorThunk (void);
extern void MaskChannelFilter_set_inverted_mD1CD0612835F5757B16C099121FA8CD2B6E42FEF_AdjustorThunk (void);
extern void MaskChannelFilter__ctor_m0EB791DE0052FF44F5E9FEC4F927F7459CAFD7D2_AdjustorThunk (void);
extern void LightStats_get_useLights_m5D283F1EE777C5125CBCF130FCBD5E65606B9685_AdjustorThunk (void);
extern void LightStats_get_useShadows_mD182F3CEE2ADAACB4E5621B0B095BBDCBFD6300C_AdjustorThunk (void);
extern void LightStats_get_useVolumetricLights_m24B97C84F76DD41D6D9D6A1AB1CB65AD08333D92_AdjustorThunk (void);
extern void LightStats_get_useVolumetricShadowLights_m4F51EC079FF136ED8D217328333FBCAA990C8324_AdjustorThunk (void);
extern void LightStats_get_useNormalMap_m62161A14013BBDB018B9DA75376A1864199FCDED_AdjustorThunk (void);
extern void LayerBatch_InitRTIds_mC15F5BAF8B993D83A36EC1220A772C86C6C3935D_AdjustorThunk (void);
extern void LayerBatch_GetRTId_mD844336931BE54E9203E890BFAB139DF9F713266_AdjustorThunk (void);
extern void LayerBatch_ReleaseRT_mD2C253FB4B36C42D7DFE559872AFD91CFA7E5024_AdjustorThunk (void);
extern void EdgeDictionary_GetOutsideEdges_m02DF9E4693CDEA1D5D5FB07DF069F31C0966CFBC_AdjustorThunk (void);
extern void MinMaxBounds_Intersects_mB283C28F81335E978846E325A9482892D24644C1_AdjustorThunk (void);
extern void MinMaxBounds__ctor_m3D75999E612D51C1A02E2768D6291255386D9B40_AdjustorThunk (void);
extern void ShadowEdge__ctor_m92189DE1A34AA7F4D481C640A7415EE57584C537_AdjustorThunk (void);
extern void ShadowEdge_Reverse_m267B65507DE000FD5359CAEB003472B65D26A890_AdjustorThunk (void);
extern void ShadowMeshVertex__ctor_m2AA5F11678057369D90EC50FB7617477A9E909C9_AdjustorThunk (void);
extern void RemappingInfo_Initialize_mC79F12A1E91E25F931C9F858473E38216719C497_AdjustorThunk (void);
extern void Tessellator_SetAllocator_m66AFC39E8CFBDC82638D7D7BC3BA70CB4B818C96_AdjustorThunk (void);
extern void Tessellator_AddPoint_m1B8B2A1C5A2F7742F1E5AF1D7618E65C1B75C70B_AdjustorThunk (void);
extern void Tessellator_SplitHulls_mC558F30B7A383F1169945126EF5FF34B2E9B54C9_AdjustorThunk (void);
extern void Tessellator_MergeHulls_m9A96C241CD12A18A2CF1FAC88978385323F57DFE_AdjustorThunk (void);
extern void Tessellator_PrepareDelaunay_mB942127F1C81668537200C7D5BEBEAB81A8E8396_AdjustorThunk (void);
extern void Tessellator_OppositeOf_m22FD730F3F313FD1E8DD6FCA4ECA99883B92F38C_AdjustorThunk (void);
extern void Tessellator_FindConstraint_m6913A7E958CC2DF88EF0FF08E6651728B26C547A_AdjustorThunk (void);
extern void Tessellator_AddTriangle_m007B2BB6D24FC02369BD0777488E181BF3D18544_AdjustorThunk (void);
extern void Tessellator_RemovePair_mFCFB259D9EB11DF42B6D598B2577324DEB458C45_AdjustorThunk (void);
extern void Tessellator_RemoveTriangle_m34E53EFC4378A920679A5FD8C30C02630CD78968_AdjustorThunk (void);
extern void Tessellator_EdgeFlip_m1C6D8B63CCA7B3A8183D14680904C72FBA89C843_AdjustorThunk (void);
extern void Tessellator_Flip_m6DA43B217DF80D40A990220077BB66AF23D04A8A_AdjustorThunk (void);
extern void Tessellator_GetCells_m67140E0BC8D71B44D99C55F76B7FB4160F853854_AdjustorThunk (void);
extern void Tessellator_ApplyDelaunay_m39B60F4E8FE33D874D762C034A2D1157F28FCF49_AdjustorThunk (void);
extern void Tessellator_FindNeighbor_m3BD0F869CABD9603D3EAF91D591931C8BF79C2A1_AdjustorThunk (void);
extern void Tessellator_Constrain_mF9C988B9CE9F1C8E4F518FE3850D5FE9577D954F_AdjustorThunk (void);
extern void Tessellator_RemoveExterior_m63CD3C9CFC589841E8071F8095AE44C9DAC4F4F3_AdjustorThunk (void);
extern void Tessellator_RemoveInterior_m613C8F301ABDA9F6C96049E2F1675539E2F56756_AdjustorThunk (void);
extern void Tessellator_Triangulate_mE55C698C207EA1A8DA592191AB17C3FB7688989E_AdjustorThunk (void);
extern void Tessellator_Cleanup_m54EFF571CA8971F236A791FC880C8046286D2A37_AdjustorThunk (void);
extern void TestHullPointL_Test_mD67B5326AED143E216C7DC238637E8E0B3131ECC_AdjustorThunk (void);
extern void TestHullPointU_Test_m3AF0A2CD6162B20C77771850BF94DC7A2D8ED9C2_AdjustorThunk (void);
extern void TestHullEventLe_Test_m4FE651114100DE7E07DBADE520E75FC168A97CB3_AdjustorThunk (void);
extern void TestHullEventE_Test_mD7344227B61F43A6A023EBC2F1C13140FD082F49_AdjustorThunk (void);
extern void TestEdgePointE_Test_m1405C8998CF580F46817D5D0F33BBC98B4CC240B_AdjustorThunk (void);
extern void TestCellE_Test_m41B697E52090E8C53B5FF1ABE7981AAFB2E48C3E_AdjustorThunk (void);
extern void XCompare_Compare_mC591D9D097659ECE784A849856E80AB4D5E2C11A_AdjustorThunk (void);
extern void IntersectionCompare_Compare_m47AF21131D95CE78ACD63B1848834162EE5FB582_AdjustorThunk (void);
extern void TessEventCompare_Compare_mCB358637173C49F5385C5C10E6EF7FB2998FEF40_AdjustorThunk (void);
extern void TessEdgeCompare_Compare_mE3BC27F4E0F907DADA5C2B96C0826122EBB34E97_AdjustorThunk (void);
extern void TessCellCompare_Compare_m33DEA3199CA382144A3B0376035D2F1A789B4108_AdjustorThunk (void);
extern void TessJunctionCompare_Compare_m28B7673AAA0AD8D3EAFE0DD9289AB335D4350F13_AdjustorThunk (void);
extern void DelaEdgeCompare_Compare_m4AE4388767DAE729E5F497C66009C5DEB796B61A_AdjustorThunk (void);
extern void TessLink_Find_mEFBB6D30AF8D5CCDD565E36A2EAD62534AC52D08_AdjustorThunk (void);
extern void TessLink_Link_m371C8A216AFD9AC8BCD5F015C59DD926F8088B8B_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[78] = 
{
	{ 0x06000003, VertexDictionary_GetIndexRemap_m0E5260046A68AA40445328A6A22F21EB718AA143_AdjustorThunk },
	{ 0x06000009, DoublePoint__ctor_m29F8204086F248FC19FB9593452E8EECF612563A_AdjustorThunk },
	{ 0x0600000A, DoublePoint__ctor_m52141CF0CACA19656612BC12A2C4F7664763F1B2_AdjustorThunk },
	{ 0x0600000B, DoublePoint__ctor_m41A0279EEC0F9151AE7450FB7897C28252712545_AdjustorThunk },
	{ 0x0600001C, Int128__ctor_m2BB0A4CF9E48910476A58754D2AE1250D3C12966_AdjustorThunk },
	{ 0x0600001D, Int128__ctor_mBC356544A97CDF5EACE12C41CBB3C286CC68B723_AdjustorThunk },
	{ 0x0600001E, Int128__ctor_mE8A298C41D274843EE69B61FA4505EEE3D2C1054_AdjustorThunk },
	{ 0x0600001F, Int128_IsNegative_m70EBBF367872796AFF9BEA2D5B0C44B391B2FD91_AdjustorThunk },
	{ 0x06000022, Int128_Equals_m9E0BF4D381BD6AB4201A7F1C8D6DE2E76FCA7CDE_AdjustorThunk },
	{ 0x06000023, Int128_GetHashCode_m4D04D0D584560C74DC48A4931C0FEFC434233CA2_AdjustorThunk },
	{ 0x0600002B, IntPoint__ctor_m975E8CAF60B3C05EA5C947E1E5A5D46A6991C9F7_AdjustorThunk },
	{ 0x0600002C, IntPoint__ctor_m1C17D5437492194E95980214849A1BA4FEC919B0_AdjustorThunk },
	{ 0x0600002D, IntPoint__ctor_mA6DA01E8192EAB09DAEE3C900A83D4A2E87AA8FE_AdjustorThunk },
	{ 0x06000030, IntPoint_Equals_mB95B6A612FF6F33DAFB3AF701DF7554CFB414833_AdjustorThunk },
	{ 0x06000031, IntPoint_GetHashCode_mCC421CBDCDE3C6A291D3CAA06D787AF76B6F8EA9_AdjustorThunk },
	{ 0x06000032, IntRect__ctor_m2679B769D75424420E84A8348164D92437FD003A_AdjustorThunk },
	{ 0x06000033, IntRect__ctor_m61FE9261A36D5EE88EB7CFDF926045C308753D8E_AdjustorThunk },
	{ 0x06000141, Light2DBlendStyle_get_blendFactors_mC60D457EC148FAD9DF648D8744E8FD1739428D76_AdjustorThunk },
	{ 0x06000142, Light2DBlendStyle_get_maskTextureChannelFilter_m0BAA8F7231AC36BBD698EF17ADA929551CBEA5D1_AdjustorThunk },
	{ 0x06000143, Light2DBlendStyle_get_isDirty_mBAE482E03B7BBAF92D33250196D9572CBEE943DA_AdjustorThunk },
	{ 0x06000144, Light2DBlendStyle_set_isDirty_m9E685039DACBFC96AD6BFE2B0159F8D48650DE37_AdjustorThunk },
	{ 0x06000145, Light2DBlendStyle_get_hasRenderTarget_m1165817ABB896D6794DD535B31F43253F0C77326_AdjustorThunk },
	{ 0x06000146, Light2DBlendStyle_set_hasRenderTarget_m8C3590F7559A6A200B2A07395275A10E4BB453FA_AdjustorThunk },
	{ 0x06000147, MaskChannelFilter_get_mask_mE84FC5DE03D3B8FC30FCDD09098E5BA108FEE550_AdjustorThunk },
	{ 0x06000148, MaskChannelFilter_set_mask_mBA4F50BB0878FF53E2BCB044D4AE6056EBF6E15B_AdjustorThunk },
	{ 0x06000149, MaskChannelFilter_get_inverted_m4F955DAACAED39F771A5D442479390BA5B87D623_AdjustorThunk },
	{ 0x0600014A, MaskChannelFilter_set_inverted_mD1CD0612835F5757B16C099121FA8CD2B6E42FEF_AdjustorThunk },
	{ 0x0600014B, MaskChannelFilter__ctor_m0EB791DE0052FF44F5E9FEC4F927F7459CAFD7D2_AdjustorThunk },
	{ 0x0600014C, LightStats_get_useLights_m5D283F1EE777C5125CBCF130FCBD5E65606B9685_AdjustorThunk },
	{ 0x0600014D, LightStats_get_useShadows_mD182F3CEE2ADAACB4E5621B0B095BBDCBFD6300C_AdjustorThunk },
	{ 0x0600014E, LightStats_get_useVolumetricLights_m24B97C84F76DD41D6D9D6A1AB1CB65AD08333D92_AdjustorThunk },
	{ 0x0600014F, LightStats_get_useVolumetricShadowLights_m4F51EC079FF136ED8D217328333FBCAA990C8324_AdjustorThunk },
	{ 0x06000150, LightStats_get_useNormalMap_m62161A14013BBDB018B9DA75376A1864199FCDED_AdjustorThunk },
	{ 0x06000187, LayerBatch_InitRTIds_mC15F5BAF8B993D83A36EC1220A772C86C6C3935D_AdjustorThunk },
	{ 0x06000188, LayerBatch_GetRTId_mD844336931BE54E9203E890BFAB139DF9F713266_AdjustorThunk },
	{ 0x06000189, LayerBatch_ReleaseRT_mD2C253FB4B36C42D7DFE559872AFD91CFA7E5024_AdjustorThunk },
	{ 0x060002E6, EdgeDictionary_GetOutsideEdges_m02DF9E4693CDEA1D5D5FB07DF069F31C0966CFBC_AdjustorThunk },
	{ 0x060002F4, MinMaxBounds_Intersects_mB283C28F81335E978846E325A9482892D24644C1_AdjustorThunk },
	{ 0x060002F5, MinMaxBounds__ctor_m3D75999E612D51C1A02E2768D6291255386D9B40_AdjustorThunk },
	{ 0x06000310, ShadowEdge__ctor_m92189DE1A34AA7F4D481C640A7415EE57584C537_AdjustorThunk },
	{ 0x06000311, ShadowEdge_Reverse_m267B65507DE000FD5359CAEB003472B65D26A890_AdjustorThunk },
	{ 0x06000350, ShadowMeshVertex__ctor_m2AA5F11678057369D90EC50FB7617477A9E909C9_AdjustorThunk },
	{ 0x06000351, RemappingInfo_Initialize_mC79F12A1E91E25F931C9F858473E38216719C497_AdjustorThunk },
	{ 0x060003DE, Tessellator_SetAllocator_m66AFC39E8CFBDC82638D7D7BC3BA70CB4B818C96_AdjustorThunk },
	{ 0x060003DF, Tessellator_AddPoint_m1B8B2A1C5A2F7742F1E5AF1D7618E65C1B75C70B_AdjustorThunk },
	{ 0x060003E2, Tessellator_SplitHulls_mC558F30B7A383F1169945126EF5FF34B2E9B54C9_AdjustorThunk },
	{ 0x060003E3, Tessellator_MergeHulls_m9A96C241CD12A18A2CF1FAC88978385323F57DFE_AdjustorThunk },
	{ 0x060003E5, Tessellator_PrepareDelaunay_mB942127F1C81668537200C7D5BEBEAB81A8E8396_AdjustorThunk },
	{ 0x060003E6, Tessellator_OppositeOf_m22FD730F3F313FD1E8DD6FCA4ECA99883B92F38C_AdjustorThunk },
	{ 0x060003E7, Tessellator_FindConstraint_m6913A7E958CC2DF88EF0FF08E6651728B26C547A_AdjustorThunk },
	{ 0x060003E8, Tessellator_AddTriangle_m007B2BB6D24FC02369BD0777488E181BF3D18544_AdjustorThunk },
	{ 0x060003E9, Tessellator_RemovePair_mFCFB259D9EB11DF42B6D598B2577324DEB458C45_AdjustorThunk },
	{ 0x060003EA, Tessellator_RemoveTriangle_m34E53EFC4378A920679A5FD8C30C02630CD78968_AdjustorThunk },
	{ 0x060003EB, Tessellator_EdgeFlip_m1C6D8B63CCA7B3A8183D14680904C72FBA89C843_AdjustorThunk },
	{ 0x060003EC, Tessellator_Flip_m6DA43B217DF80D40A990220077BB66AF23D04A8A_AdjustorThunk },
	{ 0x060003ED, Tessellator_GetCells_m67140E0BC8D71B44D99C55F76B7FB4160F853854_AdjustorThunk },
	{ 0x060003EE, Tessellator_ApplyDelaunay_m39B60F4E8FE33D874D762C034A2D1157F28FCF49_AdjustorThunk },
	{ 0x060003EF, Tessellator_FindNeighbor_m3BD0F869CABD9603D3EAF91D591931C8BF79C2A1_AdjustorThunk },
	{ 0x060003F0, Tessellator_Constrain_mF9C988B9CE9F1C8E4F518FE3850D5FE9577D954F_AdjustorThunk },
	{ 0x060003F1, Tessellator_RemoveExterior_m63CD3C9CFC589841E8071F8095AE44C9DAC4F4F3_AdjustorThunk },
	{ 0x060003F2, Tessellator_RemoveInterior_m613C8F301ABDA9F6C96049E2F1675539E2F56756_AdjustorThunk },
	{ 0x060003F3, Tessellator_Triangulate_mE55C698C207EA1A8DA592191AB17C3FB7688989E_AdjustorThunk },
	{ 0x060003F5, Tessellator_Cleanup_m54EFF571CA8971F236A791FC880C8046286D2A37_AdjustorThunk },
	{ 0x060003F6, TestHullPointL_Test_mD67B5326AED143E216C7DC238637E8E0B3131ECC_AdjustorThunk },
	{ 0x060003F7, TestHullPointU_Test_m3AF0A2CD6162B20C77771850BF94DC7A2D8ED9C2_AdjustorThunk },
	{ 0x060003F8, TestHullEventLe_Test_m4FE651114100DE7E07DBADE520E75FC168A97CB3_AdjustorThunk },
	{ 0x060003F9, TestHullEventE_Test_mD7344227B61F43A6A023EBC2F1C13140FD082F49_AdjustorThunk },
	{ 0x060003FA, TestEdgePointE_Test_m1405C8998CF580F46817D5D0F33BBC98B4CC240B_AdjustorThunk },
	{ 0x060003FB, TestCellE_Test_m41B697E52090E8C53B5FF1ABE7981AAFB2E48C3E_AdjustorThunk },
	{ 0x060003FD, XCompare_Compare_mC591D9D097659ECE784A849856E80AB4D5E2C11A_AdjustorThunk },
	{ 0x060003FE, IntersectionCompare_Compare_m47AF21131D95CE78ACD63B1848834162EE5FB582_AdjustorThunk },
	{ 0x060003FF, TessEventCompare_Compare_mCB358637173C49F5385C5C10E6EF7FB2998FEF40_AdjustorThunk },
	{ 0x06000400, TessEdgeCompare_Compare_mE3BC27F4E0F907DADA5C2B96C0826122EBB34E97_AdjustorThunk },
	{ 0x06000401, TessCellCompare_Compare_m33DEA3199CA382144A3B0376035D2F1A789B4108_AdjustorThunk },
	{ 0x06000402, TessJunctionCompare_Compare_m28B7673AAA0AD8D3EAFE0DD9289AB335D4350F13_AdjustorThunk },
	{ 0x06000403, DelaEdgeCompare_Compare_m4AE4388767DAE729E5F497C66009C5DEB796B61A_AdjustorThunk },
	{ 0x06000406, TessLink_Find_mEFBB6D30AF8D5CCDD565E36A2EAD62534AC52D08_AdjustorThunk },
	{ 0x06000407, TessLink_Link_m371C8A216AFD9AC8BCD5F015C59DD926F8088B8B_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1059] = 
{
	28240,
	34103,
	4017,
	34252,
	34317,
	21016,
	21016,
	21016,
	6734,
	15805,
	15905,
	21016,
	20761,
	20694,
	21016,
	20550,
	20694,
	20761,
	15968,
	20761,
	20761,
	20761,
	20761,
	20550,
	20550,
	15757,
	21016,
	15904,
	7904,
	15901,
	20550,
	27471,
	27471,
	11681,
	20694,
	27471,
	27471,
	27753,
	27753,
	31740,
	31670,
	27754,
	7900,
	6734,
	15905,
	27481,
	27481,
	11681,
	20694,
	2588,
	15907,
	21016,
	21016,
	5704,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	31555,
	20550,
	15757,
	6503,
	31561,
	4977,
	1447,
	3071,
	25325,
	23715,
	22234,
	21016,
	21016,
	21016,
	7905,
	2842,
	7985,
	13820,
	6087,
	3095,
	3095,
	3070,
	13820,
	15968,
	15968,
	4971,
	15968,
	21016,
	31857,
	15904,
	11447,
	20550,
	20761,
	15903,
	15721,
	7995,
	15968,
	15903,
	15904,
	20694,
	15903,
	20550,
	15757,
	20550,
	15757,
	3047,
	3047,
	1443,
	1443,
	15968,
	20550,
	21016,
	3760,
	7987,
	15904,
	7995,
	5018,
	11681,
	11681,
	11681,
	15968,
	15968,
	11447,
	21016,
	7995,
	3760,
	3325,
	6092,
	13820,
	6503,
	1446,
	7995,
	5342,
	5018,
	13820,
	6094,
	5018,
	13811,
	7995,
	15968,
	29176,
	29176,
	3760,
	15968,
	21016,
	2717,
	15968,
	6091,
	11681,
	5014,
	5014,
	13820,
	13820,
	11620,
	15904,
	11681,
	27855,
	20550,
	21016,
	31820,
	27908,
	3750,
	15904,
	15968,
	32764,
	31561,
	13212,
	15968,
	15968,
	15968,
	15968,
	6087,
	520,
	529,
	3102,
	27843,
	27843,
	27508,
	7995,
	7995,
	7995,
	32090,
	21016,
	15968,
	21016,
	31675,
	12315,
	12315,
	28064,
	28064,
	27697,
	25390,
	23716,
	25296,
	32090,
	28060,
	28060,
	24327,
	25678,
	28066,
	25678,
	28070,
	32090,
	26549,
	32090,
	32090,
	20593,
	15804,
	15804,
	21016,
	31820,
	3722,
	3722,
	21016,
	27709,
	15804,
	3518,
	6513,
	3609,
	7405,
	3621,
	7405,
	15968,
	20761,
	15968,
	20761,
	15968,
	20694,
	15903,
	20694,
	20550,
	20546,
	15753,
	20761,
	20550,
	20694,
	15903,
	20694,
	15903,
	20873,
	16071,
	20873,
	16071,
	20550,
	15757,
	20873,
	16071,
	20550,
	15757,
	20556,
	15762,
	20873,
	16071,
	20873,
	20873,
	16071,
	20550,
	15757,
	20550,
	15757,
	20761,
	15968,
	20873,
	16071,
	20873,
	16071,
	20550,
	20694,
	15903,
	20694,
	15903,
	20873,
	20694,
	20550,
	20761,
	15968,
	11681,
	11681,
	11619,
	11681,
	11619,
	21016,
	21016,
	20694,
	20547,
	21016,
	20550,
	21016,
	15757,
	21016,
	11619,
	20743,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	20873,
	16071,
	20873,
	16071,
	20873,
	16071,
	20873,
	16071,
	20873,
	20694,
	20550,
	20694,
	20873,
	20873,
	16071,
	20873,
	16071,
	20761,
	15968,
	15968,
	21016,
	21002,
	21168,
	20550,
	15757,
	20550,
	15757,
	21006,
	16201,
	21006,
	16201,
	8210,
	20550,
	20550,
	20550,
	20550,
	20550,
	-1,
	-1,
	-1,
	-1,
	20761,
	20761,
	20550,
	6022,
	6520,
	21016,
	34252,
	21016,
	5704,
	34156,
	34252,
	34252,
	32764,
	32764,
	32764,
	25289,
	27475,
	34156,
	34252,
	27473,
	27499,
	27473,
	27557,
	27421,
	25320,
	25677,
	28070,
	25677,
	28074,
	23193,
	23635,
	21921,
	25247,
	31787,
	34252,
	-1,
	15903,
	8101,
	34252,
	2843,
	15757,
	3850,
	31750,
	684,
	1324,
	159,
	8101,
	20761,
	21016,
	34252,
	15903,
	3369,
	15968,
	34239,
	32782,
	32782,
	23708,
	25293,
	25492,
	32764,
	28055,
	26531,
	34252,
	34156,
	34156,
	34252,
	34156,
	34156,
	34156,
	34156,
	34252,
	20761,
	18846,
	18860,
	21016,
	21016,
	21016,
	34252,
	34138,
	34200,
	34103,
	18860,
	20761,
	18846,
	13883,
	34138,
	7479,
	34200,
	31794,
	27855,
	21016,
	21016,
	21016,
	21016,
	13157,
	21016,
	1473,
	364,
	15968,
	21016,
	34252,
	34138,
	24679,
	28190,
	24678,
	26545,
	32764,
	26561,
	21800,
	27504,
	27504,
	21650,
	21906,
	21576,
	29176,
	26566,
	32262,
	32266,
	29156,
	22109,
	24649,
	27508,
	29176,
	26571,
	21907,
	23550,
	26548,
	28383,
	25678,
	25678,
	34252,
	20694,
	15903,
	20694,
	15903,
	20873,
	20694,
	15903,
	20694,
	15903,
	20694,
	15903,
	20550,
	15757,
	20550,
	15757,
	20550,
	15757,
	20550,
	15757,
	20550,
	15757,
	20694,
	20550,
	14279,
	14026,
	20694,
	21003,
	21003,
	21016,
	21016,
	21016,
	8102,
	8102,
	21016,
	21016,
	21016,
	21016,
	21016,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	15968,
	21016,
	21016,
	7405,
	6151,
	14026,
	20550,
	20550,
	20761,
	20761,
	20761,
	20761,
	20761,
	20694,
	15968,
	15757,
	21016,
	20761,
	16781,
	399,
	8101,
	6503,
	15968,
	13820,
	13820,
	15757,
	34103,
	20550,
	20761,
	20761,
	11681,
	8330,
	21016,
	15968,
	2855,
	3722,
	3722,
	8005,
	11681,
	8005,
	21016,
	7985,
	8011,
	21016,
	15968,
	15968,
	15968,
	15968,
	21016,
	34252,
	34252,
	21016,
	11681,
	11681,
	11681,
	20873,
	20873,
	20761,
	20550,
	20761,
	15968,
	20694,
	21004,
	20984,
	20984,
	20550,
	20694,
	20694,
	20712,
	20761,
	21016,
	21016,
	20761,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	21016,
	26531,
	15968,
	8101,
	26471,
	29176,
	7995,
	34252,
	21016,
	34252,
	21016,
	8000,
	7957,
	25270,
	8101,
	26561,
	750,
	21016,
	34252,
	21016,
	34252,
	21016,
	8000,
	8101,
	29176,
	1282,
	21016,
	34252,
	21016,
	34252,
	21016,
	8000,
	8101,
	29205,
	749,
	751,
	21016,
	34252,
	21016,
	21016,
	34252,
	21016,
	8000,
	8000,
	8101,
	24661,
	750,
	21016,
	34252,
	21016,
	34252,
	21016,
	7995,
	24662,
	21016,
	34252,
	21016,
	34252,
	21016,
	8000,
	7461,
	717,
	21016,
	8101,
	29176,
	2812,
	34252,
	21016,
	34252,
	21016,
	8000,
	21016,
	21016,
	21016,
	20694,
	15903,
	20761,
	20546,
	20873,
	16071,
	20873,
	16071,
	20761,
	20694,
	15903,
	20694,
	15903,
	20761,
	15968,
	20761,
	15968,
	20694,
	21016,
	15903,
	20694,
	15757,
	20550,
	15757,
	20550,
	15757,
	20550,
	34156,
	11681,
	11619,
	15968,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	20761,
	20694,
	15968,
	15968,
	21016,
	34156,
	34252,
	29176,
	29176,
	32090,
	31787,
	25307,
	29176,
	32764,
	32764,
	21016,
	4062,
	34252,
	5091,
	13286,
	21016,
	-1,
	27387,
	28970,
	32764,
	3751,
	11681,
	7995,
	3693,
	21016,
	11447,
	15721,
	7995,
	3749,
	3752,
	15968,
	20694,
	11681,
	7995,
	3693,
	7995,
	7995,
	21016,
	7995,
	20694,
	7995,
	7995,
	11681,
	7995,
	3693,
	21016,
	3752,
	15968,
	20694,
	11681,
	7995,
	3693,
	21016,
	7405,
	21016,
	20761,
	20546,
	20694,
	15903,
	20873,
	16071,
	29156,
	15968,
	477,
	338,
	220,
	371,
	393,
	10710,
	641,
	3469,
	6689,
	6503,
	16071,
	15968,
	21016,
	-1,
	-1,
	-1,
	-1,
	-1,
	21016,
	13820,
	20694,
	7995,
	7995,
	-1,
	7995,
	3693,
	21016,
	23934,
	23241,
	24458,
	23241,
	28910,
	21854,
	21664,
	24458,
	22058,
	23637,
	21856,
	28910,
	27797,
	21851,
	21722,
	21016,
	34252,
	23241,
	24458,
	23241,
	28910,
	21854,
	24458,
	22058,
	21856,
	28910,
	21722,
	3657,
	21016,
	7988,
	1152,
	376,
	15968,
	32748,
	34140,
	23241,
	7988,
	2363,
	574,
	15968,
	32748,
	34140,
	24458,
	7988,
	1152,
	376,
	15968,
	32748,
	34140,
	23241,
	7988,
	6503,
	2232,
	15968,
	32748,
	34140,
	28910,
	7988,
	396,
	160,
	15968,
	32748,
	34140,
	21854,
	7988,
	2363,
	574,
	15968,
	32748,
	34140,
	24458,
	7988,
	652,
	269,
	15968,
	32748,
	34140,
	22058,
	7988,
	398,
	161,
	15968,
	32748,
	34140,
	21856,
	7988,
	6503,
	2232,
	15968,
	32748,
	34140,
	28910,
	7988,
	278,
	112,
	15968,
	32748,
	34140,
	21722,
	24658,
	26566,
	21016,
	34239,
	32782,
	34156,
	32782,
	25675,
	24330,
	32090,
	32090,
	32090,
	32090,
	32090,
	32090,
	26577,
	26577,
	26952,
	29176,
	24678,
	22107,
	21801,
	24638,
	29169,
	26566,
	26564,
	32764,
	31561,
	25680,
	21887,
	27855,
	21570,
	27477,
	24644,
	34252,
	34252,
	21016,
	13820,
	13820,
	13820,
	13820,
	13820,
	13820,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	24456,
	23785,
	23785,
	22273,
	21781,
	21924,
	21583,
	23242,
	21588,
	34252,
	28258,
	15903,
	818,
	24447,
	26102,
	1358,
	1358,
	26159,
	6409,
	5649,
	5649,
	3622,
	3622,
	3622,
	7405,
	500,
	8956,
	4647,
	855,
	8956,
	8956,
	8957,
	1360,
	21588,
	21016,
	3133,
	3133,
	3132,
	3132,
	3180,
	3181,
	-1,
	5616,
	5841,
	5791,
	5841,
	5842,
	5841,
	5843,
	28318,
	32778,
	13157,
	7405,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	25770,
	25397,
	32407,
	27600,
	25770,
	25770,
	23786,
	23786,
	23786,
	21633,
	21431,
	21494,
	21719,
	21718,
	21432,
	22050,
	22085,
	23285,
	21661,
	21661,
	34252,
	34252,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[10] = 
{
	{ 0x06000336, 29,  (void**)&ShadowUtility_CalculateProjectionInfo_m952E6AB7BDD578374B99ED119819090E81F81067_RuntimeMethod_var, 0 },
	{ 0x06000337, 31,  (void**)&ShadowUtility_CalculateVertices_m1087DC5C13D021D10BB58FA9EAD2D99416D2974E_RuntimeMethod_var, 0 },
	{ 0x06000338, 30,  (void**)&ShadowUtility_CalculateTriangles_m9D055B9D0E3B41D00713797D5074A7BD42A4D216_RuntimeMethod_var, 0 },
	{ 0x06000339, 28,  (void**)&ShadowUtility_CalculateLocalBounds_mCD7AB254F81081483F03BEF504C2B2918777D32D_RuntimeMethod_var, 0 },
	{ 0x0600033A, 33,  (void**)&ShadowUtility_GenerateInteriorMesh_mBF198238681A38CB0ECED75E112BBEAE45F2B362_RuntimeMethod_var, 0 },
	{ 0x0600033C, 26,  (void**)&ShadowUtility_CalculateEdgesFromLines_m95B59AC1CE430C8952290FAC5C4DFEAD0EF53008_RuntimeMethod_var, 0 },
	{ 0x0600033D, 34,  (void**)&ShadowUtility_GetVertexReferenceStats_m1CB3AC1703058D94FF1211F9BA9430600C57B106_RuntimeMethod_var, 0 },
	{ 0x0600033F, 27,  (void**)&ShadowUtility_CalculateEdgesFromTriangles_m1F08E1F50422891DCB5A4A70D57ABD9F1354CB63_RuntimeMethod_var, 0 },
	{ 0x06000340, 35,  (void**)&ShadowUtility_ReverseWindingOrder_m9F8C8065FA5294F22C51C1552E34416B3AED38DE_RuntimeMethod_var, 0 },
	{ 0x06000343, 32,  (void**)&ShadowUtility_ClipEdges_m104D8B0F86040657D28698C6FEDD40210FC710FB_RuntimeMethod_var, 0 },
};
static const Il2CppTokenRangePair s_rgctxIndices[8] = 
{
	{ 0x0200009E, { 0, 15 } },
	{ 0x0200009F, { 15, 5 } },
	{ 0x06000408, { 20, 3 } },
	{ 0x06000409, { 23, 2 } },
	{ 0x0600040A, { 25, 6 } },
	{ 0x0600040B, { 31, 8 } },
	{ 0x0600040C, { 39, 8 } },
	{ 0x0600040D, { 47, 8 } },
};
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tA91CD8F3F34DC403E9C2A2D4A1BB79ECFF9D33D6_mCCE311F4D76FD18FB3B4B7A066557B7DEBEBA430;
extern const uint32_t g_rgctx_ArraySlice_1_t4D6293366AC9FD55DBC6628FB039533198A963CF;
extern const uint32_t g_rgctx_NativeArray_1_t814F8F2930E9DC80B8CF05843FFF8DE4ADF2C9EA;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_tA91CD8F3F34DC403E9C2A2D4A1BB79ECFF9D33D6_m6356BEAF60FB118881FC69E412746F52BF6829F7;
extern const uint32_t g_rgctx_ArraySlice_1_Equals_mBED252A8441574CBAD27748583E6A3E9B02D40F1;
extern const uint32_t g_rgctx_ArraySlice_1_t4D6293366AC9FD55DBC6628FB039533198A963CF;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElementWithStride_TisT_tA91CD8F3F34DC403E9C2A2D4A1BB79ECFF9D33D6_mDEC0BB2821435928F0E37E55B549496C5CA8E266;
extern const uint32_t g_rgctx_T_tA91CD8F3F34DC403E9C2A2D4A1BB79ECFF9D33D6;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElementWithStride_TisT_tA91CD8F3F34DC403E9C2A2D4A1BB79ECFF9D33D6_mAFDB222B1816F553BE510063B78EA12017181C4A;
extern const uint32_t g_rgctx_TU5BU5D_t7D772980313BE25386C39AD1AC04CBF38F971669;
extern const uint32_t g_rgctx_ArraySlice_1_GetUnsafeReadOnlyPtr_mFA0007ABA77735FACC2E47036B56CF7EDAF87E4A;
extern const uint32_t g_rgctx_ArraySlice_1_get_Stride_mDEC159A4D01D9840EA1B429D1A55E3CB4501AEEE;
extern const uint32_t g_rgctx_ArraySlice_1_get_Length_m2ED84EAABC691ED7EDBEB070F0398DBC7F4548C5;
extern const uint32_t g_rgctx_TU5BU5D_t7D772980313BE25386C39AD1AC04CBF38F971669;
extern const uint32_t g_rgctx_ArraySlice_1_CopyTo_m8279938978F39FB5C37481C7B328F298B877EDAE;
extern const uint32_t g_rgctx_ArraySlice_1_t71F143840F41C112074C9AD2E953A7C79DD0D323;
extern const uint32_t g_rgctx_ArraySliceDebugView_1_tB245A0A489311B05F9E95941A3121E5661049555;
extern const uint32_t g_rgctx_ArraySlice_1_ToArray_m66D721ED36F9A8091138847FFDFBCB57B2CBD016;
extern const uint32_t g_rgctx_ArraySlice_1_t71F143840F41C112074C9AD2E953A7C79DD0D323;
extern const uint32_t g_rgctx_TU5BU5D_tFC645B8EF8738BA89FDEE01D5E7D4A7383071551;
extern const uint32_t g_rgctx_NativeArray_1_tB41FFF23E8DEE9652CBD7406BDC9690E9B879402;
extern const uint32_t g_rgctx_NativeArray_1_Copy_mC1487B874C7BD0729155B5903573E36C3DFC27AC;
extern const uint32_t g_rgctx_NativeArray_1_tB41FFF23E8DEE9652CBD7406BDC9690E9B879402;
extern const uint32_t g_rgctx_NativeArray_1_tF61506BCC30E546DA1D51FB6A5DE23A7BDABC68E;
extern const uint32_t g_rgctx_ModuleHandle_Copy_TisT_t29DA22CBA8265439D6C6298F71C88FD1874BC5AB_m4FEBF4843CCEFE5A28573269F69631DA9D04F292;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tD7CD7C5AD277CCCAC7054E68992C8DC6CD8BBB35_m0C0CA44049C8675C4216620ED2A5C7C0B28AECA3;
extern const uint32_t g_rgctx_T_tD7CD7C5AD277CCCAC7054E68992C8DC6CD8BBB35;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElement_TisT_tD7CD7C5AD277CCCAC7054E68992C8DC6CD8BBB35_mC4836DDE5AF9E45AE2629224B789724C000D2F54;
extern const uint32_t g_rgctx_U_t5AEB10D8963DFF20F177A7951A1E28E034408B8C;
extern const uint32_t g_rgctx_IComparer_1_t1E71CBF34329F4A54FABD5ECFAA968583825CE4A;
extern const Il2CppRGCTXConstrainedData g_rgctx_U_t5AEB10D8963DFF20F177A7951A1E28E034408B8C_IComparer_1_Compare_mFBAA23CC97F643FCDF4D58EBC2B7848BDA12B4D4;
extern const uint32_t g_rgctx_X_t4DA2AD3F62D18E8F5D18277FF120906022A4063B;
extern const uint32_t g_rgctx_NativeArray_1_t86BA84EE5ADCE868EAC65FF06AF03B0B3CF39048;
extern const uint32_t g_rgctx_NativeArray_1_get_Item_m5E629951C56B2FE4BD26FE2D2F97787CA7C71AE9;
extern const uint32_t g_rgctx_NativeArray_1_t86BA84EE5ADCE868EAC65FF06AF03B0B3CF39048;
extern const uint32_t g_rgctx_T_t1721D117C2B101587EBE6259952AE034E7C484A0;
extern const uint32_t g_rgctx_U_t3A219073E60AE8947C111F12D53A415013DAA034;
extern const uint32_t g_rgctx_ICondition2_2_tABF3D2AC565D8F02A972F720B1ED53DCA49E6C7E;
extern const Il2CppRGCTXConstrainedData g_rgctx_X_t4DA2AD3F62D18E8F5D18277FF120906022A4063B_ICondition2_2_Test_mF17FC92C365EDB9514BFC1CB9228C879720442AB;
extern const uint32_t g_rgctx_X_tFB85170D4BCC00A2F9DE1CDA29EDAAF1D31F5058;
extern const uint32_t g_rgctx_NativeArray_1_tFF692C541FFB2465E53D19B7B1CD1BFD33F56A09;
extern const uint32_t g_rgctx_NativeArray_1_get_Item_mD49E083E523F35F2C29EE824AFCAC7D1A8E9E9B0;
extern const uint32_t g_rgctx_NativeArray_1_tFF692C541FFB2465E53D19B7B1CD1BFD33F56A09;
extern const uint32_t g_rgctx_T_t77FAC11549DF1C5B05EECC41F5E659C12CDDBFDD;
extern const uint32_t g_rgctx_U_t3556D1ECDCDBA49AE97AB4664C0885BE79DD6888;
extern const uint32_t g_rgctx_ICondition2_2_tD50DC4B9F50E58A56DBDA5BD6472936576A997C3;
extern const Il2CppRGCTXConstrainedData g_rgctx_X_tFB85170D4BCC00A2F9DE1CDA29EDAAF1D31F5058_ICondition2_2_Test_m1065B2C430E7B433A449B30EC2996443FE864315;
extern const uint32_t g_rgctx_X_t21C1F01D50C46DEA5E5A6B025E2CDF53F55F8983;
extern const uint32_t g_rgctx_NativeArray_1_tBE1959E38A4D196837F906555F761A78829BDC02;
extern const uint32_t g_rgctx_NativeArray_1_get_Item_m968AFA821FC648853C4CD3833D9AA6B0A352E878;
extern const uint32_t g_rgctx_NativeArray_1_tBE1959E38A4D196837F906555F761A78829BDC02;
extern const uint32_t g_rgctx_T_tA3FDD766096857FBE04022784FCAC42E6C716E52;
extern const uint32_t g_rgctx_U_t195909B15F970F02F780580FCE4700B303C8DCE0;
extern const uint32_t g_rgctx_ICondition2_2_t738CE7129EE65FC87066D8994088EC93F07BC6F9;
extern const Il2CppRGCTXConstrainedData g_rgctx_X_t21C1F01D50C46DEA5E5A6B025E2CDF53F55F8983_ICondition2_2_Test_m1C2F4E5BEE38C6036929C4E547643374BED9E1C2;
static const Il2CppRGCTXDefinition s_rgctxValues[55] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tA91CD8F3F34DC403E9C2A2D4A1BB79ECFF9D33D6_mCCE311F4D76FD18FB3B4B7A066557B7DEBEBA430 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySlice_1_t4D6293366AC9FD55DBC6628FB039533198A963CF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t814F8F2930E9DC80B8CF05843FFF8DE4ADF2C9EA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_tA91CD8F3F34DC403E9C2A2D4A1BB79ECFF9D33D6_m6356BEAF60FB118881FC69E412746F52BF6829F7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySlice_1_Equals_mBED252A8441574CBAD27748583E6A3E9B02D40F1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySlice_1_t4D6293366AC9FD55DBC6628FB039533198A963CF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElementWithStride_TisT_tA91CD8F3F34DC403E9C2A2D4A1BB79ECFF9D33D6_mDEC0BB2821435928F0E37E55B549496C5CA8E266 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA91CD8F3F34DC403E9C2A2D4A1BB79ECFF9D33D6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElementWithStride_TisT_tA91CD8F3F34DC403E9C2A2D4A1BB79ECFF9D33D6_mAFDB222B1816F553BE510063B78EA12017181C4A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t7D772980313BE25386C39AD1AC04CBF38F971669 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySlice_1_GetUnsafeReadOnlyPtr_mFA0007ABA77735FACC2E47036B56CF7EDAF87E4A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySlice_1_get_Stride_mDEC159A4D01D9840EA1B429D1A55E3CB4501AEEE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySlice_1_get_Length_m2ED84EAABC691ED7EDBEB070F0398DBC7F4548C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t7D772980313BE25386C39AD1AC04CBF38F971669 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySlice_1_CopyTo_m8279938978F39FB5C37481C7B328F298B877EDAE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySlice_1_t71F143840F41C112074C9AD2E953A7C79DD0D323 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySliceDebugView_1_tB245A0A489311B05F9E95941A3121E5661049555 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySlice_1_ToArray_m66D721ED36F9A8091138847FFDFBCB57B2CBD016 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySlice_1_t71F143840F41C112074C9AD2E953A7C79DD0D323 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tFC645B8EF8738BA89FDEE01D5E7D4A7383071551 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tB41FFF23E8DEE9652CBD7406BDC9690E9B879402 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Copy_mC1487B874C7BD0729155B5903573E36C3DFC27AC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tB41FFF23E8DEE9652CBD7406BDC9690E9B879402 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tF61506BCC30E546DA1D51FB6A5DE23A7BDABC68E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ModuleHandle_Copy_TisT_t29DA22CBA8265439D6C6298F71C88FD1874BC5AB_m4FEBF4843CCEFE5A28573269F69631DA9D04F292 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tD7CD7C5AD277CCCAC7054E68992C8DC6CD8BBB35_m0C0CA44049C8675C4216620ED2A5C7C0B28AECA3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD7CD7C5AD277CCCAC7054E68992C8DC6CD8BBB35 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElement_TisT_tD7CD7C5AD277CCCAC7054E68992C8DC6CD8BBB35_mC4836DDE5AF9E45AE2629224B789724C000D2F54 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t5AEB10D8963DFF20F177A7951A1E28E034408B8C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IComparer_1_t1E71CBF34329F4A54FABD5ECFAA968583825CE4A },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_U_t5AEB10D8963DFF20F177A7951A1E28E034408B8C_IComparer_1_Compare_mFBAA23CC97F643FCDF4D58EBC2B7848BDA12B4D4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_X_t4DA2AD3F62D18E8F5D18277FF120906022A4063B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t86BA84EE5ADCE868EAC65FF06AF03B0B3CF39048 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Item_m5E629951C56B2FE4BD26FE2D2F97787CA7C71AE9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t86BA84EE5ADCE868EAC65FF06AF03B0B3CF39048 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1721D117C2B101587EBE6259952AE034E7C484A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t3A219073E60AE8947C111F12D53A415013DAA034 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICondition2_2_tABF3D2AC565D8F02A972F720B1ED53DCA49E6C7E },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_X_t4DA2AD3F62D18E8F5D18277FF120906022A4063B_ICondition2_2_Test_mF17FC92C365EDB9514BFC1CB9228C879720442AB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_X_tFB85170D4BCC00A2F9DE1CDA29EDAAF1D31F5058 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tFF692C541FFB2465E53D19B7B1CD1BFD33F56A09 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Item_mD49E083E523F35F2C29EE824AFCAC7D1A8E9E9B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tFF692C541FFB2465E53D19B7B1CD1BFD33F56A09 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t77FAC11549DF1C5B05EECC41F5E659C12CDDBFDD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t3556D1ECDCDBA49AE97AB4664C0885BE79DD6888 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICondition2_2_tD50DC4B9F50E58A56DBDA5BD6472936576A997C3 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_X_tFB85170D4BCC00A2F9DE1CDA29EDAAF1D31F5058_ICondition2_2_Test_m1065B2C430E7B433A449B30EC2996443FE864315 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_X_t21C1F01D50C46DEA5E5A6B025E2CDF53F55F8983 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tBE1959E38A4D196837F906555F761A78829BDC02 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Item_m968AFA821FC648853C4CD3833D9AA6B0A352E878 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tBE1959E38A4D196837F906555F761A78829BDC02 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA3FDD766096857FBE04022784FCAC42E6C716E52 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t195909B15F970F02F780580FCE4700B303C8DCE0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICondition2_2_t738CE7129EE65FC87066D8994088EC93F07BC6F9 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_X_t21C1F01D50C46DEA5E5A6B025E2CDF53F55F8983_ICondition2_2_Test_m1C2F4E5BEE38C6036929C4E547643374BED9E1C2 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_RenderPipelines_Universal_2D_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_Unity_RenderPipelines_Universal_2D_Runtime_CodeGenModule = 
{
	"Unity.RenderPipelines.Universal.2D.Runtime.dll",
	1059,
	s_methodPointers,
	78,
	s_adjustorThunks,
	s_InvokerIndices,
	10,
	s_reversePInvokeIndices,
	8,
	s_rgctxIndices,
	55,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
