1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="com.unity3d.player" >
5
6    <uses-sdk android:minSdkVersion="23" />
7
8    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
8-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:3:3-77
8-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:3:20-74
9    <uses-permission android:name="android.permission.VIBRATE" />
9-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:3-64
9-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:20-61
10    <uses-permission android:name="android.permission.INTERNET" />
10-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:5:3-65
10-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:5:20-62
11
12    <uses-feature android:glEsVersion="0x00030000" />
12-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:6:3-52
12-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:6:17-49
13    <uses-feature
13-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:3-88
14        android:name="android.hardware.touchscreen"
14-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:17-60
15        android:required="false" />
15-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:61-85
16    <uses-feature
16-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:8:3-99
17        android:name="android.hardware.touchscreen.multitouch"
17-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:8:17-71
18        android:required="false" />
18-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:8:72-96
19    <uses-feature
19-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:3-108
20        android:name="android.hardware.touchscreen.multitouch.distinct"
20-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:17-80
21        android:required="false" />
21-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:81-105
22
23    <application
23-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:3-31:17
24        android:allowBackup="false"
24-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:16-43
25        android:enableOnBackInvokedCallback="false"
25-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:44-87
26        android:extractNativeLibs="true"
26-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:88-120
27        android:usesCleartextTraffic="false" >
27-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:121-157
28        <meta-data
28-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:11:5-69
29            android:name="unity.splash-mode"
29-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:11:16-48
30            android:value="0" />
30-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:11:49-66
31        <meta-data
31-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:5-74
32            android:name="unity.splash-enable"
32-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:16-50
33            android:value="True" />
33-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:51-71
34        <meta-data
34-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:5-78
35            android:name="unity.launch-fullscreen"
35-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:16-54
36            android:value="True" />
36-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:55-75
37        <meta-data
37-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:5-84
38            android:name="unity.render-outside-safearea"
38-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:16-60
39            android:value="True" />
39-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:61-81
40        <meta-data
40-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:5-81
41            android:name="notch.config"
41-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:16-43
42            android:value="portrait|landscape" />
42-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:44-78
43        <meta-data
43-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:16:5-84
44            android:name="unity.auto-report-fully-drawn"
44-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:16:16-60
45            android:value="true" />
45-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:16:61-81
46        <meta-data
46-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:5-80
47            android:name="unity.auto-set-game-state"
47-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:16-56
48            android:value="true" />
48-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:57-77
49        <meta-data
49-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:5-78
50            android:name="unity.strip-engine-code"
50-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:16-54
51            android:value="true" />
51-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:55-75
52
53        <activity
53-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:5-30:16
54            android:name="com.unity3d.player.appui.AppUIGameActivity"
54-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:289-346
55            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
55-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:15-196
56            android:exported="true"
56-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:197-220
57            android:hardwareAccelerated="false"
57-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:221-256
58            android:launchMode="singleTask"
58-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:257-288
59            android:resizeableActivity="true"
59-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:347-380
60            android:screenOrientation="userPortrait"
60-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:381-421
61            android:theme="@style/BaseUnityGameActivityTheme" >
61-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:422-471
62            <intent-filter>
62-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:20:7-23:23
63                <category android:name="android.intent.category.LAUNCHER" />
63-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:21:9-69
63-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:21:19-66
64
65                <action android:name="android.intent.action.MAIN" />
65-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:22:9-61
65-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:22:17-58
66            </intent-filter>
67
68            <meta-data
68-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:24:7-82
69                android:name="unityplayer.UnityActivity"
69-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:24:18-58
70                android:value="true" />
70-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:24:59-79
71            <meta-data
71-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:25:7-77
72                android:name="android.app.lib_name"
72-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:25:18-53
73                android:value="game" />
73-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:25:54-74
74            <meta-data
74-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:26:7-130
75                android:name="WindowManagerPreference:FreeformWindowSize"
75-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:26:18-75
76                android:value="@string/FreeformWindowSize_maximize" />
76-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:26:76-127
77            <meta-data
77-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:27:7-144
78                android:name="WindowManagerPreference:FreeformWindowOrientation"
78-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:27:18-82
79                android:value="@string/FreeformWindowOrientation_portrait" />
79-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:27:83-141
80            <meta-data
80-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:28:7-70
81                android:name="notch_support"
81-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:28:18-46
82                android:value="true" />
82-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:28:47-67
83
84            <layout
84-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:29:7-68
85                android:minHeight="300px"
85-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:29:15-40
86                android:minWidth="400px" />
86-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:29:41-65
87        </activity>
88    </application>
89
90</manifest>
