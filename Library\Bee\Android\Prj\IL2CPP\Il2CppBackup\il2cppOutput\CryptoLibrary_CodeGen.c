﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void EmbeddedAttribute__ctor_m4A6BA6A991311C1733A5989DDD2C8801329118DF (void);
extern void NullableAttribute__ctor_m74D7FC5A3FA7E687218CC40D4A4BFCEDA9E3FCAC (void);
extern void NullableAttribute__ctor_m93669698AC809E8AFC0F78CEE89BEF2D92CBF472 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m5E156A04B26425725499F2F80E412EE5462B4209 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m30DFBC0FBB378AECB23D2ABFDC0C4F916B38E14C (void);
extern void CryptoController_SetDefaultProvider_mDB4C62117E00A089BCD1AA1FB06667174432669F (void);
extern void CryptoController_Decrypt_mAC2B0DE1290A229A8C2F2C9B694361C7E597C135 (void);
extern void CryptoController_Decrypt_m8F3621981FCCFB23448C1645FE5D5971EA68340C (void);
extern void CryptoController_Encrypt_m060B6E5A27DF9F4F60367BA6FA926222F41E348D (void);
extern void CryptoController_Encrypt_m02ED37FC9ADCF02A3712895EE20DBB9FDD7FB42A (void);
extern void CryptoController_Test_m794DB032DAB7A004CF46890946CD70CB72F62E9F (void);
extern void CryptoController__cctor_mFEF4F49371394482002C5CC1D3443D11729F958D (void);
extern void CryptoFile_SetDefaultProvider_m8EBC39967352DFD7CA523B8C0880AE4ACF24E2F1 (void);
extern void CryptoFile_WriteText_m31A725172308D3B12E93DBD1095BE377BD52DEA9 (void);
extern void CryptoFile_WriteBinary_m9D2A0F79576FC9727088F3394314B88B7CF4A96C (void);
extern void CryptoFile_ReadTextAsync_m6630AA2820FA040E3E25422D738F8B421DD9E27D (void);
extern void CryptoFile_ReadBinaryAsync_m4E6B6E8E0D69092368F7448677963D0D8184287D (void);
extern void CryptoFile_WriteTextAsync_mBC411A9F808A3852D9F5AB8910EEFE386E376638 (void);
extern void CryptoFile_WriteBinaryAsync_m8249356DB54A430005368F58C245378B78490CCD (void);
extern void CryptoFile_ReadText_m1EEDB0BFC58F1645673BA5B6282D6D7A04482B57 (void);
extern void CryptoFile_ReadBinary_mFD4F09D9D3170D2881B51F8B71A3D8FD07360253 (void);
extern void CryptoFile__cctor_m14481FBAE3DBB4A71B6B30479AD2072F6D9AEEBE (void);
extern void U3CReadBinaryAsyncU3Ed__5_MoveNext_m89E9A03A5356EE2CCECE7A3070CDAC322C54B086 (void);
extern void U3CReadBinaryAsyncU3Ed__5_SetStateMachine_m173A385CBCD23DEFBA37228D2F46A9C2F7269523 (void);
extern void U3CReadTextAsyncU3Ed__4_MoveNext_m111A4EA5F7448EAA68057259F89424D22D1E92E7 (void);
extern void U3CReadTextAsyncU3Ed__4_SetStateMachine_mB9480544C979B15804DC4D787D0EE1B5C9E353F5 (void);
extern void U3CWriteBinaryAsyncU3Ed__7_MoveNext_m6486E00CD962D2D90C0A27754CF332BFABBCCE0C (void);
extern void U3CWriteBinaryAsyncU3Ed__7_SetStateMachine_m678D8A05A9C7A9D12B3EC138D8D9FE229D8BBBAE (void);
extern void U3CWriteTextAsyncU3Ed__6_MoveNext_mE5358D5F48808F831F4BB36953B56287F9D26CC8 (void);
extern void U3CWriteTextAsyncU3Ed__6_SetStateMachine_m5700E9263D60EF298ADF8D537A421F2729CAA208 (void);
extern void xxHashEncryptionOptions__ctor_m5B257C0F6CAEBF38CC97C7AF1C93D10F810D8301 (void);
extern void xxHashProvider__ctor_mB941CC61F4B19DDCDB621C7B0E282C24FFAFB02F (void);
extern void xxHashProvider_DecryptString_m3C20E5611182ED98D974E9727F33FEC4BD142DA7 (void);
extern void xxHashProvider_DecryptBinary_m9D3867B6F9B288F9DD4BE853403C62216FDDCF1B (void);
extern void xxHashProvider_EncryptString_m8615F86DAEEA8B1B638A277F485C3B38AD170134 (void);
extern void xxHashProvider_EncryptBinary_m9D1301C6A28AC5D2BEC310579A43F8A52AB02C87 (void);
extern void XOREncryptionOptions__ctor_mF3B41A0973AC921FBF88BC0D797C39B8603B2441 (void);
extern void XORProvider__ctor_m2284BECC5B83C712AA8A0A8E74E462F498D094F3 (void);
extern void XORProvider_DecryptString_mF6F7D41E3036D59173B9B03F140069D44240E3EC (void);
extern void XORProvider_DecryptBinary_mA4540A4FC7EF61E1B406EF4A97CF950EB1FCF5E6 (void);
extern void XORProvider_EncryptString_m04B1E9D2E5E1B0B5CE98D07AC894AD0CFAE8F2E0 (void);
extern void XORProvider_EncryptBinary_m76E4A5DB773424F05B04DFFF2527D86908821B3C (void);
extern void XORProvider_EncryptOrDecrypt_mF4B4E75382FF8AD93BB803C09C556FEEF3D6FE75 (void);
extern void Twofish__ctor_mD8F3CCFD567754F8646F198C6916616997CE117C (void);
extern void Twofish_CreateDecryptor_mCDFEE2E4C3F9B3E9A2A9E62E0FBFD9B8F31702EE (void);
extern void Twofish_CreateEncryptor_mA270ADC85E5E6901AAB16C1091DE5B8265C2C977 (void);
extern void Twofish_GenerateIV_mB9412756F69B618678E33BBFDE009BC0A9628D6B (void);
extern void Twofish_GenerateKey_m9A701EA6501E5C7A3F168B6FD021AF922FA00B7F (void);
extern void Twofish_get_Mode_m37243163EF4BB1181CFEBD3C04F044C63FDB5F39 (void);
extern void Twofish_set_Mode_mF3EAEB1D44C5F63FC1914E8892C3E663C2D5AE6A (void);
extern void Twofish_get_Padding_m0F5F9DCB46C5E3F81C3A850F39F268D948816E28 (void);
extern void Twofish_set_Padding_m9A96D44D4AA043776DF656AE5EA54EE665884F4A (void);
extern void TwofishTransform__ctor_m7031C917E98A3B8D8C718D22D27339069D7A6D22 (void);
extern void TwofishTransform_get_CanReuseTransform_m0D9D434BF07084965B03F92DE92748E8A6BE8F75 (void);
extern void TwofishTransform_get_CanTransformMultipleBlocks_mD8D274E5E433E26BF903F335A8F05228EF7BF264 (void);
extern void TwofishTransform_get_InputBlockSize_m96833A56CF463B2912E80DA25A980D795EBD3161 (void);
extern void TwofishTransform_get_OutputBlockSize_mEB3F02A8BA822023B49CF2157D74FDEB22C73B7F (void);
extern void TwofishTransform_Dispose_m6A5C856A04C41AC572A05C02D9744EA2CA8EE767 (void);
extern void TwofishTransform_Dispose_mFA00C5A3FEF099286EB824B5FAA16A0FFC8D0E06 (void);
extern void TwofishTransform_TransformBlock_mC94E079C49FFC72F64EA2CAF6B3860A448159FF7 (void);
extern void TwofishTransform_TransformFinalBlock_m89DC74A80F4FB3692C0219F759CA260B830D150C (void);
extern void TwofishTransform_RemovePadding_m6E15E2738BA3496755786C7D8B2F1EC2420D5244 (void);
extern void TwofishTransform_ReKey_m0887B79CFDED35D5C3B1902A22DF94501DAA08AC (void);
extern void TwofishTransform_BlockEncrypt_mFB308C526E8F8218C6BA4235C44219EBD328B4DB (void);
extern void TwofishTransform_BlockDecrypt_m6A229E17D987EE91713102FD5F967B0F9715C6AD (void);
extern void TwofishTransform_F32_m9EFF6028D0FC2893D84C4E5B5753D32682774EF8 (void);
extern void TwofishTransform_BuildMds_m810D4F886726EC61BE4988AC8E2D9FEAABFCCC72 (void);
extern void TwofishTransform_ReedSolomonMdsEncode_mFFE78BA214B362F621D5DC091DFC391C18005CEC (void);
extern void TwofishTransform_Mul_X_m41B809E49E74C09A241F4E2ACACBEC11762D67F4 (void);
extern void TwofishTransform_Mul_Y_m578D5316C2390A44B3E9DA9249A7301A5904A8AE (void);
extern void TwofishTransform_Mx_X_m6B784D594E3C57CEADB7078A2DF96D45608F6480 (void);
extern void TwofishTransform_Mx_Y_m54B8E94B440FE323679EA8594B7492DCF9D18486 (void);
extern void TwofishTransform_LFSR1_mFD39191323FCB9D6A3CF01B16D4F3310F7291A1A (void);
extern void TwofishTransform_LFSR2_mD759A1C942AE30EA93F2C11D115A8FB02221E2FD (void);
extern void TwofishTransform__cctor_m98434B3ABD4CFD11C2BDA567487EB8B8D6C456B3 (void);
extern void DWord__ctor_mACC837ACE0348FC302899B8BCD930E070D4CD4FD (void);
extern void DWord__ctor_mB70C2651AE6EECFFD45F58014DB08B37285E8769 (void);
extern void DWord_op_Explicit_mA92E44909A43A1055876D77D9C22800AB16BD16B (void);
extern void DWord_op_Explicit_m34FE99E5A7A694A8C459D0AA5BA1DE7A228CA731 (void);
extern void DWord_op_Explicit_mA5879E4C36CB423D28EDCF6C73CA8D4E27A53DF3 (void);
extern void DWord_op_Addition_mAD994E2BA864C451C747625A8AE18F7FC8AC46C9 (void);
extern void DWord_op_Multiply_m7BE5F7EB6DE8C44562E2C6A929535FC13F50C4D2 (void);
extern void DWord_op_BitwiseOr_mDD0958FBB91D66DEFE6B9C05354E7C7ECBB1A240 (void);
extern void DWord_op_ExclusiveOr_m8F83B81960075E52CA624B7C7E49B81C666CDA42 (void);
extern void DWord_op_LeftShift_m4D84BBE63465498FDF7D773EC19DCDBF8FD9DAF1 (void);
extern void DWord_op_RightShift_m65BD364EDDAD86CFD6E9FDC59ED20A13B32DB267 (void);
extern void DWord_RotateLeft_m3CBDE2EBF04EAD8843C14AD78C6A2E50455FEDFF (void);
extern void DWord_RotateRight_m4BA6508C728180BA843E3139E4204761DE33D8EC (void);
extern void TwofishEncryptionOptions__ctor_m7B1FAC9BDABBE13C28A80B0A1972B9BF8F9A9C0D (void);
extern void TwofishProvider__ctor_m3F7E3394C47D449351A866B28744148DA6FE9DE2 (void);
extern void TwofishProvider_DecryptString_m0CE54D7B75DC81E3CFE8910F1F1BA940DEEA7B80 (void);
extern void TwofishProvider_DecryptBinary_m7156ACD7BFF628808DEB86421888D0E37584450C (void);
extern void TwofishProvider_EncryptString_m5A0019623B092A0232D63DCCA51506B66A4AB4CF (void);
extern void TwofishProvider_EncryptBinary_mA0A42B386947025A88F17F0E6A128E73DA12788C (void);
extern void TripleDESEncryptionOptions__ctor_m2AB5960D7F55BE9AB13A81B38F76124F256537CC (void);
extern void TripleDESProvider__ctor_m6876FB8CA6C0243DB85F4214739ECF328BB37F01 (void);
extern void TripleDESProvider_DecryptString_mE4E17B1A0F730C5449C16002E72D6646AFCC2479 (void);
extern void TripleDESProvider_DecryptBinary_m25E521DCBEC78AAFD5C2F0BEF29CA9763F0B27CA (void);
extern void TripleDESProvider_EncryptString_m892A56654054A8B5B93620A3827308E3FB760562 (void);
extern void TripleDESProvider_EncryptBinary_m5BDCEB24C6BEA3BE632B56202E7A3AED285E9F17 (void);
extern void SHA1Provider__ctor_mFCF91FD765C90DD228290D75FD7B7FC0F1933F9C (void);
extern void SHA1Provider_DecryptString_m7F0476489653AF7B84526DAD5E5116E3280775F3 (void);
extern void SHA1Provider_DecryptBinary_mE743F90592EF747F3182658FE3871B45662476ED (void);
extern void SHA1Provider_EncryptString_m3452B796764FB74E5E80C720F7BE01109CC27A38 (void);
extern void SHA1Provider_EncryptBinary_m8C226E571744D4DBE284F5EED312229148646DA3 (void);
extern void SHA256Provider__ctor_m7601FA0E460D7B13A9D8188A3C3FEBAF02E7FB88 (void);
extern void SHA256Provider_DecryptString_m79E2C415C93BEEBEBB4C3DEC562139A92184D7E3 (void);
extern void SHA256Provider_DecryptBinary_m50B99CEFEE4A78C5FA8F159C3548C0454E33B328 (void);
extern void SHA256Provider_EncryptString_mD24142EABA6C2B5C127F59F22353B2DE9E527126 (void);
extern void SHA256Provider_EncryptBinary_mDBAC1DA31D1132D87F929EBC964CC4B14A913EB3 (void);
extern void SHA512Provider__ctor_mD5B922F70CF85BF471620C3C63474436DDFDCC11 (void);
extern void SHA512Provider_DecryptString_m6487B0563785AF75CA85ABA89123E8A5818D05A2 (void);
extern void SHA512Provider_DecryptBinary_mE7BF962938B570833DDD48C0823B7F962A712F89 (void);
extern void SHA512Provider_EncryptString_mA74A99683BADCB0784727CEAD72B239043BC29D4 (void);
extern void SHA512Provider_EncryptBinary_m409EB20AEBBC1E59629E8BBB1BFB9609A91FFEE3 (void);
extern void SHAEncryptionOptions__ctor_mD08971210EC8B0B7AC61AF7CDA729AF8985E2714 (void);
extern void RSAEncryptionOptions__ctor_m280FFFA63B62469393B5DB7298344B379A60573F (void);
extern void RSAProvider__ctor_m67632A64B89A11E8D6B0EFA4BB075729C5031A6D (void);
extern void RSAProvider_DecryptString_m89CFAAAA9F979206B5AAD664E5A167C60524D158 (void);
extern void RSAProvider_DecryptBinary_mBB320030A6C61F9EC3A29ED4AC1DCE627082B280 (void);
extern void RSAProvider_EncryptString_m38DFE6FA3F1C4AB0B7C058276EBE26EE944DAB78 (void);
extern void RSAProvider_EncryptBinary_m5DF22D4C1FF7B70C0D2775677116ED877BCE7428 (void);
extern void RSAProvider_GenerateKeyPair_mD312742D012735122287B51A38DAE43E54E00757 (void);
extern void RIPEMD160__ctor_mCD75024904E24F668702045629E09DB169A09D0A (void);
extern void RIPEMD160_Create_mB03030EA7621AAAFB7DBE3F51492FC31BF02FE0E (void);
extern void RIPEMD160_Create_m3244C9A2ADFF97EABF59107C781FBEFF5CF9A23A (void);
extern void RIPEMD160Managed_ReadUInt32_m58A635D62FBAAB47121BFE39EB514A0AD6AC5002 (void);
extern void RIPEMD160Managed_RotateLeft_m7EDEC93A2855A5F14B94FC4448E52C76B067161C (void);
extern void RIPEMD160Managed_F_mF727CE59DD808C001FE38A15BF6D734A97C8EC69 (void);
extern void RIPEMD160Managed_G_mD3C22766B92DFF080ACBFA80D7D52B46C6B1E4A5 (void);
extern void RIPEMD160Managed_H_m20F3F54AE6EF56FC1E9EFB5E0521E7857898FDF0 (void);
extern void RIPEMD160Managed_I_m8800E3D9EFD037A2B557FDE57084D0842CCE9776 (void);
extern void RIPEMD160Managed_J_mC01CE9613EA666502A1F3EBF1FF386F9A064D73A (void);
extern void RIPEMD160Managed_FF_mB0CF095AD43CDC142A526D6BF05BA55158BA9DCA (void);
extern void RIPEMD160Managed_GG_m476C7C80D200F6FF2C110E708453C64375367C4C (void);
extern void RIPEMD160Managed_HH_m0B9E88346B587812A0CDA8757CD97F46BD5EA4FA (void);
extern void RIPEMD160Managed_II_mAEBA6A6FE0DF4D5FDA08CEEB1B74740AF1C0F2D5 (void);
extern void RIPEMD160Managed_JJ_m96E58BBEE4C5F529188B66C7B4EE1064FEAEAB56 (void);
extern void RIPEMD160Managed_FFF_mFE5F87BC225989B14596268A706E9395F45957A1 (void);
extern void RIPEMD160Managed_GGG_m8C0A1CE28CA1306F55D42BA17712F38125E46154 (void);
extern void RIPEMD160Managed_HHH_m4E7E08C1A20E8AF7D0C6284A0D96490BD9800F80 (void);
extern void RIPEMD160Managed_III_mC08E2B7A6BFDEEDC4AA6B059E1DCD590D8501E2B (void);
extern void RIPEMD160Managed_JJJ_mBD3E4AD3C5D52A7730BF38FC33447D8BDB15F387 (void);
extern void RIPEMD160Managed_MDinit_m16F04CA058BED84D6C8D482A148C328A5A5B743B (void);
extern void RIPEMD160Managed_compress_m983DCE1E9D9532D087417694AAE1765D188C3700 (void);
extern void RIPEMD160Managed_MDfinish_m5BC217470E2D23522CB1219FA55120507CF02B18 (void);
extern void RIPEMD160Managed__ctor_mA711F6097C8FE4D0ED0A40CE76B2341318D8DC5B (void);
extern void RIPEMD160Managed_HashCore_mEFFF45C2E7FA8F9D01660241543E27B85CB524D0 (void);
extern void RIPEMD160Managed_HashFinal_mDC86D641B11C3FE8DF7FD3C9F37F95603C08DEC3 (void);
extern void RIPEMD160Managed_Initialize_m513545C5D7B8B5BCA25E17D80425C67877883155 (void);
extern void RIPEMD160Managed__cctor_m9DB4784A89059A11CFBB2F662A03C1EA0F13ADDD (void);
extern void RipemdEncryptionOptions__ctor_mDE658460C065CEBDC8B0C19583E212D819592A12 (void);
extern void RipemdProvider__ctor_m9AA8290294B90C8AB73DE9BB9231A50E688FC4B3 (void);
extern void RipemdProvider_DecryptString_m85114AAB0AA8C52270D284F9213E65930859B19A (void);
extern void RipemdProvider_DecryptBinary_m05DDF4142A817D6870E7D1DDBB1BF1D9D1F598BB (void);
extern void RipemdProvider_EncryptString_mB366B416B9DE1C2971BCA91EC7E5BBD24330E29A (void);
extern void RipemdProvider_EncryptBinary_mDF8B1B2B4BDD99660F46A3F15EB82DAD475ADD0E (void);
extern void PBKDF2EncryptionOptions__ctor_m1DEA921825F1F347BDBBB2457F2B75FE2616B968 (void);
extern void PBKDF2Provider__ctor_mFBFD82ABDDE8533C618A06F0BF6421E9D3247EA4 (void);
extern void PBKDF2Provider_DecryptString_mB960DDED90A9D016A66DEE2CCB46CFD3615297F8 (void);
extern void PBKDF2Provider_DecryptBinary_m0D6E358167EBF68A15E06579805E6EAC232B86EE (void);
extern void PBKDF2Provider_EncryptString_m8455B1E022D5BF77B1AAC56AEA8276928AF595C6 (void);
extern void PBKDF2Provider_EncryptBinary_m47553A05107A2F08B0D6DDE148D94E2C4B60F4CE (void);
extern void PBKDF2Provider_GenerateSalt_mB06D50F185113460FA1C79FC70D9A8FEF0E296D9 (void);
extern void PBKDF2Provider_calculateHash_m16FEFC1DE5D753ADC099AC9E8A7C55239E0DB6D1 (void);
extern void PBKDF2Provider_calculateHash_m6FC6792D23AEF2BFC741DFAD2A6E395CC5DB2DA3 (void);
extern void PBKDF2Provider_expandSalt_m1DFF821191BBFDA8DD63120936BF2C1BED86DAE9 (void);
extern void MD5EncryptionOptions__ctor_m1CB8BFA93DE7A55CD6302518A04EB7CC9E6F1D92 (void);
extern void MD5Provider__ctor_mC7E635F90DE8BFD1111B5ACADD3ED228382B992F (void);
extern void MD5Provider_DecryptString_mCAD9438B040068626B940259AA957BEBE6361BE1 (void);
extern void MD5Provider_DecryptBinary_m446AF770A71224A4000BA91965629ECD2FC6DE33 (void);
extern void MD5Provider_EncryptString_m3B5EE8BF4666C10E837462B8AE8D803ABDD2E99E (void);
extern void MD5Provider_EncryptBinary_m3A6E019A67CD364AE70E54EC610A6BC55FDF96C4 (void);
extern void DESEncryptionOptions__ctor_m7AA07EF86BC6B5B657878236C091B48DD83E52B3 (void);
extern void DESProvider__ctor_m3C68F115A5CEC37B49439EA9C04C98713D94E1DF (void);
extern void DESProvider_DecryptString_mA0B6549B62EC07A2ED8B00BD1C0DB8CB55074EC5 (void);
extern void DESProvider_DecryptBinary_mFA23CF3758FC27791C1F07BAF9C0B870BBADEB66 (void);
extern void DESProvider_EncryptString_m142BCE12E551172548F4EE82B843AD0F6EF717BA (void);
extern void DESProvider_EncryptBinary_m504C81E1E78963FF3A5F5D6A1D3F19903FF25B3A (void);
extern void Crc32__ctor_m1567E6547204A5500FE9128B2C2508EE676C161F (void);
extern void Crc32__ctor_mED9DDD9C1437D3F83F62E2D175FFC34B4C661D95 (void);
extern void Crc32_Initialize_m2582811B8F89EE3DAC1DAC4E6A1879A4A3A383E4 (void);
extern void Crc32_HashCore_m8166F804AE3F86C6B26CBB1F21DA61AFAED7A3B6 (void);
extern void Crc32_HashFinal_m22B17274F0AF113AC5D11936DDE2720E853A5812 (void);
extern void Crc32_get_HashSize_m6E54846324F6BDAD647D2F5741A2B8EBABCBB6C7 (void);
extern void Crc32_Compute_mDD284BB5A69F0C760C2225606533BDE9717EE3DB (void);
extern void Crc32_Compute_m8EF0438BEA526E2A29ABB96A5D4F396CA7AFD686 (void);
extern void Crc32_Compute_mCEECCB8AC113BD11F532BC7161592CD3B88D6745 (void);
extern void Crc32_InitializeTable_m25DADE1291246AFD578BEB4B733A471E63DA9A9E (void);
extern void Crc32_CalculateHash_m5EB9DC247C2DD7CE32D7CB2C7C52DEF1CF334111 (void);
extern void Crc32_UInt32ToBigEndianBytes_mD3CA034E5FDB8A3B30D3C6267C2A2192113C068F (void);
extern void Crc32_Get_m045F51DEFEE4F40C3A0803DCA60C9D0B29D5E48E (void);
extern void CRC32EncryptionOptions__ctor_m76E3732ADB1C7AEB21EFAE87D06EE773D6736A34 (void);
extern void CRC32Provider__ctor_m99E5C7B1C431703D82F4A296F3FBBF1293C197C2 (void);
extern void CRC32Provider_DecryptString_m672DCC3BA29A26DA25688D272E8DFEED53D0F809 (void);
extern void CRC32Provider_DecryptBinary_m32AA463F4B41A72876ECA32E8813C62C591E382B (void);
extern void CRC32Provider_EncryptString_mD4E32BCCA0E6E99F15DE843B3AEE8C27384236EE (void);
extern void CRC32Provider_EncryptBinary_m1F0657F67BF524CD568375D4D98BEE49D97BADD6 (void);
extern void BlowfishEncryptionOptions__ctor_m3AE38AAB9A40BFC3BB1F7BBBB95012BF642C5204 (void);
extern void BlowfishProvider__ctor_mA5D52A82A41DE9DFAC5D2E433F44DB54EB9386FE (void);
extern void BlowfishProvider_F_m634A6687262B0C2BB88EA7D244F9C0D43686C00A (void);
extern void BlowfishProvider_Encipher_m73252884E0AF8C82A874258EFC7559C27EBC457F (void);
extern void BlowfishProvider_Encipher_m64A3D5D975AE3161F410E0B422481C0C8EFD1B7D (void);
extern void BlowfishProvider_Encipher_m445C0B681E3EC3CE9290678E279A88AEBB90FEF4 (void);
extern void BlowfishProvider_Decipher_mF8351E93161CF3BB93592D55188BBFBD9EED975A (void);
extern void BlowfishProvider_Decipher_m56F91C06CCA9FFB33DD500F0AED8EF6A7AB5C50B (void);
extern void BlowfishProvider_Decipher_m1FCB7153C4A795483B30D50F65EEA29A5D5CA9AD (void);
extern void BlowfishProvider_DecryptString_m8E5257E0714D3615B88E27E37A1E108DA7059A3B (void);
extern void BlowfishProvider_DecryptBinary_m34B2A6559D1360CB11E61065C1C7C24A3A9E9209 (void);
extern void BlowfishProvider_EncryptString_m295D0D84CC583139261D4E49076AB06BAC1104DC (void);
extern void BlowfishProvider_EncryptBinary_m55E837E07F76FBC754ECD8ACAE97AE4D92D8721A (void);
extern void BlowfishProvider__cctor_m86197176F99DC46B060FE9365A963D9E379A40DA (void);
extern void Base64EncryptionOptions__ctor_m755899C4F6C82D2093341FBBF129F36589E33116 (void);
extern void Base64Provider__ctor_m737954AD852BBC6B9B294B71DA373996D909AF23 (void);
extern void Base64Provider_DecryptString_m72CC12B5B2305742758C4293F7D9B4B7D0B2FAC4 (void);
extern void Base64Provider_DecryptBinary_m8523E2FE02D83A2E1890378E8E8E71F1FCB322A3 (void);
extern void Base64Provider_EncryptString_m6ACDD1ADFBA1D8046BD1474254C71B44BFB0E626 (void);
extern void Base64Provider_EncryptBinary_mE619B3C839CBB802655B09CBF66B918354183C97 (void);
extern void AESEncryptionOptions__ctor_m52905147E0D2BE3C196537D5F057111005274489 (void);
extern void AESProvider__ctor_m5467A8CA2EBA8F0E510F6E33B3FBCB3A8517C588 (void);
extern void AESProvider_DecryptString_m4825670F9D9E33D216551FD9250292577117193B (void);
extern void AESProvider_DecryptBinary_m9899E579AA9093E8F264F1C1FD3EEE042A06E588 (void);
extern void AESProvider_EncryptString_mFA893EB9112131B923A28E5651194B7EF9B17538 (void);
extern void AESProvider_EncryptBinary_m00BD26F0D90769C840DB1CB725943D705D020852 (void);
extern void AESProvider_get_setupRijndaelManaged_mEBFF1ECC585EDB16300FC149E9BA5D31AFF188B1 (void);
static Il2CppMethodPointer s_methodPointers[229] = 
{
	EmbeddedAttribute__ctor_m4A6BA6A991311C1733A5989DDD2C8801329118DF,
	NullableAttribute__ctor_m74D7FC5A3FA7E687218CC40D4A4BFCEDA9E3FCAC,
	NullableAttribute__ctor_m93669698AC809E8AFC0F78CEE89BEF2D92CBF472,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m5E156A04B26425725499F2F80E412EE5462B4209,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m30DFBC0FBB378AECB23D2ABFDC0C4F916B38E14C,
	CryptoController_SetDefaultProvider_mDB4C62117E00A089BCD1AA1FB06667174432669F,
	CryptoController_Decrypt_mAC2B0DE1290A229A8C2F2C9B694361C7E597C135,
	CryptoController_Decrypt_m8F3621981FCCFB23448C1645FE5D5971EA68340C,
	CryptoController_Encrypt_m060B6E5A27DF9F4F60367BA6FA926222F41E348D,
	CryptoController_Encrypt_m02ED37FC9ADCF02A3712895EE20DBB9FDD7FB42A,
	CryptoController_Test_m794DB032DAB7A004CF46890946CD70CB72F62E9F,
	CryptoController__cctor_mFEF4F49371394482002C5CC1D3443D11729F958D,
	CryptoFile_SetDefaultProvider_m8EBC39967352DFD7CA523B8C0880AE4ACF24E2F1,
	CryptoFile_WriteText_m31A725172308D3B12E93DBD1095BE377BD52DEA9,
	CryptoFile_WriteBinary_m9D2A0F79576FC9727088F3394314B88B7CF4A96C,
	CryptoFile_ReadTextAsync_m6630AA2820FA040E3E25422D738F8B421DD9E27D,
	CryptoFile_ReadBinaryAsync_m4E6B6E8E0D69092368F7448677963D0D8184287D,
	CryptoFile_WriteTextAsync_mBC411A9F808A3852D9F5AB8910EEFE386E376638,
	CryptoFile_WriteBinaryAsync_m8249356DB54A430005368F58C245378B78490CCD,
	CryptoFile_ReadText_m1EEDB0BFC58F1645673BA5B6282D6D7A04482B57,
	CryptoFile_ReadBinary_mFD4F09D9D3170D2881B51F8B71A3D8FD07360253,
	CryptoFile__cctor_m14481FBAE3DBB4A71B6B30479AD2072F6D9AEEBE,
	U3CReadBinaryAsyncU3Ed__5_MoveNext_m89E9A03A5356EE2CCECE7A3070CDAC322C54B086,
	U3CReadBinaryAsyncU3Ed__5_SetStateMachine_m173A385CBCD23DEFBA37228D2F46A9C2F7269523,
	U3CReadTextAsyncU3Ed__4_MoveNext_m111A4EA5F7448EAA68057259F89424D22D1E92E7,
	U3CReadTextAsyncU3Ed__4_SetStateMachine_mB9480544C979B15804DC4D787D0EE1B5C9E353F5,
	U3CWriteBinaryAsyncU3Ed__7_MoveNext_m6486E00CD962D2D90C0A27754CF332BFABBCCE0C,
	U3CWriteBinaryAsyncU3Ed__7_SetStateMachine_m678D8A05A9C7A9D12B3EC138D8D9FE229D8BBBAE,
	U3CWriteTextAsyncU3Ed__6_MoveNext_mE5358D5F48808F831F4BB36953B56287F9D26CC8,
	U3CWriteTextAsyncU3Ed__6_SetStateMachine_m5700E9263D60EF298ADF8D537A421F2729CAA208,
	xxHashEncryptionOptions__ctor_m5B257C0F6CAEBF38CC97C7AF1C93D10F810D8301,
	xxHashProvider__ctor_mB941CC61F4B19DDCDB621C7B0E282C24FFAFB02F,
	xxHashProvider_DecryptString_m3C20E5611182ED98D974E9727F33FEC4BD142DA7,
	xxHashProvider_DecryptBinary_m9D3867B6F9B288F9DD4BE853403C62216FDDCF1B,
	xxHashProvider_EncryptString_m8615F86DAEEA8B1B638A277F485C3B38AD170134,
	xxHashProvider_EncryptBinary_m9D1301C6A28AC5D2BEC310579A43F8A52AB02C87,
	XOREncryptionOptions__ctor_mF3B41A0973AC921FBF88BC0D797C39B8603B2441,
	XORProvider__ctor_m2284BECC5B83C712AA8A0A8E74E462F498D094F3,
	XORProvider_DecryptString_mF6F7D41E3036D59173B9B03F140069D44240E3EC,
	XORProvider_DecryptBinary_mA4540A4FC7EF61E1B406EF4A97CF950EB1FCF5E6,
	XORProvider_EncryptString_m04B1E9D2E5E1B0B5CE98D07AC894AD0CFAE8F2E0,
	XORProvider_EncryptBinary_m76E4A5DB773424F05B04DFFF2527D86908821B3C,
	XORProvider_EncryptOrDecrypt_mF4B4E75382FF8AD93BB803C09C556FEEF3D6FE75,
	Twofish__ctor_mD8F3CCFD567754F8646F198C6916616997CE117C,
	Twofish_CreateDecryptor_mCDFEE2E4C3F9B3E9A2A9E62E0FBFD9B8F31702EE,
	Twofish_CreateEncryptor_mA270ADC85E5E6901AAB16C1091DE5B8265C2C977,
	Twofish_GenerateIV_mB9412756F69B618678E33BBFDE009BC0A9628D6B,
	Twofish_GenerateKey_m9A701EA6501E5C7A3F168B6FD021AF922FA00B7F,
	Twofish_get_Mode_m37243163EF4BB1181CFEBD3C04F044C63FDB5F39,
	Twofish_set_Mode_mF3EAEB1D44C5F63FC1914E8892C3E663C2D5AE6A,
	Twofish_get_Padding_m0F5F9DCB46C5E3F81C3A850F39F268D948816E28,
	Twofish_set_Padding_m9A96D44D4AA043776DF656AE5EA54EE665884F4A,
	TwofishTransform__ctor_m7031C917E98A3B8D8C718D22D27339069D7A6D22,
	TwofishTransform_get_CanReuseTransform_m0D9D434BF07084965B03F92DE92748E8A6BE8F75,
	TwofishTransform_get_CanTransformMultipleBlocks_mD8D274E5E433E26BF903F335A8F05228EF7BF264,
	TwofishTransform_get_InputBlockSize_m96833A56CF463B2912E80DA25A980D795EBD3161,
	TwofishTransform_get_OutputBlockSize_mEB3F02A8BA822023B49CF2157D74FDEB22C73B7F,
	TwofishTransform_Dispose_m6A5C856A04C41AC572A05C02D9744EA2CA8EE767,
	TwofishTransform_Dispose_mFA00C5A3FEF099286EB824B5FAA16A0FFC8D0E06,
	TwofishTransform_TransformBlock_mC94E079C49FFC72F64EA2CAF6B3860A448159FF7,
	TwofishTransform_TransformFinalBlock_m89DC74A80F4FB3692C0219F759CA260B830D150C,
	TwofishTransform_RemovePadding_m6E15E2738BA3496755786C7D8B2F1EC2420D5244,
	TwofishTransform_ReKey_m0887B79CFDED35D5C3B1902A22DF94501DAA08AC,
	TwofishTransform_BlockEncrypt_mFB308C526E8F8218C6BA4235C44219EBD328B4DB,
	TwofishTransform_BlockDecrypt_m6A229E17D987EE91713102FD5F967B0F9715C6AD,
	TwofishTransform_F32_m9EFF6028D0FC2893D84C4E5B5753D32682774EF8,
	TwofishTransform_BuildMds_m810D4F886726EC61BE4988AC8E2D9FEAABFCCC72,
	TwofishTransform_ReedSolomonMdsEncode_mFFE78BA214B362F621D5DC091DFC391C18005CEC,
	TwofishTransform_Mul_X_m41B809E49E74C09A241F4E2ACACBEC11762D67F4,
	TwofishTransform_Mul_Y_m578D5316C2390A44B3E9DA9249A7301A5904A8AE,
	TwofishTransform_Mx_X_m6B784D594E3C57CEADB7078A2DF96D45608F6480,
	TwofishTransform_Mx_Y_m54B8E94B440FE323679EA8594B7492DCF9D18486,
	TwofishTransform_LFSR1_mFD39191323FCB9D6A3CF01B16D4F3310F7291A1A,
	TwofishTransform_LFSR2_mD759A1C942AE30EA93F2C11D115A8FB02221E2FD,
	TwofishTransform__cctor_m98434B3ABD4CFD11C2BDA567487EB8B8D6C456B3,
	DWord__ctor_mACC837ACE0348FC302899B8BCD930E070D4CD4FD,
	DWord__ctor_mB70C2651AE6EECFFD45F58014DB08B37285E8769,
	DWord_op_Explicit_mA92E44909A43A1055876D77D9C22800AB16BD16B,
	DWord_op_Explicit_m34FE99E5A7A694A8C459D0AA5BA1DE7A228CA731,
	DWord_op_Explicit_mA5879E4C36CB423D28EDCF6C73CA8D4E27A53DF3,
	DWord_op_Addition_mAD994E2BA864C451C747625A8AE18F7FC8AC46C9,
	DWord_op_Multiply_m7BE5F7EB6DE8C44562E2C6A929535FC13F50C4D2,
	DWord_op_BitwiseOr_mDD0958FBB91D66DEFE6B9C05354E7C7ECBB1A240,
	DWord_op_ExclusiveOr_m8F83B81960075E52CA624B7C7E49B81C666CDA42,
	DWord_op_LeftShift_m4D84BBE63465498FDF7D773EC19DCDBF8FD9DAF1,
	DWord_op_RightShift_m65BD364EDDAD86CFD6E9FDC59ED20A13B32DB267,
	DWord_RotateLeft_m3CBDE2EBF04EAD8843C14AD78C6A2E50455FEDFF,
	DWord_RotateRight_m4BA6508C728180BA843E3139E4204761DE33D8EC,
	TwofishEncryptionOptions__ctor_m7B1FAC9BDABBE13C28A80B0A1972B9BF8F9A9C0D,
	TwofishProvider__ctor_m3F7E3394C47D449351A866B28744148DA6FE9DE2,
	TwofishProvider_DecryptString_m0CE54D7B75DC81E3CFE8910F1F1BA940DEEA7B80,
	TwofishProvider_DecryptBinary_m7156ACD7BFF628808DEB86421888D0E37584450C,
	TwofishProvider_EncryptString_m5A0019623B092A0232D63DCCA51506B66A4AB4CF,
	TwofishProvider_EncryptBinary_mA0A42B386947025A88F17F0E6A128E73DA12788C,
	TripleDESEncryptionOptions__ctor_m2AB5960D7F55BE9AB13A81B38F76124F256537CC,
	TripleDESProvider__ctor_m6876FB8CA6C0243DB85F4214739ECF328BB37F01,
	TripleDESProvider_DecryptString_mE4E17B1A0F730C5449C16002E72D6646AFCC2479,
	TripleDESProvider_DecryptBinary_m25E521DCBEC78AAFD5C2F0BEF29CA9763F0B27CA,
	TripleDESProvider_EncryptString_m892A56654054A8B5B93620A3827308E3FB760562,
	TripleDESProvider_EncryptBinary_m5BDCEB24C6BEA3BE632B56202E7A3AED285E9F17,
	SHA1Provider__ctor_mFCF91FD765C90DD228290D75FD7B7FC0F1933F9C,
	SHA1Provider_DecryptString_m7F0476489653AF7B84526DAD5E5116E3280775F3,
	SHA1Provider_DecryptBinary_mE743F90592EF747F3182658FE3871B45662476ED,
	SHA1Provider_EncryptString_m3452B796764FB74E5E80C720F7BE01109CC27A38,
	SHA1Provider_EncryptBinary_m8C226E571744D4DBE284F5EED312229148646DA3,
	SHA256Provider__ctor_m7601FA0E460D7B13A9D8188A3C3FEBAF02E7FB88,
	SHA256Provider_DecryptString_m79E2C415C93BEEBEBB4C3DEC562139A92184D7E3,
	SHA256Provider_DecryptBinary_m50B99CEFEE4A78C5FA8F159C3548C0454E33B328,
	SHA256Provider_EncryptString_mD24142EABA6C2B5C127F59F22353B2DE9E527126,
	SHA256Provider_EncryptBinary_mDBAC1DA31D1132D87F929EBC964CC4B14A913EB3,
	SHA512Provider__ctor_mD5B922F70CF85BF471620C3C63474436DDFDCC11,
	SHA512Provider_DecryptString_m6487B0563785AF75CA85ABA89123E8A5818D05A2,
	SHA512Provider_DecryptBinary_mE7BF962938B570833DDD48C0823B7F962A712F89,
	SHA512Provider_EncryptString_mA74A99683BADCB0784727CEAD72B239043BC29D4,
	SHA512Provider_EncryptBinary_m409EB20AEBBC1E59629E8BBB1BFB9609A91FFEE3,
	SHAEncryptionOptions__ctor_mD08971210EC8B0B7AC61AF7CDA729AF8985E2714,
	RSAEncryptionOptions__ctor_m280FFFA63B62469393B5DB7298344B379A60573F,
	RSAProvider__ctor_m67632A64B89A11E8D6B0EFA4BB075729C5031A6D,
	RSAProvider_DecryptString_m89CFAAAA9F979206B5AAD664E5A167C60524D158,
	RSAProvider_DecryptBinary_mBB320030A6C61F9EC3A29ED4AC1DCE627082B280,
	RSAProvider_EncryptString_m38DFE6FA3F1C4AB0B7C058276EBE26EE944DAB78,
	RSAProvider_EncryptBinary_m5DF22D4C1FF7B70C0D2775677116ED877BCE7428,
	RSAProvider_GenerateKeyPair_mD312742D012735122287B51A38DAE43E54E00757,
	RIPEMD160__ctor_mCD75024904E24F668702045629E09DB169A09D0A,
	RIPEMD160_Create_mB03030EA7621AAAFB7DBE3F51492FC31BF02FE0E,
	RIPEMD160_Create_m3244C9A2ADFF97EABF59107C781FBEFF5CF9A23A,
	RIPEMD160Managed_ReadUInt32_m58A635D62FBAAB47121BFE39EB514A0AD6AC5002,
	RIPEMD160Managed_RotateLeft_m7EDEC93A2855A5F14B94FC4448E52C76B067161C,
	RIPEMD160Managed_F_mF727CE59DD808C001FE38A15BF6D734A97C8EC69,
	RIPEMD160Managed_G_mD3C22766B92DFF080ACBFA80D7D52B46C6B1E4A5,
	RIPEMD160Managed_H_m20F3F54AE6EF56FC1E9EFB5E0521E7857898FDF0,
	RIPEMD160Managed_I_m8800E3D9EFD037A2B557FDE57084D0842CCE9776,
	RIPEMD160Managed_J_mC01CE9613EA666502A1F3EBF1FF386F9A064D73A,
	RIPEMD160Managed_FF_mB0CF095AD43CDC142A526D6BF05BA55158BA9DCA,
	RIPEMD160Managed_GG_m476C7C80D200F6FF2C110E708453C64375367C4C,
	RIPEMD160Managed_HH_m0B9E88346B587812A0CDA8757CD97F46BD5EA4FA,
	RIPEMD160Managed_II_mAEBA6A6FE0DF4D5FDA08CEEB1B74740AF1C0F2D5,
	RIPEMD160Managed_JJ_m96E58BBEE4C5F529188B66C7B4EE1064FEAEAB56,
	RIPEMD160Managed_FFF_mFE5F87BC225989B14596268A706E9395F45957A1,
	RIPEMD160Managed_GGG_m8C0A1CE28CA1306F55D42BA17712F38125E46154,
	RIPEMD160Managed_HHH_m4E7E08C1A20E8AF7D0C6284A0D96490BD9800F80,
	RIPEMD160Managed_III_mC08E2B7A6BFDEEDC4AA6B059E1DCD590D8501E2B,
	RIPEMD160Managed_JJJ_mBD3E4AD3C5D52A7730BF38FC33447D8BDB15F387,
	RIPEMD160Managed_MDinit_m16F04CA058BED84D6C8D482A148C328A5A5B743B,
	RIPEMD160Managed_compress_m983DCE1E9D9532D087417694AAE1765D188C3700,
	RIPEMD160Managed_MDfinish_m5BC217470E2D23522CB1219FA55120507CF02B18,
	RIPEMD160Managed__ctor_mA711F6097C8FE4D0ED0A40CE76B2341318D8DC5B,
	RIPEMD160Managed_HashCore_mEFFF45C2E7FA8F9D01660241543E27B85CB524D0,
	RIPEMD160Managed_HashFinal_mDC86D641B11C3FE8DF7FD3C9F37F95603C08DEC3,
	RIPEMD160Managed_Initialize_m513545C5D7B8B5BCA25E17D80425C67877883155,
	RIPEMD160Managed__cctor_m9DB4784A89059A11CFBB2F662A03C1EA0F13ADDD,
	RipemdEncryptionOptions__ctor_mDE658460C065CEBDC8B0C19583E212D819592A12,
	RipemdProvider__ctor_m9AA8290294B90C8AB73DE9BB9231A50E688FC4B3,
	RipemdProvider_DecryptString_m85114AAB0AA8C52270D284F9213E65930859B19A,
	RipemdProvider_DecryptBinary_m05DDF4142A817D6870E7D1DDBB1BF1D9D1F598BB,
	RipemdProvider_EncryptString_mB366B416B9DE1C2971BCA91EC7E5BBD24330E29A,
	RipemdProvider_EncryptBinary_mDF8B1B2B4BDD99660F46A3F15EB82DAD475ADD0E,
	PBKDF2EncryptionOptions__ctor_m1DEA921825F1F347BDBBB2457F2B75FE2616B968,
	PBKDF2Provider__ctor_mFBFD82ABDDE8533C618A06F0BF6421E9D3247EA4,
	PBKDF2Provider_DecryptString_mB960DDED90A9D016A66DEE2CCB46CFD3615297F8,
	PBKDF2Provider_DecryptBinary_m0D6E358167EBF68A15E06579805E6EAC232B86EE,
	PBKDF2Provider_EncryptString_m8455B1E022D5BF77B1AAC56AEA8276928AF595C6,
	PBKDF2Provider_EncryptBinary_m47553A05107A2F08B0D6DDE148D94E2C4B60F4CE,
	PBKDF2Provider_GenerateSalt_mB06D50F185113460FA1C79FC70D9A8FEF0E296D9,
	PBKDF2Provider_calculateHash_m16FEFC1DE5D753ADC099AC9E8A7C55239E0DB6D1,
	PBKDF2Provider_calculateHash_m6FC6792D23AEF2BFC741DFAD2A6E395CC5DB2DA3,
	PBKDF2Provider_expandSalt_m1DFF821191BBFDA8DD63120936BF2C1BED86DAE9,
	MD5EncryptionOptions__ctor_m1CB8BFA93DE7A55CD6302518A04EB7CC9E6F1D92,
	MD5Provider__ctor_mC7E635F90DE8BFD1111B5ACADD3ED228382B992F,
	MD5Provider_DecryptString_mCAD9438B040068626B940259AA957BEBE6361BE1,
	MD5Provider_DecryptBinary_m446AF770A71224A4000BA91965629ECD2FC6DE33,
	MD5Provider_EncryptString_m3B5EE8BF4666C10E837462B8AE8D803ABDD2E99E,
	MD5Provider_EncryptBinary_m3A6E019A67CD364AE70E54EC610A6BC55FDF96C4,
	DESEncryptionOptions__ctor_m7AA07EF86BC6B5B657878236C091B48DD83E52B3,
	DESProvider__ctor_m3C68F115A5CEC37B49439EA9C04C98713D94E1DF,
	DESProvider_DecryptString_mA0B6549B62EC07A2ED8B00BD1C0DB8CB55074EC5,
	DESProvider_DecryptBinary_mFA23CF3758FC27791C1F07BAF9C0B870BBADEB66,
	DESProvider_EncryptString_m142BCE12E551172548F4EE82B843AD0F6EF717BA,
	DESProvider_EncryptBinary_m504C81E1E78963FF3A5F5D6A1D3F19903FF25B3A,
	Crc32__ctor_m1567E6547204A5500FE9128B2C2508EE676C161F,
	Crc32__ctor_mED9DDD9C1437D3F83F62E2D175FFC34B4C661D95,
	Crc32_Initialize_m2582811B8F89EE3DAC1DAC4E6A1879A4A3A383E4,
	Crc32_HashCore_m8166F804AE3F86C6B26CBB1F21DA61AFAED7A3B6,
	Crc32_HashFinal_m22B17274F0AF113AC5D11936DDE2720E853A5812,
	Crc32_get_HashSize_m6E54846324F6BDAD647D2F5741A2B8EBABCBB6C7,
	Crc32_Compute_mDD284BB5A69F0C760C2225606533BDE9717EE3DB,
	Crc32_Compute_m8EF0438BEA526E2A29ABB96A5D4F396CA7AFD686,
	Crc32_Compute_mCEECCB8AC113BD11F532BC7161592CD3B88D6745,
	Crc32_InitializeTable_m25DADE1291246AFD578BEB4B733A471E63DA9A9E,
	Crc32_CalculateHash_m5EB9DC247C2DD7CE32D7CB2C7C52DEF1CF334111,
	Crc32_UInt32ToBigEndianBytes_mD3CA034E5FDB8A3B30D3C6267C2A2192113C068F,
	Crc32_Get_m045F51DEFEE4F40C3A0803DCA60C9D0B29D5E48E,
	CRC32EncryptionOptions__ctor_m76E3732ADB1C7AEB21EFAE87D06EE773D6736A34,
	CRC32Provider__ctor_m99E5C7B1C431703D82F4A296F3FBBF1293C197C2,
	CRC32Provider_DecryptString_m672DCC3BA29A26DA25688D272E8DFEED53D0F809,
	CRC32Provider_DecryptBinary_m32AA463F4B41A72876ECA32E8813C62C591E382B,
	CRC32Provider_EncryptString_mD4E32BCCA0E6E99F15DE843B3AEE8C27384236EE,
	CRC32Provider_EncryptBinary_m1F0657F67BF524CD568375D4D98BEE49D97BADD6,
	BlowfishEncryptionOptions__ctor_m3AE38AAB9A40BFC3BB1F7BBBB95012BF642C5204,
	BlowfishProvider__ctor_mA5D52A82A41DE9DFAC5D2E433F44DB54EB9386FE,
	BlowfishProvider_F_m634A6687262B0C2BB88EA7D244F9C0D43686C00A,
	BlowfishProvider_Encipher_m73252884E0AF8C82A874258EFC7559C27EBC457F,
	BlowfishProvider_Encipher_m64A3D5D975AE3161F410E0B422481C0C8EFD1B7D,
	BlowfishProvider_Encipher_m445C0B681E3EC3CE9290678E279A88AEBB90FEF4,
	BlowfishProvider_Decipher_mF8351E93161CF3BB93592D55188BBFBD9EED975A,
	BlowfishProvider_Decipher_m56F91C06CCA9FFB33DD500F0AED8EF6A7AB5C50B,
	BlowfishProvider_Decipher_m1FCB7153C4A795483B30D50F65EEA29A5D5CA9AD,
	BlowfishProvider_DecryptString_m8E5257E0714D3615B88E27E37A1E108DA7059A3B,
	BlowfishProvider_DecryptBinary_m34B2A6559D1360CB11E61065C1C7C24A3A9E9209,
	BlowfishProvider_EncryptString_m295D0D84CC583139261D4E49076AB06BAC1104DC,
	BlowfishProvider_EncryptBinary_m55E837E07F76FBC754ECD8ACAE97AE4D92D8721A,
	BlowfishProvider__cctor_m86197176F99DC46B060FE9365A963D9E379A40DA,
	Base64EncryptionOptions__ctor_m755899C4F6C82D2093341FBBF129F36589E33116,
	Base64Provider__ctor_m737954AD852BBC6B9B294B71DA373996D909AF23,
	Base64Provider_DecryptString_m72CC12B5B2305742758C4293F7D9B4B7D0B2FAC4,
	Base64Provider_DecryptBinary_m8523E2FE02D83A2E1890378E8E8E71F1FCB322A3,
	Base64Provider_EncryptString_m6ACDD1ADFBA1D8046BD1474254C71B44BFB0E626,
	Base64Provider_EncryptBinary_mE619B3C839CBB802655B09CBF66B918354183C97,
	AESEncryptionOptions__ctor_m52905147E0D2BE3C196537D5F057111005274489,
	AESProvider__ctor_m5467A8CA2EBA8F0E510F6E33B3FBCB3A8517C588,
	AESProvider_DecryptString_m4825670F9D9E33D216551FD9250292577117193B,
	AESProvider_DecryptBinary_m9899E579AA9093E8F264F1C1FD3EEE042A06E588,
	AESProvider_EncryptString_mFA893EB9112131B923A28E5651194B7EF9B17538,
	AESProvider_EncryptBinary_m00BD26F0D90769C840DB1CB725943D705D020852,
	AESProvider_get_setupRijndaelManaged_mEBFF1ECC585EDB16300FC149E9BA5D31AFF188B1,
	NULL,
	NULL,
	NULL,
	NULL,
};
extern void U3CReadBinaryAsyncU3Ed__5_MoveNext_m89E9A03A5356EE2CCECE7A3070CDAC322C54B086_AdjustorThunk (void);
extern void U3CReadBinaryAsyncU3Ed__5_SetStateMachine_m173A385CBCD23DEFBA37228D2F46A9C2F7269523_AdjustorThunk (void);
extern void U3CReadTextAsyncU3Ed__4_MoveNext_m111A4EA5F7448EAA68057259F89424D22D1E92E7_AdjustorThunk (void);
extern void U3CReadTextAsyncU3Ed__4_SetStateMachine_mB9480544C979B15804DC4D787D0EE1B5C9E353F5_AdjustorThunk (void);
extern void U3CWriteBinaryAsyncU3Ed__7_MoveNext_m6486E00CD962D2D90C0A27754CF332BFABBCCE0C_AdjustorThunk (void);
extern void U3CWriteBinaryAsyncU3Ed__7_SetStateMachine_m678D8A05A9C7A9D12B3EC138D8D9FE229D8BBBAE_AdjustorThunk (void);
extern void U3CWriteTextAsyncU3Ed__6_MoveNext_mE5358D5F48808F831F4BB36953B56287F9D26CC8_AdjustorThunk (void);
extern void U3CWriteTextAsyncU3Ed__6_SetStateMachine_m5700E9263D60EF298ADF8D537A421F2729CAA208_AdjustorThunk (void);
extern void DWord__ctor_mACC837ACE0348FC302899B8BCD930E070D4CD4FD_AdjustorThunk (void);
extern void DWord__ctor_mB70C2651AE6EECFFD45F58014DB08B37285E8769_AdjustorThunk (void);
extern void DWord_RotateLeft_m3CBDE2EBF04EAD8843C14AD78C6A2E50455FEDFF_AdjustorThunk (void);
extern void DWord_RotateRight_m4BA6508C728180BA843E3139E4204761DE33D8EC_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[12] = 
{
	{ 0x06000017, U3CReadBinaryAsyncU3Ed__5_MoveNext_m89E9A03A5356EE2CCECE7A3070CDAC322C54B086_AdjustorThunk },
	{ 0x06000018, U3CReadBinaryAsyncU3Ed__5_SetStateMachine_m173A385CBCD23DEFBA37228D2F46A9C2F7269523_AdjustorThunk },
	{ 0x06000019, U3CReadTextAsyncU3Ed__4_MoveNext_m111A4EA5F7448EAA68057259F89424D22D1E92E7_AdjustorThunk },
	{ 0x0600001A, U3CReadTextAsyncU3Ed__4_SetStateMachine_mB9480544C979B15804DC4D787D0EE1B5C9E353F5_AdjustorThunk },
	{ 0x0600001B, U3CWriteBinaryAsyncU3Ed__7_MoveNext_m6486E00CD962D2D90C0A27754CF332BFABBCCE0C_AdjustorThunk },
	{ 0x0600001C, U3CWriteBinaryAsyncU3Ed__7_SetStateMachine_m678D8A05A9C7A9D12B3EC138D8D9FE229D8BBBAE_AdjustorThunk },
	{ 0x0600001D, U3CWriteTextAsyncU3Ed__6_MoveNext_mE5358D5F48808F831F4BB36953B56287F9D26CC8_AdjustorThunk },
	{ 0x0600001E, U3CWriteTextAsyncU3Ed__6_SetStateMachine_m5700E9263D60EF298ADF8D537A421F2729CAA208_AdjustorThunk },
	{ 0x0600004C, DWord__ctor_mACC837ACE0348FC302899B8BCD930E070D4CD4FD_AdjustorThunk },
	{ 0x0600004D, DWord__ctor_mB70C2651AE6EECFFD45F58014DB08B37285E8769_AdjustorThunk },
	{ 0x06000057, DWord_RotateLeft_m3CBDE2EBF04EAD8843C14AD78C6A2E50455FEDFF_AdjustorThunk },
	{ 0x06000058, DWord_RotateRight_m4BA6508C728180BA843E3139E4204761DE33D8EC_AdjustorThunk },
};
static const int32_t s_InvokerIndices[229] = 
{
	21016,
	15757,
	15968,
	34284,
	21016,
	32764,
	28070,
	28070,
	28070,
	28070,
	34252,
	34252,
	32764,
	25327,
	25327,
	28070,
	28070,
	25682,
	25682,
	28070,
	28070,
	34252,
	21016,
	15968,
	21016,
	15968,
	21016,
	15968,
	21016,
	15968,
	21016,
	15968,
	13820,
	13820,
	13820,
	13820,
	21016,
	15968,
	13820,
	13820,
	13820,
	13820,
	6094,
	21016,
	6094,
	6094,
	21016,
	21016,
	20694,
	15903,
	20694,
	15903,
	1275,
	20550,
	20550,
	20694,
	20694,
	21016,
	15757,
	949,
	3315,
	28064,
	21016,
	2795,
	2795,
	26815,
	34252,
	29964,
	32445,
	32445,
	32445,
	32445,
	32445,
	32445,
	34252,
	16179,
	7985,
	32514,
	33546,
	33547,
	29964,
	29962,
	29964,
	29964,
	29963,
	29963,
	16846,
	16846,
	21016,
	15968,
	13820,
	13820,
	13820,
	13820,
	21016,
	15968,
	13820,
	13820,
	13820,
	13820,
	15968,
	13820,
	13820,
	13820,
	13820,
	15968,
	13820,
	13820,
	13820,
	13820,
	15968,
	13820,
	13820,
	13820,
	13820,
	21016,
	21016,
	15968,
	13820,
	13820,
	13820,
	13820,
	29991,
	21016,
	34156,
	32090,
	28385,
	28394,
	25831,
	25831,
	25831,
	25831,
	25831,
	21859,
	21859,
	21859,
	21859,
	21859,
	21859,
	21859,
	21859,
	21859,
	21859,
	32748,
	28919,
	23278,
	21016,
	3722,
	20761,
	21016,
	34252,
	21016,
	15968,
	13820,
	13820,
	13820,
	13820,
	21016,
	15968,
	13820,
	13820,
	13820,
	13820,
	20761,
	6076,
	6076,
	21016,
	21016,
	15968,
	13820,
	13820,
	13820,
	13820,
	21016,
	15968,
	13820,
	13820,
	13820,
	13820,
	21016,
	8166,
	21016,
	3722,
	20761,
	20694,
	32439,
	28395,
	25830,
	32111,
	22868,
	13838,
	13820,
	21016,
	15968,
	13820,
	13820,
	13820,
	13820,
	21016,
	15968,
	14223,
	7985,
	6503,
	13820,
	7985,
	6503,
	13820,
	13820,
	13820,
	13820,
	13820,
	34252,
	21016,
	15968,
	13820,
	13820,
	13820,
	13820,
	21016,
	15968,
	13820,
	13820,
	13820,
	13820,
	20761,
	-1,
	-1,
	-1,
	-1,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_CryptoLibrary_CodeGenModule;
const Il2CppCodeGenModule g_CryptoLibrary_CodeGenModule = 
{
	"CryptoLibrary.dll",
	229,
	s_methodPointers,
	12,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
