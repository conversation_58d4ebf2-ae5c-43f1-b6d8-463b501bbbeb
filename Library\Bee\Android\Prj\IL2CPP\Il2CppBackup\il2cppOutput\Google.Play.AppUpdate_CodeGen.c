﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mAE5C6FE1DA19052442ED27E25FE17686CF49B512 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m83AEE1B0DE3F31C1E2156B19AB1A78ECFAC9ED73 (void);
extern void AppUpdateInfo_get_AvailableVersionCode_mD82E5BDC689B82633FD8681ABA55DF96665514BE (void);
extern void AppUpdateInfo_get_UpdateAvailability_m9D791BC6AD2F144905B9BB11623A9F5FF83B37D2 (void);
extern void AppUpdateInfo_get_AppUpdateStatus_mFBED6B2C60B521413BE1C015D64C868193EE9F7E (void);
extern void AppUpdateInfo_get_ClientVersionStalenessDays_m3553196D417BF55FB4A1D452B20FE73965A561BE (void);
extern void AppUpdateInfo_get_UpdatePriority_mC298B15155CCDFA7356B5BC35DF371D18904084C (void);
extern void AppUpdateInfo_get_BytesDownloaded_mC4F7EF616A4B860F79A5B22659B93E23CCBA7A41 (void);
extern void AppUpdateInfo_get_TotalBytesToDownload_m4F03749918D44D1711D6B9BD4EA8A32311F2F14F (void);
extern void AppUpdateInfo_IsUpdateTypeAllowed_m6BC875CF28C016B9B34E0F3CBA8078E6CCA2A9CA (void);
extern void AppUpdateInfo_ToString_m6B16EADC825525C862277795553BCE093E7725E8 (void);
extern void AppUpdateInfo__ctor_mD7A6705AAD0826069C4C154490DC8E34FD554A9D (void);
extern void AppUpdateInfo_GetJavaUpdateInfo_mD67F7350CAAEED795F12D03E201B267602D6D5FC (void);
extern void AppUpdateManager__ctor_m3299B5AE8FD3F15C77C7DB9B992B8613BA0AEB7C (void);
extern void AppUpdateManager_GetAppUpdateInfo_m2387A02DF7B1127CFCEBD16834EE85A2EEFA5D4D (void);
extern void AppUpdateManager_StartUpdate_mA6CB776AEE490486D327C8F1216E147FCDCE0909 (void);
extern void AppUpdateManager_CompleteUpdate_m6F79D7A593DBE5BD0E4B0FF5904CA93731775DE5 (void);
extern void AppUpdateOptions_ImmediateAppUpdateOptions_m1474744F2B5A50DA9EA318E2696B15A5A030DEE6 (void);
extern void AppUpdateOptions_FlexibleAppUpdateOptions_m2FAD843D34CCA1E316BD96E408028CC337918EA4 (void);
extern void AppUpdateOptions_get_AppUpdateType_mE8815DA2C537EB32670CB6690B4D6A5E08D3AF95 (void);
extern void AppUpdateOptions_get_AllowAssetPackDeletion_m2A98A83DC590FCFCAF151A3A384388EF920A8DF5 (void);
extern void AppUpdateOptions_GetJavaAppUpdateOptions_mFB9886E08085B4C57A593587635730AE1FE5DD73 (void);
extern void AppUpdateOptions__ctor_m4D30E08C686539161BBAEC39DEEE55FB60462763 (void);
extern void AppUpdateRequest_add_Completed_m35C2FEAC8F788BAB8065F66B153E130ECE1ABB51 (void);
extern void AppUpdateRequest_remove_Completed_mC7A0A5DA3858CEA10868EDA1171A7C283A956EE8 (void);
extern void AppUpdateRequest_get_IsDone_m47229A623C28AF1EBBC64FAEFFB827B8B15BE03E (void);
extern void AppUpdateRequest_set_IsDone_m70038582CE88FEAF736E3E33A9C3B44008987EE1 (void);
extern void AppUpdateRequest_get_Status_m4DBFC4F05DA0FB8491ED1196747A7A52BD0F8E40 (void);
extern void AppUpdateRequest_set_Status_mDD2F8CCF51DACDD52D7E964CE618680172ED4722 (void);
extern void AppUpdateRequest_get_Error_m1A0B539295AC0B3C4FD132CC111043B2D7EB1131 (void);
extern void AppUpdateRequest_set_Error_m2BB5BA63C208CBD835C6D14AE34B171B9D3B0C6F (void);
extern void AppUpdateRequest_get_DownloadProgress_mA87BBFF0F76963C344324EFECAF1E6012E7767A6 (void);
extern void AppUpdateRequest_set_DownloadProgress_mEB55B111CA6D5DC336A86314AF221616B6A617EB (void);
extern void AppUpdateRequest_get_BytesDownloaded_m0B994C4DC78E1917221F173B66A35C05147B6806 (void);
extern void AppUpdateRequest_set_BytesDownloaded_m90B780DEEDF96B7B54B87B1B36BE1F3A164B3428 (void);
extern void AppUpdateRequest_get_TotalBytesToDownload_m6BCB8693F86B05889F7E2E4F4B821F60A3F6037F (void);
extern void AppUpdateRequest_set_TotalBytesToDownload_mAAF3A65F870195E1F60F1B53E77C28A735EBB7D5 (void);
extern void AppUpdateRequest_get_keepWaiting_m9223F52B47B85655F1C68823E3AE0E68A4E8AAF1 (void);
extern void AppUpdateRequest_InvokeCompletedEvent_m30543BDF68CEEEF4DD78D1A1CE7D0A9535FFDC11 (void);
extern void AppUpdateRequest__ctor_mCA8616DFF30F23ADB6E91512CB7E4761744891B5 (void);
extern void U3CU3Ec__cctor_m1C90E857C6AD05BEC61ABB8EA1562F31854C2016 (void);
extern void U3CU3Ec__ctor_m8940DA84D9E419753021C4A40113C0D8ACFA4593 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__30_0_mEFE876974D54693F2963E609824389D84DA7A420 (void);
extern void AppUpdateHandler_add_OnStateUpdateEvent_m7B2D9F9FDD58F4A81E4474B7D46DF69A2E3A7E33 (void);
extern void AppUpdateHandler_remove_OnStateUpdateEvent_m6EBB2D686B6BEA8A4CA3AA5787233BC1927F861F (void);
extern void AppUpdateHandler_CreateInScene_m05A1E5C344EE575E63DBD216552056565A2F6CA6 (void);
extern void AppUpdateHandler_Init_mC338B67D27E72B780FB1F2B687FAE1D3F838FF74 (void);
extern void AppUpdateHandler_Start_m0A95584C3A3C72F63FD89A88C321A44D8EB738D1 (void);
extern void AppUpdateHandler_OnApplicationPause_mF34877AE736783B617674CC1F465A80FB369987A (void);
extern void AppUpdateHandler_OnDestroy_m1F7DEF187A9B7852CDC537E8F8DAB43A80236EE7 (void);
extern void AppUpdateHandler_ForceStatesUpdate_mE2F7214717BBF6C6766CBEAD9AE8660388F2B270 (void);
extern void AppUpdateHandler_ProcessUpdateInfo_m8E3278B9E623C9DE1DED9D5D8A61F60AA36462EE (void);
extern void AppUpdateHandler_OnStateUpdateReceived_mAD9896BB585633B65D2167387E6A4EB035874160 (void);
extern void AppUpdateHandler_StartListeningForUpdates_mFC937F3348C9218117DCFDD2EA2E324251FEA08B (void);
extern void AppUpdateHandler_StopListeningForUpdates_mE24ECF12052E661BE91A6D5BCF93F4D12FA015B4 (void);
extern void AppUpdateHandler__ctor_mD18481B2C2B5E72903F6D0C52B1F32FAC565F5C7 (void);
extern void U3CU3Ec__cctor_m34BD0AA286360B024ED516606D28E6B02540CA4C (void);
extern void U3CU3Ec__ctor_m0B2971DD78CAADADC4720264457E51D51A7F4F61 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__15_0_m21E01E802593F87DFF3B4A9398D183904AF9F882 (void);
extern void AppUpdateManagerInternal__ctor_m3A48B777A63399C2AF131AB119BF797467716F03 (void);
extern void AppUpdateManagerInternal_GetAppUpdateInfo_m579B6658CEABAC7FB8E4947B32CEFD41355B8752 (void);
extern void AppUpdateManagerInternal_GetAppUpdateInfoInternal_mAFC765D93171B21985E914922D91B775777991C8 (void);
extern void AppUpdateManagerInternal_RegisterListener_m09BB06A682B3CB884FFE95622BAC0246A6F3D0D7 (void);
extern void AppUpdateManagerInternal_UnregisterListener_m70C4F70A6E3A110B2FA8D2154D1EA8D543480389 (void);
extern void AppUpdateManagerInternal_StartUpdateInternal_m2330296EA61563D644B418620143086E0F009F52 (void);
extern void AppUpdateManagerInternal_CompleteUpdateInternal_m328B9D322DA12971C2BE86AD7190F36F55C82AFC (void);
extern void AppUpdateManagerInternal_InitiateRequest_mAC105BBE089FBA2A408CEC20EE1CE0F34F337DFF (void);
extern void AppUpdateManagerInternal_ProcessStateUpdate_m3EEE068EC79FAF28F3C2F277C4FE30C184DE3284 (void);
extern void AppUpdateManagerInternal_AppUpdateRequest_m515896B32B0B5B2DCBA630E4818C96C19E5BEFA6 (void);
extern void AppUpdateManagerInternal_U3CStartUpdateInternalU3Eb__8_0_m2D5E273BBD834DC06DE6F30CCA39AFE728C4065D (void);
extern void U3CU3Ec__DisplayClass10_0__ctor_m09C04EF2FEC3E947CB8CED0208B733EC6ADCF8D4 (void);
extern void U3CU3Ec__DisplayClass10_0_U3CInitiateRequestU3Eb__0_m7C65252C47CBD55B80A2704CC061DDD666805FAC (void);
extern void U3CU3Ec__DisplayClass10_0_U3CInitiateRequestU3Eb__1_mC98CF9E537E49DC89C1C7DE15E9C6D1625FD44A9 (void);
extern void U3CU3Ec__DisplayClass5_0__ctor_m7A7B7C98BBE6D5E5A2F75F4B06E82BC841B6B1C4 (void);
extern void U3CU3Ec__DisplayClass5_0_U3CGetAppUpdateInfoInternalU3Eb__0_m91C6F2F3881CF0C4BB4B6AEFB10AFADA10B90AEF (void);
extern void U3CU3Ec__DisplayClass5_0_U3CGetAppUpdateInfoInternalU3Eb__1_m86B75B0DEE9755B2F3519BC92E9B56505EA97C7D (void);
extern void U3CU3Ec__DisplayClass9_0__ctor_mA87F12B3434DE658EF8EB6D9092145F895211852 (void);
extern void U3CU3Ec__DisplayClass9_0_U3CCompleteUpdateInternalU3Eb__0_mA4C8C3B8AC21611B1A3FC125AE576C6F8576D2DE (void);
extern void U3CU3Ec__DisplayClass9_0_U3CCompleteUpdateInternalU3Eb__1_m7024B766F90C47AADFAD94F619DA2CEFE51198C0 (void);
extern void AppUpdateManagerPlayCore__ctor_mD7B14B55FE17FE8F810C4637C75321A6AFF8DAB1 (void);
extern void AppUpdateManagerPlayCore_RegisterListener_m06D6F8B5294B322F84EE592B7B501BDDEC308F69 (void);
extern void AppUpdateManagerPlayCore_UnregisterListener_mA290B1E9A62A5AA86B0512D7E8EC924DF1DB5E88 (void);
extern void AppUpdateManagerPlayCore_GetAppUpdateInfo_m33D7431726DB777E9F3DD9222BD3AD930F71A717 (void);
extern void AppUpdateManagerPlayCore_StartUpdateFlow_m56B583C8E67A466412EBED1AF10B613858FFA5B6 (void);
extern void AppUpdateManagerPlayCore_CompleteUpdate_m10AFB79D8C14403275E87E8E4BDF3B6E04AAF144 (void);
extern void AppUpdateManagerPlayCore_Dispose_mA1DE0FD2D7ADFEF660368FD8B1D223FB960C9FA7 (void);
extern void AppUpdatePlayCoreTranslator_TranslatePlayCoreErrorCode_m6B2AAEA99CBB84B1CFD0F0F4268221AB87470F2E (void);
extern void AppUpdatePlayCoreTranslator_TranslatePlayCoreUpdateStatus_mD789F0307A7D58A6389139040AC2FD856B0B1B8C (void);
extern void AppUpdatePlayCoreTranslator_TranslatePlayCoreUpdateAvailabilities_m24865DB6C84040AC39E504B5CFA5B4102F6128DA (void);
extern void AppUpdatePlayCoreTranslator_TranslateUpdateActivityResult_m88375AE304050A34636DA8CF1F79DF12D04E6B97 (void);
extern void AppUpdatePlayCoreTranslator_TranslatePlayCoreAppUpdateType_m34C38545670433AC2B6C0CF3C35BCB9CE99AE5BE (void);
extern void AppUpdatePlayCoreTranslator__cctor_m87D5E802C7180C67D12D6FE3244F6673339D5ECD (void);
extern void AppUpdateRequestImpl__ctor_m5BFF7F48C09DE718F782E31D6BEE81F1FF2B039C (void);
extern void AppUpdateRequestImpl_add_Completed_mBBF1255D02A342067BB3DFA236275CE9CDD57398 (void);
extern void AppUpdateRequestImpl_remove_Completed_m52C0476305D1B9529FA94C320A1DEFC7596198A2 (void);
extern void AppUpdateRequestImpl_UpdateState_m6BE65D06B91459A5134535FDCB998D182A74A721 (void);
extern void AppUpdateRequestImpl_SetUpdateActivityResult_m852A7C9E28BD309648603B1EBA9741F08AC8ED35 (void);
extern void AppUpdateRequestImpl_OnUpdateDownloaded_mFF0DCC15F166B11FC540EB528DBA435F1B851F9B (void);
extern void AppUpdateRequestImpl_OnErrorOccurred_m87C09CFEEAB10D3C5E681CC2D8D4F07A2D6F6E5E (void);
extern void AppUpdateState_get_BytesDownloaded_m4DC49E32F2356D729D0EF21FD4E333A992B57D8B (void);
extern void AppUpdateState_set_BytesDownloaded_m5A05FD6BE15CB6B0E6B1D7BA08C7C04739E95988 (void);
extern void AppUpdateState_get_TotalBytesToDownload_m9ADC2397A187C256ED6259AB5DC0C32FB837D460 (void);
extern void AppUpdateState_set_TotalBytesToDownload_mE5C99FAB76B51FAB5DD58745E31225D9DB3B1356 (void);
extern void AppUpdateState_get_Status_m3E2CDDAE9BB73AD74C33AB9745151BE2B723B5DC (void);
extern void AppUpdateState_set_Status_m25C6B82F179CBB87A33DF50D1316D26A05F60391 (void);
extern void AppUpdateState_get_ErrorCode_m20F98464388C500F720F8E422348869654B488AD (void);
extern void AppUpdateState_set_ErrorCode_m4FC7816733C53BEEDB92B9D391A1DC6A9B5BA164 (void);
extern void AppUpdateState__ctor_mD793603FB50E67E8A1A8D5C512F9287024BFE49A (void);
extern void AppUpdateState_ToString_mF510A10A5E5FB0F30C02F9BDE36ECBC5853822EF (void);
extern void AppUpdateStateListener_add_OnStateUpdateEvent_mB76AE82155ECBC3A2A12632E9AC7CD3D79A40867 (void);
extern void AppUpdateStateListener_remove_OnStateUpdateEvent_mE34AF82666613C741D167BA8486E5422B21055BD (void);
extern void AppUpdateStateListener__ctor_mAE2AB7EC15AF6B73764B54CAC36B5D38A920D6B5 (void);
extern void AppUpdateStateListener_onStateUpdate_m696F5D88FB186CA4F8D8395B1704F436C26334C9 (void);
extern void U3CU3Ec__cctor_m2D5275E0D9213A8726E3852B37BAD73058751262 (void);
extern void U3CU3Ec__ctor_m48E7D3D211B4FA76406B4A17EFE6EF725A873FB4 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__3_0_mC296740D4108FBE2583F3AD3301F56AFBECF8596 (void);
extern void U3CU3Ec__DisplayClass4_0__ctor_mA81988A091FA481B35D82390C0B463DE1DB1D6CA (void);
extern void U3CU3Ec__DisplayClass4_0_U3ConStateUpdateU3Eb__0_mA6239E524F811710785D9BA89AFCCEA59D82BE1F (void);
static Il2CppMethodPointer s_methodPointers[120] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mAE5C6FE1DA19052442ED27E25FE17686CF49B512,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m83AEE1B0DE3F31C1E2156B19AB1A78ECFAC9ED73,
	AppUpdateInfo_get_AvailableVersionCode_mD82E5BDC689B82633FD8681ABA55DF96665514BE,
	AppUpdateInfo_get_UpdateAvailability_m9D791BC6AD2F144905B9BB11623A9F5FF83B37D2,
	AppUpdateInfo_get_AppUpdateStatus_mFBED6B2C60B521413BE1C015D64C868193EE9F7E,
	AppUpdateInfo_get_ClientVersionStalenessDays_m3553196D417BF55FB4A1D452B20FE73965A561BE,
	AppUpdateInfo_get_UpdatePriority_mC298B15155CCDFA7356B5BC35DF371D18904084C,
	AppUpdateInfo_get_BytesDownloaded_mC4F7EF616A4B860F79A5B22659B93E23CCBA7A41,
	AppUpdateInfo_get_TotalBytesToDownload_m4F03749918D44D1711D6B9BD4EA8A32311F2F14F,
	AppUpdateInfo_IsUpdateTypeAllowed_m6BC875CF28C016B9B34E0F3CBA8078E6CCA2A9CA,
	AppUpdateInfo_ToString_m6B16EADC825525C862277795553BCE093E7725E8,
	AppUpdateInfo__ctor_mD7A6705AAD0826069C4C154490DC8E34FD554A9D,
	AppUpdateInfo_GetJavaUpdateInfo_mD67F7350CAAEED795F12D03E201B267602D6D5FC,
	AppUpdateManager__ctor_m3299B5AE8FD3F15C77C7DB9B992B8613BA0AEB7C,
	AppUpdateManager_GetAppUpdateInfo_m2387A02DF7B1127CFCEBD16834EE85A2EEFA5D4D,
	AppUpdateManager_StartUpdate_mA6CB776AEE490486D327C8F1216E147FCDCE0909,
	AppUpdateManager_CompleteUpdate_m6F79D7A593DBE5BD0E4B0FF5904CA93731775DE5,
	AppUpdateOptions_ImmediateAppUpdateOptions_m1474744F2B5A50DA9EA318E2696B15A5A030DEE6,
	AppUpdateOptions_FlexibleAppUpdateOptions_m2FAD843D34CCA1E316BD96E408028CC337918EA4,
	AppUpdateOptions_get_AppUpdateType_mE8815DA2C537EB32670CB6690B4D6A5E08D3AF95,
	AppUpdateOptions_get_AllowAssetPackDeletion_m2A98A83DC590FCFCAF151A3A384388EF920A8DF5,
	AppUpdateOptions_GetJavaAppUpdateOptions_mFB9886E08085B4C57A593587635730AE1FE5DD73,
	AppUpdateOptions__ctor_m4D30E08C686539161BBAEC39DEEE55FB60462763,
	AppUpdateRequest_add_Completed_m35C2FEAC8F788BAB8065F66B153E130ECE1ABB51,
	AppUpdateRequest_remove_Completed_mC7A0A5DA3858CEA10868EDA1171A7C283A956EE8,
	AppUpdateRequest_get_IsDone_m47229A623C28AF1EBBC64FAEFFB827B8B15BE03E,
	AppUpdateRequest_set_IsDone_m70038582CE88FEAF736E3E33A9C3B44008987EE1,
	AppUpdateRequest_get_Status_m4DBFC4F05DA0FB8491ED1196747A7A52BD0F8E40,
	AppUpdateRequest_set_Status_mDD2F8CCF51DACDD52D7E964CE618680172ED4722,
	AppUpdateRequest_get_Error_m1A0B539295AC0B3C4FD132CC111043B2D7EB1131,
	AppUpdateRequest_set_Error_m2BB5BA63C208CBD835C6D14AE34B171B9D3B0C6F,
	AppUpdateRequest_get_DownloadProgress_mA87BBFF0F76963C344324EFECAF1E6012E7767A6,
	AppUpdateRequest_set_DownloadProgress_mEB55B111CA6D5DC336A86314AF221616B6A617EB,
	AppUpdateRequest_get_BytesDownloaded_m0B994C4DC78E1917221F173B66A35C05147B6806,
	AppUpdateRequest_set_BytesDownloaded_m90B780DEEDF96B7B54B87B1B36BE1F3A164B3428,
	AppUpdateRequest_get_TotalBytesToDownload_m6BCB8693F86B05889F7E2E4F4B821F60A3F6037F,
	AppUpdateRequest_set_TotalBytesToDownload_mAAF3A65F870195E1F60F1B53E77C28A735EBB7D5,
	AppUpdateRequest_get_keepWaiting_m9223F52B47B85655F1C68823E3AE0E68A4E8AAF1,
	AppUpdateRequest_InvokeCompletedEvent_m30543BDF68CEEEF4DD78D1A1CE7D0A9535FFDC11,
	AppUpdateRequest__ctor_mCA8616DFF30F23ADB6E91512CB7E4761744891B5,
	U3CU3Ec__cctor_m1C90E857C6AD05BEC61ABB8EA1562F31854C2016,
	U3CU3Ec__ctor_m8940DA84D9E419753021C4A40113C0D8ACFA4593,
	U3CU3Ec_U3C_ctorU3Eb__30_0_mEFE876974D54693F2963E609824389D84DA7A420,
	NULL,
	NULL,
	AppUpdateHandler_add_OnStateUpdateEvent_m7B2D9F9FDD58F4A81E4474B7D46DF69A2E3A7E33,
	AppUpdateHandler_remove_OnStateUpdateEvent_m6EBB2D686B6BEA8A4CA3AA5787233BC1927F861F,
	AppUpdateHandler_CreateInScene_m05A1E5C344EE575E63DBD216552056565A2F6CA6,
	AppUpdateHandler_Init_mC338B67D27E72B780FB1F2B687FAE1D3F838FF74,
	AppUpdateHandler_Start_m0A95584C3A3C72F63FD89A88C321A44D8EB738D1,
	AppUpdateHandler_OnApplicationPause_mF34877AE736783B617674CC1F465A80FB369987A,
	AppUpdateHandler_OnDestroy_m1F7DEF187A9B7852CDC537E8F8DAB43A80236EE7,
	AppUpdateHandler_ForceStatesUpdate_mE2F7214717BBF6C6766CBEAD9AE8660388F2B270,
	AppUpdateHandler_ProcessUpdateInfo_m8E3278B9E623C9DE1DED9D5D8A61F60AA36462EE,
	AppUpdateHandler_OnStateUpdateReceived_mAD9896BB585633B65D2167387E6A4EB035874160,
	AppUpdateHandler_StartListeningForUpdates_mFC937F3348C9218117DCFDD2EA2E324251FEA08B,
	AppUpdateHandler_StopListeningForUpdates_mE24ECF12052E661BE91A6D5BCF93F4D12FA015B4,
	AppUpdateHandler__ctor_mD18481B2C2B5E72903F6D0C52B1F32FAC565F5C7,
	U3CU3Ec__cctor_m34BD0AA286360B024ED516606D28E6B02540CA4C,
	U3CU3Ec__ctor_m0B2971DD78CAADADC4720264457E51D51A7F4F61,
	U3CU3Ec_U3C_ctorU3Eb__15_0_m21E01E802593F87DFF3B4A9398D183904AF9F882,
	AppUpdateManagerInternal__ctor_m3A48B777A63399C2AF131AB119BF797467716F03,
	AppUpdateManagerInternal_GetAppUpdateInfo_m579B6658CEABAC7FB8E4947B32CEFD41355B8752,
	AppUpdateManagerInternal_GetAppUpdateInfoInternal_mAFC765D93171B21985E914922D91B775777991C8,
	AppUpdateManagerInternal_RegisterListener_m09BB06A682B3CB884FFE95622BAC0246A6F3D0D7,
	AppUpdateManagerInternal_UnregisterListener_m70C4F70A6E3A110B2FA8D2154D1EA8D543480389,
	AppUpdateManagerInternal_StartUpdateInternal_m2330296EA61563D644B418620143086E0F009F52,
	AppUpdateManagerInternal_CompleteUpdateInternal_m328B9D322DA12971C2BE86AD7190F36F55C82AFC,
	AppUpdateManagerInternal_InitiateRequest_mAC105BBE089FBA2A408CEC20EE1CE0F34F337DFF,
	AppUpdateManagerInternal_ProcessStateUpdate_m3EEE068EC79FAF28F3C2F277C4FE30C184DE3284,
	AppUpdateManagerInternal_AppUpdateRequest_m515896B32B0B5B2DCBA630E4818C96C19E5BEFA6,
	AppUpdateManagerInternal_U3CStartUpdateInternalU3Eb__8_0_m2D5E273BBD834DC06DE6F30CCA39AFE728C4065D,
	U3CU3Ec__DisplayClass10_0__ctor_m09C04EF2FEC3E947CB8CED0208B733EC6ADCF8D4,
	U3CU3Ec__DisplayClass10_0_U3CInitiateRequestU3Eb__0_m7C65252C47CBD55B80A2704CC061DDD666805FAC,
	U3CU3Ec__DisplayClass10_0_U3CInitiateRequestU3Eb__1_mC98CF9E537E49DC89C1C7DE15E9C6D1625FD44A9,
	U3CU3Ec__DisplayClass5_0__ctor_m7A7B7C98BBE6D5E5A2F75F4B06E82BC841B6B1C4,
	U3CU3Ec__DisplayClass5_0_U3CGetAppUpdateInfoInternalU3Eb__0_m91C6F2F3881CF0C4BB4B6AEFB10AFADA10B90AEF,
	U3CU3Ec__DisplayClass5_0_U3CGetAppUpdateInfoInternalU3Eb__1_m86B75B0DEE9755B2F3519BC92E9B56505EA97C7D,
	U3CU3Ec__DisplayClass9_0__ctor_mA87F12B3434DE658EF8EB6D9092145F895211852,
	U3CU3Ec__DisplayClass9_0_U3CCompleteUpdateInternalU3Eb__0_mA4C8C3B8AC21611B1A3FC125AE576C6F8576D2DE,
	U3CU3Ec__DisplayClass9_0_U3CCompleteUpdateInternalU3Eb__1_m7024B766F90C47AADFAD94F619DA2CEFE51198C0,
	AppUpdateManagerPlayCore__ctor_mD7B14B55FE17FE8F810C4637C75321A6AFF8DAB1,
	AppUpdateManagerPlayCore_RegisterListener_m06D6F8B5294B322F84EE592B7B501BDDEC308F69,
	AppUpdateManagerPlayCore_UnregisterListener_mA290B1E9A62A5AA86B0512D7E8EC924DF1DB5E88,
	AppUpdateManagerPlayCore_GetAppUpdateInfo_m33D7431726DB777E9F3DD9222BD3AD930F71A717,
	AppUpdateManagerPlayCore_StartUpdateFlow_m56B583C8E67A466412EBED1AF10B613858FFA5B6,
	AppUpdateManagerPlayCore_CompleteUpdate_m10AFB79D8C14403275E87E8E4BDF3B6E04AAF144,
	AppUpdateManagerPlayCore_Dispose_mA1DE0FD2D7ADFEF660368FD8B1D223FB960C9FA7,
	AppUpdatePlayCoreTranslator_TranslatePlayCoreErrorCode_m6B2AAEA99CBB84B1CFD0F0F4268221AB87470F2E,
	AppUpdatePlayCoreTranslator_TranslatePlayCoreUpdateStatus_mD789F0307A7D58A6389139040AC2FD856B0B1B8C,
	AppUpdatePlayCoreTranslator_TranslatePlayCoreUpdateAvailabilities_m24865DB6C84040AC39E504B5CFA5B4102F6128DA,
	AppUpdatePlayCoreTranslator_TranslateUpdateActivityResult_m88375AE304050A34636DA8CF1F79DF12D04E6B97,
	AppUpdatePlayCoreTranslator_TranslatePlayCoreAppUpdateType_m34C38545670433AC2B6C0CF3C35BCB9CE99AE5BE,
	AppUpdatePlayCoreTranslator__cctor_m87D5E802C7180C67D12D6FE3244F6673339D5ECD,
	AppUpdateRequestImpl__ctor_m5BFF7F48C09DE718F782E31D6BEE81F1FF2B039C,
	AppUpdateRequestImpl_add_Completed_mBBF1255D02A342067BB3DFA236275CE9CDD57398,
	AppUpdateRequestImpl_remove_Completed_m52C0476305D1B9529FA94C320A1DEFC7596198A2,
	AppUpdateRequestImpl_UpdateState_m6BE65D06B91459A5134535FDCB998D182A74A721,
	AppUpdateRequestImpl_SetUpdateActivityResult_m852A7C9E28BD309648603B1EBA9741F08AC8ED35,
	AppUpdateRequestImpl_OnUpdateDownloaded_mFF0DCC15F166B11FC540EB528DBA435F1B851F9B,
	AppUpdateRequestImpl_OnErrorOccurred_m87C09CFEEAB10D3C5E681CC2D8D4F07A2D6F6E5E,
	AppUpdateState_get_BytesDownloaded_m4DC49E32F2356D729D0EF21FD4E333A992B57D8B,
	AppUpdateState_set_BytesDownloaded_m5A05FD6BE15CB6B0E6B1D7BA08C7C04739E95988,
	AppUpdateState_get_TotalBytesToDownload_m9ADC2397A187C256ED6259AB5DC0C32FB837D460,
	AppUpdateState_set_TotalBytesToDownload_mE5C99FAB76B51FAB5DD58745E31225D9DB3B1356,
	AppUpdateState_get_Status_m3E2CDDAE9BB73AD74C33AB9745151BE2B723B5DC,
	AppUpdateState_set_Status_m25C6B82F179CBB87A33DF50D1316D26A05F60391,
	AppUpdateState_get_ErrorCode_m20F98464388C500F720F8E422348869654B488AD,
	AppUpdateState_set_ErrorCode_m4FC7816733C53BEEDB92B9D391A1DC6A9B5BA164,
	AppUpdateState__ctor_mD793603FB50E67E8A1A8D5C512F9287024BFE49A,
	AppUpdateState_ToString_mF510A10A5E5FB0F30C02F9BDE36ECBC5853822EF,
	AppUpdateStateListener_add_OnStateUpdateEvent_mB76AE82155ECBC3A2A12632E9AC7CD3D79A40867,
	AppUpdateStateListener_remove_OnStateUpdateEvent_mE34AF82666613C741D167BA8486E5422B21055BD,
	AppUpdateStateListener__ctor_mAE2AB7EC15AF6B73764B54CAC36B5D38A920D6B5,
	AppUpdateStateListener_onStateUpdate_m696F5D88FB186CA4F8D8395B1704F436C26334C9,
	U3CU3Ec__cctor_m2D5275E0D9213A8726E3852B37BAD73058751262,
	U3CU3Ec__ctor_m48E7D3D211B4FA76406B4A17EFE6EF725A873FB4,
	U3CU3Ec_U3C_ctorU3Eb__3_0_mC296740D4108FBE2583F3AD3301F56AFBECF8596,
	U3CU3Ec__DisplayClass4_0__ctor_mA81988A091FA481B35D82390C0B463DE1DB1D6CA,
	U3CU3Ec__DisplayClass4_0_U3ConStateUpdateU3Eb__0_mA6239E524F811710785D9BA89AFCCEA59D82BE1F,
};
static const int32_t s_InvokerIndices[120] = 
{
	34286,
	21016,
	20694,
	20694,
	20694,
	18935,
	20694,
	20985,
	20985,
	11681,
	20761,
	15968,
	20761,
	21016,
	20761,
	6094,
	20761,
	32070,
	32070,
	20694,
	20550,
	20761,
	7312,
	15968,
	15968,
	20550,
	15757,
	20694,
	15903,
	20694,
	15903,
	20873,
	16071,
	20985,
	16180,
	20985,
	16180,
	20550,
	21016,
	21016,
	34252,
	21016,
	15968,
	-1,
	-1,
	15968,
	15968,
	32090,
	15968,
	21016,
	15757,
	21016,
	21016,
	15968,
	15968,
	21016,
	21016,
	21016,
	34252,
	21016,
	15968,
	21016,
	20761,
	20761,
	15968,
	15968,
	6094,
	20761,
	3763,
	15968,
	7995,
	15968,
	21016,
	15903,
	7985,
	21016,
	15968,
	7985,
	21016,
	15968,
	7985,
	21016,
	15968,
	15968,
	20761,
	6094,
	20761,
	21016,
	31782,
	31782,
	31782,
	31782,
	31782,
	34252,
	21016,
	15968,
	15968,
	3636,
	15903,
	21016,
	15903,
	20695,
	15904,
	20695,
	15904,
	20694,
	15903,
	20694,
	15903,
	15968,
	20761,
	15968,
	15968,
	21016,
	15968,
	34252,
	21016,
	15968,
	21016,
	21016,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x0200000D, { 0, 5 } },
};
extern const uint32_t g_rgctx_PlayAsyncOperation_2_get_IsDone_m52F17C8130D8240A0A7535ED5C614420B5A3B5D6;
extern const uint32_t g_rgctx_PlayAsyncOperation_2_get_Error_mE8C273DC0F3018919C7A34ACA4108640CDC93D7D;
extern const uint32_t g_rgctx_PlayAsyncOperationImpl_2__ctor_m2CE1EED68850052E09328DB95B4D69B6A3352339;
extern const uint32_t g_rgctx_PlayAsyncOperationImpl_2_t8B27E14427A0E945B97475D39EAFC7D630B7F23B;
extern const uint32_t g_rgctx_PlayAsyncOperation_2_tF2ACFF2567EAEF35FD79A7087A8403B3011D07B6;
static const Il2CppRGCTXDefinition s_rgctxValues[5] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PlayAsyncOperation_2_get_IsDone_m52F17C8130D8240A0A7535ED5C614420B5A3B5D6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PlayAsyncOperation_2_get_Error_mE8C273DC0F3018919C7A34ACA4108640CDC93D7D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PlayAsyncOperationImpl_2__ctor_m2CE1EED68850052E09328DB95B4D69B6A3352339 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PlayAsyncOperationImpl_2_t8B27E14427A0E945B97475D39EAFC7D630B7F23B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PlayAsyncOperation_2_tF2ACFF2567EAEF35FD79A7087A8403B3011D07B6 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Google_Play_AppUpdate_CodeGenModule;
const Il2CppCodeGenModule g_Google_Play_AppUpdate_CodeGenModule = 
{
	"Google.Play.AppUpdate.dll",
	120,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	5,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
