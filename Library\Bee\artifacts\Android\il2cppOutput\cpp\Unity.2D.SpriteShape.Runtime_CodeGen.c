﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* SpriteShapeGenerator_UTessellator_mA975B9F66E80F2334124BFADD19F331AB2F6E90D_RuntimeMethod_var;



extern void SpriteShapeGeometryCache_get_indexArray_m588E8148EC80D38ADBBBA39AB87D9B1B2F46536C (void);
extern void SpriteShapeGeometryCache_get_posArray_m59FA7B7F1CC7D199BE0D7DB312F57B51B119093C (void);
extern void SpriteShapeGeometryCache_get_tanArray_mE6F8D44BBCBAB1ADBE6DF4893FA3135343723C5B (void);
extern void SpriteShapeGeometryCache_get_maxArrayCount_m0D5DD80D325407DD8BEFD67804BE450C16900F29 (void);
extern void SpriteShapeGeometryCache_get_requiresUpdate_mC372F7AA991A8F396DA73B92629E4344CAF9B1A1 (void);
extern void SpriteShapeGeometryCache_get_requiresUpload_mD219F2D99B6E98B35D77FE17AE28C9A97333426D (void);
extern void SpriteShapeGeometryCache_OnEnable_m69AF1DBD6C2E96FAD202AB497AB6FD40807C2C41 (void);
extern void SpriteShapeGeometryCache_SetGeometryCache_m3D28669BE6D581591EAFFF99C19005918B953117 (void);
extern void SpriteShapeGeometryCache_UpdateGeometryCache_mB11BB67BC6A491573BADD23338C16E4CB65645D8 (void);
extern void SpriteShapeGeometryCache_Upload_mD06F59772108B75C0AB79CD2440BA32CF5C95107 (void);
extern void SpriteShapeGeometryCache__ctor_m7F2281E0CF39244394B0D2232FFA24F14EA87A74 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m02110F67DB594A7FECAC573BBE725F1C97882E09 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m8D180D0F4E7B86FF4888DED316FD1161A6A7533E (void);
extern void Geom_IsWindingInside_mF1E1D0DFFC5438424F614993FDA27CBC37C35B4D (void);
extern void Geom_VertCCW_m729F20E41F384442DA8623B179051B8CCEB96321 (void);
extern void Geom_VertEq_m185C5D61E54B5F9A48999AB1FF64DC30033673F0 (void);
extern void Geom_VertLeq_m643E83BDCBB7CAC1A3FA1480F81701433155D21F (void);
extern void Geom_EdgeEval_m5C733B47B92D0D71D3F2EAAF3ACFE22615D2410E (void);
extern void Geom_EdgeSign_m585A164610F6CA2B34B1F7C8F2596E3AEDA1669A (void);
extern void Geom_TransLeq_m51E8B752216D3CC7B3D1997D1136CD3523BB9503 (void);
extern void Geom_TransEval_mF27A561158C7835A953334914C17081EEECA641E (void);
extern void Geom_TransSign_m2C6FC8DEF955A96C2F3FF2BFEDD4BEF40C576046 (void);
extern void Geom_EdgeGoesLeft_m8C4A572A6E0F2BC8D6457787CBD3B09200B34808 (void);
extern void Geom_EdgeGoesRight_m1279AC98D42D6CDF49D720E4F735FD3AFDE242C6 (void);
extern void Geom_VertL1dist_m774CD04E2CC44868B97A806E8462588358AFF8CE (void);
extern void Geom_AddWinding_mEF9B45F1F1264B9A4057470FBCABF4A1C43132BC (void);
extern void Geom_Interpolate_m786E5018A615B1E1019DBC5BE287641A88C1672D (void);
extern void Geom_Swap_mC7EC25B8240634331DBA3131664431F8B368197B (void);
extern void Geom_EdgeIntersect_m01A9D8616F0A580C84D6298C0A3F7CF6AFE29019 (void);
extern void Mesh__ctor_mA5921D39DFE78C3E50EB6B50221280253EA8118A (void);
extern void Mesh_Reset_m3D3859F18567F2AC3A97599B082A0C7237933BD8 (void);
extern void Mesh_OnFree_m050CA42D85225FACEE3A5E90704BEE67D9A94790 (void);
extern void Mesh_MakeEdge_mB4FA8F615512C8C712E8133902FF8DC996036FBE (void);
extern void Mesh_Splice_m2E87747EE346CDBBB6E0EC0ED09804E6D124C76F (void);
extern void Mesh_Delete_mD97D1D9DB96444628E39D0A2A525F0E998CB4B57 (void);
extern void Mesh_AddEdgeVertex_mB1981481C4DA004BA1E66432E8894306FF9B435C (void);
extern void Mesh_SplitEdge_m03C876CEB3B2E04E9A8592E8D425C9D36E6F4C4E (void);
extern void Mesh_Connect_mE5B23363499165A6B2A6A585AED053D962D072D1 (void);
extern void Mesh_ZapFace_mFA0354F3915F506B1FADD91567B35F91AD1C9FF5 (void);
extern void Mesh_MergeConvexFaces_m0D46F821F2F2296DD1342E0E49E56F8CEBCB9D6C (void);
extern void Mesh_Check_mDA1197C89B6C32B6EE7F342A127BA7F1E3AAFF9E (void);
extern void Vec3_get_Item_m18BE18D5B8DE79B3C906B13FA63CA2AC304BBE37 (void);
extern void Vec3_set_Item_m0E3994FE25BEBE75671FFCC4DD45A6ABD438B25B (void);
extern void Vec3_Sub_mE2347B74B4877CB5E0269FE8FAEFF8B82AD1D3EF (void);
extern void Vec3_Neg_mAFFD69D42B4584D9541B5A866A41B37CE92778EA (void);
extern void Vec3_Dot_m95643401DDBB86E315DA9FB7FF2DEA2AEE65E37F (void);
extern void Vec3_Normalize_mC63D61BDE73C850F8C834E9F1481C8473FCC2F1E (void);
extern void Vec3_LongAxis_mDD3D8BCD04B089AD3E7B383F1EBE72A0C548F141 (void);
extern void Vec3_ToString_mFCCD5C16E92C7EB6A1EADB61183CF3C6C1CD93AB (void);
extern void Vec3__cctor_m02B9EF2CF498A9A4B5EB4C34152259D1B8B786CC (void);
extern void MeshUtils_MakeEdge_mCDC8989099CFDAA6AD48EF037E4CDC272D6B41D4 (void);
extern void MeshUtils_Splice_mD0C74018EFACEB37BD20709F2F304A4B6517E576 (void);
extern void MeshUtils_MakeVertex_m2A7DE2405EA5E3D12B1D56FC0A7B1748273D1C90 (void);
extern void MeshUtils_MakeFace_mF4A86DDE85C8A79222BF4BB4C392C46E8BF9C4EB (void);
extern void MeshUtils_KillEdge_m0DEEEAD04B86B35228DFF0DF124892F255DBC813 (void);
extern void MeshUtils_KillVertex_m5D5C72B07825085DF50B2D646CC710B56A46058C (void);
extern void MeshUtils_KillFace_m6AC8E688D30E7AC18981D29E901340ABC854CB24 (void);
extern void MeshUtils_FaceArea_m58594CC100DB8CB29D939B7F9703D67975881DB6 (void);
extern void Vertex_Reset_m9A97638D164C2A1B8A41C4217B94FA5BC364B953 (void);
extern void Vertex__ctor_m62F3E7DBCF7687E71A414E9D83E95D093CE0F0BD (void);
extern void Face_get_VertsCount_m8AD6AEC1ADAA78E4377ECA96CB59A7AC7FEEFCB6 (void);
extern void Face_Reset_m75870EF8D66A9659EC360E3AEF7F2517D3C513CD (void);
extern void Face__ctor_m1DB8A63B49D3E39084BA09A197A4AFD6F578B9EC (void);
extern void EdgePair_Create_m984F08E6275A4688EB8164D3184F8BA8E1233923 (void);
extern void EdgePair_Reset_m0B56DA5E426A184272B24B74B7A26085C1F162A4 (void);
extern void Edge_get__Rface_mA14DC262D09B8C4730E2DC670A0360D80EF9E026 (void);
extern void Edge_set__Rface_m5A829430D83BA32FC3107AA02FA3114058D5AAA0 (void);
extern void Edge_get__Dst_m70C56414A729CC9D426FE1BD1EC95C6026610E3A (void);
extern void Edge_set__Dst_m9B0B595758DCF864743C498BC5FA2B5825FB8EC0 (void);
extern void Edge_get__Oprev_mF9454D77C5B98CB63628398729BACA3D9C1F6D6B (void);
extern void Edge_set__Oprev_m906E96E92A3A01081D882E9B3193765E5CA0151C (void);
extern void Edge_get__Lprev_m7C649C346AD4E5475DC33C49CE48B33C60BC3B31 (void);
extern void Edge_set__Lprev_m3D201CBBCFCDCA1D18F9F6DC8DFF42DBD73D5714 (void);
extern void Edge_get__Dprev_mC86D92C59E60E15D7F47FEEA5381776F8A8750EC (void);
extern void Edge_set__Dprev_mE9E2DFD3D4E887D21DF7BA60C5BB014E966FAA95 (void);
extern void Edge_get__Rprev_mE3B995FFD33AD26DAF5F1AED18382BB100065DCD (void);
extern void Edge_set__Rprev_m98EF5CA40B14C8516FB5F3167BA6FEAB155EA8F6 (void);
extern void Edge_get__Dnext_m9BC35069C20236976B6572B35B1724A7EFF1E342 (void);
extern void Edge_set__Dnext_mA1A5E3FB201FD369ABC79A0D2EC8AA9EEADADD7A (void);
extern void Edge_get__Rnext_m542A8A55A8403B0DB8E81F25E3F9907C9DDD49CB (void);
extern void Edge_set__Rnext_m2FC8E82DD7CFDFD0A9A95A5C8490C4D471D7C84A (void);
extern void Edge_EnsureFirst_m74A479649B38135FBFB43F21EAA44C32EEAD144F (void);
extern void Edge_Reset_m0F5A5AE64C90B77C9A4B14B560EA2153BA30129E (void);
extern void Edge__ctor_m7D4DBA226B3214C1CC5FF6603000790359A80C2F (void);
extern void PQHandle__cctor_mF329E28D9AD8E1CA1C9DCFA1317373C71E4D186D (void);
extern void Tess_RegionBelow_mB4663FB383114F9E38C7E65BB43F0A5152D5A5BB (void);
extern void Tess_RegionAbove_mD8753526CB8C95B8F123324647D96CDACB14EB3B (void);
extern void Tess_EdgeLeq_m6ADCC1E1709D8E3ED4E8C4D9E5B9DE1B21D35ED2 (void);
extern void Tess_DeleteRegion_mCFA4F24A80189DC8A75FD2B452400823E799C170 (void);
extern void Tess_FixUpperEdge_m80D33493DCEBD2341C55C4F2A171A22D92C708EA (void);
extern void Tess_TopLeftRegion_m3C1E8AC1640F14D04C27C163191E1AB4E8D356BE (void);
extern void Tess_TopRightRegion_mDEF4F555F567428E63A1DA74FFBAEBF29454FB8D (void);
extern void Tess_AddRegionBelow_mD41DF8630F4C54D537E9F00D3A2672D13E9E68A9 (void);
extern void Tess_ComputeWinding_m4765320038B4D8253740B36018BAEA29AEF95931 (void);
extern void Tess_FinishRegion_m5EA98BD2CA47DD5D8E5480E7855DD4D756C84094 (void);
extern void Tess_FinishLeftRegions_mAC94EA6A1A8483C3A17573244F154D64BC52319F (void);
extern void Tess_AddRightEdges_m6C4B1B30D4DF33EA6A0CB10109CF85A03FD63958 (void);
extern void Tess_SpliceMergeVertices_mB6DD59676ACD512C3BD69CCF643B50E2AEC7631E (void);
extern void Tess_VertexWeights_m6D7FD684368293FDBC8770818E173C9E996B5533 (void);
extern void Tess_GetIntersectData_mB0CAFD39DAABEC4403569B04190EC627E06F7A14 (void);
extern void Tess_CheckForRightSplice_m25942FDEF6276480112696ECB9D7D6E0C27395C4 (void);
extern void Tess_CheckForLeftSplice_mCEDEE452591B6F893366A093E076E4F65830509F (void);
extern void Tess_CheckForIntersect_mA71310105191E5048AB2F6C630CC787856FA1F34 (void);
extern void Tess_WalkDirtyRegions_m2F929B139BD96213AB133AAA0A8544995FB9F4F5 (void);
extern void Tess_ConnectRightVertex_m0CC9FCEA3FDAE9778455E286278790F36DF4C077 (void);
extern void Tess_ConnectLeftDegenerate_m38444E9763440FDCBB461AF361C1E3887B5B1E5F (void);
extern void Tess_ConnectLeftVertex_m3AB1D90C0A4F80DD0B5B831742052A422CE18564 (void);
extern void Tess_SweepEvent_mB4073EC3BC83DFA4F24AA119B2C2FF3C69012B0E (void);
extern void Tess_AddSentinel_m972F272F20047718E2430E66B0EAA6C8A6A59988 (void);
extern void Tess_InitEdgeDict_m0EE5C5B7E788CCF1AAC029C56A8FEC413A981125 (void);
extern void Tess_DoneEdgeDict_m24F4E64FF899AA4BB85C79C107EFEE03C9980606 (void);
extern void Tess_RemoveDegenerateEdges_mF5450F835EFDC8D5186087608CA7EC56CFCA82C9 (void);
extern void Tess_InitPriorityQ_mC576B39EBE83F875099F080406972D6A7570BC11 (void);
extern void Tess_DonePriorityQ_m27DEF72BC94B68743B99CA7BA9A042377F13BA5C (void);
extern void Tess_RemoveDegenerateFaces_m175F0E09E0A14E1F45ABAA71EEA744BB48863D8C (void);
extern void Tess_ComputeInterior_mF4250ABC1DECA9FC271ED960F2A3EE44F7A57317 (void);
extern void Tess_get_Normal_mDEBEA69F532C0140100A035E96702D873B95118F (void);
extern void Tess_set_Normal_m68789191B92C93512D87FA1ADFF406FBFC9B8950 (void);
extern void Tess_get_Vertices_m419E01868C54850A20B52BE6DBF7EF5FB4385AF4 (void);
extern void Tess_get_VertexCount_m7B92CEC062CF196A1B997836F7D89A631240E986 (void);
extern void Tess_get_Elements_mB47D1FF47667C405AD450B8E0362A52D8DF8E1F0 (void);
extern void Tess_get_ElementCount_m3ED7EE9F572A069D4A3F8959C04A917A0161FA36 (void);
extern void Tess__ctor_mCE5C877A8F5D236916729D7E07F34C5FDB49972F (void);
extern void Tess_ComputeNormal_m3A49B801D5E0CEF00327B0DAA038F61D89B45056 (void);
extern void Tess_CheckOrientation_m4471242836839206D425EA88F294817B6C3AC68A (void);
extern void Tess_ProjectPolygon_m0548FB034FED6B171622A1BFB1318C23A477BBE6 (void);
extern void Tess_TessellateMonoRegion_m495AA620F323F60E855E96B3001AA4FC6614E423 (void);
extern void Tess_TessellateInterior_m3E5F1397A5AAF242394E63A44AF7EEF2A0CE1955 (void);
extern void Tess_DiscardExterior_mF056909C106E045C2E3D653AB9E79C4520C3A122 (void);
extern void Tess_SetWindingNumber_mFAEAB440C99283550B5C0595E208981B32C0E839 (void);
extern void Tess_GetNeighbourFace_m923653D242BA94542FC5566F35B4F4A968F8EA6F (void);
extern void Tess_OutputPolymesh_m84AB594E58778801BA24B6AEA0757B0CA07F381F (void);
extern void Tess_OutputContours_m3FA17B0D69F28B4671F8FE03CE0350C2A094FEB7 (void);
extern void Tess_SignedArea_m31E6133A5142ABCCA1ED940A3C74CF0093DF0E38 (void);
extern void Tess_AddContour_m641C81306AA0BF4690484C92FA7215C1AF99AB01 (void);
extern void Tess_AddContour_mE7863D3CC312FE9F732455527CA3180C6F449B64 (void);
extern void Tess_Tessellate_m72F1A1E69A5A02650C5089C57339B2DA9E55F2C9 (void);
extern void Tess_Tessellate_m15B65DE9A337183354DF80726FB2C2129850C386 (void);
extern void ActiveRegion__ctor_m69C2395743F453BA9A59BC88EE6759F9FEC6B299 (void);
extern void ContourVertex_ToString_mD386EFF0657B8A3D8060BF097ABFD6473440CF9F (void);
extern void CombineCallback__ctor_m154E6180D29646A02C17191DABB70023BDB0310E (void);
extern void CombineCallback_Invoke_mEE5A2E4A63D0F05B0816C82AEA86846FD403CB94 (void);
extern void CombineCallback_BeginInvoke_m8586CA8B8E8CC9C34F85CA793C5E9D1E758B89EB (void);
extern void CombineCallback_EndInvoke_m0E78F0FD884D698B3A2EFBD525ED33B6FA3B144E (void);
extern void BezierUtility_BezierPoint_m58FBF49282434261BFE28A39F3C891889F6FAF54 (void);
extern void BezierUtility_GetSpritePixelWidth_mA2692A4D73384256BB96A733B8140EE3F5F352B9 (void);
extern void BezierUtility_BezierLength_m1A8E5F24FB82045705EB1C61BB9D3BE5CA1CBF4B (void);
extern void BezierUtility_ClosestPointOnCurve_m91458888AC7667493117C7BE8E339A404533656C (void);
extern void BezierUtility_ClosestPointOnCurveFast_m5D0F9E0CBF9DFA9DAB1E5D86868A42DF55D0E461 (void);
extern void BezierUtility_ClosestPointOnCurveIterative_m78F8F9079174AB5B04E12729AF83906A2F5FDE87 (void);
extern void BezierUtility_SplitBezier_m9593DD5DFA7501AB75001CDA9D691C9190A79B85 (void);
extern void BezierUtility_ClosestPointToSegment_mFB7C681C534EA0EC34965D3E301AADD059B0E8F6 (void);
extern void BezierUtility_SqrDistanceToPolyLine_mD6FF1BD8D9084329F65B51AAA813BC017AC21C4F (void);
extern void BezierUtility_SqrDistanceToSegment_m4D20F35424BCEA2DCB615C14889D261F4176D859 (void);
extern void BezierUtility_Colinear_m362FB913354FA25183931964CD05E0D071D4754A (void);
extern void BezierUtility__cctor_mAB51CBA927852DC032BECA9760D60F887C98BB7C (void);
extern void Spline_get_isOpenEnded_m09BD0EAEDB52B06A28C0D5718F63043791DC6861 (void);
extern void Spline_set_isOpenEnded_mB2DDFA961D86385B8DDEA3685E1EBE5B2794F3CC (void);
extern void Spline_IsPositionValid_m9D5B86A0EC6C11CAD23748553C28DE1D6BE65AD1 (void);
extern void Spline_Clear_m90018779EEB8B0130F1C0FFFAFC0B9D5C75C840B (void);
extern void Spline_GetPointCount_m9A872A9A4C7CA81296EF70F93D48B8C47A5F7415 (void);
extern void Spline_InsertPointAt_m2615F34617A42F174ADEE4AE74EB97EFB07CBEE8 (void);
extern void Spline_RemovePointAt_m5A344FCDA4EABC1F431BE3B8E89BC193D98627F3 (void);
extern void Spline_GetPosition_m68F7EECA5140A4D6C0FAA8AB9F8DED9BF214CF9C (void);
extern void Spline_SetPosition_m4E21C4A9BBC833A874550A9C264D804682A3BB82 (void);
extern void Spline_GetLeftTangent_m722F61220067D72DD8CDD817E9174F331778A398 (void);
extern void Spline_SetLeftTangent_mCCBE0DC5309767DBBF8B71060441F67ECDC568E9 (void);
extern void Spline_GetRightTangent_m6008A3450C5E0D85D57FA8A1453670CBC5B280EF (void);
extern void Spline_SetRightTangent_mCBCD52EAFAD250ADEFF2DD8320CCD269FDBAE1B9 (void);
extern void Spline_GetTangentMode_m2C2C980AE7A7C3C2B253033A24636586C33AF43D (void);
extern void Spline_SetTangentMode_m85B50417E072CF9EBA8ED0879B784D05264699C3 (void);
extern void Spline_GetHeight_m06E2206C720560307DD537BFD984EFB7E60B2A51 (void);
extern void Spline_SetHeight_mCE3D0999537E246E6796D118B8AC6A5AE07E2E29 (void);
extern void Spline_GetSpriteIndex_m83CE4FDE1C4AFF2ECAC9D05FE8AEC262D37B3D25 (void);
extern void Spline_SetSpriteIndex_m8C3259530B010FA108B751CD02F721FAD16879F0 (void);
extern void Spline_GetCorner_m1F6AE552B79F42077369DB3117CA249788D8F876 (void);
extern void Spline_SetCorner_mB0BFDAE0E37D28FB18F007881472B99BE6F88759 (void);
extern void Spline_SetCornerMode_mB5CEDAB1AD77EA3E1981387E30DF60C10B150358 (void);
extern void Spline_GetCornerMode_m7EB28D7DA4B3092DC077C4529F3C02321E55F0D5 (void);
extern void Spline_GetChangeIndex_m1510C6FEBEF8ED5A66E81A5434FF5D984518AC74 (void);
extern void Spline_GetHashCode_m74B9851C65265BFE98C03AE31BB0CB1DB34F18A7 (void);
extern void Spline__ctor_m77058E9CAC3C3BF9252B278EFDCAC44A1335077B (void);
extern void Spline__cctor_m5BB2E03036FA75F2681DDCC4B73C3917BF1B0D81 (void);
extern void SplineUtility_SlopeAngle_m44A0B68A01BC0F6ED3255011DDE2388530E3B8B8 (void);
extern void SplineUtility_CalculateTangents_m1410CAE7A0FF8C4A9ED479CB6B6BA92B73CA59D4 (void);
extern void SplineUtility_NextIndex_m8F7FCF6B59B163E7E3A11AC4303EE2ED1F407D2C (void);
extern void SplineUtility_PreviousIndex_mF11B26DB018A7D9F823BCD9D7BE872D17D0C9BB4 (void);
extern void SplineUtility_Mod_m1E360DCA061CFC1F7545F80B720608AE292744B2 (void);
extern void SplineUtility__ctor_m048BC127ECEA698E37616379AA08A97A64906FA1 (void);
extern void SplineControlPoint_get_cornerMode_m95DB1388626D5DB42A4490B3A6998AB7DE6D48AF (void);
extern void SplineControlPoint_set_cornerMode_m1CCDB4EA35131D2DB1B0A117CDE6B34117D935C6 (void);
extern void SplineControlPoint_GetHashCode_m3CB65BFD0FA3BD8428CE5991C73B5039BBCABEFA (void);
extern void SplineControlPoint__ctor_mC43E9567FABB0F0F28B7AF0BA52B346704AF44E1 (void);
extern void AngleRange_get_start_mD0468EC3E8E1935141A977DB0593921DCAEF4738 (void);
extern void AngleRange_set_start_mB85C33F416988AF1A7A896FFAF94326B28EE27DE (void);
extern void AngleRange_get_end_m2C9ED13CD1759C45BFE955F05A126EE5869DF1E2 (void);
extern void AngleRange_set_end_m57A30FA8AD7BDFCC672C65EEB4967D201F54E187 (void);
extern void AngleRange_get_order_m4B94B4ABE3C9280BC7D7C42C3BAFD215C4FDE65A (void);
extern void AngleRange_set_order_mCFC0245542FC55D286442DDC9EF3E5FE8D6726F2 (void);
extern void AngleRange_get_sprites_m28E5E8275AD72ABF48906F92189ACD56F7855744 (void);
extern void AngleRange_set_sprites_mA53B82D0E47C10B042E6D3324E693416D9A7BA37 (void);
extern void AngleRange_Clone_m1F1EFC618FCB936BFCF052344E27B0470933514C (void);
extern void AngleRange_Equals_mB03532F9BFF710AD77CE481CB027E1151D658A2D (void);
extern void AngleRange_GetHashCode_m1E1CCEAFA6658CBAA130382B022A0EF8071EF7C0 (void);
extern void AngleRange__ctor_m3F4962B6208CFFDB3B458EEDF9698FE2B64FD7E6 (void);
extern void CornerSprite_get_cornerType_m3AAD26BC2C63513DE93092A7F1FE89C851642BC5 (void);
extern void CornerSprite_set_cornerType_mEC433705D57B3EDC4804B0243C852B6354E89332 (void);
extern void CornerSprite_get_sprites_m3114E6A75522F8924A235950378A2419D8C41FB5 (void);
extern void CornerSprite_set_sprites_m713C5FEFAAB212FA42AEF83FEC42509D02414979 (void);
extern void CornerSprite_Clone_mC2E08E79BB69C8B45220FD765D38A6509B2BFE78 (void);
extern void CornerSprite_Equals_mA88F7927D0856777B66F585894C168806AF7B3EA (void);
extern void CornerSprite_GetHashCode_mB40CABE597D887F4BB3BDBDADAC0D0D799C2E379 (void);
extern void CornerSprite__ctor_m129B0F37BC95A899CE43D4130B64ABB7301F1894 (void);
extern void SpriteShape_get_angleRanges_m0C62D6A5E07E32DCCF9EE67FABF0EFC60D0D0745 (void);
extern void SpriteShape_set_angleRanges_m3DEEE05F24BDA0C0584928E64645C50251AEC3E8 (void);
extern void SpriteShape_get_fillTexture_mCFAA6A4C131C79E81A2ED7333609FE357C83D627 (void);
extern void SpriteShape_set_fillTexture_m953DBF70A2E67363D79ABE0957E91D59EDDD5F9E (void);
extern void SpriteShape_get_cornerSprites_m087D4444A163AA72121E45EE562260C2ACED951C (void);
extern void SpriteShape_set_cornerSprites_m19AB6640AC7E4DCCE883BEC88E44A5B26D8B3F42 (void);
extern void SpriteShape_get_fillOffset_mAB5DCDA5724331DAD614D975D32C5040708BF601 (void);
extern void SpriteShape_set_fillOffset_m76103B635C4175AFC103BEF302EE2050F7C58FD9 (void);
extern void SpriteShape_get_useSpriteBorders_m71A77DB6C2DD9240181C19700E2E68A2CC69F964 (void);
extern void SpriteShape_set_useSpriteBorders_mA6F0796BA9450B971B65D1487648A43F311DA57D (void);
extern void SpriteShape_GetCornerSprite_m33DA8C403F036A099D246C22879E7D099C80E004 (void);
extern void SpriteShape_ResetCornerList_m8B8505F12CB2798A97C301E9E4AC3216CC08A536 (void);
extern void SpriteShape_OnValidate_mDB9ED7A83A57AA09DDACB7E4EC0BB412BAF786EB (void);
extern void SpriteShape_Reset_m46FA053BEA2E457CAD8C8E8A9B131559FFFAD521 (void);
extern void SpriteShape_GetSpriteShapeHashCode_m9212F83587FD6084A616CE5E897F40146CAF3AF5 (void);
extern void SpriteShape__ctor_m498F0AB94EAE1AF1392438B694711E79143628B6 (void);
extern void SpriteShapeController_get_maxArrayCount_m1CD21D97E9DFB36FB05C64261E6B2AE8AE7A9F29 (void);
extern void SpriteShapeController_set_maxArrayCount_mE4A6A50AAECB96C743CC1A701840776699833ECD (void);
extern void SpriteShapeController_get_geometryCached_mD9C381E9B9CF0720F751F3E8DDBCA7F810D2A07A (void);
extern void SpriteShapeController_set_geometryCached_m9EB40925B5661EF984777327FD4F9782B8AB2B53 (void);
extern void SpriteShapeController_get_splineHashCode_m5FD24A66B300EA0F8E1A1B0E5100ED3D0AA8FB98 (void);
extern void SpriteShapeController_get_spriteArray_mE12E018678D8DFDA098E94255BDEE7E488292DAB (void);
extern void SpriteShapeController_get_spriteShapeParameters_m5EB8EFD2C04CC8797FDC67F2E381AB833D287AA0 (void);
extern void SpriteShapeController_get_spriteShapeGeometryCache_m677A25E93851BACE158237F9878AE78D21E17AE8 (void);
extern void SpriteShapeController_get_cornerSpriteArray_mF0A3ACBD990E4DCE4BFE3CA2192C0058DD3F7E9E (void);
extern void SpriteShapeController_get_edgeSpriteArray_mB562E639F3951B0A5432AA7F9BA18BCD69387071 (void);
extern void SpriteShapeController_get_shadowData_m6D637C25E16688807369372E8B00026509CD1EB3 (void);
extern void SpriteShapeController_get_angleRangeInfoArray_mED8D0B5CD5EE8724A44A6CA86DCE37AE085C11F4 (void);
extern void SpriteShapeController_get_spriteShapeCreator_m3E277D63975226E2A58979508A782BC02117C95A (void);
extern void SpriteShapeController_set_spriteShapeCreator_mCC6692B6ADA3C0812401E066A030F518EC74C2EA (void);
extern void SpriteShapeController_get_modifiers_m5CE76EC7CD1814B13FFDA0807356972AE59B80A3 (void);
extern void SpriteShapeController_get_spriteShapeHashCode_mF2D6ABCAF590DFC5A0461E61A8B78356FD2FB520 (void);
extern void SpriteShapeController_get_worldSpaceUVs_m334F4BDD28C1980124898063E3FA35A82E48E8A3 (void);
extern void SpriteShapeController_set_worldSpaceUVs_m3426FE81A540AD44FBE6CB78368C846902BA1D3D (void);
extern void SpriteShapeController_get_fillPixelsPerUnit_m03D2274AA79D8FE84520825C4658C89328B40AC9 (void);
extern void SpriteShapeController_set_fillPixelsPerUnit_m838E1E5F179458D2483A4B7D60595B8677E8B2F3 (void);
extern void SpriteShapeController_get_enableTangents_m3B01758CFC4CF46ADF8707266A87F506C68FF6C9 (void);
extern void SpriteShapeController_set_enableTangents_m213DBDA0EB1F827A4255E47B6E7D229EEFCF73B0 (void);
extern void SpriteShapeController_get_stretchTiling_m8B867630BCC7929A238CB8F43F687F3F9618C658 (void);
extern void SpriteShapeController_set_stretchTiling_m51EEDC3EBAEDFDD5393CD1A371904D1113921E6A (void);
extern void SpriteShapeController_get_splineDetail_mB03C316DE81143E38A30C15C339D36DAD7C56E33 (void);
extern void SpriteShapeController_set_splineDetail_m9580F13305955BA3C89914EBD8EB99EF11249325 (void);
extern void SpriteShapeController_get_colliderDetail_m11726DDBB4219DB8DB66B0C34ACFB877DA82330B (void);
extern void SpriteShapeController_set_colliderDetail_m9DB395B7A2C311E17542FADF33DB04128F3E2E3E (void);
extern void SpriteShapeController_get_colliderOffset_mFE87071B23F19A7C3CF672E12AD049D3D92CBBCF (void);
extern void SpriteShapeController_set_colliderOffset_m1A87B0F5B035C07393CC6412504E8CD0A681BFED (void);
extern void SpriteShapeController_get_cornerAngleThreshold_m3A66C56D956590DF46B1EC99F18D8E17BA3D28B1 (void);
extern void SpriteShapeController_set_cornerAngleThreshold_m3757272F360166C704601DD56FA4014B6D20E2C1 (void);
extern void SpriteShapeController_get_autoUpdateCollider_m54D843E58B97438ACF30071768B0F5FB4A8473B7 (void);
extern void SpriteShapeController_set_autoUpdateCollider_m70DEFCF590D8D3CDDE1082B7E96226C993B00D18 (void);
extern void SpriteShapeController_get_optimizeCollider_mCBE85BA595F9221E2442125F6615A958BA1353EC (void);
extern void SpriteShapeController_get_optimizeGeometry_m7D907B4FB2924B44C252DE590CA3F151B6C711D9 (void);
extern void SpriteShapeController_get_hasCollider_mB354FB04E19BA1CBC5F486BB32DF692382FBDD42 (void);
extern void SpriteShapeController_get_spline_m1266CB84F33024475FA4FC6C2F35E4E3E74AD6B9 (void);
extern void SpriteShapeController_get_boundsScale_m19CD4C76DF1DDCD5D74D0E1E578179E7B3A8BC65 (void);
extern void SpriteShapeController_set_boundsScale_mB43FE245DD2766D8B1928C6B6D56ED76B0940878 (void);
extern void SpriteShapeController_get_spriteShape_m47FA441FCE1F593405E92D174E556EDC8DD4D260 (void);
extern void SpriteShapeController_set_spriteShape_m1C26E9221CAF1319F40D50D8967F9C5D6198C7AE (void);
extern void SpriteShapeController_get_edgeCollider_m2E6019F72F1AFB428BF9D2089BEDA849D0D9577E (void);
extern void SpriteShapeController_get_polygonCollider_m225EE23CFBB87B3FFFACFF7CC6B2CA5515236015 (void);
extern void SpriteShapeController_get_spriteShapeRenderer_m1CE6071F363FDDC49999CD3482311619EDA97F00 (void);
extern void SpriteShapeController_get_updateShadow_mE4867AC0CCEB59E1A84FB5838549B06A76744237 (void);
extern void SpriteShapeController_set_updateShadow_m9DD4AAFA8325D765049D6DAEFCF4F4BC0839B2A0 (void);
extern void SpriteShapeController_get_shadowDetail_mD3D6007CEB98D403E2AFDCC95964064AEB19A06A (void);
extern void SpriteShapeController_set_shadowDetail_m05EA9DC9F7477FBAD89EF9450DBD715B6EEDB119 (void);
extern void SpriteShapeController_get_shadowOffset_m8DE3562CAD5C0F9D85A994D43CEC58EB71CC0182 (void);
extern void SpriteShapeController_set_shadowOffset_m19289874F3727373E359E8F9385480043280EDE3 (void);
extern void SpriteShapeController_get_shadowSegment_m1483EF0CB76FC6CD13ACCC66F4B73412CE1DF59A (void);
extern void SpriteShapeController_get_stats_m3130C2A150F3CFF99A6FD0D5EC06147E907D96FD (void);
extern void SpriteShapeController_get_WaitForBake_mCFEB55DB30F87647EBA33F2FA42EC7CFC72E02BB (void);
extern void SpriteShapeController_set_WaitForBake_m8D0507BE12B808039A1B54BB2A9ED1746915005E (void);
extern void SpriteShapeController_get_autoUpdateGeometry_m54BF6EDE449B378252E42D4E356F0BB35143A119 (void);
extern void SpriteShapeController_set_autoUpdateGeometry_mDC9CC6F1CF726D454F31C56E2209176BA5E01153 (void);
extern void SpriteShapeController_DisposeInternal_mAAEB238F72B78D551F83EB312D6619987899B98D (void);
extern void SpriteShapeController_OnApplicationQuit_m8A657FEEFC44C425C81EFC0E79E593B9E7CB0CB4 (void);
extern void SpriteShapeController_OnEnable_m9DDC5591B1D66745D6AD2922FA54119E39E8CC55 (void);
extern void SpriteShapeController_OnDisable_m0205031B7FF69736F230D4045F256AA66335DA76 (void);
extern void SpriteShapeController_OnDestroy_m3CE9200A30C616BE7761C2B73A725772FD46D18D (void);
extern void SpriteShapeController_Reset_mBF3168EA991377B3FCD3C1F48F5BC20DE54757C3 (void);
extern void SpriteShapeController_SmartDestroy_m2536D729E67C1909A7FC8C1C1691408C7BE04C06 (void);
extern void SpriteShapeController_InitBounds_m2B81084A9168AD6B0C98629900A76A579408DD79 (void);
extern void SpriteShapeController_RefreshSpriteShape_m35888DA0521C69F3B6E56A69C2DF4C53CFC419BB (void);
extern void SpriteShapeController_ValidateSpline_m574D40EEB9533DC63C07FE45A83E5434A8A950D9 (void);
extern void SpriteShapeController_ValidateSpriteShapeTexture_mD3A351394676E4AB9C1EA7127E77E144D6A05B00 (void);
extern void SpriteShapeController_ValidateUTess2D_mC71A1EC29B1BC0FF00C49C695A96846D709F458E (void);
extern void SpriteShapeController_HasSpriteShapeChanged_mD2828F60582F8702356BBEB50CC95A7A6BA55237 (void);
extern void SpriteShapeController_HasSpriteShapeDataChanged_mCC909CA241E0D2E116A51C32B8A22CC7D4726F58 (void);
extern void SpriteShapeController_GetCustomScriptHashCode_m42FEDCC62FFACA8D3C5F1FE06309D57634D3CAA4 (void);
extern void SpriteShapeController_HasSplineDataChanged_m5AEE03084F30CD6E726BF388636C8B5CD419CCB2 (void);
extern void SpriteShapeController_OnBecameInvisible_m2ADE87FA7014E00DEE586DA6B1A8440BBD81C142 (void);
extern void SpriteShapeController_LateUpdate_m5D3DECBC12536092B882C2028C7B4E4C634C747D (void);
extern void SpriteShapeController_OnWillRenderObject_mD1D3D260CAE2FFFFFDC26F0FE059C020A9C62D1C (void);
extern void SpriteShapeController_BakeMesh_mD8809E911D574437001B446DDFA90724BF384743 (void);
extern void SpriteShapeController_UpdateGeometryCache_m4D27DBA3C6C424293BDD963C968C0B2014C7C7F7 (void);
extern void SpriteShapeController_UpdateSpriteShapeParameters_m10CFBD964FC92FC1619319ABAA545A1391FC1296 (void);
extern void SpriteShapeController_UpdateSpriteData_mD8A87BDA7AC4BC6EA3521AEE52B38F62C01A579D (void);
extern void SpriteShapeController_GetShapeControlPoints_m4A0BF704DB4274546156AC4085353536541B30D5 (void);
extern void SpriteShapeController_GetSplinePointMetaData_m56AE94299696DA36F56EE24B792FAD9892ECF786 (void);
extern void SpriteShapeController_CalculateMaxArrayCount_m3460A750AFF7144E55395AF683DD508233190ECB (void);
extern void SpriteShapeController_ScheduleBake_m8ECD1C9C3FC8EB1E7194CE61723ECE10A7C74D73 (void);
extern void SpriteShapeController_BakeShadow_m9C49E19FCD75A0A954296DD323A60B63F3BFAD09 (void);
extern void SpriteShapeController_BakeCollider_mEFCB850D14CBB3949E2C7FCD62A24FA5C06593D7 (void);
extern void SpriteShapeController_BakeMeshForced_m69B0B10D80E51470C0F7C12A9595986CDEACC559 (void);
extern void SpriteShapeController_ForceShadowShapeUpdate_m93C5104E9CADBBC62894A8002005EC5F6FF37A50 (void);
extern void SpriteShapeController_GetShadowShapeData_mDF06899BCBB123DC0C6C4258B624A22DAAA0556C (void);
extern void SpriteShapeController__ctor_mBB2958EC26C34FE3C1097186C45AA80F3A1AC31D (void);
extern void SpriteShapeController__cctor_m58C44D77C0B734C34DB44B7A128A8B24E4E42531 (void);
extern void U3CU3Ec__cctor_m46528AD52B64571CEF44A8F2722DC493302EB6C2 (void);
extern void U3CU3Ec__ctor_m3D2A053B5948BDFC84870DCBEAD76989CF259BF1 (void);
extern void U3CU3Ec_U3CUpdateSpriteDataU3Eb__166_0_mA1368E9968F5B0A4865D4994245C3AB3703B2823 (void);
extern void SpriteShapeDefaultCreator_GetVertexArrayCount_mDDE727F55D07FBBF570F7617443C2782ED87B4CC (void);
extern void SpriteShapeDefaultCreator_MakeCreatorJob_m95E60274C1A0757B5AC8B71DBF5142F1C7E5379C (void);
extern void SpriteShapeDefaultCreator_get_defaultInstance_m3D7F2CB26AD65BA670E221E672716B4AEB5764E4 (void);
extern void SpriteShapeDefaultCreator_GetVersion_mBF55AB1CA538BEBDAC4AD7C3D6D416C976E22995 (void);
extern void SpriteShapeDefaultCreator__ctor_m1686195DD7A0692725025943A87FEF4130923707 (void);
extern void SpriteShapeGenerator_get_vertexDataCount_mDC30ECF81BCDFA0769E3F7EACE2718E929637EFD (void);
extern void SpriteShapeGenerator_get_vertexArrayCount_m776FBDEEB5A98DDBE16B219D2587D981B606D910 (void);
extern void SpriteShapeGenerator_get_indexDataCount_m1E6F0585FA2A3F592856EBB491409A7B39ABBB16 (void);
extern void SpriteShapeGenerator_get_spriteCount_m8D39765E70D833493EEA503ED982F410EA9DFA53 (void);
extern void SpriteShapeGenerator_get_cornerSpriteCount_mF842CCBF80F27E03641B10A9A12D721EA6957642 (void);
extern void SpriteShapeGenerator_get_angleRangeCount_mE6EB56E420B87315E754A66D294CF201F93FA1A8 (void);
extern void SpriteShapeGenerator_get_controlPointCount_m0B72EE45FD6E4BF63650958439B43712ADD39696 (void);
extern void SpriteShapeGenerator_get_contourPointCount_m45CA4B09B39611D4A1C09F494A3BED828258CC41 (void);
extern void SpriteShapeGenerator_get_segmentCount_m5B0A226EBA7CA12B095A92DB429DEB30DF3C7210 (void);
extern void SpriteShapeGenerator_get_hasCollider_m6C79BA0841071B0B97D33C195716F14C0B5453DC (void);
extern void SpriteShapeGenerator_get_hasShadow_mF8B09961DA6FE6A05AA217EA3DAC9FF8D026FBB3 (void);
extern void SpriteShapeGenerator_get_colliderPivot_m8D45A62C4E63DFD67D502547812616043B2582E7 (void);
extern void SpriteShapeGenerator_get_shadowPivot_mDF1695D06772C0DF9F3D30F628780F1EF4A2B39E (void);
extern void SpriteShapeGenerator_get_borderPivot_m1CB177826C97190744B42A7A39F1E4B2ACA17941 (void);
extern void SpriteShapeGenerator_get_splineDetail_m3739C3D78D2F7D5EE0C31ADC7EFF1A28E1620D5F (void);
extern void SpriteShapeGenerator_get_isCarpet_m82A7BACAF2824B38318697B6BD0BC97FE07624B2 (void);
extern void SpriteShapeGenerator_get_isAdaptive_m8022015FFC581294D888FCC63FEDC6B503985C85 (void);
extern void SpriteShapeGenerator_get_hasSpriteBorder_m015043D5050787F1744E27679E6681101C24C01F (void);
extern void SpriteShapeGenerator_GetSpriteInfo_mC06825216D7CF04273E559CBBACEA742CCCC129E (void);
extern void SpriteShapeGenerator_GetCornerSpriteInfo_m6170012E001B82F923367E339A60AE997AEC7335 (void);
extern void SpriteShapeGenerator_GetAngleRange_m5FCB024AC77D929F60F35E4852920B49EC40D53F (void);
extern void SpriteShapeGenerator_GetControlPoint_mF73708A8E099838AC8806D2EE1305D230C709A37 (void);
extern void SpriteShapeGenerator_GetContourPoint_m7BD62D1044854A6E2811C65FC8C4C3451133A20A (void);
extern void SpriteShapeGenerator_GetSegmentInfo_m72363F976D7D18181DED537467749537D0999DA9 (void);
extern void SpriteShapeGenerator_GetContourIndex_m8705A508B3B54F8268411F3FDF3DCBD030705500 (void);
extern void SpriteShapeGenerator_GetEndContourIndexOfSegment_m34A178F013D01C7EA2713CCE22E43BFF85D2E8CD (void);
extern void SpriteShapeGenerator_SetResult_mB943A248C968BCA647038BD8CF5F568BB4669475 (void);
extern void SpriteShapeGenerator_IsPointOnLine_m9ADB4EB472366B1B12FE900C9395F6A50A01DE8B (void);
extern void SpriteShapeGenerator_IsPointOnLines_m2CA862D27FF37797951B22A515F697BFE97848C4 (void);
extern void SpriteShapeGenerator_Colinear_mD902FD4B26FE511CE9F0177B99A0BF16AD82DB41 (void);
extern void SpriteShapeGenerator_Det_m1CBF814EBFAF65729676E04E7C2B25556B96BD34 (void);
extern void SpriteShapeGenerator_LineIntersectionTest_mE62B6C1918AFA11BD5F4B17D32063D86DFD374BC (void);
extern void SpriteShapeGenerator_LineIntersection_mB00C5425B41C9CA3C6665C14817D7BA812F0C68C (void);
extern void SpriteShapeGenerator_AngleBetweenVector_mE576D3B5B635113A6402D84E3C7E9C02898E2685 (void);
extern void SpriteShapeGenerator_GenerateColumnsBi_m432FE4F034EE3FEB5856B11DD3F08AD87B8C5CB5 (void);
extern void SpriteShapeGenerator_GenerateColumnsTri_mC86F836BF87D9E75633C54280BADB76A9751B8D4 (void);
extern void SpriteShapeGenerator_AppendCornerCoordinates_mDCB86690220150FEE68F15E621D8507E22095B2B (void);
extern void SpriteShapeGenerator_PrepareInput_mE4FDC5415D5B004748A141D7F51383B5255E0FBF (void);
extern void SpriteShapeGenerator_TransferSprites_m7EB37DB492012CA10801310B4A6AB9D23C9D604A (void);
extern void SpriteShapeGenerator_PrepareSprites_m5423E297D56773AD47D53877570C57D2B38B4E24 (void);
extern void SpriteShapeGenerator_PrepareAngleRanges_m612B2151F30299B9B7AACDA7A041510E65EB618B (void);
extern void SpriteShapeGenerator_PrepareControlPoints_mABC163490DAF124A972C1440B19D0140EB2673D4 (void);
extern void SpriteShapeGenerator_WithinRange_mA4F9BD34C7AAA5111DF48DFCC66FCE28FB70E31E (void);
extern void SpriteShapeGenerator_AngleWithinRange_mBFC8803A87435D94B5AA0D8E779A79729AC7FC90 (void);
extern void SpriteShapeGenerator_BezierPoint_mF0FE84A56DDB2BB6D2DF7C76053FFFF41EFFBA58 (void);
extern void SpriteShapeGenerator_SlopeAngle_mA77578AE85D18BE9DBA77F08EDA58F854C3FEB38 (void);
extern void SpriteShapeGenerator_SlopeAngle_m4B4B9571E7C898E103D6FEB88D6D6B22019A4011 (void);
extern void SpriteShapeGenerator_ResolveAngle_m718F26C825AD850CDE3B9780C7EA4F4FD09360A1 (void);
extern void SpriteShapeGenerator_GetSpriteIndex_mFE565014AF490E45B712164C61A27C3E6B230163 (void);
extern void SpriteShapeGenerator_GenerateSegments_mFD269D1C42D371CD823610D0DF68AFAF6C221E9F (void);
extern void SpriteShapeGenerator_UpdateSegments_m7D6F5BA1B8A1E53960801B0B947F46CAE379AA8E (void);
extern void SpriteShapeGenerator_GetSegmentBoundaryColumn_m93EFD1563B99BCBA319FA4A4ADF2F9CA382190CB (void);
extern void SpriteShapeGenerator_GenerateControlPoints_mAD773E2807C8D8E29AC20CD125A78C516A1C6F82 (void);
extern void SpriteShapeGenerator_SegmentDistance_mC59D5EFEDEEB7AA90180AF4B4F53DF6BF49BFDD5 (void);
extern void SpriteShapeGenerator_GenerateContour_m6EBB4C1E95B82166E226FD2EAE9B33341EBBB5A2 (void);
extern void SpriteShapeGenerator_PrepareContour_m89E38CF7A6CD6D93457277CA6AA43BA7BFA61973 (void);
extern void SpriteShapeGenerator_UTessellator_mA975B9F66E80F2334124BFADD19F331AB2F6E90D (void);
extern void SpriteShapeGenerator_TessellateContour_mF70D92C350C736C57382EAD8AB02413344FD5545 (void);
extern void SpriteShapeGenerator_TessellateContourMainThread_m4509E90FD48DB95C993D9144DDA67B825F809EE1 (void);
extern void SpriteShapeGenerator_CalculateBoundingBox_m51E8608113267A80B08E671CD0E6F9E98403DE3A (void);
extern void SpriteShapeGenerator_CalculateTexCoords_m601199E7DD5A81D4586B708E9BF175692744B3A3 (void);
extern void SpriteShapeGenerator_CopyVertexData_mBD9B93DAB3A30EA5D20DD8EB415C91437C957F0D (void);
extern void SpriteShapeGenerator_CopySegmentRenderData_m48B6A14833D7B597E6E97A6F53E1542FA0A23F76 (void);
extern void SpriteShapeGenerator_GetLineSegments_m42433AFD66A0FE4716E83F2D8F1EB3FFBAE614FE (void);
extern void SpriteShapeGenerator_TessellateSegment_m85D8FE88E38E3AC599183C5CB25D61EF203C3878 (void);
extern void SpriteShapeGenerator_SkipSegment_m60AD06EAE4EC71495A2C4DDF16E30CCCC7AC48D1 (void);
extern void SpriteShapeGenerator_InterpolateLinear_m6655E2A9307AFE3E0F947E22789FD1D6709260BB (void);
extern void SpriteShapeGenerator_InterpolateSmooth_m8A19C193EC1ECCA8116A4FE8834C22E4CDC38A1F (void);
extern void SpriteShapeGenerator_AddVertex_m299A47F7A33CAB37533B90ACF8AD971DD9D358F2 (void);
extern void SpriteShapeGenerator_TessellateSegments_m42A1531B9F7C1DEC85DB7367542CFEC3D90E7FAA (void);
extern void SpriteShapeGenerator_FetchStretcher_m62E10BC6A8E62E8B07FAB981EA8791443792ECFC (void);
extern void SpriteShapeGenerator_StretchCorners_mEF433099727D0C24530F28C9AA5A688FD001AEF4 (void);
extern void SpriteShapeGenerator_ExtendSegment_mA0441D565DA580DFA03ED402D55C01FCE45A62DC (void);
extern void SpriteShapeGenerator_GetIntersection_m7A6907C124D4C476D9F6A474EB8D2C32B3ED22D0 (void);
extern void SpriteShapeGenerator_AttachCorner_mE870FE2AFC67B7EA889191D1D4290690B3D9AEDE (void);
extern void SpriteShapeGenerator_CornerTextureCoordinate_m832C4406CE02463EA7B1E0B1BAD91F0D3902E485 (void);
extern void SpriteShapeGenerator_CalculateCorner_m2889F0F78372F001B2827FC7F97311A6FF801828 (void);
extern void SpriteShapeGenerator_InsertCorner_m2A32C77F92888AAEFDE80128460D5FAAE8C8DEC5 (void);
extern void SpriteShapeGenerator_TessellateCorners_mBEC539F9EBD424BC9682C3EF748E500E2D1A5C27 (void);
extern void SpriteShapeGenerator_AreCollinear_m2CF9A2780D5320C72BBC9415DDEC1A04FE57D89F (void);
extern void SpriteShapeGenerator_OptimizePoints_m12067570C5E61E1291B57D9C6F8D284CCE19F46D (void);
extern void SpriteShapeGenerator_AttachCornerToCollider_m2468C75A72FD1AFD391A373FECDB44FABF9BE78F (void);
extern void SpriteShapeGenerator_UpdateExtraGeometry_mECAD4B1A09D3025A61B93F90029FC864AFD2D6EF (void);
extern void SpriteShapeGenerator_TrimOverlaps_m259624880804C1A90EDE8B6621DDE70315F3137B (void);
extern void SpriteShapeGenerator_OptimizeCollider_mBA6D3CB699F42750C239CA0BC201B672B1FD9AA8 (void);
extern void SpriteShapeGenerator_OptimizeShadow_m23799E7E865F53073846F752F6495DFBEBB0ACB6 (void);
extern void SpriteShapeGenerator_Prepare_mF79A57490E60706BE72D78830EC71DEC17B1DCF1 (void);
extern void SpriteShapeGenerator_Prepare_m8358A9BD95A1D45F0FBE63A5661421DD0D8B4DB7 (void);
extern void SpriteShapeGenerator_Execute_m6F11F51210E20869A78D43F7AD6E0702D8515A91 (void);
extern void SpriteShapeGenerator_Cleanup_m2662B1967B4613B611E1E7E57341E0E7862B7FF1 (void);
extern void SpriteShapeGenerator_UTessellatorU24BurstManaged_m6F4F57B7363784CE1724AE26829C356319E88118 (void);
extern void U3CU3Ec__cctor_m3B0AC2339A8DE4BC31CEDD5BADA65B9D7949A349 (void);
extern void U3CU3Ec__ctor_mA91FE5EA2C14E43491278E311B2F74B7478EF999 (void);
extern void U3CU3Ec_U3CTessellateContourMainThreadU3Eb__155_0_m4DC5973A9C496EEA6F7E0098F7A5D14133CF8FA8 (void);
extern void U3CU3Ec_U3CTessellateContourMainThreadU3Eb__155_1_mF10AA4653D6AE7C5ECCE75B6EE424F6A82D6B37C (void);
extern void UTessellator_0000017FU24PostfixBurstDelegate__ctor_m7D82A379E92F612522917026D1FBB234A0299719 (void);
extern void UTessellator_0000017FU24PostfixBurstDelegate_Invoke_m3FE651BD796903A59B0486C44F86A6101BB31024 (void);
extern void UTessellator_0000017FU24PostfixBurstDelegate_BeginInvoke_m098909B481B8513DBEABBFAF8389B36B3B207D98 (void);
extern void UTessellator_0000017FU24PostfixBurstDelegate_EndInvoke_m0CA934E4E8F4AF159D45C6766236E3C036AA0304 (void);
extern void UTessellator_0000017FU24BurstDirectCall_GetFunctionPointerDiscard_m674170764EDF0A0753BD85F5B4326892F27ABA44 (void);
extern void UTessellator_0000017FU24BurstDirectCall_GetFunctionPointer_m5DDBAB15141B65F9C34F7A02735B490B7CB79DFB (void);
extern void UTessellator_0000017FU24BurstDirectCall_Invoke_mA24AF835EA088D43CAC380DDCAB35C8BD81A688C (void);
extern void SpriteShapeGeometryCreator_GetVersion_m44860C0EE661F0B0B7D145D084BA7D5CE6C7ACA8 (void);
extern void SpriteShapeGeometryCreator__ctor_m11995DD5FE4D69A420FABBD8AE3DB243E9D4EF7B (void);
extern void SpriteShapeGeometryModifier_GetVersion_m279A107B00A417E5058A6456CEAA750C66C88D89 (void);
extern void SpriteShapeGeometryModifier__ctor_mC8B2D08FC71266F54041FAD12592B2009883C48E (void);
extern void SpriteShapeObjectPlacement_get_setNormal_mDF4F4E07D5CD4B8A7D5A3B1A280BB21FBD8A0567 (void);
extern void SpriteShapeObjectPlacement_set_setNormal_mC372153584297F251B6C0010110900584F0C7FC4 (void);
extern void SpriteShapeObjectPlacement_get_mode_mC07CCEC65A4CF7075CE09754A2B503D3926E428E (void);
extern void SpriteShapeObjectPlacement_set_mode_m4F946059DDF7E8EEBC818BE0ECCE59C3A7193F2F (void);
extern void SpriteShapeObjectPlacement_get_ratio_m1AD37D2E67595975F88822C70707DF5EFB8B5F3B (void);
extern void SpriteShapeObjectPlacement_set_ratio_mED658B93A3A246178CCB30DEC877EA54DA1842EC (void);
extern void SpriteShapeObjectPlacement_get_spriteShapeController_mA90BED3A705B97FDC4B368365F65922EB268BB2E (void);
extern void SpriteShapeObjectPlacement_set_spriteShapeController_m20610E17F7CF90F7DA016ED8D7F3E335AACE7F01 (void);
extern void SpriteShapeObjectPlacement_get_startPoint_m1C539FDCDDB00A0918771704D55D113549A08D12 (void);
extern void SpriteShapeObjectPlacement_set_startPoint_m2C880BECFF67C3478402FF2A88C8F5F5E2E60CA6 (void);
extern void SpriteShapeObjectPlacement_get_endPoint_m1183FA74B1AF9A9806EC708DC9AB9FC183983335 (void);
extern void SpriteShapeObjectPlacement_set_endPoint_m62B77B64D9F7332D75A19F768B1CFF1FB919C040 (void);
extern void SpriteShapeObjectPlacement_PlaceObjectOnHashChange_m71263E4A1E32BA74D19300AC80B4D9A544441692 (void);
extern void SpriteShapeObjectPlacement_Angle_m133A7031202A395E8963E07B72900C9559763B6E (void);
extern void SpriteShapeObjectPlacement_GetDistance_m26F046CB844C933DD5EE6A5D744A857B8FB093FB (void);
extern void SpriteShapeObjectPlacement_PlaceObjectInternal_m3936E149DD314A1669C8C3CA8652C68D8350361C (void);
extern void SpriteShapeObjectPlacement_PlaceObject_m7FB81E5C890FECC6A79C3A46C6BC326B2C9A4D5E (void);
extern void SpriteShapeObjectPlacement_GetSplinePointCount_mF27D6D3AAE57EF8C5A3C0FDC91A341E3BA3EE117 (void);
extern void SpriteShapeObjectPlacement_Place_m67C08775427481BF5175EF5BA5455F6B837140E9 (void);
extern void SpriteShapeObjectPlacement_Start_m31DF5A864CF990FAF9C0C1F1EAA899872848B0D8 (void);
extern void SpriteShapeObjectPlacement_Update_m6EE91B72EC1D2A270BE170A5B58C88EA3D0F90A6 (void);
extern void SpriteShapeObjectPlacement__ctor_mAF1BA203CB3BF07C913E949F60542FA53B607C71 (void);
extern void SpriteShapeObjectPlacement__cctor_m78FEDC6133B00B5AD3D7E02315D054DBC519D209 (void);
extern void U24BurstDirectCallInitializer_Initialize_m9CB01D973E72EFDACCBA8B435C93D60D5369B5FD (void);
static Il2CppMethodPointer s_methodPointers[508] = 
{
	SpriteShapeGeometryCache_get_indexArray_m588E8148EC80D38ADBBBA39AB87D9B1B2F46536C,
	SpriteShapeGeometryCache_get_posArray_m59FA7B7F1CC7D199BE0D7DB312F57B51B119093C,
	SpriteShapeGeometryCache_get_tanArray_mE6F8D44BBCBAB1ADBE6DF4893FA3135343723C5B,
	SpriteShapeGeometryCache_get_maxArrayCount_m0D5DD80D325407DD8BEFD67804BE450C16900F29,
	SpriteShapeGeometryCache_get_requiresUpdate_mC372F7AA991A8F396DA73B92629E4344CAF9B1A1,
	SpriteShapeGeometryCache_get_requiresUpload_mD219F2D99B6E98B35D77FE17AE28C9A97333426D,
	SpriteShapeGeometryCache_OnEnable_m69AF1DBD6C2E96FAD202AB497AB6FD40807C2C41,
	SpriteShapeGeometryCache_SetGeometryCache_m3D28669BE6D581591EAFFF99C19005918B953117,
	SpriteShapeGeometryCache_UpdateGeometryCache_mB11BB67BC6A491573BADD23338C16E4CB65645D8,
	SpriteShapeGeometryCache_Upload_mD06F59772108B75C0AB79CD2440BA32CF5C95107,
	SpriteShapeGeometryCache__ctor_m7F2281E0CF39244394B0D2232FFA24F14EA87A74,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m02110F67DB594A7FECAC573BBE725F1C97882E09,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m8D180D0F4E7B86FF4888DED316FD1161A6A7533E,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Geom_IsWindingInside_mF1E1D0DFFC5438424F614993FDA27CBC37C35B4D,
	Geom_VertCCW_m729F20E41F384442DA8623B179051B8CCEB96321,
	Geom_VertEq_m185C5D61E54B5F9A48999AB1FF64DC30033673F0,
	Geom_VertLeq_m643E83BDCBB7CAC1A3FA1480F81701433155D21F,
	Geom_EdgeEval_m5C733B47B92D0D71D3F2EAAF3ACFE22615D2410E,
	Geom_EdgeSign_m585A164610F6CA2B34B1F7C8F2596E3AEDA1669A,
	Geom_TransLeq_m51E8B752216D3CC7B3D1997D1136CD3523BB9503,
	Geom_TransEval_mF27A561158C7835A953334914C17081EEECA641E,
	Geom_TransSign_m2C6FC8DEF955A96C2F3FF2BFEDD4BEF40C576046,
	Geom_EdgeGoesLeft_m8C4A572A6E0F2BC8D6457787CBD3B09200B34808,
	Geom_EdgeGoesRight_m1279AC98D42D6CDF49D720E4F735FD3AFDE242C6,
	Geom_VertL1dist_m774CD04E2CC44868B97A806E8462588358AFF8CE,
	Geom_AddWinding_mEF9B45F1F1264B9A4057470FBCABF4A1C43132BC,
	Geom_Interpolate_m786E5018A615B1E1019DBC5BE287641A88C1672D,
	Geom_Swap_mC7EC25B8240634331DBA3131664431F8B368197B,
	Geom_EdgeIntersect_m01A9D8616F0A580C84D6298C0A3F7CF6AFE29019,
	Mesh__ctor_mA5921D39DFE78C3E50EB6B50221280253EA8118A,
	Mesh_Reset_m3D3859F18567F2AC3A97599B082A0C7237933BD8,
	Mesh_OnFree_m050CA42D85225FACEE3A5E90704BEE67D9A94790,
	Mesh_MakeEdge_mB4FA8F615512C8C712E8133902FF8DC996036FBE,
	Mesh_Splice_m2E87747EE346CDBBB6E0EC0ED09804E6D124C76F,
	Mesh_Delete_mD97D1D9DB96444628E39D0A2A525F0E998CB4B57,
	Mesh_AddEdgeVertex_mB1981481C4DA004BA1E66432E8894306FF9B435C,
	Mesh_SplitEdge_m03C876CEB3B2E04E9A8592E8D425C9D36E6F4C4E,
	Mesh_Connect_mE5B23363499165A6B2A6A585AED053D962D072D1,
	Mesh_ZapFace_mFA0354F3915F506B1FADD91567B35F91AD1C9FF5,
	Mesh_MergeConvexFaces_m0D46F821F2F2296DD1342E0E49E56F8CEBCB9D6C,
	Mesh_Check_mDA1197C89B6C32B6EE7F342A127BA7F1E3AAFF9E,
	Vec3_get_Item_m18BE18D5B8DE79B3C906B13FA63CA2AC304BBE37,
	Vec3_set_Item_m0E3994FE25BEBE75671FFCC4DD45A6ABD438B25B,
	Vec3_Sub_mE2347B74B4877CB5E0269FE8FAEFF8B82AD1D3EF,
	Vec3_Neg_mAFFD69D42B4584D9541B5A866A41B37CE92778EA,
	Vec3_Dot_m95643401DDBB86E315DA9FB7FF2DEA2AEE65E37F,
	Vec3_Normalize_mC63D61BDE73C850F8C834E9F1481C8473FCC2F1E,
	Vec3_LongAxis_mDD3D8BCD04B089AD3E7B383F1EBE72A0C548F141,
	Vec3_ToString_mFCCD5C16E92C7EB6A1EADB61183CF3C6C1CD93AB,
	Vec3__cctor_m02B9EF2CF498A9A4B5EB4C34152259D1B8B786CC,
	MeshUtils_MakeEdge_mCDC8989099CFDAA6AD48EF037E4CDC272D6B41D4,
	MeshUtils_Splice_mD0C74018EFACEB37BD20709F2F304A4B6517E576,
	MeshUtils_MakeVertex_m2A7DE2405EA5E3D12B1D56FC0A7B1748273D1C90,
	MeshUtils_MakeFace_mF4A86DDE85C8A79222BF4BB4C392C46E8BF9C4EB,
	MeshUtils_KillEdge_m0DEEEAD04B86B35228DFF0DF124892F255DBC813,
	MeshUtils_KillVertex_m5D5C72B07825085DF50B2D646CC710B56A46058C,
	MeshUtils_KillFace_m6AC8E688D30E7AC18981D29E901340ABC854CB24,
	MeshUtils_FaceArea_m58594CC100DB8CB29D939B7F9703D67975881DB6,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Vertex_Reset_m9A97638D164C2A1B8A41C4217B94FA5BC364B953,
	Vertex__ctor_m62F3E7DBCF7687E71A414E9D83E95D093CE0F0BD,
	Face_get_VertsCount_m8AD6AEC1ADAA78E4377ECA96CB59A7AC7FEEFCB6,
	Face_Reset_m75870EF8D66A9659EC360E3AEF7F2517D3C513CD,
	Face__ctor_m1DB8A63B49D3E39084BA09A197A4AFD6F578B9EC,
	EdgePair_Create_m984F08E6275A4688EB8164D3184F8BA8E1233923,
	EdgePair_Reset_m0B56DA5E426A184272B24B74B7A26085C1F162A4,
	Edge_get__Rface_mA14DC262D09B8C4730E2DC670A0360D80EF9E026,
	Edge_set__Rface_m5A829430D83BA32FC3107AA02FA3114058D5AAA0,
	Edge_get__Dst_m70C56414A729CC9D426FE1BD1EC95C6026610E3A,
	Edge_set__Dst_m9B0B595758DCF864743C498BC5FA2B5825FB8EC0,
	Edge_get__Oprev_mF9454D77C5B98CB63628398729BACA3D9C1F6D6B,
	Edge_set__Oprev_m906E96E92A3A01081D882E9B3193765E5CA0151C,
	Edge_get__Lprev_m7C649C346AD4E5475DC33C49CE48B33C60BC3B31,
	Edge_set__Lprev_m3D201CBBCFCDCA1D18F9F6DC8DFF42DBD73D5714,
	Edge_get__Dprev_mC86D92C59E60E15D7F47FEEA5381776F8A8750EC,
	Edge_set__Dprev_mE9E2DFD3D4E887D21DF7BA60C5BB014E966FAA95,
	Edge_get__Rprev_mE3B995FFD33AD26DAF5F1AED18382BB100065DCD,
	Edge_set__Rprev_m98EF5CA40B14C8516FB5F3167BA6FEAB155EA8F6,
	Edge_get__Dnext_m9BC35069C20236976B6572B35B1724A7EFF1E342,
	Edge_set__Dnext_mA1A5E3FB201FD369ABC79A0D2EC8AA9EEADADD7A,
	Edge_get__Rnext_m542A8A55A8403B0DB8E81F25E3F9907C9DDD49CB,
	Edge_set__Rnext_m2FC8E82DD7CFDFD0A9A95A5C8490C4D471D7C84A,
	Edge_EnsureFirst_m74A479649B38135FBFB43F21EAA44C32EEAD144F,
	Edge_Reset_m0F5A5AE64C90B77C9A4B14B560EA2153BA30129E,
	Edge__ctor_m7D4DBA226B3214C1CC5FF6603000790359A80C2F,
	PQHandle__cctor_mF329E28D9AD8E1CA1C9DCFA1317373C71E4D186D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Tess_RegionBelow_mB4663FB383114F9E38C7E65BB43F0A5152D5A5BB,
	Tess_RegionAbove_mD8753526CB8C95B8F123324647D96CDACB14EB3B,
	Tess_EdgeLeq_m6ADCC1E1709D8E3ED4E8C4D9E5B9DE1B21D35ED2,
	Tess_DeleteRegion_mCFA4F24A80189DC8A75FD2B452400823E799C170,
	Tess_FixUpperEdge_m80D33493DCEBD2341C55C4F2A171A22D92C708EA,
	Tess_TopLeftRegion_m3C1E8AC1640F14D04C27C163191E1AB4E8D356BE,
	Tess_TopRightRegion_mDEF4F555F567428E63A1DA74FFBAEBF29454FB8D,
	Tess_AddRegionBelow_mD41DF8630F4C54D537E9F00D3A2672D13E9E68A9,
	Tess_ComputeWinding_m4765320038B4D8253740B36018BAEA29AEF95931,
	Tess_FinishRegion_m5EA98BD2CA47DD5D8E5480E7855DD4D756C84094,
	Tess_FinishLeftRegions_mAC94EA6A1A8483C3A17573244F154D64BC52319F,
	Tess_AddRightEdges_m6C4B1B30D4DF33EA6A0CB10109CF85A03FD63958,
	Tess_SpliceMergeVertices_mB6DD59676ACD512C3BD69CCF643B50E2AEC7631E,
	Tess_VertexWeights_m6D7FD684368293FDBC8770818E173C9E996B5533,
	Tess_GetIntersectData_mB0CAFD39DAABEC4403569B04190EC627E06F7A14,
	Tess_CheckForRightSplice_m25942FDEF6276480112696ECB9D7D6E0C27395C4,
	Tess_CheckForLeftSplice_mCEDEE452591B6F893366A093E076E4F65830509F,
	Tess_CheckForIntersect_mA71310105191E5048AB2F6C630CC787856FA1F34,
	Tess_WalkDirtyRegions_m2F929B139BD96213AB133AAA0A8544995FB9F4F5,
	Tess_ConnectRightVertex_m0CC9FCEA3FDAE9778455E286278790F36DF4C077,
	Tess_ConnectLeftDegenerate_m38444E9763440FDCBB461AF361C1E3887B5B1E5F,
	Tess_ConnectLeftVertex_m3AB1D90C0A4F80DD0B5B831742052A422CE18564,
	Tess_SweepEvent_mB4073EC3BC83DFA4F24AA119B2C2FF3C69012B0E,
	Tess_AddSentinel_m972F272F20047718E2430E66B0EAA6C8A6A59988,
	Tess_InitEdgeDict_m0EE5C5B7E788CCF1AAC029C56A8FEC413A981125,
	Tess_DoneEdgeDict_m24F4E64FF899AA4BB85C79C107EFEE03C9980606,
	Tess_RemoveDegenerateEdges_mF5450F835EFDC8D5186087608CA7EC56CFCA82C9,
	Tess_InitPriorityQ_mC576B39EBE83F875099F080406972D6A7570BC11,
	Tess_DonePriorityQ_m27DEF72BC94B68743B99CA7BA9A042377F13BA5C,
	Tess_RemoveDegenerateFaces_m175F0E09E0A14E1F45ABAA71EEA744BB48863D8C,
	Tess_ComputeInterior_mF4250ABC1DECA9FC271ED960F2A3EE44F7A57317,
	Tess_get_Normal_mDEBEA69F532C0140100A035E96702D873B95118F,
	Tess_set_Normal_m68789191B92C93512D87FA1ADFF406FBFC9B8950,
	Tess_get_Vertices_m419E01868C54850A20B52BE6DBF7EF5FB4385AF4,
	Tess_get_VertexCount_m7B92CEC062CF196A1B997836F7D89A631240E986,
	Tess_get_Elements_mB47D1FF47667C405AD450B8E0362A52D8DF8E1F0,
	Tess_get_ElementCount_m3ED7EE9F572A069D4A3F8959C04A917A0161FA36,
	Tess__ctor_mCE5C877A8F5D236916729D7E07F34C5FDB49972F,
	Tess_ComputeNormal_m3A49B801D5E0CEF00327B0DAA038F61D89B45056,
	Tess_CheckOrientation_m4471242836839206D425EA88F294817B6C3AC68A,
	Tess_ProjectPolygon_m0548FB034FED6B171622A1BFB1318C23A477BBE6,
	Tess_TessellateMonoRegion_m495AA620F323F60E855E96B3001AA4FC6614E423,
	Tess_TessellateInterior_m3E5F1397A5AAF242394E63A44AF7EEF2A0CE1955,
	Tess_DiscardExterior_mF056909C106E045C2E3D653AB9E79C4520C3A122,
	Tess_SetWindingNumber_mFAEAB440C99283550B5C0595E208981B32C0E839,
	Tess_GetNeighbourFace_m923653D242BA94542FC5566F35B4F4A968F8EA6F,
	Tess_OutputPolymesh_m84AB594E58778801BA24B6AEA0757B0CA07F381F,
	Tess_OutputContours_m3FA17B0D69F28B4671F8FE03CE0350C2A094FEB7,
	Tess_SignedArea_m31E6133A5142ABCCA1ED940A3C74CF0093DF0E38,
	Tess_AddContour_m641C81306AA0BF4690484C92FA7215C1AF99AB01,
	Tess_AddContour_mE7863D3CC312FE9F732455527CA3180C6F449B64,
	Tess_Tessellate_m72F1A1E69A5A02650C5089C57339B2DA9E55F2C9,
	Tess_Tessellate_m15B65DE9A337183354DF80726FB2C2129850C386,
	ActiveRegion__ctor_m69C2395743F453BA9A59BC88EE6759F9FEC6B299,
	ContourVertex_ToString_mD386EFF0657B8A3D8060BF097ABFD6473440CF9F,
	CombineCallback__ctor_m154E6180D29646A02C17191DABB70023BDB0310E,
	CombineCallback_Invoke_mEE5A2E4A63D0F05B0816C82AEA86846FD403CB94,
	CombineCallback_BeginInvoke_m8586CA8B8E8CC9C34F85CA793C5E9D1E758B89EB,
	CombineCallback_EndInvoke_m0E78F0FD884D698B3A2EFBD525ED33B6FA3B144E,
	BezierUtility_BezierPoint_m58FBF49282434261BFE28A39F3C891889F6FAF54,
	BezierUtility_GetSpritePixelWidth_mA2692A4D73384256BB96A733B8140EE3F5F352B9,
	BezierUtility_BezierLength_m1A8E5F24FB82045705EB1C61BB9D3BE5CA1CBF4B,
	BezierUtility_ClosestPointOnCurve_m91458888AC7667493117C7BE8E339A404533656C,
	BezierUtility_ClosestPointOnCurveFast_m5D0F9E0CBF9DFA9DAB1E5D86868A42DF55D0E461,
	BezierUtility_ClosestPointOnCurveIterative_m78F8F9079174AB5B04E12729AF83906A2F5FDE87,
	BezierUtility_SplitBezier_m9593DD5DFA7501AB75001CDA9D691C9190A79B85,
	BezierUtility_ClosestPointToSegment_mFB7C681C534EA0EC34965D3E301AADD059B0E8F6,
	BezierUtility_SqrDistanceToPolyLine_mD6FF1BD8D9084329F65B51AAA813BC017AC21C4F,
	BezierUtility_SqrDistanceToSegment_m4D20F35424BCEA2DCB615C14889D261F4176D859,
	BezierUtility_Colinear_m362FB913354FA25183931964CD05E0D071D4754A,
	BezierUtility__cctor_mAB51CBA927852DC032BECA9760D60F887C98BB7C,
	Spline_get_isOpenEnded_m09BD0EAEDB52B06A28C0D5718F63043791DC6861,
	Spline_set_isOpenEnded_mB2DDFA961D86385B8DDEA3685E1EBE5B2794F3CC,
	Spline_IsPositionValid_m9D5B86A0EC6C11CAD23748553C28DE1D6BE65AD1,
	Spline_Clear_m90018779EEB8B0130F1C0FFFAFC0B9D5C75C840B,
	Spline_GetPointCount_m9A872A9A4C7CA81296EF70F93D48B8C47A5F7415,
	Spline_InsertPointAt_m2615F34617A42F174ADEE4AE74EB97EFB07CBEE8,
	Spline_RemovePointAt_m5A344FCDA4EABC1F431BE3B8E89BC193D98627F3,
	Spline_GetPosition_m68F7EECA5140A4D6C0FAA8AB9F8DED9BF214CF9C,
	Spline_SetPosition_m4E21C4A9BBC833A874550A9C264D804682A3BB82,
	Spline_GetLeftTangent_m722F61220067D72DD8CDD817E9174F331778A398,
	Spline_SetLeftTangent_mCCBE0DC5309767DBBF8B71060441F67ECDC568E9,
	Spline_GetRightTangent_m6008A3450C5E0D85D57FA8A1453670CBC5B280EF,
	Spline_SetRightTangent_mCBCD52EAFAD250ADEFF2DD8320CCD269FDBAE1B9,
	Spline_GetTangentMode_m2C2C980AE7A7C3C2B253033A24636586C33AF43D,
	Spline_SetTangentMode_m85B50417E072CF9EBA8ED0879B784D05264699C3,
	Spline_GetHeight_m06E2206C720560307DD537BFD984EFB7E60B2A51,
	Spline_SetHeight_mCE3D0999537E246E6796D118B8AC6A5AE07E2E29,
	Spline_GetSpriteIndex_m83CE4FDE1C4AFF2ECAC9D05FE8AEC262D37B3D25,
	Spline_SetSpriteIndex_m8C3259530B010FA108B751CD02F721FAD16879F0,
	Spline_GetCorner_m1F6AE552B79F42077369DB3117CA249788D8F876,
	Spline_SetCorner_mB0BFDAE0E37D28FB18F007881472B99BE6F88759,
	Spline_SetCornerMode_mB5CEDAB1AD77EA3E1981387E30DF60C10B150358,
	Spline_GetCornerMode_m7EB28D7DA4B3092DC077C4529F3C02321E55F0D5,
	Spline_GetChangeIndex_m1510C6FEBEF8ED5A66E81A5434FF5D984518AC74,
	Spline_GetHashCode_m74B9851C65265BFE98C03AE31BB0CB1DB34F18A7,
	Spline__ctor_m77058E9CAC3C3BF9252B278EFDCAC44A1335077B,
	Spline__cctor_m5BB2E03036FA75F2681DDCC4B73C3917BF1B0D81,
	SplineUtility_SlopeAngle_m44A0B68A01BC0F6ED3255011DDE2388530E3B8B8,
	SplineUtility_CalculateTangents_m1410CAE7A0FF8C4A9ED479CB6B6BA92B73CA59D4,
	SplineUtility_NextIndex_m8F7FCF6B59B163E7E3A11AC4303EE2ED1F407D2C,
	SplineUtility_PreviousIndex_mF11B26DB018A7D9F823BCD9D7BE872D17D0C9BB4,
	SplineUtility_Mod_m1E360DCA061CFC1F7545F80B720608AE292744B2,
	SplineUtility__ctor_m048BC127ECEA698E37616379AA08A97A64906FA1,
	NULL,
	NULL,
	NULL,
	SplineControlPoint_get_cornerMode_m95DB1388626D5DB42A4490B3A6998AB7DE6D48AF,
	SplineControlPoint_set_cornerMode_m1CCDB4EA35131D2DB1B0A117CDE6B34117D935C6,
	SplineControlPoint_GetHashCode_m3CB65BFD0FA3BD8428CE5991C73B5039BBCABEFA,
	SplineControlPoint__ctor_mC43E9567FABB0F0F28B7AF0BA52B346704AF44E1,
	AngleRange_get_start_mD0468EC3E8E1935141A977DB0593921DCAEF4738,
	AngleRange_set_start_mB85C33F416988AF1A7A896FFAF94326B28EE27DE,
	AngleRange_get_end_m2C9ED13CD1759C45BFE955F05A126EE5869DF1E2,
	AngleRange_set_end_m57A30FA8AD7BDFCC672C65EEB4967D201F54E187,
	AngleRange_get_order_m4B94B4ABE3C9280BC7D7C42C3BAFD215C4FDE65A,
	AngleRange_set_order_mCFC0245542FC55D286442DDC9EF3E5FE8D6726F2,
	AngleRange_get_sprites_m28E5E8275AD72ABF48906F92189ACD56F7855744,
	AngleRange_set_sprites_mA53B82D0E47C10B042E6D3324E693416D9A7BA37,
	AngleRange_Clone_m1F1EFC618FCB936BFCF052344E27B0470933514C,
	AngleRange_Equals_mB03532F9BFF710AD77CE481CB027E1151D658A2D,
	AngleRange_GetHashCode_m1E1CCEAFA6658CBAA130382B022A0EF8071EF7C0,
	AngleRange__ctor_m3F4962B6208CFFDB3B458EEDF9698FE2B64FD7E6,
	CornerSprite_get_cornerType_m3AAD26BC2C63513DE93092A7F1FE89C851642BC5,
	CornerSprite_set_cornerType_mEC433705D57B3EDC4804B0243C852B6354E89332,
	CornerSprite_get_sprites_m3114E6A75522F8924A235950378A2419D8C41FB5,
	CornerSprite_set_sprites_m713C5FEFAAB212FA42AEF83FEC42509D02414979,
	CornerSprite_Clone_mC2E08E79BB69C8B45220FD765D38A6509B2BFE78,
	CornerSprite_Equals_mA88F7927D0856777B66F585894C168806AF7B3EA,
	CornerSprite_GetHashCode_mB40CABE597D887F4BB3BDBDADAC0D0D799C2E379,
	CornerSprite__ctor_m129B0F37BC95A899CE43D4130B64ABB7301F1894,
	SpriteShape_get_angleRanges_m0C62D6A5E07E32DCCF9EE67FABF0EFC60D0D0745,
	SpriteShape_set_angleRanges_m3DEEE05F24BDA0C0584928E64645C50251AEC3E8,
	SpriteShape_get_fillTexture_mCFAA6A4C131C79E81A2ED7333609FE357C83D627,
	SpriteShape_set_fillTexture_m953DBF70A2E67363D79ABE0957E91D59EDDD5F9E,
	SpriteShape_get_cornerSprites_m087D4444A163AA72121E45EE562260C2ACED951C,
	SpriteShape_set_cornerSprites_m19AB6640AC7E4DCCE883BEC88E44A5B26D8B3F42,
	SpriteShape_get_fillOffset_mAB5DCDA5724331DAD614D975D32C5040708BF601,
	SpriteShape_set_fillOffset_m76103B635C4175AFC103BEF302EE2050F7C58FD9,
	SpriteShape_get_useSpriteBorders_m71A77DB6C2DD9240181C19700E2E68A2CC69F964,
	SpriteShape_set_useSpriteBorders_mA6F0796BA9450B971B65D1487648A43F311DA57D,
	SpriteShape_GetCornerSprite_m33DA8C403F036A099D246C22879E7D099C80E004,
	SpriteShape_ResetCornerList_m8B8505F12CB2798A97C301E9E4AC3216CC08A536,
	SpriteShape_OnValidate_mDB9ED7A83A57AA09DDACB7E4EC0BB412BAF786EB,
	SpriteShape_Reset_m46FA053BEA2E457CAD8C8E8A9B131559FFFAD521,
	SpriteShape_GetSpriteShapeHashCode_m9212F83587FD6084A616CE5E897F40146CAF3AF5,
	SpriteShape__ctor_m498F0AB94EAE1AF1392438B694711E79143628B6,
	SpriteShapeController_get_maxArrayCount_m1CD21D97E9DFB36FB05C64261E6B2AE8AE7A9F29,
	SpriteShapeController_set_maxArrayCount_mE4A6A50AAECB96C743CC1A701840776699833ECD,
	SpriteShapeController_get_geometryCached_mD9C381E9B9CF0720F751F3E8DDBCA7F810D2A07A,
	SpriteShapeController_set_geometryCached_m9EB40925B5661EF984777327FD4F9782B8AB2B53,
	SpriteShapeController_get_splineHashCode_m5FD24A66B300EA0F8E1A1B0E5100ED3D0AA8FB98,
	SpriteShapeController_get_spriteArray_mE12E018678D8DFDA098E94255BDEE7E488292DAB,
	SpriteShapeController_get_spriteShapeParameters_m5EB8EFD2C04CC8797FDC67F2E381AB833D287AA0,
	SpriteShapeController_get_spriteShapeGeometryCache_m677A25E93851BACE158237F9878AE78D21E17AE8,
	SpriteShapeController_get_cornerSpriteArray_mF0A3ACBD990E4DCE4BFE3CA2192C0058DD3F7E9E,
	SpriteShapeController_get_edgeSpriteArray_mB562E639F3951B0A5432AA7F9BA18BCD69387071,
	SpriteShapeController_get_shadowData_m6D637C25E16688807369372E8B00026509CD1EB3,
	SpriteShapeController_get_angleRangeInfoArray_mED8D0B5CD5EE8724A44A6CA86DCE37AE085C11F4,
	SpriteShapeController_get_spriteShapeCreator_m3E277D63975226E2A58979508A782BC02117C95A,
	SpriteShapeController_set_spriteShapeCreator_mCC6692B6ADA3C0812401E066A030F518EC74C2EA,
	SpriteShapeController_get_modifiers_m5CE76EC7CD1814B13FFDA0807356972AE59B80A3,
	SpriteShapeController_get_spriteShapeHashCode_mF2D6ABCAF590DFC5A0461E61A8B78356FD2FB520,
	SpriteShapeController_get_worldSpaceUVs_m334F4BDD28C1980124898063E3FA35A82E48E8A3,
	SpriteShapeController_set_worldSpaceUVs_m3426FE81A540AD44FBE6CB78368C846902BA1D3D,
	SpriteShapeController_get_fillPixelsPerUnit_m03D2274AA79D8FE84520825C4658C89328B40AC9,
	SpriteShapeController_set_fillPixelsPerUnit_m838E1E5F179458D2483A4B7D60595B8677E8B2F3,
	SpriteShapeController_get_enableTangents_m3B01758CFC4CF46ADF8707266A87F506C68FF6C9,
	SpriteShapeController_set_enableTangents_m213DBDA0EB1F827A4255E47B6E7D229EEFCF73B0,
	SpriteShapeController_get_stretchTiling_m8B867630BCC7929A238CB8F43F687F3F9618C658,
	SpriteShapeController_set_stretchTiling_m51EEDC3EBAEDFDD5393CD1A371904D1113921E6A,
	SpriteShapeController_get_splineDetail_mB03C316DE81143E38A30C15C339D36DAD7C56E33,
	SpriteShapeController_set_splineDetail_m9580F13305955BA3C89914EBD8EB99EF11249325,
	SpriteShapeController_get_colliderDetail_m11726DDBB4219DB8DB66B0C34ACFB877DA82330B,
	SpriteShapeController_set_colliderDetail_m9DB395B7A2C311E17542FADF33DB04128F3E2E3E,
	SpriteShapeController_get_colliderOffset_mFE87071B23F19A7C3CF672E12AD049D3D92CBBCF,
	SpriteShapeController_set_colliderOffset_m1A87B0F5B035C07393CC6412504E8CD0A681BFED,
	SpriteShapeController_get_cornerAngleThreshold_m3A66C56D956590DF46B1EC99F18D8E17BA3D28B1,
	SpriteShapeController_set_cornerAngleThreshold_m3757272F360166C704601DD56FA4014B6D20E2C1,
	SpriteShapeController_get_autoUpdateCollider_m54D843E58B97438ACF30071768B0F5FB4A8473B7,
	SpriteShapeController_set_autoUpdateCollider_m70DEFCF590D8D3CDDE1082B7E96226C993B00D18,
	SpriteShapeController_get_optimizeCollider_mCBE85BA595F9221E2442125F6615A958BA1353EC,
	SpriteShapeController_get_optimizeGeometry_m7D907B4FB2924B44C252DE590CA3F151B6C711D9,
	SpriteShapeController_get_hasCollider_mB354FB04E19BA1CBC5F486BB32DF692382FBDD42,
	SpriteShapeController_get_spline_m1266CB84F33024475FA4FC6C2F35E4E3E74AD6B9,
	SpriteShapeController_get_boundsScale_m19CD4C76DF1DDCD5D74D0E1E578179E7B3A8BC65,
	SpriteShapeController_set_boundsScale_mB43FE245DD2766D8B1928C6B6D56ED76B0940878,
	SpriteShapeController_get_spriteShape_m47FA441FCE1F593405E92D174E556EDC8DD4D260,
	SpriteShapeController_set_spriteShape_m1C26E9221CAF1319F40D50D8967F9C5D6198C7AE,
	SpriteShapeController_get_edgeCollider_m2E6019F72F1AFB428BF9D2089BEDA849D0D9577E,
	SpriteShapeController_get_polygonCollider_m225EE23CFBB87B3FFFACFF7CC6B2CA5515236015,
	SpriteShapeController_get_spriteShapeRenderer_m1CE6071F363FDDC49999CD3482311619EDA97F00,
	SpriteShapeController_get_updateShadow_mE4867AC0CCEB59E1A84FB5838549B06A76744237,
	SpriteShapeController_set_updateShadow_m9DD4AAFA8325D765049D6DAEFCF4F4BC0839B2A0,
	SpriteShapeController_get_shadowDetail_mD3D6007CEB98D403E2AFDCC95964064AEB19A06A,
	SpriteShapeController_set_shadowDetail_m05EA9DC9F7477FBAD89EF9450DBD715B6EEDB119,
	SpriteShapeController_get_shadowOffset_m8DE3562CAD5C0F9D85A994D43CEC58EB71CC0182,
	SpriteShapeController_set_shadowOffset_m19289874F3727373E359E8F9385480043280EDE3,
	SpriteShapeController_get_shadowSegment_m1483EF0CB76FC6CD13ACCC66F4B73412CE1DF59A,
	SpriteShapeController_get_stats_m3130C2A150F3CFF99A6FD0D5EC06147E907D96FD,
	SpriteShapeController_get_WaitForBake_mCFEB55DB30F87647EBA33F2FA42EC7CFC72E02BB,
	SpriteShapeController_set_WaitForBake_m8D0507BE12B808039A1B54BB2A9ED1746915005E,
	SpriteShapeController_get_autoUpdateGeometry_m54BF6EDE449B378252E42D4E356F0BB35143A119,
	SpriteShapeController_set_autoUpdateGeometry_mDC9CC6F1CF726D454F31C56E2209176BA5E01153,
	SpriteShapeController_DisposeInternal_mAAEB238F72B78D551F83EB312D6619987899B98D,
	SpriteShapeController_OnApplicationQuit_m8A657FEEFC44C425C81EFC0E79E593B9E7CB0CB4,
	SpriteShapeController_OnEnable_m9DDC5591B1D66745D6AD2922FA54119E39E8CC55,
	SpriteShapeController_OnDisable_m0205031B7FF69736F230D4045F256AA66335DA76,
	SpriteShapeController_OnDestroy_m3CE9200A30C616BE7761C2B73A725772FD46D18D,
	SpriteShapeController_Reset_mBF3168EA991377B3FCD3C1F48F5BC20DE54757C3,
	SpriteShapeController_SmartDestroy_m2536D729E67C1909A7FC8C1C1691408C7BE04C06,
	SpriteShapeController_InitBounds_m2B81084A9168AD6B0C98629900A76A579408DD79,
	SpriteShapeController_RefreshSpriteShape_m35888DA0521C69F3B6E56A69C2DF4C53CFC419BB,
	SpriteShapeController_ValidateSpline_m574D40EEB9533DC63C07FE45A83E5434A8A950D9,
	SpriteShapeController_ValidateSpriteShapeTexture_mD3A351394676E4AB9C1EA7127E77E144D6A05B00,
	SpriteShapeController_ValidateUTess2D_mC71A1EC29B1BC0FF00C49C695A96846D709F458E,
	SpriteShapeController_HasSpriteShapeChanged_mD2828F60582F8702356BBEB50CC95A7A6BA55237,
	SpriteShapeController_HasSpriteShapeDataChanged_mCC909CA241E0D2E116A51C32B8A22CC7D4726F58,
	SpriteShapeController_GetCustomScriptHashCode_m42FEDCC62FFACA8D3C5F1FE06309D57634D3CAA4,
	SpriteShapeController_HasSplineDataChanged_m5AEE03084F30CD6E726BF388636C8B5CD419CCB2,
	SpriteShapeController_OnBecameInvisible_m2ADE87FA7014E00DEE586DA6B1A8440BBD81C142,
	SpriteShapeController_LateUpdate_m5D3DECBC12536092B882C2028C7B4E4C634C747D,
	SpriteShapeController_OnWillRenderObject_mD1D3D260CAE2FFFFFDC26F0FE059C020A9C62D1C,
	SpriteShapeController_BakeMesh_mD8809E911D574437001B446DDFA90724BF384743,
	SpriteShapeController_UpdateGeometryCache_m4D27DBA3C6C424293BDD963C968C0B2014C7C7F7,
	SpriteShapeController_UpdateSpriteShapeParameters_m10CFBD964FC92FC1619319ABAA545A1391FC1296,
	SpriteShapeController_UpdateSpriteData_mD8A87BDA7AC4BC6EA3521AEE52B38F62C01A579D,
	SpriteShapeController_GetShapeControlPoints_m4A0BF704DB4274546156AC4085353536541B30D5,
	SpriteShapeController_GetSplinePointMetaData_m56AE94299696DA36F56EE24B792FAD9892ECF786,
	SpriteShapeController_CalculateMaxArrayCount_m3460A750AFF7144E55395AF683DD508233190ECB,
	SpriteShapeController_ScheduleBake_m8ECD1C9C3FC8EB1E7194CE61723ECE10A7C74D73,
	SpriteShapeController_BakeShadow_m9C49E19FCD75A0A954296DD323A60B63F3BFAD09,
	SpriteShapeController_BakeCollider_mEFCB850D14CBB3949E2C7FCD62A24FA5C06593D7,
	SpriteShapeController_BakeMeshForced_m69B0B10D80E51470C0F7C12A9595986CDEACC559,
	SpriteShapeController_ForceShadowShapeUpdate_m93C5104E9CADBBC62894A8002005EC5F6FF37A50,
	SpriteShapeController_GetShadowShapeData_mDF06899BCBB123DC0C6C4258B624A22DAAA0556C,
	SpriteShapeController__ctor_mBB2958EC26C34FE3C1097186C45AA80F3A1AC31D,
	SpriteShapeController__cctor_m58C44D77C0B734C34DB44B7A128A8B24E4E42531,
	U3CU3Ec__cctor_m46528AD52B64571CEF44A8F2722DC493302EB6C2,
	U3CU3Ec__ctor_m3D2A053B5948BDFC84870DCBEAD76989CF259BF1,
	U3CU3Ec_U3CUpdateSpriteDataU3Eb__166_0_mA1368E9968F5B0A4865D4994245C3AB3703B2823,
	SpriteShapeDefaultCreator_GetVertexArrayCount_mDDE727F55D07FBBF570F7617443C2782ED87B4CC,
	SpriteShapeDefaultCreator_MakeCreatorJob_m95E60274C1A0757B5AC8B71DBF5142F1C7E5379C,
	SpriteShapeDefaultCreator_get_defaultInstance_m3D7F2CB26AD65BA670E221E672716B4AEB5764E4,
	SpriteShapeDefaultCreator_GetVersion_mBF55AB1CA538BEBDAC4AD7C3D6D416C976E22995,
	SpriteShapeDefaultCreator__ctor_m1686195DD7A0692725025943A87FEF4130923707,
	SpriteShapeGenerator_get_vertexDataCount_mDC30ECF81BCDFA0769E3F7EACE2718E929637EFD,
	SpriteShapeGenerator_get_vertexArrayCount_m776FBDEEB5A98DDBE16B219D2587D981B606D910,
	SpriteShapeGenerator_get_indexDataCount_m1E6F0585FA2A3F592856EBB491409A7B39ABBB16,
	SpriteShapeGenerator_get_spriteCount_m8D39765E70D833493EEA503ED982F410EA9DFA53,
	SpriteShapeGenerator_get_cornerSpriteCount_mF842CCBF80F27E03641B10A9A12D721EA6957642,
	SpriteShapeGenerator_get_angleRangeCount_mE6EB56E420B87315E754A66D294CF201F93FA1A8,
	SpriteShapeGenerator_get_controlPointCount_m0B72EE45FD6E4BF63650958439B43712ADD39696,
	SpriteShapeGenerator_get_contourPointCount_m45CA4B09B39611D4A1C09F494A3BED828258CC41,
	SpriteShapeGenerator_get_segmentCount_m5B0A226EBA7CA12B095A92DB429DEB30DF3C7210,
	SpriteShapeGenerator_get_hasCollider_m6C79BA0841071B0B97D33C195716F14C0B5453DC,
	SpriteShapeGenerator_get_hasShadow_mF8B09961DA6FE6A05AA217EA3DAC9FF8D026FBB3,
	SpriteShapeGenerator_get_colliderPivot_m8D45A62C4E63DFD67D502547812616043B2582E7,
	SpriteShapeGenerator_get_shadowPivot_mDF1695D06772C0DF9F3D30F628780F1EF4A2B39E,
	SpriteShapeGenerator_get_borderPivot_m1CB177826C97190744B42A7A39F1E4B2ACA17941,
	SpriteShapeGenerator_get_splineDetail_m3739C3D78D2F7D5EE0C31ADC7EFF1A28E1620D5F,
	SpriteShapeGenerator_get_isCarpet_m82A7BACAF2824B38318697B6BD0BC97FE07624B2,
	SpriteShapeGenerator_get_isAdaptive_m8022015FFC581294D888FCC63FEDC6B503985C85,
	SpriteShapeGenerator_get_hasSpriteBorder_m015043D5050787F1744E27679E6681101C24C01F,
	SpriteShapeGenerator_GetSpriteInfo_mC06825216D7CF04273E559CBBACEA742CCCC129E,
	SpriteShapeGenerator_GetCornerSpriteInfo_m6170012E001B82F923367E339A60AE997AEC7335,
	SpriteShapeGenerator_GetAngleRange_m5FCB024AC77D929F60F35E4852920B49EC40D53F,
	SpriteShapeGenerator_GetControlPoint_mF73708A8E099838AC8806D2EE1305D230C709A37,
	SpriteShapeGenerator_GetContourPoint_m7BD62D1044854A6E2811C65FC8C4C3451133A20A,
	SpriteShapeGenerator_GetSegmentInfo_m72363F976D7D18181DED537467749537D0999DA9,
	SpriteShapeGenerator_GetContourIndex_m8705A508B3B54F8268411F3FDF3DCBD030705500,
	SpriteShapeGenerator_GetEndContourIndexOfSegment_m34A178F013D01C7EA2713CCE22E43BFF85D2E8CD,
	SpriteShapeGenerator_SetResult_mB943A248C968BCA647038BD8CF5F568BB4669475,
	NULL,
	NULL,
	SpriteShapeGenerator_IsPointOnLine_m9ADB4EB472366B1B12FE900C9395F6A50A01DE8B,
	SpriteShapeGenerator_IsPointOnLines_m2CA862D27FF37797951B22A515F697BFE97848C4,
	SpriteShapeGenerator_Colinear_mD902FD4B26FE511CE9F0177B99A0BF16AD82DB41,
	SpriteShapeGenerator_Det_m1CBF814EBFAF65729676E04E7C2B25556B96BD34,
	SpriteShapeGenerator_LineIntersectionTest_mE62B6C1918AFA11BD5F4B17D32063D86DFD374BC,
	SpriteShapeGenerator_LineIntersection_mB00C5425B41C9CA3C6665C14817D7BA812F0C68C,
	SpriteShapeGenerator_AngleBetweenVector_mE576D3B5B635113A6402D84E3C7E9C02898E2685,
	SpriteShapeGenerator_GenerateColumnsBi_m432FE4F034EE3FEB5856B11DD3F08AD87B8C5CB5,
	SpriteShapeGenerator_GenerateColumnsTri_mC86F836BF87D9E75633C54280BADB76A9751B8D4,
	SpriteShapeGenerator_AppendCornerCoordinates_mDCB86690220150FEE68F15E621D8507E22095B2B,
	SpriteShapeGenerator_PrepareInput_mE4FDC5415D5B004748A141D7F51383B5255E0FBF,
	SpriteShapeGenerator_TransferSprites_m7EB37DB492012CA10801310B4A6AB9D23C9D604A,
	SpriteShapeGenerator_PrepareSprites_m5423E297D56773AD47D53877570C57D2B38B4E24,
	SpriteShapeGenerator_PrepareAngleRanges_m612B2151F30299B9B7AACDA7A041510E65EB618B,
	SpriteShapeGenerator_PrepareControlPoints_mABC163490DAF124A972C1440B19D0140EB2673D4,
	SpriteShapeGenerator_WithinRange_mA4F9BD34C7AAA5111DF48DFCC66FCE28FB70E31E,
	SpriteShapeGenerator_AngleWithinRange_mBFC8803A87435D94B5AA0D8E779A79729AC7FC90,
	SpriteShapeGenerator_BezierPoint_mF0FE84A56DDB2BB6D2DF7C76053FFFF41EFFBA58,
	SpriteShapeGenerator_SlopeAngle_mA77578AE85D18BE9DBA77F08EDA58F854C3FEB38,
	SpriteShapeGenerator_SlopeAngle_m4B4B9571E7C898E103D6FEB88D6D6B22019A4011,
	SpriteShapeGenerator_ResolveAngle_m718F26C825AD850CDE3B9780C7EA4F4FD09360A1,
	SpriteShapeGenerator_GetSpriteIndex_mFE565014AF490E45B712164C61A27C3E6B230163,
	SpriteShapeGenerator_GenerateSegments_mFD269D1C42D371CD823610D0DF68AFAF6C221E9F,
	SpriteShapeGenerator_UpdateSegments_m7D6F5BA1B8A1E53960801B0B947F46CAE379AA8E,
	SpriteShapeGenerator_GetSegmentBoundaryColumn_m93EFD1563B99BCBA319FA4A4ADF2F9CA382190CB,
	SpriteShapeGenerator_GenerateControlPoints_mAD773E2807C8D8E29AC20CD125A78C516A1C6F82,
	SpriteShapeGenerator_SegmentDistance_mC59D5EFEDEEB7AA90180AF4B4F53DF6BF49BFDD5,
	SpriteShapeGenerator_GenerateContour_m6EBB4C1E95B82166E226FD2EAE9B33341EBBB5A2,
	SpriteShapeGenerator_PrepareContour_m89E38CF7A6CD6D93457277CA6AA43BA7BFA61973,
	SpriteShapeGenerator_UTessellator_mA975B9F66E80F2334124BFADD19F331AB2F6E90D,
	SpriteShapeGenerator_TessellateContour_mF70D92C350C736C57382EAD8AB02413344FD5545,
	SpriteShapeGenerator_TessellateContourMainThread_m4509E90FD48DB95C993D9144DDA67B825F809EE1,
	SpriteShapeGenerator_CalculateBoundingBox_m51E8608113267A80B08E671CD0E6F9E98403DE3A,
	SpriteShapeGenerator_CalculateTexCoords_m601199E7DD5A81D4586B708E9BF175692744B3A3,
	SpriteShapeGenerator_CopyVertexData_mBD9B93DAB3A30EA5D20DD8EB415C91437C957F0D,
	SpriteShapeGenerator_CopySegmentRenderData_m48B6A14833D7B597E6E97A6F53E1542FA0A23F76,
	SpriteShapeGenerator_GetLineSegments_m42433AFD66A0FE4716E83F2D8F1EB3FFBAE614FE,
	SpriteShapeGenerator_TessellateSegment_m85D8FE88E38E3AC599183C5CB25D61EF203C3878,
	SpriteShapeGenerator_SkipSegment_m60AD06EAE4EC71495A2C4DDF16E30CCCC7AC48D1,
	SpriteShapeGenerator_InterpolateLinear_m6655E2A9307AFE3E0F947E22789FD1D6709260BB,
	SpriteShapeGenerator_InterpolateSmooth_m8A19C193EC1ECCA8116A4FE8834C22E4CDC38A1F,
	SpriteShapeGenerator_AddVertex_m299A47F7A33CAB37533B90ACF8AD971DD9D358F2,
	SpriteShapeGenerator_TessellateSegments_m42A1531B9F7C1DEC85DB7367542CFEC3D90E7FAA,
	SpriteShapeGenerator_FetchStretcher_m62E10BC6A8E62E8B07FAB981EA8791443792ECFC,
	SpriteShapeGenerator_StretchCorners_mEF433099727D0C24530F28C9AA5A688FD001AEF4,
	SpriteShapeGenerator_ExtendSegment_mA0441D565DA580DFA03ED402D55C01FCE45A62DC,
	SpriteShapeGenerator_GetIntersection_m7A6907C124D4C476D9F6A474EB8D2C32B3ED22D0,
	SpriteShapeGenerator_AttachCorner_mE870FE2AFC67B7EA889191D1D4290690B3D9AEDE,
	SpriteShapeGenerator_CornerTextureCoordinate_m832C4406CE02463EA7B1E0B1BAD91F0D3902E485,
	SpriteShapeGenerator_CalculateCorner_m2889F0F78372F001B2827FC7F97311A6FF801828,
	SpriteShapeGenerator_InsertCorner_m2A32C77F92888AAEFDE80128460D5FAAE8C8DEC5,
	SpriteShapeGenerator_TessellateCorners_mBEC539F9EBD424BC9682C3EF748E500E2D1A5C27,
	SpriteShapeGenerator_AreCollinear_m2CF9A2780D5320C72BBC9415DDEC1A04FE57D89F,
	SpriteShapeGenerator_OptimizePoints_m12067570C5E61E1291B57D9C6F8D284CCE19F46D,
	SpriteShapeGenerator_AttachCornerToCollider_m2468C75A72FD1AFD391A373FECDB44FABF9BE78F,
	SpriteShapeGenerator_UpdateExtraGeometry_mECAD4B1A09D3025A61B93F90029FC864AFD2D6EF,
	SpriteShapeGenerator_TrimOverlaps_m259624880804C1A90EDE8B6621DDE70315F3137B,
	SpriteShapeGenerator_OptimizeCollider_mBA6D3CB699F42750C239CA0BC201B672B1FD9AA8,
	SpriteShapeGenerator_OptimizeShadow_m23799E7E865F53073846F752F6495DFBEBB0ACB6,
	SpriteShapeGenerator_Prepare_mF79A57490E60706BE72D78830EC71DEC17B1DCF1,
	SpriteShapeGenerator_Prepare_m8358A9BD95A1D45F0FBE63A5661421DD0D8B4DB7,
	SpriteShapeGenerator_Execute_m6F11F51210E20869A78D43F7AD6E0702D8515A91,
	SpriteShapeGenerator_Cleanup_m2662B1967B4613B611E1E7E57341E0E7862B7FF1,
	SpriteShapeGenerator_UTessellatorU24BurstManaged_m6F4F57B7363784CE1724AE26829C356319E88118,
	U3CU3Ec__cctor_m3B0AC2339A8DE4BC31CEDD5BADA65B9D7949A349,
	U3CU3Ec__ctor_mA91FE5EA2C14E43491278E311B2F74B7478EF999,
	U3CU3Ec_U3CTessellateContourMainThreadU3Eb__155_0_m4DC5973A9C496EEA6F7E0098F7A5D14133CF8FA8,
	U3CU3Ec_U3CTessellateContourMainThreadU3Eb__155_1_mF10AA4653D6AE7C5ECCE75B6EE424F6A82D6B37C,
	UTessellator_0000017FU24PostfixBurstDelegate__ctor_m7D82A379E92F612522917026D1FBB234A0299719,
	UTessellator_0000017FU24PostfixBurstDelegate_Invoke_m3FE651BD796903A59B0486C44F86A6101BB31024,
	UTessellator_0000017FU24PostfixBurstDelegate_BeginInvoke_m098909B481B8513DBEABBFAF8389B36B3B207D98,
	UTessellator_0000017FU24PostfixBurstDelegate_EndInvoke_m0CA934E4E8F4AF159D45C6766236E3C036AA0304,
	UTessellator_0000017FU24BurstDirectCall_GetFunctionPointerDiscard_m674170764EDF0A0753BD85F5B4326892F27ABA44,
	UTessellator_0000017FU24BurstDirectCall_GetFunctionPointer_m5DDBAB15141B65F9C34F7A02735B490B7CB79DFB,
	UTessellator_0000017FU24BurstDirectCall_Invoke_mA24AF835EA088D43CAC380DDCAB35C8BD81A688C,
	NULL,
	NULL,
	SpriteShapeGeometryCreator_GetVersion_m44860C0EE661F0B0B7D145D084BA7D5CE6C7ACA8,
	SpriteShapeGeometryCreator__ctor_m11995DD5FE4D69A420FABBD8AE3DB243E9D4EF7B,
	NULL,
	SpriteShapeGeometryModifier_GetVersion_m279A107B00A417E5058A6456CEAA750C66C88D89,
	SpriteShapeGeometryModifier__ctor_mC8B2D08FC71266F54041FAD12592B2009883C48E,
	SpriteShapeObjectPlacement_get_setNormal_mDF4F4E07D5CD4B8A7D5A3B1A280BB21FBD8A0567,
	SpriteShapeObjectPlacement_set_setNormal_mC372153584297F251B6C0010110900584F0C7FC4,
	SpriteShapeObjectPlacement_get_mode_mC07CCEC65A4CF7075CE09754A2B503D3926E428E,
	SpriteShapeObjectPlacement_set_mode_m4F946059DDF7E8EEBC818BE0ECCE59C3A7193F2F,
	SpriteShapeObjectPlacement_get_ratio_m1AD37D2E67595975F88822C70707DF5EFB8B5F3B,
	SpriteShapeObjectPlacement_set_ratio_mED658B93A3A246178CCB30DEC877EA54DA1842EC,
	SpriteShapeObjectPlacement_get_spriteShapeController_mA90BED3A705B97FDC4B368365F65922EB268BB2E,
	SpriteShapeObjectPlacement_set_spriteShapeController_m20610E17F7CF90F7DA016ED8D7F3E335AACE7F01,
	SpriteShapeObjectPlacement_get_startPoint_m1C539FDCDDB00A0918771704D55D113549A08D12,
	SpriteShapeObjectPlacement_set_startPoint_m2C880BECFF67C3478402FF2A88C8F5F5E2E60CA6,
	SpriteShapeObjectPlacement_get_endPoint_m1183FA74B1AF9A9806EC708DC9AB9FC183983335,
	SpriteShapeObjectPlacement_set_endPoint_m62B77B64D9F7332D75A19F768B1CFF1FB919C040,
	SpriteShapeObjectPlacement_PlaceObjectOnHashChange_m71263E4A1E32BA74D19300AC80B4D9A544441692,
	SpriteShapeObjectPlacement_Angle_m133A7031202A395E8963E07B72900C9559763B6E,
	SpriteShapeObjectPlacement_GetDistance_m26F046CB844C933DD5EE6A5D744A857B8FB093FB,
	SpriteShapeObjectPlacement_PlaceObjectInternal_m3936E149DD314A1669C8C3CA8652C68D8350361C,
	SpriteShapeObjectPlacement_PlaceObject_m7FB81E5C890FECC6A79C3A46C6BC326B2C9A4D5E,
	SpriteShapeObjectPlacement_GetSplinePointCount_mF27D6D3AAE57EF8C5A3C0FDC91A341E3BA3EE117,
	SpriteShapeObjectPlacement_Place_m67C08775427481BF5175EF5BA5455F6B837140E9,
	SpriteShapeObjectPlacement_Start_m31DF5A864CF990FAF9C0C1F1EAA899872848B0D8,
	SpriteShapeObjectPlacement_Update_m6EE91B72EC1D2A270BE170A5B58C88EA3D0F90A6,
	SpriteShapeObjectPlacement__ctor_mAF1BA203CB3BF07C913E949F60542FA53B607C71,
	SpriteShapeObjectPlacement__cctor_m78FEDC6133B00B5AD3D7E02315D054DBC519D209,
	U24BurstDirectCallInitializer_Initialize_m9CB01D973E72EFDACCBA8B435C93D60D5369B5FD,
};
extern void Vec3_get_Item_m18BE18D5B8DE79B3C906B13FA63CA2AC304BBE37_AdjustorThunk (void);
extern void Vec3_set_Item_m0E3994FE25BEBE75671FFCC4DD45A6ABD438B25B_AdjustorThunk (void);
extern void Vec3_ToString_mFCCD5C16E92C7EB6A1EADB61183CF3C6C1CD93AB_AdjustorThunk (void);
extern void EdgePair_Reset_m0B56DA5E426A184272B24B74B7A26085C1F162A4_AdjustorThunk (void);
extern void ContourVertex_ToString_mD386EFF0657B8A3D8060BF097ABFD6473440CF9F_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_vertexDataCount_mDC30ECF81BCDFA0769E3F7EACE2718E929637EFD_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_vertexArrayCount_m776FBDEEB5A98DDBE16B219D2587D981B606D910_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_indexDataCount_m1E6F0585FA2A3F592856EBB491409A7B39ABBB16_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_spriteCount_m8D39765E70D833493EEA503ED982F410EA9DFA53_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_cornerSpriteCount_mF842CCBF80F27E03641B10A9A12D721EA6957642_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_angleRangeCount_mE6EB56E420B87315E754A66D294CF201F93FA1A8_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_controlPointCount_m0B72EE45FD6E4BF63650958439B43712ADD39696_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_contourPointCount_m45CA4B09B39611D4A1C09F494A3BED828258CC41_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_segmentCount_m5B0A226EBA7CA12B095A92DB429DEB30DF3C7210_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_hasCollider_m6C79BA0841071B0B97D33C195716F14C0B5453DC_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_hasShadow_mF8B09961DA6FE6A05AA217EA3DAC9FF8D026FBB3_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_colliderPivot_m8D45A62C4E63DFD67D502547812616043B2582E7_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_shadowPivot_mDF1695D06772C0DF9F3D30F628780F1EF4A2B39E_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_borderPivot_m1CB177826C97190744B42A7A39F1E4B2ACA17941_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_splineDetail_m3739C3D78D2F7D5EE0C31ADC7EFF1A28E1620D5F_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_isCarpet_m82A7BACAF2824B38318697B6BD0BC97FE07624B2_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_isAdaptive_m8022015FFC581294D888FCC63FEDC6B503985C85_AdjustorThunk (void);
extern void SpriteShapeGenerator_get_hasSpriteBorder_m015043D5050787F1744E27679E6681101C24C01F_AdjustorThunk (void);
extern void SpriteShapeGenerator_GetSpriteInfo_mC06825216D7CF04273E559CBBACEA742CCCC129E_AdjustorThunk (void);
extern void SpriteShapeGenerator_GetCornerSpriteInfo_m6170012E001B82F923367E339A60AE997AEC7335_AdjustorThunk (void);
extern void SpriteShapeGenerator_GetAngleRange_m5FCB024AC77D929F60F35E4852920B49EC40D53F_AdjustorThunk (void);
extern void SpriteShapeGenerator_GetControlPoint_mF73708A8E099838AC8806D2EE1305D230C709A37_AdjustorThunk (void);
extern void SpriteShapeGenerator_GetContourPoint_m7BD62D1044854A6E2811C65FC8C4C3451133A20A_AdjustorThunk (void);
extern void SpriteShapeGenerator_GetSegmentInfo_m72363F976D7D18181DED537467749537D0999DA9_AdjustorThunk (void);
extern void SpriteShapeGenerator_GetContourIndex_m8705A508B3B54F8268411F3FDF3DCBD030705500_AdjustorThunk (void);
extern void SpriteShapeGenerator_GetEndContourIndexOfSegment_m34A178F013D01C7EA2713CCE22E43BFF85D2E8CD_AdjustorThunk (void);
extern void SpriteShapeGenerator_SetResult_mB943A248C968BCA647038BD8CF5F568BB4669475_AdjustorThunk (void);
extern void SpriteShapeGenerator_AppendCornerCoordinates_mDCB86690220150FEE68F15E621D8507E22095B2B_AdjustorThunk (void);
extern void SpriteShapeGenerator_PrepareInput_mE4FDC5415D5B004748A141D7F51383B5255E0FBF_AdjustorThunk (void);
extern void SpriteShapeGenerator_TransferSprites_m7EB37DB492012CA10801310B4A6AB9D23C9D604A_AdjustorThunk (void);
extern void SpriteShapeGenerator_PrepareSprites_m5423E297D56773AD47D53877570C57D2B38B4E24_AdjustorThunk (void);
extern void SpriteShapeGenerator_PrepareAngleRanges_m612B2151F30299B9B7AACDA7A041510E65EB618B_AdjustorThunk (void);
extern void SpriteShapeGenerator_PrepareControlPoints_mABC163490DAF124A972C1440B19D0140EB2673D4_AdjustorThunk (void);
extern void SpriteShapeGenerator_WithinRange_mA4F9BD34C7AAA5111DF48DFCC66FCE28FB70E31E_AdjustorThunk (void);
extern void SpriteShapeGenerator_AngleWithinRange_mBFC8803A87435D94B5AA0D8E779A79729AC7FC90_AdjustorThunk (void);
extern void SpriteShapeGenerator_ResolveAngle_m718F26C825AD850CDE3B9780C7EA4F4FD09360A1_AdjustorThunk (void);
extern void SpriteShapeGenerator_GetSpriteIndex_mFE565014AF490E45B712164C61A27C3E6B230163_AdjustorThunk (void);
extern void SpriteShapeGenerator_GenerateSegments_mFD269D1C42D371CD823610D0DF68AFAF6C221E9F_AdjustorThunk (void);
extern void SpriteShapeGenerator_UpdateSegments_m7D6F5BA1B8A1E53960801B0B947F46CAE379AA8E_AdjustorThunk (void);
extern void SpriteShapeGenerator_GetSegmentBoundaryColumn_m93EFD1563B99BCBA319FA4A4ADF2F9CA382190CB_AdjustorThunk (void);
extern void SpriteShapeGenerator_GenerateControlPoints_mAD773E2807C8D8E29AC20CD125A78C516A1C6F82_AdjustorThunk (void);
extern void SpriteShapeGenerator_SegmentDistance_mC59D5EFEDEEB7AA90180AF4B4F53DF6BF49BFDD5_AdjustorThunk (void);
extern void SpriteShapeGenerator_GenerateContour_m6EBB4C1E95B82166E226FD2EAE9B33341EBBB5A2_AdjustorThunk (void);
extern void SpriteShapeGenerator_PrepareContour_m89E38CF7A6CD6D93457277CA6AA43BA7BFA61973_AdjustorThunk (void);
extern void SpriteShapeGenerator_TessellateContour_mF70D92C350C736C57382EAD8AB02413344FD5545_AdjustorThunk (void);
extern void SpriteShapeGenerator_TessellateContourMainThread_m4509E90FD48DB95C993D9144DDA67B825F809EE1_AdjustorThunk (void);
extern void SpriteShapeGenerator_CalculateBoundingBox_m51E8608113267A80B08E671CD0E6F9E98403DE3A_AdjustorThunk (void);
extern void SpriteShapeGenerator_CalculateTexCoords_m601199E7DD5A81D4586B708E9BF175692744B3A3_AdjustorThunk (void);
extern void SpriteShapeGenerator_CopyVertexData_mBD9B93DAB3A30EA5D20DD8EB415C91437C957F0D_AdjustorThunk (void);
extern void SpriteShapeGenerator_CopySegmentRenderData_m48B6A14833D7B597E6E97A6F53E1542FA0A23F76_AdjustorThunk (void);
extern void SpriteShapeGenerator_GetLineSegments_m42433AFD66A0FE4716E83F2D8F1EB3FFBAE614FE_AdjustorThunk (void);
extern void SpriteShapeGenerator_TessellateSegment_m85D8FE88E38E3AC599183C5CB25D61EF203C3878_AdjustorThunk (void);
extern void SpriteShapeGenerator_SkipSegment_m60AD06EAE4EC71495A2C4DDF16E30CCCC7AC48D1_AdjustorThunk (void);
extern void SpriteShapeGenerator_InterpolateLinear_m6655E2A9307AFE3E0F947E22789FD1D6709260BB_AdjustorThunk (void);
extern void SpriteShapeGenerator_InterpolateSmooth_m8A19C193EC1ECCA8116A4FE8834C22E4CDC38A1F_AdjustorThunk (void);
extern void SpriteShapeGenerator_AddVertex_m299A47F7A33CAB37533B90ACF8AD971DD9D358F2_AdjustorThunk (void);
extern void SpriteShapeGenerator_TessellateSegments_m42A1531B9F7C1DEC85DB7367542CFEC3D90E7FAA_AdjustorThunk (void);
extern void SpriteShapeGenerator_FetchStretcher_m62E10BC6A8E62E8B07FAB981EA8791443792ECFC_AdjustorThunk (void);
extern void SpriteShapeGenerator_StretchCorners_mEF433099727D0C24530F28C9AA5A688FD001AEF4_AdjustorThunk (void);
extern void SpriteShapeGenerator_ExtendSegment_mA0441D565DA580DFA03ED402D55C01FCE45A62DC_AdjustorThunk (void);
extern void SpriteShapeGenerator_GetIntersection_m7A6907C124D4C476D9F6A474EB8D2C32B3ED22D0_AdjustorThunk (void);
extern void SpriteShapeGenerator_AttachCorner_mE870FE2AFC67B7EA889191D1D4290690B3D9AEDE_AdjustorThunk (void);
extern void SpriteShapeGenerator_CornerTextureCoordinate_m832C4406CE02463EA7B1E0B1BAD91F0D3902E485_AdjustorThunk (void);
extern void SpriteShapeGenerator_CalculateCorner_m2889F0F78372F001B2827FC7F97311A6FF801828_AdjustorThunk (void);
extern void SpriteShapeGenerator_InsertCorner_m2A32C77F92888AAEFDE80128460D5FAAE8C8DEC5_AdjustorThunk (void);
extern void SpriteShapeGenerator_TessellateCorners_mBEC539F9EBD424BC9682C3EF748E500E2D1A5C27_AdjustorThunk (void);
extern void SpriteShapeGenerator_AreCollinear_m2CF9A2780D5320C72BBC9415DDEC1A04FE57D89F_AdjustorThunk (void);
extern void SpriteShapeGenerator_OptimizePoints_m12067570C5E61E1291B57D9C6F8D284CCE19F46D_AdjustorThunk (void);
extern void SpriteShapeGenerator_AttachCornerToCollider_m2468C75A72FD1AFD391A373FECDB44FABF9BE78F_AdjustorThunk (void);
extern void SpriteShapeGenerator_UpdateExtraGeometry_mECAD4B1A09D3025A61B93F90029FC864AFD2D6EF_AdjustorThunk (void);
extern void SpriteShapeGenerator_OptimizeCollider_mBA6D3CB699F42750C239CA0BC201B672B1FD9AA8_AdjustorThunk (void);
extern void SpriteShapeGenerator_OptimizeShadow_m23799E7E865F53073846F752F6495DFBEBB0ACB6_AdjustorThunk (void);
extern void SpriteShapeGenerator_Prepare_mF79A57490E60706BE72D78830EC71DEC17B1DCF1_AdjustorThunk (void);
extern void SpriteShapeGenerator_Prepare_m8358A9BD95A1D45F0FBE63A5661421DD0D8B4DB7_AdjustorThunk (void);
extern void SpriteShapeGenerator_Execute_m6F11F51210E20869A78D43F7AD6E0702D8515A91_AdjustorThunk (void);
extern void SpriteShapeGenerator_Cleanup_m2662B1967B4613B611E1E7E57341E0E7862B7FF1_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[81] = 
{
	{ 0x06000038, Vec3_get_Item_m18BE18D5B8DE79B3C906B13FA63CA2AC304BBE37_AdjustorThunk },
	{ 0x06000039, Vec3_set_Item_m0E3994FE25BEBE75671FFCC4DD45A6ABD438B25B_AdjustorThunk },
	{ 0x0600003F, Vec3_ToString_mFCCD5C16E92C7EB6A1EADB61183CF3C6C1CD93AB_AdjustorThunk },
	{ 0x06000054, EdgePair_Reset_m0B56DA5E426A184272B24B74B7A26085C1F162A4_AdjustorThunk },
	{ 0x060000B6, ContourVertex_ToString_mD386EFF0657B8A3D8060BF097ABFD6473440CF9F_AdjustorThunk },
	{ 0x06000176, SpriteShapeGenerator_get_vertexDataCount_mDC30ECF81BCDFA0769E3F7EACE2718E929637EFD_AdjustorThunk },
	{ 0x06000177, SpriteShapeGenerator_get_vertexArrayCount_m776FBDEEB5A98DDBE16B219D2587D981B606D910_AdjustorThunk },
	{ 0x06000178, SpriteShapeGenerator_get_indexDataCount_m1E6F0585FA2A3F592856EBB491409A7B39ABBB16_AdjustorThunk },
	{ 0x06000179, SpriteShapeGenerator_get_spriteCount_m8D39765E70D833493EEA503ED982F410EA9DFA53_AdjustorThunk },
	{ 0x0600017A, SpriteShapeGenerator_get_cornerSpriteCount_mF842CCBF80F27E03641B10A9A12D721EA6957642_AdjustorThunk },
	{ 0x0600017B, SpriteShapeGenerator_get_angleRangeCount_mE6EB56E420B87315E754A66D294CF201F93FA1A8_AdjustorThunk },
	{ 0x0600017C, SpriteShapeGenerator_get_controlPointCount_m0B72EE45FD6E4BF63650958439B43712ADD39696_AdjustorThunk },
	{ 0x0600017D, SpriteShapeGenerator_get_contourPointCount_m45CA4B09B39611D4A1C09F494A3BED828258CC41_AdjustorThunk },
	{ 0x0600017E, SpriteShapeGenerator_get_segmentCount_m5B0A226EBA7CA12B095A92DB429DEB30DF3C7210_AdjustorThunk },
	{ 0x0600017F, SpriteShapeGenerator_get_hasCollider_m6C79BA0841071B0B97D33C195716F14C0B5453DC_AdjustorThunk },
	{ 0x06000180, SpriteShapeGenerator_get_hasShadow_mF8B09961DA6FE6A05AA217EA3DAC9FF8D026FBB3_AdjustorThunk },
	{ 0x06000181, SpriteShapeGenerator_get_colliderPivot_m8D45A62C4E63DFD67D502547812616043B2582E7_AdjustorThunk },
	{ 0x06000182, SpriteShapeGenerator_get_shadowPivot_mDF1695D06772C0DF9F3D30F628780F1EF4A2B39E_AdjustorThunk },
	{ 0x06000183, SpriteShapeGenerator_get_borderPivot_m1CB177826C97190744B42A7A39F1E4B2ACA17941_AdjustorThunk },
	{ 0x06000184, SpriteShapeGenerator_get_splineDetail_m3739C3D78D2F7D5EE0C31ADC7EFF1A28E1620D5F_AdjustorThunk },
	{ 0x06000185, SpriteShapeGenerator_get_isCarpet_m82A7BACAF2824B38318697B6BD0BC97FE07624B2_AdjustorThunk },
	{ 0x06000186, SpriteShapeGenerator_get_isAdaptive_m8022015FFC581294D888FCC63FEDC6B503985C85_AdjustorThunk },
	{ 0x06000187, SpriteShapeGenerator_get_hasSpriteBorder_m015043D5050787F1744E27679E6681101C24C01F_AdjustorThunk },
	{ 0x06000188, SpriteShapeGenerator_GetSpriteInfo_mC06825216D7CF04273E559CBBACEA742CCCC129E_AdjustorThunk },
	{ 0x06000189, SpriteShapeGenerator_GetCornerSpriteInfo_m6170012E001B82F923367E339A60AE997AEC7335_AdjustorThunk },
	{ 0x0600018A, SpriteShapeGenerator_GetAngleRange_m5FCB024AC77D929F60F35E4852920B49EC40D53F_AdjustorThunk },
	{ 0x0600018B, SpriteShapeGenerator_GetControlPoint_mF73708A8E099838AC8806D2EE1305D230C709A37_AdjustorThunk },
	{ 0x0600018C, SpriteShapeGenerator_GetContourPoint_m7BD62D1044854A6E2811C65FC8C4C3451133A20A_AdjustorThunk },
	{ 0x0600018D, SpriteShapeGenerator_GetSegmentInfo_m72363F976D7D18181DED537467749537D0999DA9_AdjustorThunk },
	{ 0x0600018E, SpriteShapeGenerator_GetContourIndex_m8705A508B3B54F8268411F3FDF3DCBD030705500_AdjustorThunk },
	{ 0x0600018F, SpriteShapeGenerator_GetEndContourIndexOfSegment_m34A178F013D01C7EA2713CCE22E43BFF85D2E8CD_AdjustorThunk },
	{ 0x06000190, SpriteShapeGenerator_SetResult_mB943A248C968BCA647038BD8CF5F568BB4669475_AdjustorThunk },
	{ 0x0600019C, SpriteShapeGenerator_AppendCornerCoordinates_mDCB86690220150FEE68F15E621D8507E22095B2B_AdjustorThunk },
	{ 0x0600019D, SpriteShapeGenerator_PrepareInput_mE4FDC5415D5B004748A141D7F51383B5255E0FBF_AdjustorThunk },
	{ 0x0600019E, SpriteShapeGenerator_TransferSprites_m7EB37DB492012CA10801310B4A6AB9D23C9D604A_AdjustorThunk },
	{ 0x0600019F, SpriteShapeGenerator_PrepareSprites_m5423E297D56773AD47D53877570C57D2B38B4E24_AdjustorThunk },
	{ 0x060001A0, SpriteShapeGenerator_PrepareAngleRanges_m612B2151F30299B9B7AACDA7A041510E65EB618B_AdjustorThunk },
	{ 0x060001A1, SpriteShapeGenerator_PrepareControlPoints_mABC163490DAF124A972C1440B19D0140EB2673D4_AdjustorThunk },
	{ 0x060001A2, SpriteShapeGenerator_WithinRange_mA4F9BD34C7AAA5111DF48DFCC66FCE28FB70E31E_AdjustorThunk },
	{ 0x060001A3, SpriteShapeGenerator_AngleWithinRange_mBFC8803A87435D94B5AA0D8E779A79729AC7FC90_AdjustorThunk },
	{ 0x060001A7, SpriteShapeGenerator_ResolveAngle_m718F26C825AD850CDE3B9780C7EA4F4FD09360A1_AdjustorThunk },
	{ 0x060001A8, SpriteShapeGenerator_GetSpriteIndex_mFE565014AF490E45B712164C61A27C3E6B230163_AdjustorThunk },
	{ 0x060001A9, SpriteShapeGenerator_GenerateSegments_mFD269D1C42D371CD823610D0DF68AFAF6C221E9F_AdjustorThunk },
	{ 0x060001AA, SpriteShapeGenerator_UpdateSegments_m7D6F5BA1B8A1E53960801B0B947F46CAE379AA8E_AdjustorThunk },
	{ 0x060001AB, SpriteShapeGenerator_GetSegmentBoundaryColumn_m93EFD1563B99BCBA319FA4A4ADF2F9CA382190CB_AdjustorThunk },
	{ 0x060001AC, SpriteShapeGenerator_GenerateControlPoints_mAD773E2807C8D8E29AC20CD125A78C516A1C6F82_AdjustorThunk },
	{ 0x060001AD, SpriteShapeGenerator_SegmentDistance_mC59D5EFEDEEB7AA90180AF4B4F53DF6BF49BFDD5_AdjustorThunk },
	{ 0x060001AE, SpriteShapeGenerator_GenerateContour_m6EBB4C1E95B82166E226FD2EAE9B33341EBBB5A2_AdjustorThunk },
	{ 0x060001AF, SpriteShapeGenerator_PrepareContour_m89E38CF7A6CD6D93457277CA6AA43BA7BFA61973_AdjustorThunk },
	{ 0x060001B1, SpriteShapeGenerator_TessellateContour_mF70D92C350C736C57382EAD8AB02413344FD5545_AdjustorThunk },
	{ 0x060001B2, SpriteShapeGenerator_TessellateContourMainThread_m4509E90FD48DB95C993D9144DDA67B825F809EE1_AdjustorThunk },
	{ 0x060001B3, SpriteShapeGenerator_CalculateBoundingBox_m51E8608113267A80B08E671CD0E6F9E98403DE3A_AdjustorThunk },
	{ 0x060001B4, SpriteShapeGenerator_CalculateTexCoords_m601199E7DD5A81D4586B708E9BF175692744B3A3_AdjustorThunk },
	{ 0x060001B5, SpriteShapeGenerator_CopyVertexData_mBD9B93DAB3A30EA5D20DD8EB415C91437C957F0D_AdjustorThunk },
	{ 0x060001B6, SpriteShapeGenerator_CopySegmentRenderData_m48B6A14833D7B597E6E97A6F53E1542FA0A23F76_AdjustorThunk },
	{ 0x060001B7, SpriteShapeGenerator_GetLineSegments_m42433AFD66A0FE4716E83F2D8F1EB3FFBAE614FE_AdjustorThunk },
	{ 0x060001B8, SpriteShapeGenerator_TessellateSegment_m85D8FE88E38E3AC599183C5CB25D61EF203C3878_AdjustorThunk },
	{ 0x060001B9, SpriteShapeGenerator_SkipSegment_m60AD06EAE4EC71495A2C4DDF16E30CCCC7AC48D1_AdjustorThunk },
	{ 0x060001BA, SpriteShapeGenerator_InterpolateLinear_m6655E2A9307AFE3E0F947E22789FD1D6709260BB_AdjustorThunk },
	{ 0x060001BB, SpriteShapeGenerator_InterpolateSmooth_m8A19C193EC1ECCA8116A4FE8834C22E4CDC38A1F_AdjustorThunk },
	{ 0x060001BC, SpriteShapeGenerator_AddVertex_m299A47F7A33CAB37533B90ACF8AD971DD9D358F2_AdjustorThunk },
	{ 0x060001BD, SpriteShapeGenerator_TessellateSegments_m42A1531B9F7C1DEC85DB7367542CFEC3D90E7FAA_AdjustorThunk },
	{ 0x060001BE, SpriteShapeGenerator_FetchStretcher_m62E10BC6A8E62E8B07FAB981EA8791443792ECFC_AdjustorThunk },
	{ 0x060001BF, SpriteShapeGenerator_StretchCorners_mEF433099727D0C24530F28C9AA5A688FD001AEF4_AdjustorThunk },
	{ 0x060001C0, SpriteShapeGenerator_ExtendSegment_mA0441D565DA580DFA03ED402D55C01FCE45A62DC_AdjustorThunk },
	{ 0x060001C1, SpriteShapeGenerator_GetIntersection_m7A6907C124D4C476D9F6A474EB8D2C32B3ED22D0_AdjustorThunk },
	{ 0x060001C2, SpriteShapeGenerator_AttachCorner_mE870FE2AFC67B7EA889191D1D4290690B3D9AEDE_AdjustorThunk },
	{ 0x060001C3, SpriteShapeGenerator_CornerTextureCoordinate_m832C4406CE02463EA7B1E0B1BAD91F0D3902E485_AdjustorThunk },
	{ 0x060001C4, SpriteShapeGenerator_CalculateCorner_m2889F0F78372F001B2827FC7F97311A6FF801828_AdjustorThunk },
	{ 0x060001C5, SpriteShapeGenerator_InsertCorner_m2A32C77F92888AAEFDE80128460D5FAAE8C8DEC5_AdjustorThunk },
	{ 0x060001C6, SpriteShapeGenerator_TessellateCorners_mBEC539F9EBD424BC9682C3EF748E500E2D1A5C27_AdjustorThunk },
	{ 0x060001C7, SpriteShapeGenerator_AreCollinear_m2CF9A2780D5320C72BBC9415DDEC1A04FE57D89F_AdjustorThunk },
	{ 0x060001C8, SpriteShapeGenerator_OptimizePoints_m12067570C5E61E1291B57D9C6F8D284CCE19F46D_AdjustorThunk },
	{ 0x060001C9, SpriteShapeGenerator_AttachCornerToCollider_m2468C75A72FD1AFD391A373FECDB44FABF9BE78F_AdjustorThunk },
	{ 0x060001CA, SpriteShapeGenerator_UpdateExtraGeometry_mECAD4B1A09D3025A61B93F90029FC864AFD2D6EF_AdjustorThunk },
	{ 0x060001CC, SpriteShapeGenerator_OptimizeCollider_mBA6D3CB699F42750C239CA0BC201B672B1FD9AA8_AdjustorThunk },
	{ 0x060001CD, SpriteShapeGenerator_OptimizeShadow_m23799E7E865F53073846F752F6495DFBEBB0ACB6_AdjustorThunk },
	{ 0x060001CE, SpriteShapeGenerator_Prepare_mF79A57490E60706BE72D78830EC71DEC17B1DCF1_AdjustorThunk },
	{ 0x060001CF, SpriteShapeGenerator_Prepare_m8358A9BD95A1D45F0FBE63A5661421DD0D8B4DB7_AdjustorThunk },
	{ 0x060001D0, SpriteShapeGenerator_Execute_m6F11F51210E20869A78D43F7AD6E0702D8515A91_AdjustorThunk },
	{ 0x060001D1, SpriteShapeGenerator_Cleanup_m2662B1967B4613B611E1E7E57341E0E7862B7FF1_AdjustorThunk },
};
static const int32_t s_InvokerIndices[508] = 
{
	20761,
	20761,
	20761,
	20694,
	20550,
	20550,
	21016,
	670,
	21016,
	6019,
	21016,
	34301,
	21016,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	27475,
	25327,
	27508,
	27508,
	25757,
	25757,
	27508,
	25757,
	25757,
	31561,
	31561,
	28249,
	29176,
	24381,
	28910,
	23533,
	21016,
	21016,
	21016,
	20761,
	7995,
	15968,
	13820,
	13820,
	6094,
	15968,
	15903,
	21016,
	14019,
	7539,
	26244,
	32748,
	26244,
	32748,
	31768,
	20761,
	34252,
	32090,
	29176,
	29176,
	29176,
	32764,
	29176,
	29176,
	32262,
	-1,
	-1,
	-1,
	-1,
	-1,
	21016,
	21016,
	20694,
	21016,
	21016,
	34273,
	21016,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	32748,
	21016,
	21016,
	34252,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	13820,
	13820,
	5018,
	15968,
	7995,
	13820,
	13820,
	6094,
	15968,
	15968,
	6094,
	1290,
	7995,
	1281,
	1293,
	11681,
	11681,
	11681,
	15968,
	7995,
	7995,
	15968,
	15968,
	3855,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21001,
	16196,
	20761,
	20694,
	20761,
	20694,
	21016,
	15721,
	21016,
	21016,
	15968,
	21016,
	21016,
	7312,
	13212,
	7405,
	21016,
	14024,
	15968,
	7985,
	3622,
	2567,
	21016,
	20761,
	7988,
	3343,
	1130,
	13820,
	22886,
	32262,
	25744,
	21849,
	21849,
	21717,
	21419,
	24445,
	28263,
	25766,
	25358,
	34252,
	20550,
	15757,
	3043,
	21016,
	20694,
	7651,
	15903,
	14275,
	7651,
	14275,
	7651,
	14275,
	7651,
	13157,
	7405,
	14019,
	7539,
	13157,
	7405,
	11619,
	7312,
	7405,
	13157,
	20694,
	20694,
	21016,
	34252,
	28262,
	21913,
	27836,
	27836,
	27836,
	21016,
	-1,
	-1,
	-1,
	20694,
	15903,
	20694,
	21016,
	20873,
	16071,
	20873,
	16071,
	20694,
	15903,
	20761,
	15968,
	20761,
	11681,
	20694,
	21016,
	20694,
	15903,
	20761,
	15968,
	20761,
	11681,
	20694,
	21016,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20873,
	16071,
	20550,
	15757,
	13811,
	21016,
	21016,
	21016,
	31787,
	21016,
	20694,
	15903,
	20550,
	15757,
	20694,
	20761,
	20885,
	20761,
	20761,
	20761,
	18882,
	20761,
	20761,
	15968,
	20761,
	20694,
	20550,
	15757,
	20873,
	16071,
	20550,
	15757,
	20873,
	16071,
	20694,
	15903,
	20694,
	15903,
	20873,
	16071,
	20873,
	16071,
	20550,
	15757,
	20550,
	20550,
	20550,
	20761,
	20873,
	16071,
	20761,
	15968,
	20761,
	20761,
	20761,
	20550,
	15757,
	20694,
	15903,
	20873,
	16071,
	20761,
	18871,
	20550,
	15757,
	20550,
	15757,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	32764,
	20547,
	21016,
	20550,
	20550,
	20550,
	20550,
	20550,
	20694,
	20550,
	21016,
	21016,
	21016,
	20705,
	21016,
	20550,
	21016,
	18868,
	18870,
	12717,
	20705,
	21016,
	21016,
	21016,
	15757,
	18882,
	21016,
	34252,
	34252,
	21016,
	5704,
	13212,
	375,
	34156,
	20694,
	21016,
	20694,
	20694,
	20694,
	20694,
	20694,
	20694,
	20694,
	20694,
	20694,
	20550,
	20550,
	20873,
	20873,
	20873,
	20694,
	20550,
	20550,
	20550,
	16809,
	16809,
	16802,
	16804,
	16803,
	16807,
	13157,
	13569,
	15903,
	-1,
	-1,
	23759,
	21952,
	25362,
	24184,
	22259,
	21951,
	28269,
	21678,
	21593,
	654,
	105,
	3529,
	7995,
	15968,
	6365,
	5268,
	3128,
	23579,
	32273,
	28269,
	846,
	3220,
	21016,
	21016,
	263,
	21016,
	14040,
	21016,
	20550,
	21560,
	11619,
	21016,
	21016,
	21016,
	397,
	111,
	495,
	38,
	12147,
	3395,
	3395,
	2992,
	21016,
	346,
	809,
	6503,
	54,
	826,
	8302,
	1747,
	824,
	21016,
	1521,
	2896,
	2952,
	497,
	21860,
	21016,
	21016,
	318,
	201,
	21016,
	21016,
	21560,
	34252,
	21016,
	14206,
	14259,
	7988,
	117,
	66,
	15968,
	32748,
	34140,
	21560,
	-1,
	-1,
	20694,
	21016,
	-1,
	20694,
	21016,
	20550,
	15757,
	20694,
	15903,
	20873,
	16071,
	20761,
	15968,
	20694,
	15903,
	20694,
	15903,
	20550,
	28264,
	389,
	2337,
	2338,
	20694,
	20550,
	21016,
	21016,
	21016,
	34252,
	34252,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[1] = 
{
	{ 0x060001B0, 36,  (void**)&SpriteShapeGenerator_UTessellator_mA975B9F66E80F2334124BFADD19F331AB2F6E90D_RuntimeMethod_var, 0 },
};
static const Il2CppTokenRangePair s_rgctxIndices[8] = 
{
	{ 0x02000006, { 0, 7 } },
	{ 0x02000007, { 7, 2 } },
	{ 0x0200000D, { 9, 11 } },
	{ 0x02000013, { 20, 12 } },
	{ 0x02000016, { 32, 25 } },
	{ 0x02000023, { 57, 6 } },
	{ 0x06000191, { 63, 7 } },
	{ 0x06000192, { 70, 4 } },
};
extern const uint32_t g_rgctx_LessOrEqual_t235BFDE2DF026319C066D3BD3D2476E7FDC79A90;
extern const uint32_t g_rgctx_Dict_1_tC967AF6B0A4E5F745CE24A6F8DB7460D5EFC8F74;
extern const uint32_t g_rgctx_Node_tAAE440C16D3234A3D92717128D6D6B8BE2B20D8B;
extern const uint32_t g_rgctx_Node__ctor_m722DDB8721CB0E22D836860AA89B18AF2000C936;
extern const uint32_t g_rgctx_TValue_t8E200EA267C25B50E736DE3174CB8D0DD32980D9;
extern const uint32_t g_rgctx_Dict_1_InsertBefore_m5748CAC19DFCB88834D7F62C289821D6BB9F3A87;
extern const uint32_t g_rgctx_LessOrEqual_Invoke_m00851557121AE1795FF4AC403462822959167004;
extern const uint32_t g_rgctx_Node_tC89AE2B4E6D0F01DAE12753D9777552104B1806E;
extern const uint32_t g_rgctx_TValue_t6B77656D4F8B69FB73D324423F81197CE2AFAF11;
extern const uint32_t g_rgctx_Pooled_1_t5F6CF0E657E239D027487E1B1E5C5318012A089D;
extern const uint32_t g_rgctx_Stack_1_t6AD90E73441EEA2F21181B9C34CDA4B105A7900A;
extern const uint32_t g_rgctx_Pooled_1_t5F6CF0E657E239D027487E1B1E5C5318012A089D;
extern const uint32_t g_rgctx_Stack_1_get_Count_m6D9779E9386491F43207F7B348A7D4D6167CF29F;
extern const uint32_t g_rgctx_Stack_1_Pop_m82894D3F1DFF5DEA8FA311C714D8CEF7737DDC8A;
extern const uint32_t g_rgctx_T_t0CC56A6E4B526FD1AEFCBD7FFA87F268345A072F;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisT_t0CC56A6E4B526FD1AEFCBD7FFA87F268345A072F_m7A64B6B55D052901E147ED913B9A9337C569FAB6;
extern const uint32_t g_rgctx_Pooled_1_OnFree_m75D1E6A0776BB999A358A28B8F2EF6057F444706;
extern const uint32_t g_rgctx_Pooled_1_Reset_m6930409BBE488270B65758EBC2B7509CB1AF25F4;
extern const uint32_t g_rgctx_Stack_1__ctor_m021B170A70F910319B5D15F9EEFD258083A1DAED;
extern const uint32_t g_rgctx_Stack_1_Push_m6F9E52BFC7557D16B48F3A04800BCA57B80F2947;
extern const uint32_t g_rgctx_PriorityHeap_1_t3D68B532D185181A2C5F04089139FA66AFE0471E;
extern const uint32_t g_rgctx_LessOrEqual_tB0894ABAB072A663A98D63A3EA17D7546EBFC492;
extern const uint32_t g_rgctx_HandleElemU5BU5D_t9AC53BECCAEF92BD980BF4C98BD65507E5DECCA3;
extern const uint32_t g_rgctx_HandleElemU5BU5D_t9AC53BECCAEF92BD980BF4C98BD65507E5DECCA3;
extern const uint32_t g_rgctx_HandleElem_tBBB792E2CD3254F9E905F0CD9E642235ED40429E;
extern const uint32_t g_rgctx_HandleElem__ctor_m950B320D55F8CA6B27AEA48996404949C1A17BC3;
extern const uint32_t g_rgctx_TValue_t75C3385BAF6DF3DB1BC55022D41E507EB3D0B22C;
extern const uint32_t g_rgctx_LessOrEqual_Invoke_m1EEF8AECFA99980857B8AB4DC0D57900F18A1EE0;
extern const uint32_t g_rgctx_PriorityHeap_1_FloatDown_mE3BBA8B3B6BD4ECB4AB13D70E8EC2676721E7E32;
extern const uint32_t g_rgctx_Array_Resize_TisHandleElem_tBBB792E2CD3254F9E905F0CD9E642235ED40429E_m48DD043EF2BB330DA2F872C0DB8B6BAE6FAC5211;
extern const uint32_t g_rgctx_HandleElemU5BU5DU26_tB0ABAA7D512EB27395D9CA6F53B82C2F4A956275;
extern const uint32_t g_rgctx_PriorityHeap_1_FloatUp_mC196D6C800B8BA95ED52C909AFFA5A81A2FD9040;
extern const uint32_t g_rgctx_PriorityQueue_1_tDF1B775DC95824FEE4078978E91D399D98B15F55;
extern const uint32_t g_rgctx_PriorityHeap_1_tD99575E32E9401200ED4673F1E46A0AE723C5771;
extern const uint32_t g_rgctx_PriorityHeap_1_get_Empty_mF7DD9FD4F3CBD40E8DE273C1334DC766C918A568;
extern const uint32_t g_rgctx_LessOrEqual_tCCAEB5F72CFC20C902E5EB4748A317C0766262E3;
extern const uint32_t g_rgctx_PriorityHeap_1__ctor_m07B177193CB7F458FFA945E6C9F30589C968B1A1;
extern const uint32_t g_rgctx_TValueU5BU5D_t729ADCE989522363DFA06FCF0E5924EFE30CA925;
extern const uint32_t g_rgctx_TValueU5BU5D_t729ADCE989522363DFA06FCF0E5924EFE30CA925;
extern const uint32_t g_rgctx_Stack_1_t6DBBF8739144CE5230AC38E30BD9DB4653F68E30;
extern const uint32_t g_rgctx_Stack_1__ctor_m3259FAADCDA92825556E9F474FE75E7503A5E8C6;
extern const uint32_t g_rgctx_StackItem_t7C7E09F4A573772D1998B706CF11F0B1EDC5227D;
extern const uint32_t g_rgctx_StackItem__ctor_m5DE8179F80EBA29524F5EB7AC1402040BB5C4D74;
extern const uint32_t g_rgctx_Stack_1_Push_m93EDE51334990B11BE2D1A625B7DE137D2B5C482;
extern const uint32_t g_rgctx_Stack_1_Pop_m1FA0DCE5C4665CEE170042A038C81BB0A0517470;
extern const uint32_t g_rgctx_TValue_t586B3769B64A521A42ABC29D29910E821B42DF8B;
extern const uint32_t g_rgctx_LessOrEqual_Invoke_m6EAA1C21DE6D1F74FBB2260F11F64B4A31EB8022;
extern const uint32_t g_rgctx_PriorityQueue_1_Swap_m3F38A013B118DAF9656F383BE715E49D8C8AF881;
extern const uint32_t g_rgctx_PriorityQueue_1_tDF1B775DC95824FEE4078978E91D399D98B15F55;
extern const uint32_t g_rgctx_Stack_1_get_Count_m363224DE9CB7B45F70CB812F277AE84C7FF27E76;
extern const uint32_t g_rgctx_PriorityHeap_1_Init_mAB534B341F50482636470A2CFFFA677A119F3D63;
extern const uint32_t g_rgctx_PriorityHeap_1_Insert_m9AEF4FA091D6B25DEE3C3FC2E3960AF182A73E60;
extern const uint32_t g_rgctx_Array_Resize_TisTValue_t586B3769B64A521A42ABC29D29910E821B42DF8B_m24C30521F5B99B46D2178D106A4767A2B18D0FCE;
extern const uint32_t g_rgctx_TValueU5BU5DU26_t839BF2D4C1424C21DAC5BE85574FC5DC02FF0293;
extern const uint32_t g_rgctx_PriorityHeap_1_ExtractMin_m57728F02F3BC6D00AD213D4EB300689A5CD53D12;
extern const uint32_t g_rgctx_PriorityHeap_1_Minimum_m572E052246A6EB76F21BF1AC2DBBF6792D583690;
extern const uint32_t g_rgctx_PriorityHeap_1_Remove_mF1DB93EE111A97A447E41F2CB5AC9FA9F09FCA83;
extern const uint32_t g_rgctx_NativeSlice_1_tA65C870B5BD05F34522FBA2EA2BAA91DDA44674F;
extern const uint32_t g_rgctx_NativeSlice_1__ctor_mB8E29565F8FC0A6705659F325779825448DDAD17;
extern const uint32_t g_rgctx_NativeSlice_1_tA65C870B5BD05F34522FBA2EA2BAA91DDA44674F;
extern const uint32_t g_rgctx_TU5BU5D_t5B728F7A92893420A57E2DC8649103A991F57E20;
extern const uint32_t g_rgctx_NativeSlice_1_CopyFrom_m7E8B031C45CCF9102E867FF60E8455E96D900D46;
extern const uint32_t g_rgctx_NativeSlice_1_CopyTo_mF817A7F4D8EF795F49436F8572E04BE42B530B68;
extern const uint32_t g_rgctx_NativeArray_1U26_tCD0F39ACD2EF8E5851B6CB21C440966AA7501F1B;
extern const uint32_t g_rgctx_NativeArray_1_tEFE5B4CC9869B11D5113B9421AE4E974AEA5FCAB;
extern const uint32_t g_rgctx_NativeArray_1__ctor_m0135AD5C12440D633149A01937EDE1D35D086FC9;
extern const uint32_t g_rgctx_NativeArray_1_get_Item_mA06536DC6F904D69950557B448DA8F42B0CAE608;
extern const uint32_t g_rgctx_NativeArray_1_tEFE5B4CC9869B11D5113B9421AE4E974AEA5FCAB;
extern const uint32_t g_rgctx_T_t1697A2EC5A878184A5A596EBE6B94BEF15E75B78;
extern const uint32_t g_rgctx_NativeArray_1_set_Item_mA1B323AA8D92B8EAC072626245FD5188B8A49E43;
extern const uint32_t g_rgctx_NativeArray_1_t9493F4EBF8A0BCEC291401A04EA0C670C76736D1;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_mD45CA2A20789104F363366FE31DFE7EDACED19D8;
extern const uint32_t g_rgctx_NativeArray_1_t9493F4EBF8A0BCEC291401A04EA0C670C76736D1;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_mDD3299ADD43D0B3698DF1DA34CBEF974AE1906BE;
static const Il2CppRGCTXDefinition s_rgctxValues[74] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LessOrEqual_t235BFDE2DF026319C066D3BD3D2476E7FDC79A90 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dict_1_tC967AF6B0A4E5F745CE24A6F8DB7460D5EFC8F74 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Node_tAAE440C16D3234A3D92717128D6D6B8BE2B20D8B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Node__ctor_m722DDB8721CB0E22D836860AA89B18AF2000C936 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t8E200EA267C25B50E736DE3174CB8D0DD32980D9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dict_1_InsertBefore_m5748CAC19DFCB88834D7F62C289821D6BB9F3A87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LessOrEqual_Invoke_m00851557121AE1795FF4AC403462822959167004 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Node_tC89AE2B4E6D0F01DAE12753D9777552104B1806E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t6B77656D4F8B69FB73D324423F81197CE2AFAF11 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Pooled_1_t5F6CF0E657E239D027487E1B1E5C5318012A089D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Stack_1_t6AD90E73441EEA2F21181B9C34CDA4B105A7900A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Pooled_1_t5F6CF0E657E239D027487E1B1E5C5318012A089D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_get_Count_m6D9779E9386491F43207F7B348A7D4D6167CF29F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_Pop_m82894D3F1DFF5DEA8FA311C714D8CEF7737DDC8A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0CC56A6E4B526FD1AEFCBD7FFA87F268345A072F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisT_t0CC56A6E4B526FD1AEFCBD7FFA87F268345A072F_m7A64B6B55D052901E147ED913B9A9337C569FAB6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Pooled_1_OnFree_m75D1E6A0776BB999A358A28B8F2EF6057F444706 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Pooled_1_Reset_m6930409BBE488270B65758EBC2B7509CB1AF25F4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1__ctor_m021B170A70F910319B5D15F9EEFD258083A1DAED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_Push_m6F9E52BFC7557D16B48F3A04800BCA57B80F2947 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PriorityHeap_1_t3D68B532D185181A2C5F04089139FA66AFE0471E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LessOrEqual_tB0894ABAB072A663A98D63A3EA17D7546EBFC492 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HandleElemU5BU5D_t9AC53BECCAEF92BD980BF4C98BD65507E5DECCA3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HandleElemU5BU5D_t9AC53BECCAEF92BD980BF4C98BD65507E5DECCA3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HandleElem_tBBB792E2CD3254F9E905F0CD9E642235ED40429E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HandleElem__ctor_m950B320D55F8CA6B27AEA48996404949C1A17BC3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t75C3385BAF6DF3DB1BC55022D41E507EB3D0B22C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LessOrEqual_Invoke_m1EEF8AECFA99980857B8AB4DC0D57900F18A1EE0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PriorityHeap_1_FloatDown_mE3BBA8B3B6BD4ECB4AB13D70E8EC2676721E7E32 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisHandleElem_tBBB792E2CD3254F9E905F0CD9E642235ED40429E_m48DD043EF2BB330DA2F872C0DB8B6BAE6FAC5211 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HandleElemU5BU5DU26_tB0ABAA7D512EB27395D9CA6F53B82C2F4A956275 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PriorityHeap_1_FloatUp_mC196D6C800B8BA95ED52C909AFFA5A81A2FD9040 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PriorityQueue_1_tDF1B775DC95824FEE4078978E91D399D98B15F55 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PriorityHeap_1_tD99575E32E9401200ED4673F1E46A0AE723C5771 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PriorityHeap_1_get_Empty_mF7DD9FD4F3CBD40E8DE273C1334DC766C918A568 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LessOrEqual_tCCAEB5F72CFC20C902E5EB4748A317C0766262E3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PriorityHeap_1__ctor_m07B177193CB7F458FFA945E6C9F30589C968B1A1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU5BU5D_t729ADCE989522363DFA06FCF0E5924EFE30CA925 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU5BU5D_t729ADCE989522363DFA06FCF0E5924EFE30CA925 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Stack_1_t6DBBF8739144CE5230AC38E30BD9DB4653F68E30 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1__ctor_m3259FAADCDA92825556E9F474FE75E7503A5E8C6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StackItem_t7C7E09F4A573772D1998B706CF11F0B1EDC5227D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StackItem__ctor_m5DE8179F80EBA29524F5EB7AC1402040BB5C4D74 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_Push_m93EDE51334990B11BE2D1A625B7DE137D2B5C482 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_Pop_m1FA0DCE5C4665CEE170042A038C81BB0A0517470 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t586B3769B64A521A42ABC29D29910E821B42DF8B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LessOrEqual_Invoke_m6EAA1C21DE6D1F74FBB2260F11F64B4A31EB8022 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PriorityQueue_1_Swap_m3F38A013B118DAF9656F383BE715E49D8C8AF881 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PriorityQueue_1_tDF1B775DC95824FEE4078978E91D399D98B15F55 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_get_Count_m363224DE9CB7B45F70CB812F277AE84C7FF27E76 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PriorityHeap_1_Init_mAB534B341F50482636470A2CFFFA677A119F3D63 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PriorityHeap_1_Insert_m9AEF4FA091D6B25DEE3C3FC2E3960AF182A73E60 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisTValue_t586B3769B64A521A42ABC29D29910E821B42DF8B_m24C30521F5B99B46D2178D106A4767A2B18D0FCE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU5BU5DU26_t839BF2D4C1424C21DAC5BE85574FC5DC02FF0293 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PriorityHeap_1_ExtractMin_m57728F02F3BC6D00AD213D4EB300689A5CD53D12 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PriorityHeap_1_Minimum_m572E052246A6EB76F21BF1AC2DBBF6792D583690 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PriorityHeap_1_Remove_mF1DB93EE111A97A447E41F2CB5AC9FA9F09FCA83 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_tA65C870B5BD05F34522FBA2EA2BAA91DDA44674F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1__ctor_mB8E29565F8FC0A6705659F325779825448DDAD17 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_tA65C870B5BD05F34522FBA2EA2BAA91DDA44674F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t5B728F7A92893420A57E2DC8649103A991F57E20 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_CopyFrom_m7E8B031C45CCF9102E867FF60E8455E96D900D46 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_CopyTo_mF817A7F4D8EF795F49436F8572E04BE42B530B68 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_tCD0F39ACD2EF8E5851B6CB21C440966AA7501F1B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tEFE5B4CC9869B11D5113B9421AE4E974AEA5FCAB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_m0135AD5C12440D633149A01937EDE1D35D086FC9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Item_mA06536DC6F904D69950557B448DA8F42B0CAE608 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tEFE5B4CC9869B11D5113B9421AE4E974AEA5FCAB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1697A2EC5A878184A5A596EBE6B94BEF15E75B78 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_set_Item_mA1B323AA8D92B8EAC072626245FD5188B8A49E43 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t9493F4EBF8A0BCEC291401A04EA0C670C76736D1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_mD45CA2A20789104F363366FE31DFE7EDACED19D8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t9493F4EBF8A0BCEC291401A04EA0C670C76736D1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_mDD3299ADD43D0B3698DF1DA34CBEF974AE1906BE },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_2D_SpriteShape_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_Unity_2D_SpriteShape_Runtime_CodeGenModule = 
{
	"Unity.2D.SpriteShape.Runtime.dll",
	508,
	s_methodPointers,
	81,
	s_adjustorThunks,
	s_InvokerIndices,
	1,
	s_reversePInvokeIndices,
	8,
	s_rgctxIndices,
	74,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
