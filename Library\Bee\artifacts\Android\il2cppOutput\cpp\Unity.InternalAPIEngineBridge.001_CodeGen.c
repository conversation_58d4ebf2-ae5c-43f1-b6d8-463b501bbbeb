﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m2175F39F61616C1F602A6581BFEDC408C0B170A7 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m0EB629EB7250A8D7F8E9019861342F081C732BE0 (void);
extern void InternalEngineBridge_SetLocalAABB_m04DF27869CF8971148682477CFEC2A1991BB104E (void);
extern void InternalEngineBridge_SetDeformableBuffer_mB01EA52CC0746EA5EAC860A4D9F365FFE6AF74AF (void);
extern void InternalEngineBridge_SetBoneTransforms_mD7561150898288AC7FF0D34A692BDB59815FBE4F (void);
extern void InternalEngineBridge_IsUsingDeformableBuffer_m4E15DE94C232E27395214E067CCA80F9C66E5FAE (void);
extern void InternalEngineBridge_SetupMaterialProperties_m51EFE8FE2667D186E12924594C2480417EADD02F (void);
extern void InternalEngineBridge_GUIUnclip_m27FB3DF164B3F05EA08901BB809493F1020E28DD (void);
extern void InternalEngineBridge_GetGUIClipTopMostRect_mBBC6097FFBA3285ECB7481A26AB4C99AA42973F4 (void);
extern void InternalEngineBridge_GetGUIClipTopRect_m0C6313F56EEBC59EFC622F360FBA67BB3F546ABF (void);
extern void InternalEngineBridge_GetGUIClipVisibleRect_mD15733C522038DA0EB76CFED307EC365867FCD3E (void);
extern void InternalEngineBridge_IsGPUSkinningEnabled_mC125F8C7E7AF81264A3280808241E4487BC4C729 (void);
extern void InternalEngineBridge_IsSRPBatchingEnabled_m44042937D5CE1B3EED74EE93E8F664D385707F2F (void);
extern void InternalEngineBridge_SetBatchDeformableBufferAndLocalAABBArray_mE474FF62DD89223FCCC68A8DFFBA61935B3229A3 (void);
extern void InternalEngineBridge_SetBatchBoneTransformsAABBArray_m9B4A4BB2BEC170611C73E39B840F424714A71FD1 (void);
extern void InternalEngineBridge_ConvertFloatToInt_mE74F74479983A239EE750CAF78760F79F6717F44 (void);
extern void InternalEngineBridge_ConvertIntToFloat_mB38ED35A3CD802442791415694C3E8C7E86E8148 (void);
extern void InternalEngineBridge_MarkDirty_mACDE6E198BE33C863777F878F4F166F8B1D0CB9D (void);
static Il2CppMethodPointer s_methodPointers[18] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m2175F39F61616C1F602A6581BFEDC408C0B170A7,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m0EB629EB7250A8D7F8E9019861342F081C732BE0,
	InternalEngineBridge_SetLocalAABB_m04DF27869CF8971148682477CFEC2A1991BB104E,
	InternalEngineBridge_SetDeformableBuffer_mB01EA52CC0746EA5EAC860A4D9F365FFE6AF74AF,
	InternalEngineBridge_SetBoneTransforms_mD7561150898288AC7FF0D34A692BDB59815FBE4F,
	InternalEngineBridge_IsUsingDeformableBuffer_m4E15DE94C232E27395214E067CCA80F9C66E5FAE,
	InternalEngineBridge_SetupMaterialProperties_m51EFE8FE2667D186E12924594C2480417EADD02F,
	InternalEngineBridge_GUIUnclip_m27FB3DF164B3F05EA08901BB809493F1020E28DD,
	InternalEngineBridge_GetGUIClipTopMostRect_mBBC6097FFBA3285ECB7481A26AB4C99AA42973F4,
	InternalEngineBridge_GetGUIClipTopRect_m0C6313F56EEBC59EFC622F360FBA67BB3F546ABF,
	InternalEngineBridge_GetGUIClipVisibleRect_mD15733C522038DA0EB76CFED307EC365867FCD3E,
	InternalEngineBridge_IsGPUSkinningEnabled_mC125F8C7E7AF81264A3280808241E4487BC4C729,
	InternalEngineBridge_IsSRPBatchingEnabled_m44042937D5CE1B3EED74EE93E8F664D385707F2F,
	InternalEngineBridge_SetBatchDeformableBufferAndLocalAABBArray_mE474FF62DD89223FCCC68A8DFFBA61935B3229A3,
	InternalEngineBridge_SetBatchBoneTransformsAABBArray_m9B4A4BB2BEC170611C73E39B840F424714A71FD1,
	InternalEngineBridge_ConvertFloatToInt_mE74F74479983A239EE750CAF78760F79F6717F44,
	InternalEngineBridge_ConvertIntToFloat_mB38ED35A3CD802442791415694C3E8C7E86E8148,
	InternalEngineBridge_MarkDirty_mACDE6E198BE33C863777F878F4F166F8B1D0CB9D,
};
static const int32_t s_InvokerIndices[18] = 
{
	34310,
	21016,
	29158,
	28984,
	29032,
	27505,
	32764,
	32687,
	34183,
	34183,
	34183,
	34103,
	31561,
	24609,
	24609,
	31794,
	32258,
	32764,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_InternalAPIEngineBridge_001_CodeGenModule;
const Il2CppCodeGenModule g_Unity_InternalAPIEngineBridge_001_CodeGenModule = 
{
	"Unity.InternalAPIEngineBridge.001.dll",
	18,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
