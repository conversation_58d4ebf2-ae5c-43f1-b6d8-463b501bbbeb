﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m49598D1157C88221D805991C5B1C2131062B9082 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mCE72C28E2AFCE548E9417AA4DF9CEFDCCE55F3CE (void);
extern void EventMessenger__cctor_m479B4B6FAFF426A2FDBC0D330B3E7091A035EC9D (void);
extern void EventMessenger_SubscribeInternal_m66AE194677AD9037B2A185E6CB274F270862BCD6 (void);
extern void EventMessenger_Process_m8B913AF169B06C3F75E5C722312AD4F833BA6E6D (void);
extern void EventMessenger__ctor_m2BF7A27BEFA9581C61F4768B24933796B6307584 (void);
extern void Test_TestMe_mD995CC0C57033FC3075B12130086315D94FE1766 (void);
extern void Test_OnPayloadTested_m8C6EEA0E47A65EC38C021E906BEB118B2F4CB689 (void);
extern void Test__ctor_mC66CA82F95D60E32161FA2967476D53AF181D3D2 (void);
extern void TestPayload__ctor_m149BF715535F8F7FB793EA817D009CE028D99210 (void);
extern void U3CU3Ec__cctor_mF91BDE84FC038860E7497BCF7F49C1F2099ACB16 (void);
extern void U3CU3Ec__ctor_mEE5F1E7B7E2ACD87B86EBCD26D1C4557833A7960 (void);
extern void U3CU3Ec_U3CTestMeU3Eb__1_0_m0F567980208F9381E1E73EE620EE6D677D4274E0 (void);
extern void DispatcherTask_get_Action_m8145C36613C78DB816BF6E264FEC43416017CE73 (void);
extern void DispatcherTask_set_Action_m42D6648F323316BD340516707FAB0C2708E5FCC1 (void);
extern void DispatcherTask_get_Payload_mD4DD70DBBF0810A9673A1879A62B5B81D00711D4 (void);
extern void DispatcherTask_set_Payload_m063DAABEBCEAC4BF36230211D72CA2BA2BE0D590 (void);
extern void DispatcherTask__ctor_m05D7F84B09D3C857C0F551E03A8327EB73E534EB (void);
extern void DispatcherTask_Invoke_m6362C078ADE7D560F19E283D06517D52923528EE (void);
extern void DispatcherTask_Dispose_m6EE6BC65BDDADD0E56AB03BC4C153A26A1B8852F (void);
extern void MainThreadDispatcher_get_ThreadId_mA67B30638C97743CFA5754B134E5F137F0A0F869 (void);
extern void MainThreadDispatcher_set_ThreadId_m1D2068F32CE8E4F4184C90E9F0D21F57D2160952 (void);
extern void MainThreadDispatcher_get_TasksCount_mE93214289B760BC9D2337341F3F9EBB77C2B158A (void);
extern void MainThreadDispatcher_Awake_mCCE142E359F4C58B962559441C8EC5E9E7478452 (void);
extern void MainThreadDispatcher_Dispatch_mE2D0FED86EFBD7240B08A99839266AD03F4E2996 (void);
extern void MainThreadDispatcher_Update_m42FE01ADF0AE11FC8DB63A61F920D3A6173ECA29 (void);
extern void MainThreadDispatcher__ctor_mEBFA67A72833DA1300F392753891547F3E88A370 (void);
extern void WeakRefDelegate_get_Id_mC80C25C7263D11D956707795DE7FCBB9FE12AE2A (void);
extern void WeakRefDelegate_get_Method_mD20F6F0A3A9ABC7C129273BD175293B2787C6BAB (void);
extern void WeakRefDelegate_set_Method_m3BEF2CC25657D75DE51E454E6E8170FD62433A3C (void);
extern void WeakRefDelegate_GetHashCode_m6B9F68B42C1C52B945080A4038F2F6449CE33406 (void);
extern void WeakRefDelegate__ctor_mC044BF8A97D502A98C860DF1EB6F4821DCF62BD2 (void);
extern void WeakRefDelegate_Invoke_m8639BD6F5037BAFC105C2B926ABA14B8515EDABF (void);
extern void WeakRefDelegate_Contains_mAF6C23CD55CA95B71C7F4A5A468F74A35B06CCAF (void);
extern void WeakRefDelegate_Dispose_m18D6968818A9045F8427CC12AFE656B9AE096702 (void);
extern void WeakRefDelegate_Equals_m1EF33FBA85CF9EFA57ECEC52F547D8BAEC012DE6 (void);
extern void WeakRefDelegate_CompareTo_m122C743BF80DD7A4A699ABA1E3AF298290105D5D (void);
extern void WeakRefDelegate_ToString_m3FD3CCD08E1D7C14852FBF0522032BA52F6DCCDA (void);
extern void WeakRefWrapper_get_Target_m80AA5DB4D63D8D45E5B16493750EDC4D59247AAC (void);
extern void WeakRefWrapper_get_IsAlive_m7078820E3043E1F1F3C71F3994FCD1B51A96EEFE (void);
extern void WeakRefWrapper__ctor_m07CBE71FD10FB907F0AA6DFD1E0B1B7A30A66DA0 (void);
extern void WeakRefWrapper_Dispose_m9EBEA71FFC12EDDCACDC627A4F739BE62875EEDA (void);
extern void WeakRefWrapper_Dispose_m33B84FDABA3FEBEE8E9AAAE022C6EC25BB642812 (void);
extern void EventSubscriber_get_IsAlive_m773291AEEA8542250FD19015242EAFDF49C45587 (void);
extern void EventSubscriber_get_PayloadType_m0D088D4AA4C71CE5022AD5FBF9C83BAB0E675116 (void);
extern void EventSubscriber_get_Id_m3A8FB0D934FB5CC320ED055B5C05C51F61609B31 (void);
extern void EventSubscriber_GetHashCode_m4B9ACA4208E818F2F56C3B10844F31C8B492E21B (void);
extern void EventSubscriber__ctor_m83C84B6970AB7DDD8C56647FA7810A924C6583A7 (void);
extern void EventSubscriber_Dispose_mBAEE5423CA83C7AF770F665156F0AE4C7FD740C7 (void);
extern void CollectionExtensions_IsNullOrEmpty_m11B519B87927C78B5BB97C31FA06438211606B1D (void);
static Il2CppMethodPointer s_methodPointers[74] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m49598D1157C88221D805991C5B1C2131062B9082,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mCE72C28E2AFCE548E9417AA4DF9CEFDCCE55F3CE,
	EventMessenger__cctor_m479B4B6FAFF426A2FDBC0D330B3E7091A035EC9D,
	NULL,
	NULL,
	NULL,
	NULL,
	EventMessenger_SubscribeInternal_m66AE194677AD9037B2A185E6CB274F270862BCD6,
	NULL,
	NULL,
	NULL,
	EventMessenger_Process_m8B913AF169B06C3F75E5C722312AD4F833BA6E6D,
	EventMessenger__ctor_m2BF7A27BEFA9581C61F4768B24933796B6307584,
	Test_TestMe_mD995CC0C57033FC3075B12130086315D94FE1766,
	Test_OnPayloadTested_m8C6EEA0E47A65EC38C021E906BEB118B2F4CB689,
	Test__ctor_mC66CA82F95D60E32161FA2967476D53AF181D3D2,
	TestPayload__ctor_m149BF715535F8F7FB793EA817D009CE028D99210,
	U3CU3Ec__cctor_mF91BDE84FC038860E7497BCF7F49C1F2099ACB16,
	U3CU3Ec__ctor_mEE5F1E7B7E2ACD87B86EBCD26D1C4557833A7960,
	U3CU3Ec_U3CTestMeU3Eb__1_0_m0F567980208F9381E1E73EE620EE6D677D4274E0,
	DispatcherTask_get_Action_m8145C36613C78DB816BF6E264FEC43416017CE73,
	DispatcherTask_set_Action_m42D6648F323316BD340516707FAB0C2708E5FCC1,
	DispatcherTask_get_Payload_mD4DD70DBBF0810A9673A1879A62B5B81D00711D4,
	DispatcherTask_set_Payload_m063DAABEBCEAC4BF36230211D72CA2BA2BE0D590,
	DispatcherTask__ctor_m05D7F84B09D3C857C0F551E03A8327EB73E534EB,
	DispatcherTask_Invoke_m6362C078ADE7D560F19E283D06517D52923528EE,
	DispatcherTask_Dispose_m6EE6BC65BDDADD0E56AB03BC4C153A26A1B8852F,
	NULL,
	NULL,
	MainThreadDispatcher_get_ThreadId_mA67B30638C97743CFA5754B134E5F137F0A0F869,
	MainThreadDispatcher_set_ThreadId_m1D2068F32CE8E4F4184C90E9F0D21F57D2160952,
	MainThreadDispatcher_get_TasksCount_mE93214289B760BC9D2337341F3F9EBB77C2B158A,
	MainThreadDispatcher_Awake_mCCE142E359F4C58B962559441C8EC5E9E7478452,
	MainThreadDispatcher_Dispatch_mE2D0FED86EFBD7240B08A99839266AD03F4E2996,
	MainThreadDispatcher_Update_m42FE01ADF0AE11FC8DB63A61F920D3A6173ECA29,
	MainThreadDispatcher__ctor_mEBFA67A72833DA1300F392753891547F3E88A370,
	WeakRefDelegate_get_Id_mC80C25C7263D11D956707795DE7FCBB9FE12AE2A,
	WeakRefDelegate_get_Method_mD20F6F0A3A9ABC7C129273BD175293B2787C6BAB,
	WeakRefDelegate_set_Method_m3BEF2CC25657D75DE51E454E6E8170FD62433A3C,
	WeakRefDelegate_GetHashCode_m6B9F68B42C1C52B945080A4038F2F6449CE33406,
	WeakRefDelegate__ctor_mC044BF8A97D502A98C860DF1EB6F4821DCF62BD2,
	WeakRefDelegate_Invoke_m8639BD6F5037BAFC105C2B926ABA14B8515EDABF,
	WeakRefDelegate_Contains_mAF6C23CD55CA95B71C7F4A5A468F74A35B06CCAF,
	WeakRefDelegate_Dispose_m18D6968818A9045F8427CC12AFE656B9AE096702,
	WeakRefDelegate_Equals_m1EF33FBA85CF9EFA57ECEC52F547D8BAEC012DE6,
	WeakRefDelegate_CompareTo_m122C743BF80DD7A4A699ABA1E3AF298290105D5D,
	WeakRefDelegate_ToString_m3FD3CCD08E1D7C14852FBF0522032BA52F6DCCDA,
	WeakRefWrapper_get_Target_m80AA5DB4D63D8D45E5B16493750EDC4D59247AAC,
	WeakRefWrapper_get_IsAlive_m7078820E3043E1F1F3C71F3994FCD1B51A96EEFE,
	WeakRefWrapper__ctor_m07CBE71FD10FB907F0AA6DFD1E0B1B7A30A66DA0,
	WeakRefWrapper_Dispose_m9EBEA71FFC12EDDCACDC627A4F739BE62875EEDA,
	WeakRefWrapper_Dispose_m33B84FDABA3FEBEE8E9AAAE022C6EC25BB642812,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	EventSubscriber_get_IsAlive_m773291AEEA8542250FD19015242EAFDF49C45587,
	EventSubscriber_get_PayloadType_m0D088D4AA4C71CE5022AD5FBF9C83BAB0E675116,
	EventSubscriber_get_Id_m3A8FB0D934FB5CC320ED055B5C05C51F61609B31,
	EventSubscriber_GetHashCode_m4B9ACA4208E818F2F56C3B10844F31C8B492E21B,
	EventSubscriber__ctor_m83C84B6970AB7DDD8C56647FA7810A924C6583A7,
	NULL,
	EventSubscriber_Dispose_mBAEE5423CA83C7AF770F665156F0AE4C7FD740C7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	CollectionExtensions_IsNullOrEmpty_m11B519B87927C78B5BB97C31FA06438211606B1D,
};
static const int32_t s_InvokerIndices[74] = 
{
	34285,
	21016,
	34252,
	-1,
	-1,
	-1,
	-1,
	15968,
	-1,
	-1,
	-1,
	21016,
	21016,
	21016,
	15968,
	21016,
	21016,
	34252,
	21016,
	15968,
	20761,
	15968,
	20761,
	15968,
	7995,
	21016,
	21016,
	-1,
	-1,
	20694,
	15903,
	20694,
	21016,
	7995,
	21016,
	21016,
	20694,
	20761,
	15968,
	20694,
	15968,
	13820,
	11681,
	15757,
	11681,
	13212,
	20761,
	20761,
	20550,
	15968,
	15757,
	21016,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	20550,
	20761,
	20694,
	20694,
	3763,
	-1,
	21016,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	31561,
};
static const Il2CppTokenRangePair s_rgctxIndices[13] = 
{
	{ 0x0200000E, { 23, 12 } },
	{ 0x0200000F, { 35, 5 } },
	{ 0x06000004, { 0, 4 } },
	{ 0x06000005, { 4, 3 } },
	{ 0x06000006, { 7, 5 } },
	{ 0x06000007, { 12, 3 } },
	{ 0x06000009, { 15, 4 } },
	{ 0x0600000A, { 19, 2 } },
	{ 0x0600000B, { 21, 2 } },
	{ 0x06000041, { 40, 1 } },
	{ 0x06000047, { 41, 3 } },
	{ 0x06000048, { 44, 2 } },
	{ 0x06000049, { 46, 7 } },
};
extern const uint32_t g_rgctx_T_t1A35C3632DFD2E57A912A28CC1D15455F17E235C;
extern const uint32_t g_rgctx_EventMessenger_PublishInternal_TisT_t1A35C3632DFD2E57A912A28CC1D15455F17E235C_m759B3B35CFDC18383AE4C276D2F5C027DA626B60;
extern const uint32_t g_rgctx_Action_1_t0A1CA28BB824C2B50A5367A93EC90607F538AB72;
extern const uint32_t g_rgctx_Action_1__ctor_m8EF6766A7645AE5613F53FAE517FBF6259F9FAA5;
extern const uint32_t g_rgctx_T_t3B1CFA0CF4AF020080AB1447AE6C62BB8D609362;
extern const uint32_t g_rgctx_T_t3B1CFA0CF4AF020080AB1447AE6C62BB8D609362;
extern const uint32_t g_rgctx_EventSubscriber_Invoke_TisT_t3B1CFA0CF4AF020080AB1447AE6C62BB8D609362_m40D7F2557853CABB0D3E8E58B5DD68CE899977F7;
extern const uint32_t g_rgctx_Action_1_t1F26001D962574A161BBC30AF3A9DD728F4B57DB;
extern const uint32_t g_rgctx_Predicate_1_t536D80D800451C8550D26E9C1981824B6C54D090;
extern const uint32_t g_rgctx_EventMessenger_SubscribeInternal_TisT_tA5A9E39379594A66B089C48F280433B998DA74F1_mE87F5C0F240B064C0A83868E15E7ABD8E395263F;
extern const uint32_t g_rgctx_Action_2_tF4ADE7264F9DC9F7658AF69014AFE6F86D517B34;
extern const uint32_t g_rgctx_Action_2__ctor_m655B818BD3F893FA893117E788FB0A0EE8099E35;
extern const uint32_t g_rgctx_T_t8402128F9729DE271D2E21587F7F00979385A61B;
extern const uint32_t g_rgctx_Action_1_tAD4D69B7CAB85E5A284DFD104504FB0B77CF8378;
extern const uint32_t g_rgctx_Predicate_1_tE9EEFDF339797A7474E97216F4025092D07EA9D8;
extern const uint32_t g_rgctx_Action_1_tF3F1FD5F66029D177073293E365A687DFD877D17;
extern const uint32_t g_rgctx_EventMessenger_UnsubscribeInternal_TisT_tA40CDE569CE540C10C49E69E0BE7870C5856A411_m93DF49628EA7E8ED1B0EB3824B116596E929358F;
extern const uint32_t g_rgctx_Action_1_tE322496DC4FFAADDBE90F43B506F3F4DA5D46F0A;
extern const uint32_t g_rgctx_Action_1__ctor_m82F81F60D6AF0BBA90CB7F18307252656B959460;
extern const uint32_t g_rgctx_T_tCE779E364028999B4E5C7AF461D2BA9B6168CBDF;
extern const uint32_t g_rgctx_Action_1_t80D0A8F0687AB4FC6C4519DCD33DE04ABB3059AD;
extern const uint32_t g_rgctx_T_tB8A3133441B868E7416E649781D431CDC5872007;
extern const uint32_t g_rgctx_T_tB8A3133441B868E7416E649781D431CDC5872007;
extern const uint32_t g_rgctx_MonoSingleton_2_InvalidateInstance_mC81CEA75EEE0FA19DF2F4F62AAA7ED188185AF83;
extern const uint32_t g_rgctx_MonoSingleton_2_t619A08120542C9AA91F5B32D849EE3FBCC6898FA;
extern const uint32_t g_rgctx_TInterface_t84464A5162E1F04150A610BC336A99DB3587126D;
extern const uint32_t g_rgctx_MonoSingleton_2_t619A08120542C9AA91F5B32D849EE3FBCC6898FA;
extern const uint32_t g_rgctx_TInterface_t84464A5162E1F04150A610BC336A99DB3587126D;
extern const uint32_t g_rgctx_Object_FindObjectsOfType_TisTImplementation_tFC2176EF343A6F967B92C9797C14636C66B63272_mE4EC0CBBBC35A6AE7A63E16F631E4ABA9CA87C31;
extern const uint32_t g_rgctx_TImplementationU5BU5D_tA851BEA0F45643C9A9AAA4B220F5CFA99E851AD5;
extern const uint32_t g_rgctx_CollectionExtensions_IsNullOrEmpty_TisTImplementation_tFC2176EF343A6F967B92C9797C14636C66B63272_mA9340792C57B4D52287C86A915490F792599C512;
extern const uint32_t g_rgctx_ICollection_1_t0E4E6CD7528588AD57C7C9813BF9DF65804BE0BA;
extern const uint32_t g_rgctx_TImplementation_tFC2176EF343A6F967B92C9797C14636C66B63272;
extern const uint32_t g_rgctx_TImplementation_tFC2176EF343A6F967B92C9797C14636C66B63272;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisTImplementation_tFC2176EF343A6F967B92C9797C14636C66B63272_m68B412FA036E2EBD551C69A5D4457DC727F1619D;
extern const uint32_t g_rgctx_Singleton_2_tAB3C52FCC48F5E1E4441B9C36203167F4C7D957E;
extern const uint32_t g_rgctx_TInterface_t1881118104C30E5F087002CE22222394F2D1460A;
extern const uint32_t g_rgctx_Singleton_2_tAB3C52FCC48F5E1E4441B9C36203167F4C7D957E;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisTImplementation_t17A64C4102D5B8583E7E268E2BB08A1D6A18E6A7_m625390DEFC987328C9B7889029C7D428D787F5AA;
extern const uint32_t g_rgctx_TImplementation_t17A64C4102D5B8583E7E268E2BB08A1D6A18E6A7;
extern const uint32_t g_rgctx_T_t08AB3EDE3B992CC0969AA0C77A77E49210DCCAF5;
extern const uint32_t g_rgctx_TU5BU5D_tCA0376372354A4F5D5AB4058BC90FE7A55B5748C;
extern const uint32_t g_rgctx_CollectionExtensions_IsNullOrEmpty_TisT_tD42862D5ADD0A89EAFBCE4898D4270117B811525_m4B921BE9C0D2549DA2B4C1745C6D975469241CAC;
extern const uint32_t g_rgctx_ICollection_1_t316012C6D1AFB3CF75023863CE01CACE71AA21B5;
extern const uint32_t g_rgctx_ICollection_1_t48414D80B5AE4C91CE6000534ACAF7855CBBE31E;
extern const uint32_t g_rgctx_ICollection_1_get_Count_mCCB40F36C1D43E22E36640E9CE394A2E2B7E6EBC;
extern const uint32_t g_rgctx_IEnumerable_1_tF3CD6527BDA0DD7EAED4AB89A8C0F9D2B8F146D9;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m4F706E6999BAED4229165457F7AFC4CF5BE7F855;
extern const uint32_t g_rgctx_IEnumerator_1_tB461094CCA6BB4C502DDAF48AE712F360124B737;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_m5F854085A642FFD83D00F2F1EB83EA127EC86ACC;
extern const uint32_t g_rgctx_T_t7685D3C03FDE967B8FD36911A011162EBC14658A;
extern const uint32_t g_rgctx_Action_1_tFF5978EF2FD903EEAAB8EE8715F895A398A89C7C;
extern const uint32_t g_rgctx_Action_1_Invoke_m1AAE71BC50D36EC6275CC35BFFE85391B59C88ED;
static const Il2CppRGCTXDefinition s_rgctxValues[53] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1A35C3632DFD2E57A912A28CC1D15455F17E235C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EventMessenger_PublishInternal_TisT_t1A35C3632DFD2E57A912A28CC1D15455F17E235C_m759B3B35CFDC18383AE4C276D2F5C027DA626B60 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t0A1CA28BB824C2B50A5367A93EC90607F538AB72 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_m8EF6766A7645AE5613F53FAE517FBF6259F9FAA5 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t3B1CFA0CF4AF020080AB1447AE6C62BB8D609362 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3B1CFA0CF4AF020080AB1447AE6C62BB8D609362 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EventSubscriber_Invoke_TisT_t3B1CFA0CF4AF020080AB1447AE6C62BB8D609362_m40D7F2557853CABB0D3E8E58B5DD68CE899977F7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t1F26001D962574A161BBC30AF3A9DD728F4B57DB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Predicate_1_t536D80D800451C8550D26E9C1981824B6C54D090 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EventMessenger_SubscribeInternal_TisT_tA5A9E39379594A66B089C48F280433B998DA74F1_mE87F5C0F240B064C0A83868E15E7ABD8E395263F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_tF4ADE7264F9DC9F7658AF69014AFE6F86D517B34 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2__ctor_m655B818BD3F893FA893117E788FB0A0EE8099E35 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t8402128F9729DE271D2E21587F7F00979385A61B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tAD4D69B7CAB85E5A284DFD104504FB0B77CF8378 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Predicate_1_tE9EEFDF339797A7474E97216F4025092D07EA9D8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tF3F1FD5F66029D177073293E365A687DFD877D17 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EventMessenger_UnsubscribeInternal_TisT_tA40CDE569CE540C10C49E69E0BE7870C5856A411_m93DF49628EA7E8ED1B0EB3824B116596E929358F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tE322496DC4FFAADDBE90F43B506F3F4DA5D46F0A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_m82F81F60D6AF0BBA90CB7F18307252656B959460 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tCE779E364028999B4E5C7AF461D2BA9B6168CBDF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t80D0A8F0687AB4FC6C4519DCD33DE04ABB3059AD },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tB8A3133441B868E7416E649781D431CDC5872007 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB8A3133441B868E7416E649781D431CDC5872007 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MonoSingleton_2_InvalidateInstance_mC81CEA75EEE0FA19DF2F4F62AAA7ED188185AF83 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MonoSingleton_2_t619A08120542C9AA91F5B32D849EE3FBCC6898FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TInterface_t84464A5162E1F04150A610BC336A99DB3587126D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MonoSingleton_2_t619A08120542C9AA91F5B32D849EE3FBCC6898FA },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TInterface_t84464A5162E1F04150A610BC336A99DB3587126D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_FindObjectsOfType_TisTImplementation_tFC2176EF343A6F967B92C9797C14636C66B63272_mE4EC0CBBBC35A6AE7A63E16F631E4ABA9CA87C31 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TImplementationU5BU5D_tA851BEA0F45643C9A9AAA4B220F5CFA99E851AD5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionExtensions_IsNullOrEmpty_TisTImplementation_tFC2176EF343A6F967B92C9797C14636C66B63272_mA9340792C57B4D52287C86A915490F792599C512 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_t0E4E6CD7528588AD57C7C9813BF9DF65804BE0BA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TImplementation_tFC2176EF343A6F967B92C9797C14636C66B63272 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TImplementation_tFC2176EF343A6F967B92C9797C14636C66B63272 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisTImplementation_tFC2176EF343A6F967B92C9797C14636C66B63272_m68B412FA036E2EBD551C69A5D4457DC727F1619D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Singleton_2_tAB3C52FCC48F5E1E4441B9C36203167F4C7D957E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TInterface_t1881118104C30E5F087002CE22222394F2D1460A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Singleton_2_tAB3C52FCC48F5E1E4441B9C36203167F4C7D957E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisTImplementation_t17A64C4102D5B8583E7E268E2BB08A1D6A18E6A7_m625390DEFC987328C9B7889029C7D428D787F5AA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TImplementation_t17A64C4102D5B8583E7E268E2BB08A1D6A18E6A7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t08AB3EDE3B992CC0969AA0C77A77E49210DCCAF5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tCA0376372354A4F5D5AB4058BC90FE7A55B5748C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionExtensions_IsNullOrEmpty_TisT_tD42862D5ADD0A89EAFBCE4898D4270117B811525_m4B921BE9C0D2549DA2B4C1745C6D975469241CAC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_t316012C6D1AFB3CF75023863CE01CACE71AA21B5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_t48414D80B5AE4C91CE6000534ACAF7855CBBE31E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_get_Count_mCCB40F36C1D43E22E36640E9CE394A2E2B7E6EBC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_tF3CD6527BDA0DD7EAED4AB89A8C0F9D2B8F146D9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m4F706E6999BAED4229165457F7AFC4CF5BE7F855 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tB461094CCA6BB4C502DDAF48AE712F360124B737 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_m5F854085A642FFD83D00F2F1EB83EA127EC86ACC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7685D3C03FDE967B8FD36911A011162EBC14658A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tFF5978EF2FD903EEAAB8EE8715F895A398A89C7C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_m1AAE71BC50D36EC6275CC35BFFE85391B59C88ED },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_EventFramework_CodeGenModule;
const Il2CppCodeGenModule g_EventFramework_CodeGenModule = 
{
	"EventFramework.dll",
	74,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	13,
	s_rgctxIndices,
	53,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
