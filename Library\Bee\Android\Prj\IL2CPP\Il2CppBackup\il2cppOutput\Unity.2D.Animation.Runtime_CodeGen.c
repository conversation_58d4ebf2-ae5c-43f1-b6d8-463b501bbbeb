﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* BurstedSpriteSkinUtilities_SetVertexPositionFromByteBuffer_m6D9016B821006BB68DA3C0FEDAD7238BB18A287C_RuntimeMethod_var;
extern const RuntimeMethod* BurstedSpriteSkinUtilities_ValidateBoneWeights_m5E7F9120E9D22F0A097DD5B296BE3E9336D45017_RuntimeMethod_var;
extern const RuntimeMethod* MeshUtilities_AddToEdgeMap_m420B959ECD9102D5B1ED0E93A9765C7ED6373E30_RuntimeMethod_var;
extern const RuntimeMethod* MeshUtilities_GetFirstUnusedIndex_m77BD52B67B1266BD78E777DB9128850F404627A3_RuntimeMethod_var;
extern const RuntimeMethod* MeshUtilities_SortEdges_mE3EE6CD3012C39542E837B24CCC493C91737D5D7_RuntimeMethod_var;



extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m686004A7C435C4DC5CA666EE2C0798BDBE530DAD (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m9610F9F68970CA8ECB075717298D140581282036 (void);
extern void BaseDeformationSystem_RemoveBoneTransforms_m371364D40EB7743ABCB1366D347540A84FED5C12 (void);
extern void BaseDeformationSystem_AddBoneTransforms_m5E4B0179466AEACCEAD741F634597EAAD1D02E1B (void);
extern void BaseDeformationSystem_UpdateMaterial_m47C13EE86303857DCEF7E5E55711EFCC349B5244 (void);
extern void BaseDeformationSystem_AddSpriteSkin_m80C519EDAF5C85762CDD911DBB8404B1ED442F12 (void);
extern void BaseDeformationSystem_CopyToSpriteSkinData_m918A28A06AA7A00153B0E1FF467A9BBAAC22638A (void);
extern void BaseDeformationSystem_RemoveSpriteSkin_mED1EBF1A9F94F366074589BBD036FE36420B76F1 (void);
extern void BaseDeformationSystem_GetSpriteSkins_m1BD5EED916D01456F0305AB57A0E7CCC8213DB63 (void);
extern void BaseDeformationSystem_Initialize_m32FD32396C789B3D3CBC45743347D5B94BCB57CE (void);
extern void BaseDeformationSystem_InitializeArrays_m2D68A1B4666CA752CEC87B302E8946F2CAB95F73 (void);
extern void BaseDeformationSystem_BatchRemoveSpriteSkins_m899787AF35E56DCEC142937A71FCE380B0855451 (void);
extern void BaseDeformationSystem_BatchAddSpriteSkins_m30035AD40D349EC41D0B20D2E81A6692B1744C0C (void);
extern void BaseDeformationSystem_ResizeAndCopyArrays_m606F57AEF78389752872999486558BE1D50C76B9 (void);
extern void BaseDeformationSystem_Cleanup_m394845EEB446F15F8840609BCF68A4B31159FC33 (void);
extern void BaseDeformationSystem_PrepareDataForDeformation_mCBE6ED3286A4CE1A2C2AA56BF2FAE0745C3377B6 (void);
extern void BaseDeformationSystem_ValidateSpriteSkinData_mBA49FFFE7B81E007BF8E9A7D64B89B2E69A0B133 (void);
extern void BaseDeformationSystem_GotVerticesToDeform_mDC30D0A1497201E113B4A33972DA3DB8C900996D (void);
extern void BaseDeformationSystem_SchedulePrepareJob_mB17A0F938A499DD987958D57F473A766A92711E5 (void);
extern void BaseDeformationSystem_ScheduleBoneJobBatched_mC016DF6E43E02CABBC41D909AB9D84BB6DAC2252 (void);
extern void BaseDeformationSystem_ScheduleSkinDeformBatchedJob_m8AC1EB9D746184E455577AFAA403355A11BE3B7E (void);
extern void BaseDeformationSystem_ScheduleCopySpriteRendererBuffersJob_m5CFBB8804F174AF3B52FE5C7C13626EEB5366B3C (void);
extern void BaseDeformationSystem_ScheduleCalculateSpriteSkinAABBJob_mF0B152594CD68ABD0C3A3B736648A99A1AA15D11 (void);
extern void BaseDeformationSystem_DeactivateDeformableBuffers_m89AD27CF2605D033531659C4AE7FE1AFB034348E (void);
extern void BaseDeformationSystem_IsSpriteSkinActiveForDeformation_m9564CAFB8950CCE0C8C4445093956FD1A1AE208C (void);
extern void BaseDeformationSystem_GetDeformableBufferForSpriteSkin_m3109CF386076558B3DF96E6E04B3B3125F95E966 (void);
extern void BaseDeformationSystem__ctor_m6F994399F86236217B3440CDAA0F79FB1A459014 (void);
extern void Profiling__cctor_m75E0D9B5602D565D82F39C43A84C4A5EEFA212B7 (void);
extern void CpuDeformationSystem_get_deformationMethod_m3118E9B6943BE15678C67BE05D0C02CA1BB402B8 (void);
extern void CpuDeformationSystem_Cleanup_m2523D77E1744B660D80500A5AAED074FA65B3DD2 (void);
extern void CpuDeformationSystem_UpdateMaterial_m9D91BE9513E0DE267F70E16DD650391D822A55C2 (void);
extern void CpuDeformationSystem_Update_m8D763177FDD1ED3BDDDD10991AF249FC14D7E9CF (void);
extern void CpuDeformationSystem_ResizeBuffers_m26665DD8FD0A8C45C53AEA7AD5FCEEEC3432AB2E (void);
extern void CpuDeformationSystem__ctor_mB37A83FD9FF24998C06871CE978E9188F7A053A4 (void);
extern void PrepareDeformJob_Execute_m371FE77DC77A71B801C2ED2F08BD76ECAD5D4333 (void);
extern void BoneDeformBatchedJob_Execute_mD24F84E1C367AC5B671D77CD67E961D395C28C61 (void);
extern void SkinDeformBatchedJob_Execute_m105DF36043A75DCAC5E0111DE74EE0087D64E136 (void);
extern void CalculateSpriteSkinAABBJob_Execute_mD93917686FF5B004B49DC055B42C9F4DB42EF756 (void);
extern void FillPerSkinJobSingleThread_Execute_mC5F785E6FA0AC412C792AF1FE03A8D8B08EA2C84 (void);
extern void CopySpriteRendererBuffersJob_Execute_m4EFBA68E2C6B03752414B97C768E2B80F688F643 (void);
extern void CopySpriteRendererBoneTransformBuffersJob_Execute_m386F8389F4159350BC846A57D1A88C7210B605D1 (void);
extern void DeformationManager_get_instance_m15E7B260A44705AB2DF91C598D76007CF44D8FF6 (void);
extern void DeformationManager_get_helperGameObject_m640617C1EA2DEE8FC6F9E868691C389D50ECEC01 (void);
extern void DeformationManager_get_canUseGpuDeformation_m241EDCA1E704333645C3AD38F60076E7B961E802 (void);
extern void DeformationManager_set_canUseGpuDeformation_m14226B7E606903D7686D4FEB7CF255C2FFF8DF33 (void);
extern void DeformationManager_OnEnable_m312B7477973F409BCA06ADFD6986B7ED9FC74621 (void);
extern void DeformationManager_Init_m1E8DA325F03E91627E0FB22D25BAB8458F58E86E (void);
extern void DeformationManager_CreateBatchSystems_m33936D5A024BA3AA0C6D508A5FA11C4100AC2B9D (void);
extern void DeformationManager_CreateHelper_m028FE0DFFFCAED718FB94DD23DA9DF967758F635 (void);
extern void DeformationManager_OnHelperDestroyed_m2ADE4A2AA0EA78E15D65FEC11453D208DEC18955 (void);
extern void DeformationManager_OnDisable_m75606324949589DD8D1AEA2627CBF12B7FCEC35E (void);
extern void DeformationManager_Update_m785799D006868C8BCC62D2BB1D3D3A731CCF41E1 (void);
extern void DeformationManager_HasToggledGpuDeformation_m63526D3C497B7B7228BC0D942DC39830C472A397 (void);
extern void DeformationManager_MoveSpriteSkinsToActiveSystem_m85C5873BEAD1B499F4DDD8FB917DDF02888E5F18 (void);
extern void DeformationManager_AddSpriteSkin_m6556A3BCC0C80EE424D6F15239CB5AB096A7D52D (void);
extern void DeformationManager_RemoveBoneTransforms_m3DE91A617529DA79E2C3BA30C87FD258BFDADF44 (void);
extern void DeformationManager_AddSpriteSkinBoneTransform_mAE13ED4585F6B39F58E80F52A88795E9F201F2D2 (void);
extern void DeformationManager__ctor_mF8514EA8C32FC7A4FE09B7EE492C74A4B9F093D9 (void);
extern void DeformationManagerUpdater_get_onDestroyingComponent_m01B17114C587D0B57AD883CEE80776B0BA9537C2 (void);
extern void DeformationManagerUpdater_set_onDestroyingComponent_m84C335D0C2FA34C34475A425C781572A1611C5B7 (void);
extern void DeformationManagerUpdater_OnDestroy_m4D13515BA8E4B1D0AC1028A67DE19586EBEE1AD6 (void);
extern void DeformationManagerUpdater_LateUpdate_mFE7EF341ABFAE6D65BAA16E937B5D15A88D70781 (void);
extern void DeformationManagerUpdater__ctor_m8DD130BC84F485947A5ED3ADAF8B7D5754819D5E (void);
extern void GpuDeformationSystem_CreateFallbackBuffer_mB1FA6A8C3ABD31BEFC8E33ED39D3811D06018869 (void);
extern void GpuDeformationSystem_ClearFallbackBuffer_mE9853A09098301B82FE449650132AD5AF3DB4B4E (void);
extern void GpuDeformationSystem_get_deformationMethod_m62CA51E492E86A4EC3FDBFA357A8592369292548 (void);
extern void GpuDeformationSystem_DoesShaderSupportGpuDeformation_m9F9DE53A097A27F65C6634F9D45A0F4CB92BB754 (void);
extern void GpuDeformationSystem_IsComputeBufferValid_m58B66E25F26484A573F96F1027E48B19CDABDAA4 (void);
extern void GpuDeformationSystem_InitializeArrays_m11D24E9312112FFF8865054B240CB841B692CFEC (void);
extern void GpuDeformationSystem_Cleanup_m4B435152154381ADF0C4996F17C1E0E29A2C6464 (void);
extern void GpuDeformationSystem_ResizeAndCopyArrays_mA2B9722DD234C19681F9EC2AE79BE69A0C61CFF5 (void);
extern void GpuDeformationSystem_CleanupComputeResources_mC9201DFFB7DEFD7F2E1C8F13EF19EA50953E7937 (void);
extern void GpuDeformationSystem_UpdateMaterial_mF2EA570443FFF632B62EF176D58559EF11258007 (void);
extern void GpuDeformationSystem_AddSpriteSkin_m6D457B5688B6403FCA98783308C0F1D6E600201A (void);
extern void GpuDeformationSystem_Update_m2130F2105F1DEB71DACB19377CA661990D094CE7 (void);
extern void GpuDeformationSystem_ResizeBuffers_m3D78DEE29B4777803F13BE127E361E59A17FD0D0 (void);
extern void GpuDeformationSystem_CreateComputeBuffer_mB904E0EC55C0A56DDE940B0C7D62FD9508D993F1 (void);
extern void GpuDeformationSystem_SetComputeBuffer_m17619798E1CCA81052BEE4BE870187886450C4ED (void);
extern void GpuDeformationSystem_ScheduleCopySpriteRendererBoneTransformBuffersJob_m71E403C330931D7EA163CAA47CD4B80DEE124A7A (void);
extern void GpuDeformationSystem__ctor_m95F25C60430CEC53B884C006BE4E5EA6F34D6497 (void);
extern void TransformAccessJob__ctor_mD6EAD8E55DC39C30642951F0247FFA88B681583B (void);
extern void TransformAccessJob_Destroy_m7E55E78C9A1623D95B3FB7213459F570DC410779 (void);
extern void TransformAccessJob_InitializeDataStructures_m020A60543A286A9EC487E6DC777FC034734F5465 (void);
extern void TransformAccessJob_ClearDataStructures_m4AFA32B0BBA00ACAEC589AFEFC7CC6862CE88DBB (void);
extern void TransformAccessJob_ResetCache_m9DD6FA7692B7DD03131D2E9A9E6EF82F3C0FB065 (void);
extern void TransformAccessJob_get_transformData_mA6B68A115FF7CED15402C2F21E9F69BDF8E19A3B (void);
extern void TransformAccessJob_get_transformMatrix_m0CC3B74850876DE7614AECF25BC8066778B1D00E (void);
extern void TransformAccessJob_AddTransform_mBDE033F954908A88B2EBCE1B36AA8B143C86C92F (void);
extern void TransformAccessJob_UpdateTransformIndex_m48C846177A7D51F436FC360CE75C3FAC3513D2BC (void);
extern void TransformAccessJob_StartLocalToWorldJob_m4D840C72D63F3FBC7C54D7E5B771B05AFB71D386 (void);
extern void TransformAccessJob_StartWorldToLocalJob_m480EA003C27B312D9859A5FB4EB367D1666FA81C (void);
extern void TransformAccessJob_GetDebugLog_mB459C748C56775F1D4333B60892921C46EAE5F38 (void);
extern void TransformAccessJob_RemoveTransformsIfNull_mACB02AB72B9DBFF24DD5CA93D63995FD48BDA0D5 (void);
extern void TransformAccessJob_RemoveTransformsByIds_m551E8030E2E18DDAD63024323AB7F39C578AB5E3 (void);
extern void TransformAccessJob_RemoveTransformById_m7E5E23AD603EEDAC55F4E44041BD91F25D892AEF (void);
extern void TransformData__ctor_mED230D1775060C4D1C0F02F7D757436387F20BD2 (void);
extern void U3CU3Ec__cctor_m263CA5A2082ADE88BA99E13629D6F15368E5EDBD (void);
extern void U3CU3Ec__ctor_mAFCC3558764B6784F480CD970D3694414BAF495E (void);
extern void U3CU3Ec_U3CRemoveTransformsIfNullU3Eb__23_0_m27BAF4D3A88E142E4676BB5B5C71D256994251E4 (void);
extern void U3CU3Ec_U3CRemoveTransformsIfNullU3Eb__23_1_mA852FF30AA5A85B02AF3EEB4A8864FE5261AB7D0 (void);
extern void U3CU3Ec__DisplayClass24_0__ctor_m3E27CACAC8BAAD1F7AD9728819E22297D462F347 (void);
extern void U3CU3Ec__DisplayClass24_0_U3CRemoveTransformsByIdsU3Eb__0_m4D0B9B8B51621FE0B666D7BA0CC2682BEC0F162F (void);
extern void U3CU3Ec__DisplayClass25_0__ctor_m24A1C6E3C75BCB024A71C6B21A7CFDD5DE8EBF77 (void);
extern void U3CU3Ec__DisplayClass25_0_U3CRemoveTransformByIdU3Eb__0_mB9C07B9E9B096B185B12C59D074BB4C7A58A3F5F (void);
extern void LocalToWorldTransformAccessJob_Execute_mA0FF58AB3050E308CF060A21E25F5448F2C987FE (void);
extern void WorldToLocalTransformAccessJob_Execute_m35BD603238353EFDA8B1D3249BFB2B662F7FCED5 (void);
extern void UpdateBoundJob_Execute_m21EBA97DE9CDABC2874AD8C34519A3B4CC83BD62 (void);
extern void Bone_get_guid_m818AD823C67F82F6B1D2E23739495D3BA3B74ED7 (void);
extern void Bone_set_guid_mECC3F2B1AFBFB75D5DA16EFC85884C674A46CC58 (void);
extern void Bone__ctor_mDDEE3E72C5832F240207DE1839E77563E90EE846 (void);
extern void VertexBuffer_get_bufferCount_mA9148EA31A3CE24C6FFF12ED50D845524865AC0F (void);
extern void VertexBuffer__ctor_mEA21AD068678A9D0801D30D129C9797EB9399B68 (void);
extern void VertexBuffer_GetHashCode_m9C16F129B253B8703D3DEBE488837B7AA1C04E02 (void);
extern void VertexBuffer_GetCurrentFrame_m4F0295E6AA5510CE7BFB93183ECEBC34BBCFD8D7 (void);
extern void VertexBuffer_GetBuffer_m5C37AEDF559BFDBCA7688AA13E21C7B612A918DD (void);
extern void VertexBuffer_ResizeBuffer_m132FAA5F34E224593C1B388B2FE544A1C42BABF5 (void);
extern void VertexBuffer_Deactivate_mAF5B69F208E2FF0B0B0AF555C8B9190CB2846230 (void);
extern void VertexBuffer_Dispose_m85708897CAE4C5391F1E1AF1E61163E1E7A23FA1 (void);
extern void VertexBuffer_IsSafeToDispose_m738EAB0318ECA75C4DE88C867DF23A47BD4C2A48 (void);
extern void BufferManager_get_bufferCount_mF1802C500FD0AE5154977E98505E2594DF547CBA (void);
extern void BufferManager_get_needDoubleBuffering_m847E54EDC082F5C37FDDC5A222AFFEA08DFE8628 (void);
extern void BufferManager_set_needDoubleBuffering_mD2D35186E0906DCF7481D12704E06E09666175D7 (void);
extern void BufferManager_get_instance_m95AECA3426618550F12400165FF6347AA18B65D1 (void);
extern void BufferManager_OnEnable_mD97F2D77ED2E9ACB415B40F5319B4FAE18573C88 (void);
extern void BufferManager_OnDisable_mE5FB4A61B68A226026543BF094A0F0F151A4F41E (void);
extern void BufferManager_ForceClearBuffers_mA9D740563C66CB4D73FE063E52A4E9518CBB03C5 (void);
extern void BufferManager_GetBuffer_mF012693FDFC7E1CC5A28B33AB18CEF630F43AAEC (void);
extern void BufferManager_CreateBuffer_mBBB61965DFCF31AF0EE60DA8BA6F4302C8140105 (void);
extern void BufferManager_ReturnBuffer_m736DE3F45BEF786F616874A7C595E2016E394974 (void);
extern void BufferManager_Update_mBDECC9C5FD62F9CE6C1D8C1D5E369819B171466D (void);
extern void BufferManager__ctor_m18C27C56D0CDB3F2B498D19F66562E887F4B3FDF (void);
extern void MeshUtilities_GetOutlineEdges_m06213B798862A3E5EE8AD144AAD6615A594C3D16 (void);
extern void MeshUtilities_AddToEdgeMap_m420B959ECD9102D5B1ED0E93A9765C7ED6373E30 (void);
extern void MeshUtilities_SortEdges_mE3EE6CD3012C39542E837B24CCC493C91737D5D7 (void);
extern void MeshUtilities_GetFirstUnusedIndex_m77BD52B67B1266BD78E777DB9128850F404627A3 (void);
extern void MeshUtilities_AddToEdgeMapU24BurstManaged_mDD61A7116DD6F8A96340F9A96C599D6D9866C482 (void);
extern void MeshUtilities_SortEdgesU24BurstManaged_mFE2E298908DFC1D1F598D2A4170E1108CF8C77F4 (void);
extern void MeshUtilities_GetFirstUnusedIndexU24BurstManaged_m09AED0689FF0E09501BE339074F341460C5D544B (void);
extern void AddToEdgeMap_00000089U24PostfixBurstDelegate__ctor_mEAE524D40CAB3E6847A988115374972F703698C2 (void);
extern void AddToEdgeMap_00000089U24PostfixBurstDelegate_Invoke_m41BEE6E86843AEC29ADDF4DABA5A55ACAD5B63D5 (void);
extern void AddToEdgeMap_00000089U24PostfixBurstDelegate_BeginInvoke_mEFAAB330E460B2EE3B0C550957BB9E60BF7572FD (void);
extern void AddToEdgeMap_00000089U24PostfixBurstDelegate_EndInvoke_m17EF1D6162F212FDD11CFF6EC3134D297BBC6E88 (void);
extern void AddToEdgeMap_00000089U24BurstDirectCall_GetFunctionPointerDiscard_m521C50447DCA7A1131713E0843BFCE0968E2A649 (void);
extern void AddToEdgeMap_00000089U24BurstDirectCall_GetFunctionPointer_m48E2DACB7D93AAB1AA5EAAE230BF0FF9EC97AD09 (void);
extern void AddToEdgeMap_00000089U24BurstDirectCall_Invoke_m79F68298347573C323D27AF7CCDCA5858EBEFCB7 (void);
extern void SortEdges_0000008AU24PostfixBurstDelegate__ctor_m5A1D647E1F5D38541B855E6D6BB33BBA63B3FACB (void);
extern void SortEdges_0000008AU24PostfixBurstDelegate_Invoke_m857A4F5C17C6A56A1B035ACD2A2DC4D3051CB1E2 (void);
extern void SortEdges_0000008AU24PostfixBurstDelegate_BeginInvoke_m8D6F3A090C738D7E2646669C7C22BA7ADFE6D47F (void);
extern void SortEdges_0000008AU24PostfixBurstDelegate_EndInvoke_m78A9FA3F3B6718EB21CB65B63BD1DA985EF30AFE (void);
extern void SortEdges_0000008AU24BurstDirectCall_GetFunctionPointerDiscard_m5905FD953645041195AF23B2090B098967A05B1C (void);
extern void SortEdges_0000008AU24BurstDirectCall_GetFunctionPointer_m4FAA4CEABF8E902E4B2CA76B659855CA65C9A5EB (void);
extern void SortEdges_0000008AU24BurstDirectCall_Invoke_mE123FAFD29C75A735AFB20A6CBEAC348BA1D58B6 (void);
extern void GetFirstUnusedIndex_0000008BU24PostfixBurstDelegate__ctor_mBF1794C82652699BE075E69D18F4AE5073292E49 (void);
extern void GetFirstUnusedIndex_0000008BU24PostfixBurstDelegate_Invoke_m39656317B17BAC5C0111965DEDB72B5CEAC1A5D8 (void);
extern void GetFirstUnusedIndex_0000008BU24PostfixBurstDelegate_BeginInvoke_m30DD0A8E357AE0D705FF12AB225DE2886B445106 (void);
extern void GetFirstUnusedIndex_0000008BU24PostfixBurstDelegate_EndInvoke_mCFFB394AD9BF9C0E1046F7419EA903CE3F57DCA9 (void);
extern void GetFirstUnusedIndex_0000008BU24BurstDirectCall_GetFunctionPointerDiscard_m51FC9528D91BA023256307E857597494CEFACB02 (void);
extern void GetFirstUnusedIndex_0000008BU24BurstDirectCall_GetFunctionPointer_m6C0988D99DA70B49F8EC8D2BBBAC1861CD95DC30 (void);
extern void GetFirstUnusedIndex_0000008BU24BurstDirectCall_Invoke_m8191DD31B5E2DB7771369DD143A7C4D119453F50 (void);
extern void SkeletonAsset_GetSpriteBones_mB5BEEFDA9FA91A705235D2365BF06CBA2748A9B1 (void);
extern void SkeletonAsset_SetSpriteBones_m09911B5B1FFF5C4543E9390E6EA6A0F5B9F605D9 (void);
extern void SkeletonAsset__ctor_mF10C7DA596BD26853FB8C7097B1652B32CFC71F2 (void);
extern void SpriteLibrary_set_spriteLibraryAsset_mF680D67985B9B1728BF541DEE86B6AE6F05F458B (void);
extern void SpriteLibrary_get_spriteLibraryAsset_m9D80093A7507E7B5A38A35CA189841D255C58CFA (void);
extern void SpriteLibrary_OnEnable_mDE2E64C3D31D88F5B35613AAB359A3F2747EEA4A (void);
extern void SpriteLibrary_OnPreviewUpdate_m3F6F098FC434551AD6A804A1DCF04CA40466A84B (void);
extern void SpriteLibrary_GetSprite_m5C77C433511DA5BE2CAAF5B101E01B23FF8DC3C9 (void);
extern void SpriteLibrary_GetSprite_mCCA7EFC5B4AA98F93CD16F8443F0B9F39FC14B63 (void);
extern void SpriteLibrary_UpdateCacheOverridesIfNeeded_m676861DDDF9E45EDD095498C2AC988D1201B6DEB (void);
extern void SpriteLibrary_GetCategoryAndEntryNameFromHash_m0B1D65FE0944115D41BA1CBFE003430C49FEE66F (void);
extern void SpriteLibrary_GetHashForCategoryAndEntry_m4E20584B1788AB652371A7C648D9003B2E5B4871 (void);
extern void SpriteLibrary_GetSpriteFromCategoryAndEntryHash_mB44061DF02FBA182EDB058B64E7FFDFDD78AFC3E (void);
extern void SpriteLibrary_GetEntries_m139AEC5801BA2B44525EBF02447CED8C9B3931EA (void);
extern void SpriteLibrary_GetEntry_m17D299BC2C1CBF68DD3E1F3335148F5735613161 (void);
extern void SpriteLibrary_AddOverride_m700FE75CA3981C30B00F75F105EBE27C0A74B8E1 (void);
extern void SpriteLibrary_AddOverride_m15621ADB67666F5C352DAC3A85557C7014BB3C2D (void);
extern void SpriteLibrary_AddOverride_m37A60FB18F9F037B7DE8F601983EE412F1146316 (void);
extern void SpriteLibrary_RemoveOverride_mB15E1BF611EA39D9115D73C621D41167CDC67D4D (void);
extern void SpriteLibrary_RemoveOverride_mE77657B920C7303A0633B20DFD48BDB912799F53 (void);
extern void SpriteLibrary_HasOverride_m8B0B705A58B82794056EDFF17AC151ABA809CF82 (void);
extern void SpriteLibrary_RefreshSpriteResolvers_mD0866A099A97456E1EE0D07F45712EFA695AE0B3 (void);
extern void SpriteLibrary_get_categoryNames_mEB52259DFC9FEE284E492964B2029978A549EB79 (void);
extern void SpriteLibrary_GetEntryNames_m28E8ED9507751401104E3886AB94362EF6CDD2F6 (void);
extern void SpriteLibrary_CacheOverrides_m59FC3CF018930CFD09BAAB93520291E59957A52A (void);
extern void SpriteLibrary__ctor_m33873F5DE5B7FD5B6A0906AFF0D48CFD74AB3C75 (void);
extern void U3CU3Ec__DisplayClass18_0__ctor_mD7D88F1DD5B26197EDA8E4B190CDD38F72C27117 (void);
extern void U3CU3Ec__DisplayClass18_0_U3CGetEntriesU3Eb__0_m2D1B2177E273A2ED25E6D39E46CA52247E764650 (void);
extern void U3CU3Ec__DisplayClass19_0__ctor_m219436D5725E3100288824D6A924FB290865EC39 (void);
extern void U3CU3Ec__DisplayClass19_0_U3CGetEntryU3Eb__0_mDF5C1426DFB598E77FE7514733B7B5B314BFF93C (void);
extern void U3CU3Ec__DisplayClass21_0__ctor_m2847E84FDC199E452F40D0A3030D20925C66F2A8 (void);
extern void U3CU3Ec__DisplayClass21_0_U3CAddOverrideU3Eb__0_mD6C66DE4144F495C8454E13373664220751FDC2D (void);
extern void U3CU3Ec__DisplayClass23_0__ctor_m7994B72A1A84966296F8C335A3D21386A2F55D03 (void);
extern void U3CU3Ec__DisplayClass23_0_U3CRemoveOverrideU3Eb__0_m9694B8FC076E116B98960B7BDFC120CC4B7724BE (void);
extern void U3CU3Ec__DisplayClass24_0__ctor_m1C01B71D76C984EE5C21B0E73BD8CD598C4FDC57 (void);
extern void U3CU3Ec__DisplayClass24_0_U3CRemoveOverrideU3Eb__0_mD1CD99B272F301672AFA96020794742BAC121674 (void);
extern void SpriteCategoryEntry_get_name_m66ACAEEC10BCCEE30D949CC399B55CA0C52120A5 (void);
extern void SpriteCategoryEntry_set_name_mD7CB900F1FD90AE56EE92A2EF1540F85DC56B695 (void);
extern void SpriteCategoryEntry_get_hash_m2A825DA45BF3FAFD2E082C91918E86F13888A1BF (void);
extern void SpriteCategoryEntry_get_sprite_m18219ADE134A806405B5C9E96C9C1552691A32A7 (void);
extern void SpriteCategoryEntry_set_sprite_m119DC38091B5187315EEC2D2B2E5DB687EA2F15E (void);
extern void SpriteCategoryEntry_UpdateHash_m54A35B15FF1E60129599DF038406F44AA5897F9F (void);
extern void SpriteCategoryEntry__ctor_m28B160C4B16BA186FCF516C280DE63B057728D5A (void);
extern void SpriteLibCategory_get_name_m45C90D2115940247F0DB13F7564710280B6E4BE6 (void);
extern void SpriteLibCategory_set_name_mEF802FFE9E78B67130C0A5F31A29DA79390B4653 (void);
extern void SpriteLibCategory_get_hash_m2C0258EE015F53D8FF4912AAF9A735D2E8FDF88E (void);
extern void SpriteLibCategory_get_categoryList_mC734220C15AC1827A5986AB305EE50FC8F05D47B (void);
extern void SpriteLibCategory_set_categoryList_m7846E2C8DEF3F16803FF0AB50F58AD149F9E4D18 (void);
extern void SpriteLibCategory_get_labels_mED08AC6160D594C38F542D9656434744D7AD2BD5 (void);
extern void SpriteLibCategory_UpdateHash_mA2C536614AD08346369B01956A47539EA52978E2 (void);
extern void SpriteLibCategory_ValidateLabels_mDECC12291A94847ACD565888E240535C4E364FF5 (void);
extern void SpriteLibCategory__ctor_m10E30F11B96B0168EDB8A0AFB0BD6BB95B657137 (void);
extern void U3CU3Ec__DisplayClass14_0__ctor_mBE0495632BE0F5B9C9C1736155CE3286F8E3EDBF (void);
extern void U3CU3Ec__DisplayClass14_0_U3CValidateLabelsU3Eb__0_m70F2F08CF214474DF45D044FB0A747C62781DC71 (void);
extern void SpriteLibraryAsset_CreateAsset_m07E0AD1BEBB60FBE0789EAA4AAB7090A0E0C950C (void);
extern void SpriteLibraryAsset_get_categories_m805F6614A090CAF1A6A4F4FA43FCA6D0AD23B164 (void);
extern void SpriteLibraryAsset_set_categories_m22CC07C954B274364246B358B502769CF77B5483 (void);
extern void SpriteLibraryAsset_get_modificationHash_m98486690892C30EAA2D25CC182B602AFF41B2045 (void);
extern void SpriteLibraryAsset_set_modificationHash_mD493105735F7191590EE86AA3F20C0E7367633D8 (void);
extern void SpriteLibraryAsset_set_version_mB5B478A815C1605D380E8E1665DE0451787AF5A2 (void);
extern void SpriteLibraryAsset_OnEnable_m90AB0E2D8463F567E5FB96039DD4F61F0E351E80 (void);
extern void SpriteLibraryAsset_UpdateToVersionOne_mEDFDF05E4A10DE93C72FCB4AA0E7A1CA6B58AD97 (void);
extern void SpriteLibraryAsset_GetSprite_m7745DBF8D7E54E5C96FA0958A1BCE8ED3E6A3707 (void);
extern void SpriteLibraryAsset_GetSprite_m77BF394F0DBE95FCF6D11EA29EE216C7E364FF3C (void);
extern void SpriteLibraryAsset_GetSprite_m3CC5631691BB2D4A17C3AA69EDA39E29ADDC3117 (void);
extern void SpriteLibraryAsset_GetCategoryNames_mDC1C7CE8EE93F40912E520C83E34AECB24132C67 (void);
extern void SpriteLibraryAsset_GetCategorylabelNames_mECE4A546FA407607907AE7E4E6CB0E4E75B3176D (void);
extern void SpriteLibraryAsset_GetCategoryLabelNames_mE46A4E9A6F5C7787BD6C9E1DE05D894D6791F5BE (void);
extern void SpriteLibraryAsset_AddCategoryLabel_m7DADA570B01CE3F4E51E52938115763182EDCA74 (void);
extern void SpriteLibraryAsset_RemoveCategoryLabel_mE7D5FB794EF48CCAA594B341F71DE3E6FDE9A982 (void);
extern void SpriteLibraryAsset_UpdateHashes_mF4B36517B473EC071F2563EA85B4A24130349ECD (void);
extern void SpriteLibraryAsset_ValidateCategories_mB2D79C54047176864E6715A7915D4416A6177E77 (void);
extern void SpriteLibraryAsset_RenameDuplicate_mCF8C5A8B5EEF7C245DD5D985ED05329AEC3042C0 (void);
extern void SpriteLibraryAsset__ctor_m212C254CA1539AEB7F326C970CAD165BC2567E52 (void);
extern void U3CU3Ec__cctor_m13C6A7D3054C7DA2A8F43B21715FE96A22A1DAEE (void);
extern void U3CU3Ec__ctor_m2A27E85D0CF4F1BD149847EF987CA2AC82EAE31A (void);
extern void U3CU3Ec_U3CGetCategoryNamesU3Eb__17_0_m1AF70B25CE197815DB5367646846B6B3CC5F6984 (void);
extern void U3CU3Ec_U3CGetCategoryLabelNamesU3Eb__19_1_m8360D3D1EFC2F4468EECA0986E993DF7C1758DB2 (void);
extern void U3CU3Ec__DisplayClass14_0__ctor_m2BB38B03421C1CCD8EA26C3C73296B8CF20004E4 (void);
extern void U3CU3Ec__DisplayClass14_0_U3CGetSpriteU3Eb__0_m9AB0F17FC579FACBCF12339014701D06402227BF (void);
extern void U3CU3Ec__DisplayClass14_0_U3CGetSpriteU3Eb__1_m59F76293A88FDF97B30C2BF1B461B34C410E8E08 (void);
extern void U3CU3Ec__DisplayClass19_0__ctor_mEA3B5B3F8540C52131188616AAFBB993CA6E18F7 (void);
extern void U3CU3Ec__DisplayClass19_0_U3CGetCategoryLabelNamesU3Eb__0_m1C0BC7A7B264905F502400E16616FF8B1899AA87 (void);
extern void U3CU3Ec__DisplayClass20_0__ctor_mD076FF66F48C69DB96AA2369E8DE201EACF086F2 (void);
extern void U3CU3Ec__DisplayClass20_0_U3CAddCategoryLabelU3Eb__0_m97F7F63F1375CA7F87FCDEE5E4A90F763DD46A81 (void);
extern void U3CU3Ec__DisplayClass20_1__ctor_m18EA61E3DD98E5896ABBB1867788CD648A552BAF (void);
extern void U3CU3Ec__DisplayClass20_1_U3CAddCategoryLabelU3Eb__1_m9CD7C22A923BD8E8AF96FED5C277AD95224ECCF2 (void);
extern void U3CU3Ec__DisplayClass21_0__ctor_mA9EE18A5D2DEBC6B5545C4089C4972650102CA5A (void);
extern void U3CU3Ec__DisplayClass21_0_U3CRemoveCategoryLabelU3Eb__0_m38BF15789DC696F1F95197187815CCCAD44354E2 (void);
extern void U3CU3Ec__DisplayClass21_0_U3CRemoveCategoryLabelU3Eb__2_m2BE89738D8BCE4D0BC0FE38076D08D109E8B8C16 (void);
extern void U3CU3Ec__DisplayClass21_1__ctor_m304B37DF4528DB1E3916F871D6FA659A76425C54 (void);
extern void U3CU3Ec__DisplayClass21_1_U3CRemoveCategoryLabelU3Eb__1_mF68EE2630C9C48894D5C946E3C28AE5C94E0A399 (void);
extern void U3CU3Ec__DisplayClass23_0__ctor_m2468B81C090BF10DB882E9F6D70F458689B2C08C (void);
extern void U3CU3Ec__DisplayClass23_0_U3CValidateCategoriesU3Eb__0_mAD4E5E040D640ED83559697F58E7893F23DD43EC (void);
extern void U3CU3Ec__DisplayClass24_0__ctor_m3A888061943B8650679A99963D98C24B400F1011 (void);
extern void U3CU3Ec__DisplayClass24_0_U3CRenameDuplicateU3Eb__0_m3EF463D99D9086E01DBFA5BCE5BED140B89E46D3 (void);
extern void U3CU3Ec__DisplayClass24_1__ctor_m007D14B377E8CB7DB25E9CF5C97DD248171E3ABE (void);
extern void U3CU3Ec__DisplayClass24_2__ctor_m2F7BB0C664F257A13B3BDBA0C2D6AFDF83196FCF (void);
extern void U3CU3Ec__DisplayClass24_2_U3CRenameDuplicateU3Eb__1_m546BE213E0759E46BD8E4125CFC9E9294C5F8F1D (void);
extern void SpriteCategoryEntryOverride_get_fromMain_m1A72A3A0D8918C14F67675DD5D9BDA9E53643795 (void);
extern void SpriteCategoryEntryOverride_set_fromMain_m645BA5FF8B8418DF6E8D4B98EDCB5BF6AEA45F9D (void);
extern void SpriteCategoryEntryOverride_get_spriteOverride_m72C69204374EDFC107495800DF8CA1A5FDB353E0 (void);
extern void SpriteCategoryEntryOverride_set_spriteOverride_m2E7069C45FB24DC6C478D15D090BD467FA9F85EF (void);
extern void SpriteCategoryEntryOverride__ctor_m45663A814AB4FBA333389E08269E1A9F7EA499D3 (void);
extern void SpriteLibCategoryOverride_get_fromMain_m8B74389DA47A85D96F0BFDB9744A26EE5BEF8E6A (void);
extern void SpriteLibCategoryOverride_set_fromMain_m68754BA07488A714A2F5374AF73BB1CCAA2055E7 (void);
extern void SpriteLibCategoryOverride_get_entryOverrideCount_m47C53A728BF0189527C6F6D253318D31808B0447 (void);
extern void SpriteLibCategoryOverride_set_entryOverrideCount_m54B9EC134267C1E75B02F4CA0BD116142C2FDCAA (void);
extern void SpriteLibCategoryOverride_get_overrideEntries_m685E64A7AC3A7905CDC4AC0ECE1F61AB68789437 (void);
extern void SpriteLibCategoryOverride_set_overrideEntries_mBC1DF5FEEE1187B0576FFF6B8BEA952C54159D15 (void);
extern void SpriteLibCategoryOverride_UpdateOverrideCount_m5286A9F13A045B7706F0DD861BA13DA7DE730068 (void);
extern void SpriteLibCategoryOverride_RenameDuplicateOverrideEntries_mF0A118922C59E9365307866A26DD3201C82470D2 (void);
extern void SpriteLibCategoryOverride__ctor_m646772A73EBAAA8C8F195B4F0D38292EDADF165B (void);
extern void U3CU3Ec__cctor_m9F6C212B1F14452D289EFAFED0A2FE90D06E6879 (void);
extern void U3CU3Ec__ctor_m069B6644653BA574C1A1F8437032659E00E79C21 (void);
extern void U3CU3Ec_U3CRenameDuplicateOverrideEntriesU3Eb__13_0_mB2DD873D90A91EA245F8CEFEBD3EFB3246DFCD52 (void);
extern void SpriteLibrarySourceAsset_get_library_m96741941C63C9769E4402CD42C08FB9131603F29 (void);
extern void SpriteLibrarySourceAsset_get_primaryLibraryGUID_mC4CA3914F378D4459C481B5CDEB5A8D4E1C6C52F (void);
extern void SpriteLibrarySourceAsset_get_modificationHash_mAE26BF2E1E03ED09BFF4AAFC90D775CFD1FC6593 (void);
extern void SpriteLibrarySourceAsset_get_version_m1FCBACC69BED6061CE67E29C1E933614FA062BE5 (void);
extern void SpriteLibrarySourceAsset_InitializeWithAsset_mC46EE140306FE7227422DC64C860D8AB4C6CD32A (void);
extern void SpriteLibrarySourceAsset_SetLibrary_m1885B3DE115CB86B9DAA295BF0C08DAA5F4AAD5A (void);
extern void SpriteLibrarySourceAsset_SetPrimaryLibraryGUID_mCC566DBF2C4FDCCFFE5B3330EA7A2BB58E8AF17D (void);
extern void SpriteLibrarySourceAsset_AddCategory_mEE8EFC4969198627428E6FE1B9DEEDBC91684458 (void);
extern void SpriteLibrarySourceAsset_RemoveCategory_m42A98ADF5B0C9A316A27829AC26008577578B127 (void);
extern void SpriteLibrarySourceAsset_ClearCategories_m7495F048F4F04DA916096A3AE1DE16D45DAD9690 (void);
extern void SpriteLibrarySourceAsset_RemoveCategory_mBF27FB65C02578167D0DA94CFDEA6706566261A6 (void);
extern void SpriteLibrarySourceAsset_UpdateModificationHash_m54A57D3C85EABB0F9EB7E21191654FAB50FC580B (void);
extern void SpriteLibrarySourceAsset__ctor_mC0690E2F4FDBCF4A9C25C2CA79F99FE4B49216CE (void);
extern void SpriteLibraryUtility_Convert32BitTo30BitHash_m1D416A50DFA53B54E09F9AEDF3CA12EEAA9DD40E (void);
extern void SpriteLibraryUtility_Bit30Hash_GetStringHash_mA295C62C1832B95858F3EEC077FD40C3C8CA9901 (void);
extern void SpriteLibraryUtility_PreserveFirst30Bits_m9E5594A3CA7AC0DE58826845C3B6D64344FA6FD6 (void);
extern void SpriteLibraryUtility_GenerateHash_mAA7ED0100867D1851451E7844C0E71CE6C997645 (void);
extern void SpriteLibraryUtility__cctor_m114F9A7DC2AA90D59A60EF396C6B76BCFBC95B54 (void);
extern void SpriteResolver_Reset_m40816C131437BA0E5A988A85F15EC39ABA78A9CA (void);
extern void SpriteResolver_SetSprite_mCAA900EAD3933CC681F2BC3CA4984495F8B99733 (void);
extern void SpriteResolver_OnEnable_mB7614C61A30904A715380C82F5AA977DC172C053 (void);
extern void SpriteResolver_InitializeSerializedData_m09CC228C2D760A0ECC46D1BE437AA38B6A0CE558 (void);
extern void SpriteResolver_get_spriteRenderer_m56B5B46BDA69583C7CA2BE62163A7139FECF92C1 (void);
extern void SpriteResolver_SetCategoryAndLabel_mF7A1451BAF1B4B6E6406C0FF817E43DE55BE47C9 (void);
extern void SpriteResolver_GetCategory_mE85FF7205CDD9ACC3D33116C5D7CEE32837812B0 (void);
extern void SpriteResolver_GetLabel_mDCC61753D0AC00153BC9F606DD0EE0A931C37134 (void);
extern void SpriteResolver_get_spriteLibrary_m013ED298B4EF93D427312A0A0CA5EDA08AFFF3B9 (void);
extern void SpriteResolver_OnPreviewUpdate_mA50A78C9BD39FE190EA3DEC1C1CDE4CD3C6D971B (void);
extern void SpriteResolver_IsInGUIUpdateLoop_m3AD92DE12C61D45FAC8D9CDB2AE724C74D89F96E (void);
extern void SpriteResolver_LateUpdate_m272EB3E4332D2C5967A9940449B73C32A62D95FA (void);
extern void SpriteResolver_ResolveUpdatedValue_m2311F2A314FE514BA4042C97C7A5183A2B9C6E78 (void);
extern void SpriteResolver_ConvertCategoryLabelHashToSpriteKey_m95401386E8801B6E868EBC6E40A01D09B59A1892 (void);
extern void SpriteResolver_GetSprite_mFFCBA23CE0B805478F38D6895D31732AD461881F (void);
extern void SpriteResolver_ResolveSpriteToSpriteRenderer_mA32F58B657B82237302157D11521A47180346462 (void);
extern void SpriteResolver_OnTransformParentChanged_m0D7E5D476DC0D93A0897828A1CB713C4BCC9979C (void);
extern void SpriteResolver__ctor_mEE72E98F629568B1773423F089108BDED9F832C4 (void);
extern void SpriteSkin_get_boneTransformId_mE1A8085BFC05772E0674888AA677DD505A8853A6 (void);
extern void SpriteSkin_get_rootBoneTransformId_m5E71C241EB39B63C6574C22E4A8D3756F8471071 (void);
extern void SpriteSkin_get_currentDeformationMethod_mC6ECB18C21A66DC0ECE49F9909DCAB3025445F3F (void);
extern void SpriteSkin_set_currentDeformationMethod_m397540722EEFD0105FAA39FB267E69BDEE34B998 (void);
extern void SpriteSkin_get_deformationSystem_m0A177888E1EBBE3ECAC9813FE337821FF8D3A3D4 (void);
extern void SpriteSkin_set_deformationSystem_mCDDEF1841F603869B17C7D58C3F56EBA5EB1ECA1 (void);
extern void SpriteSkin_get_outlineIndices_m81C096423C1F78B36A3EB22E90E1B4BA0125A0C4 (void);
extern void SpriteSkin_get_outlineVertices_mFA10B556E9F0CB7B3F3E64F10E4DA273FF7A0F29 (void);
extern void SpriteSkin_get_vertexDeformationHash_m51F41C75A00FBE5A25CF97DE23E50B6A9B08DCE4 (void);
extern void SpriteSkin_get_sprite_mF15B0997564F1606CF54BABBE096459267570BDA (void);
extern void SpriteSkin_get_spriteRenderer_m6BA6C5145F499FE6E718CE0C5BAE27450A9BDC80 (void);
extern void SpriteSkin_get_spriteBoneWeights_m3F8CC9F85725ABD50DBB038E98869B86A53B0D73 (void);
extern void SpriteSkin_get_dataIndex_m0254FD14560F5DBE6AF5D3F2C195E19C24A89FA6 (void);
extern void SpriteSkin_SetDataIndex_m887214CCC9BDD1F1A65A6710A1071308BADFA67A (void);
extern void SpriteSkin_get_autoRebind_m95820D1D14C64B25C58D2781C2D25556C75B5968 (void);
extern void SpriteSkin_set_autoRebind_mDEE587A61DA9E162B6DF4B00DD50948493A04ADE (void);
extern void SpriteSkin_get_boneTransforms_mD8CC46A5BAECECFEF8EE686CBE3C6E23569EE1D9 (void);
extern void SpriteSkin_SetBoneTransforms_mF36BC9130E34C6EAAFFF2F183A2DB782F66DFBB7 (void);
extern void SpriteSkin_get_rootBone_m68D6479EC449C6C0FD1F4D3C552C3D06480A6599 (void);
extern void SpriteSkin_SetRootBone_mBF809803C1F8016AEB052A627064150503C8CB1F (void);
extern void SpriteSkin_get_bounds_mF17F41D61D122A6837D953521CE8F88ED45F163C (void);
extern void SpriteSkin_set_bounds_mE4789ADF55BD07D4824B4EB8150521C08C95AD26 (void);
extern void SpriteSkin_get_alwaysUpdate_m06F0BCB5A2CC28CC60CDB215F75D753264D2D3DE (void);
extern void SpriteSkin_set_alwaysUpdate_m215C1C335A1E837281E2F194FFABE59465A87B7D (void);
extern void SpriteSkin_get_forceCpuDeformation_mC107F5E5B1A34D7B409F59E0EB7A18AFB3622F03 (void);
extern void SpriteSkin_set_forceCpuDeformation_m27ED41A9A550E126ED9438CA839D9CCBD1C0ECD6 (void);
extern void SpriteSkin_ResetBindPose_m3C817783DA7E7E445CC2761FBEE67A63584B079B (void);
extern void SpriteSkin_get_isValid_mE36B48E6DBC4AEEF21E73F6EC1FE5F01C35E4AF9 (void);
extern void SpriteSkin_Awake_mF6D527563052A0C49F3E18D906B13C32DDB9C855 (void);
extern void SpriteSkin_OnEnable_mC03816C41AF28D93A959C09C69A446824CBB5ADA (void);
extern void SpriteSkin_OnDisable_mB3730C1F02BC3C78CBCEA37BC5957513A958D1F2 (void);
extern void SpriteSkin_RefreshBoneTransforms_mACF09172532DFD37E9933D8F51D12211DD8C9D2E (void);
extern void SpriteSkin_OnSpriteChanged_m1A73B3958E8FAB380FFF668314D4C6E8FDF5A79A (void);
extern void SpriteSkin_CacheBoneTransformIds_m2BAC9C1CF292F340B30E9015F1F67DBE87D1CB41 (void);
extern void SpriteSkin_OnBoneTransformChanged_m9B1716380B1EAE284F29B44DA3CEE52C3C87BA84 (void);
extern void SpriteSkin_OnBeforeSerialize_m89975D54F2D66674A64968A7A5705D6CB73DB758 (void);
extern void SpriteSkin_OnAfterDeserialize_m4EB0A78A7FDD574FD12E092415EDBA462F44219E (void);
extern void SpriteSkin_OnBeforeSerializeBatch_m02C4FB69D476968C7ED6A6F25783735BD90FD74A (void);
extern void SpriteSkin_OnAfterSerializeBatch_m9CAB751D9E6A23B41F7725FFF0F26AED7477421C (void);
extern void SpriteSkin_OnEditorEnable_mA5B23CEB3CD53064F6FFDF750BEDD9DECAA20911 (void);
extern void SpriteSkin_CacheValidFlag_m31EF3C3454A24531F688858F239D7FC5C9E2AE7A (void);
extern void SpriteSkin_BatchValidate_m2467FF2FDD5BEDD85C5B9798BD5CC4FC28B79394 (void);
extern void SpriteSkin_Reset_mD2D7C8A74A75AA2977A25B498C30DE234E4CF4A0 (void);
extern void SpriteSkin_ResetBoneTransformIdCache_mAE195FF805BCC26229DE618F3288B753E08BB69E (void);
extern void SpriteSkin_GetDeformedVertices_m02ABE856F01A596C406A71F9D64645783A23177C (void);
extern void SpriteSkin_HasCurrentDeformedVertices_m8B65CAF8F18F2B1263CECD6CA67E50587BE65AB3 (void);
extern void SpriteSkin_GetCurrentDeformedVertices_m4B6CC2B08DCA4FFBA7DFDC7606CFE6C88B1EB8E2 (void);
extern void SpriteSkin_GetCurrentDeformedVertexPositions_m3303010762D2A175E99257A463B8F4356D09F7B7 (void);
extern void SpriteSkin_GetCurrentDeformedVertexPositionsAndTangents_m2F9A8FF3BFBAE465DB5FC870155EEC8103CCE995 (void);
extern void SpriteSkin_GetDeformedVertexPositionData_m86AD284E8E717D3557DDAD8F735AD5412A076103 (void);
extern void SpriteSkin_GetDeformedVertexTangentData_m272E4265DD563DD8572D2405C77EF9844D3029BA (void);
extern void SpriteSkin_DisposeOutlineCaches_m62915E89AEB6B9251CFB47936BD18B20DBA44A03 (void);
extern void SpriteSkin_OnPreviewUpdate_m82A0E8A1917E38C891E71A9DD421A21B04FB2369 (void);
extern void SpriteSkin_IsInGUIUpdateLoop_mB02E1A31856C8B26AA8D64730A261A802B8929AF (void);
extern void SpriteSkin_Deform_m507374A93373F84548AE95DDE450AD5539D3CCA6 (void);
extern void SpriteSkin_PostDeform_mD3FFC2298A5DF1662F13DFEFEB4E6AC4502639CB (void);
extern void SpriteSkin_CacheCurrentSprite_mDAAC09263A952D94E544BD8A01DDDAF7142C682B (void);
extern void SpriteSkin_UpdateSpriteDeformationData_m4566B0074A11F7EAB13098D94668FE0766EC95E3 (void);
extern void SpriteSkin_UpdateDeformedOutlineCache_m4C02FF6FDAD422DB30FC3625B69D17981C00C9A5 (void);
extern void SpriteSkin_CacheSpriteOutline_mF4FE2F3EF5698D1DFD63BF2E973D641B251DD74D (void);
extern void SpriteSkin_CacheOutlineIndices_mD4ECE2DEC9551CF22A503870266CA5D663317088 (void);
extern void SpriteSkin_CacheOutlineVertices_m268578A339E1676B1690ECC00F81EA4233013FD0 (void);
extern void SpriteSkin_CopyToSpriteSkinData_mD059DF7D8F7695A6740798F7A5B8AFC083C98C8D (void);
extern void SpriteSkin_NeedToUpdateDeformationCache_mD0634471F66B27F0D502296F1C824269EFC786BF (void);
extern void SpriteSkin_CacheHierarchy_mC0A030A1A61012989B7E51086BAD22D95A56079B (void);
extern void SpriteSkin_DeactivateSkinning_mE3A93D904D7FD085827A85326A877E2CAAC7EE02 (void);
extern void SpriteSkin_ResetSprite_m06E181F4DB7BFA9099DA1A453DC3CC6835D7F1F1 (void);
extern void SpriteSkin_SetDeformationSystem_m6512B097A1FF4511AFDEAD456F0971E85AB4AEF3 (void);
extern void SpriteSkin_CountChildren_mF12E34775BCD805D3F7090212A0AFB00B8EEF7AD (void);
extern void SpriteSkin_GetNewVertexDeformationHash_mCF8641139FA856721622F27CDB5FD3C884C3164B (void);
extern void SpriteSkin__ctor_m68477AF9394FB0E996C4A6208E327B3683FA6AC5 (void);
extern void Profiling__cctor_m71142E4CC419BC908E7DCB53FB855DC6F1AA40B2 (void);
extern void SpriteSkinContainer_add_onAddedSpriteSkin_mAA0920845F49D797080974592F7D69C0D844CE60 (void);
extern void SpriteSkinContainer_remove_onAddedSpriteSkin_m680B6655B0191987E5510758805E947B1BED623C (void);
extern void SpriteSkinContainer_add_onRemovedSpriteSkin_m5EF7E123048AFA763F47E126092CCC5D5FF2C585 (void);
extern void SpriteSkinContainer_remove_onRemovedSpriteSkin_mD8504216A4B3736732E99B44805DB5609FAB3060 (void);
extern void SpriteSkinContainer_add_onBoneTransformChanged_m9F21B24CFF7B74185CBE9A08364C5347D4737A78 (void);
extern void SpriteSkinContainer_remove_onBoneTransformChanged_mCDBDEB0E2BDE0A98F81B1C4F6A8480CA28B3E4EF (void);
extern void SpriteSkinContainer_get_instance_mA9D39D50D0CD9377802D2852FDED53BC366DA9B7 (void);
extern void SpriteSkinContainer_get_spriteSkins_m3EB584921E931EB48328EE5DC94493658E76E3CE (void);
extern void SpriteSkinContainer_AddSpriteSkin_m428AAEB741AA1C442730A7451E66E2AB706737FA (void);
extern void SpriteSkinContainer_RemoveSpriteSkin_m8C42A7ED34E08FA91A0EFDD5EDE24DA4E1A53EF1 (void);
extern void SpriteSkinContainer_BoneTransformsChanged_mCFA94288D68E29EB82F349E236E79590CC2C74E3 (void);
extern void SpriteSkinContainer__ctor_mEC32A7C89375784FB475323F9C520A63FA650EBD (void);
extern void SpriteSkinHelpers_CacheChildren_mE6DF934082EEF2A4B6779885C6F7A4A710E4B5A8 (void);
extern void SpriteSkinHelpers_GenerateTransformPath_mDC9A42EEBFAC4F7E67159740F8C53DB09DBAA063 (void);
extern void SpriteSkinHelpers_GetSpriteBonesTransforms_m1EB9C5D67C66A91168CA2B580CD26608A262EF39 (void);
extern void SpriteSkinHelpers_GetSpriteBonesTransformFromPath_m273A6B44C759665490D2E6A63E1736F137A9E55F (void);
extern void SpriteSkinHelpers_CalculateBoneTransformsPath_mC5CDED70F116BBDC82E10964DC7ECD0E23D3F4F1 (void);
extern void U3CU3Ec__DisplayClass2_0__ctor_m5D3EE163C1900F1C79E0A396F3C982F1C0818F59 (void);
extern void U3CU3Ec__DisplayClass2_0_U3CGetSpriteBonesTransformsU3Eb__0_m4BA4D4C40D9328B5998954A9F4DFB8F439400068 (void);
extern void NativeByteArray_get_Length_mCBB70CAA06A6248216B787273A280F23B67707C7 (void);
extern void NativeByteArray_get_IsCreated_mADB5E11A5FE061E98672AAC451A5AB294E051031 (void);
extern void NativeByteArray_get_Item_m5A5D5F59F4B4BACE3123324D6A4D64D4249F9DD4 (void);
extern void NativeByteArray_get_array_m6DD098B95665876510BC0FB925B47989B33D02DE (void);
extern void NativeByteArray__ctor_m279E3142238F49166ADCEBA75E17B6BA931BFA81 (void);
extern void NativeByteArray_Dispose_m012B713339FDE3095C9C92F8C1A58827ED9129F1 (void);
extern void SpriteSkinUtility_CanUseGpuDeformation_m1AFFD12E08C3AED27103C79B287151D48F049D25 (void);
extern void SpriteSkinUtility_IsUsingGpuDeformation_mF50D05BFCC9B1AC8DDD49DA47A83708B2E041944 (void);
extern void SpriteSkinUtility_IsGpuDeformationActive_mCCB150114CCFAF90F225B64C857E735D225978CC (void);
extern void SpriteSkinUtility_CanSpriteSkinUseGpuDeformation_mBC783FE2A8827BB255C26BA9376AA5134C6079F3 (void);
extern void SpriteSkinUtility_Validate_mC31EB7E41D972DD369D497BA36E2E32CB5A4E369 (void);
extern void SpriteSkinUtility_CreateBoneHierarchy_m5388397FC3E4D2A56B8D6534D9179B8CEBF3D85E (void);
extern void SpriteSkinUtility_GetVertexStreamSize_mA431E0E2846881F30F60D36D40D2E3B2ACE651F8 (void);
extern void SpriteSkinUtility_GetVertexStreamOffset_m8B47F6747D2C64AFA32325A63962C52AE5150743 (void);
extern void SpriteSkinUtility_CreateGameObject_mE070DBC6CE5E164E53FA6B3B8F69F226F534334E (void);
extern void SpriteSkinUtility_GetHash_m3EB3A276AB986508F0E6876045E363B185B60A4D (void);
extern void SpriteSkinUtility_CalculateTransformHash_mA3BE1DDA36921D2CFC68814E1215EB774E0CB3DE (void);
extern void SpriteSkinUtility_Deform_m559F3F0AEE4E775B0774706B643A4492DE6904B0 (void);
extern void SpriteSkinUtility_Deform_mB62EB14E78CE661C661C59B5905AD92F265B8AB5 (void);
extern void SpriteSkinUtility_Deform_m4A6CF4CFC4939F9BF405E008069E98AEB3D316FE (void);
extern void SpriteSkinUtility_Deform_m0DD5368630CA0379ACAEE40DAD4F68743B43F10C (void);
extern void SpriteSkinUtility_Bake_m5CF1EAAD01CB2B764CA39C649F66DA83A889F2CC (void);
extern void SpriteSkinUtility_CalculateBounds_m340DD259C2CCC217F1D2332AEB07E8D6E8F3A73E (void);
extern void SpriteSkinUtility_CalculateSpriteSkinBounds_m99D083830A48F235E94E56283A56848A0A9659B0 (void);
extern void SpriteSkinUtility_UpdateBounds_m9AE2780487507192D3150091AD31C6BCBD78CCD2 (void);
extern void BurstedSpriteSkinUtilities_ValidateBoneWeights_m5E7F9120E9D22F0A097DD5B296BE3E9336D45017 (void);
extern void BurstedSpriteSkinUtilities_SetVertexPositionFromByteBuffer_m6D9016B821006BB68DA3C0FEDAD7238BB18A287C (void);
extern void BurstedSpriteSkinUtilities_ValidateBoneWeightsU24BurstManaged_m21CC2B3A2D0773D4D06FC7A380277502C103AC44 (void);
extern void BurstedSpriteSkinUtilities_SetVertexPositionFromByteBufferU24BurstManaged_m6AE31BDEE33E5E3FD3E2EB37F37A400EBF4FF45C (void);
extern void ValidateBoneWeights_000001B0U24PostfixBurstDelegate__ctor_m4DA9B7B1554BAA889120835762BFE4FE3E590A04 (void);
extern void ValidateBoneWeights_000001B0U24PostfixBurstDelegate_Invoke_m33E5BBEF21BB1C75A925DC7F07BB6C8E179EF024 (void);
extern void ValidateBoneWeights_000001B0U24PostfixBurstDelegate_BeginInvoke_m46425994A8CC6A5B1915F907F74149971190BBCD (void);
extern void ValidateBoneWeights_000001B0U24PostfixBurstDelegate_EndInvoke_m702E9AA1472077E017794A1D060D489DD51B9CB4 (void);
extern void ValidateBoneWeights_000001B0U24BurstDirectCall_GetFunctionPointerDiscard_mC1DADDCD3AA5364C1342E7F32DD31916D5076EDF (void);
extern void ValidateBoneWeights_000001B0U24BurstDirectCall_GetFunctionPointer_m1A223574762C317CAD0D5D9E92265C2A24BB130E (void);
extern void ValidateBoneWeights_000001B0U24BurstDirectCall_Invoke_mB65F58FC212A921DE77B913869F7B260A05F7DF2 (void);
extern void SetVertexPositionFromByteBuffer_000001B1U24PostfixBurstDelegate__ctor_mBA084804138B82C11C3084C8095E4D2A5D3DCF32 (void);
extern void SetVertexPositionFromByteBuffer_000001B1U24PostfixBurstDelegate_Invoke_mEFFEC74EB72A7888C1554F0B0E25A97BAF8BC865 (void);
extern void SetVertexPositionFromByteBuffer_000001B1U24PostfixBurstDelegate_BeginInvoke_mCD0A93729E4E61708C646ACB8B74937669D9CE46 (void);
extern void SetVertexPositionFromByteBuffer_000001B1U24PostfixBurstDelegate_EndInvoke_mC9AE252AB060AA87596E4826D9F735CB581F1B9E (void);
extern void SetVertexPositionFromByteBuffer_000001B1U24BurstDirectCall_GetFunctionPointerDiscard_mF064D520F7D256F7A555230CD67D5004F63E1C59 (void);
extern void SetVertexPositionFromByteBuffer_000001B1U24BurstDirectCall_GetFunctionPointer_m4291F8D4F9D4B26DDADDBDED8A89F0298213E47A (void);
extern void SetVertexPositionFromByteBuffer_000001B1U24BurstDirectCall_Invoke_m5EBCEE5C013079B2531774411BE3EB46A6BEFB63 (void);
extern void __JobReflectionRegistrationOutput__12640072059193112690_CreateJobReflectionData_mA063968188DB2EF135AC7DB46CC2EB45A360EBB8 (void);
extern void __JobReflectionRegistrationOutput__12640072059193112690_EarlyInit_mCF112CAD6822389D903F18CEB4D464A1EDE4C3FC (void);
extern void U24BurstDirectCallInitializer_Initialize_m3D7699F95291919D3D61A7D414F04C3C9703A382 (void);
static Il2CppMethodPointer s_methodPointers[476] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m686004A7C435C4DC5CA666EE2C0798BDBE530DAD,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m9610F9F68970CA8ECB075717298D140581282036,
	NULL,
	BaseDeformationSystem_RemoveBoneTransforms_m371364D40EB7743ABCB1366D347540A84FED5C12,
	BaseDeformationSystem_AddBoneTransforms_m5E4B0179466AEACCEAD741F634597EAAD1D02E1B,
	BaseDeformationSystem_UpdateMaterial_m47C13EE86303857DCEF7E5E55711EFCC349B5244,
	BaseDeformationSystem_AddSpriteSkin_m80C519EDAF5C85762CDD911DBB8404B1ED442F12,
	BaseDeformationSystem_CopyToSpriteSkinData_m918A28A06AA7A00153B0E1FF467A9BBAAC22638A,
	BaseDeformationSystem_RemoveSpriteSkin_mED1EBF1A9F94F366074589BBD036FE36420B76F1,
	BaseDeformationSystem_GetSpriteSkins_m1BD5EED916D01456F0305AB57A0E7CCC8213DB63,
	BaseDeformationSystem_Initialize_m32FD32396C789B3D3CBC45743347D5B94BCB57CE,
	BaseDeformationSystem_InitializeArrays_m2D68A1B4666CA752CEC87B302E8946F2CAB95F73,
	BaseDeformationSystem_BatchRemoveSpriteSkins_m899787AF35E56DCEC142937A71FCE380B0855451,
	BaseDeformationSystem_BatchAddSpriteSkins_m30035AD40D349EC41D0B20D2E81A6692B1744C0C,
	BaseDeformationSystem_ResizeAndCopyArrays_m606F57AEF78389752872999486558BE1D50C76B9,
	BaseDeformationSystem_Cleanup_m394845EEB446F15F8840609BCF68A4B31159FC33,
	NULL,
	BaseDeformationSystem_PrepareDataForDeformation_mCBE6ED3286A4CE1A2C2AA56BF2FAE0745C3377B6,
	BaseDeformationSystem_ValidateSpriteSkinData_mBA49FFFE7B81E007BF8E9A7D64B89B2E69A0B133,
	BaseDeformationSystem_GotVerticesToDeform_mDC30D0A1497201E113B4A33972DA3DB8C900996D,
	BaseDeformationSystem_SchedulePrepareJob_mB17A0F938A499DD987958D57F473A766A92711E5,
	BaseDeformationSystem_ScheduleBoneJobBatched_mC016DF6E43E02CABBC41D909AB9D84BB6DAC2252,
	BaseDeformationSystem_ScheduleSkinDeformBatchedJob_m8AC1EB9D746184E455577AFAA403355A11BE3B7E,
	BaseDeformationSystem_ScheduleCopySpriteRendererBuffersJob_m5CFBB8804F174AF3B52FE5C7C13626EEB5366B3C,
	BaseDeformationSystem_ScheduleCalculateSpriteSkinAABBJob_mF0B152594CD68ABD0C3A3B736648A99A1AA15D11,
	BaseDeformationSystem_DeactivateDeformableBuffers_m89AD27CF2605D033531659C4AE7FE1AFB034348E,
	BaseDeformationSystem_IsSpriteSkinActiveForDeformation_m9564CAFB8950CCE0C8C4445093956FD1A1AE208C,
	BaseDeformationSystem_GetDeformableBufferForSpriteSkin_m3109CF386076558B3DF96E6E04B3B3125F95E966,
	BaseDeformationSystem__ctor_m6F994399F86236217B3440CDAA0F79FB1A459014,
	Profiling__cctor_m75E0D9B5602D565D82F39C43A84C4A5EEFA212B7,
	CpuDeformationSystem_get_deformationMethod_m3118E9B6943BE15678C67BE05D0C02CA1BB402B8,
	CpuDeformationSystem_Cleanup_m2523D77E1744B660D80500A5AAED074FA65B3DD2,
	CpuDeformationSystem_UpdateMaterial_m9D91BE9513E0DE267F70E16DD650391D822A55C2,
	CpuDeformationSystem_Update_m8D763177FDD1ED3BDDDD10991AF249FC14D7E9CF,
	CpuDeformationSystem_ResizeBuffers_m26665DD8FD0A8C45C53AEA7AD5FCEEEC3432AB2E,
	CpuDeformationSystem__ctor_mB37A83FD9FF24998C06871CE978E9188F7A053A4,
	PrepareDeformJob_Execute_m371FE77DC77A71B801C2ED2F08BD76ECAD5D4333,
	BoneDeformBatchedJob_Execute_mD24F84E1C367AC5B671D77CD67E961D395C28C61,
	SkinDeformBatchedJob_Execute_m105DF36043A75DCAC5E0111DE74EE0087D64E136,
	CalculateSpriteSkinAABBJob_Execute_mD93917686FF5B004B49DC055B42C9F4DB42EF756,
	FillPerSkinJobSingleThread_Execute_mC5F785E6FA0AC412C792AF1FE03A8D8B08EA2C84,
	CopySpriteRendererBuffersJob_Execute_m4EFBA68E2C6B03752414B97C768E2B80F688F643,
	CopySpriteRendererBoneTransformBuffersJob_Execute_m386F8389F4159350BC846A57D1A88C7210B605D1,
	DeformationManager_get_instance_m15E7B260A44705AB2DF91C598D76007CF44D8FF6,
	DeformationManager_get_helperGameObject_m640617C1EA2DEE8FC6F9E868691C389D50ECEC01,
	DeformationManager_get_canUseGpuDeformation_m241EDCA1E704333645C3AD38F60076E7B961E802,
	DeformationManager_set_canUseGpuDeformation_m14226B7E606903D7686D4FEB7CF255C2FFF8DF33,
	DeformationManager_OnEnable_m312B7477973F409BCA06ADFD6986B7ED9FC74621,
	DeformationManager_Init_m1E8DA325F03E91627E0FB22D25BAB8458F58E86E,
	DeformationManager_CreateBatchSystems_m33936D5A024BA3AA0C6D508A5FA11C4100AC2B9D,
	DeformationManager_CreateHelper_m028FE0DFFFCAED718FB94DD23DA9DF967758F635,
	DeformationManager_OnHelperDestroyed_m2ADE4A2AA0EA78E15D65FEC11453D208DEC18955,
	DeformationManager_OnDisable_m75606324949589DD8D1AEA2627CBF12B7FCEC35E,
	DeformationManager_Update_m785799D006868C8BCC62D2BB1D3D3A731CCF41E1,
	DeformationManager_HasToggledGpuDeformation_m63526D3C497B7B7228BC0D942DC39830C472A397,
	DeformationManager_MoveSpriteSkinsToActiveSystem_m85C5873BEAD1B499F4DDD8FB917DDF02888E5F18,
	DeformationManager_AddSpriteSkin_m6556A3BCC0C80EE424D6F15239CB5AB096A7D52D,
	DeformationManager_RemoveBoneTransforms_m3DE91A617529DA79E2C3BA30C87FD258BFDADF44,
	DeformationManager_AddSpriteSkinBoneTransform_mAE13ED4585F6B39F58E80F52A88795E9F201F2D2,
	DeformationManager__ctor_mF8514EA8C32FC7A4FE09B7EE492C74A4B9F093D9,
	DeformationManagerUpdater_get_onDestroyingComponent_m01B17114C587D0B57AD883CEE80776B0BA9537C2,
	DeformationManagerUpdater_set_onDestroyingComponent_m84C335D0C2FA34C34475A425C781572A1611C5B7,
	DeformationManagerUpdater_OnDestroy_m4D13515BA8E4B1D0AC1028A67DE19586EBEE1AD6,
	DeformationManagerUpdater_LateUpdate_mFE7EF341ABFAE6D65BAA16E937B5D15A88D70781,
	DeformationManagerUpdater__ctor_m8DD130BC84F485947A5ED3ADAF8B7D5754819D5E,
	GpuDeformationSystem_CreateFallbackBuffer_mB1FA6A8C3ABD31BEFC8E33ED39D3811D06018869,
	GpuDeformationSystem_ClearFallbackBuffer_mE9853A09098301B82FE449650132AD5AF3DB4B4E,
	GpuDeformationSystem_get_deformationMethod_m62CA51E492E86A4EC3FDBFA357A8592369292548,
	GpuDeformationSystem_DoesShaderSupportGpuDeformation_m9F9DE53A097A27F65C6634F9D45A0F4CB92BB754,
	GpuDeformationSystem_IsComputeBufferValid_m58B66E25F26484A573F96F1027E48B19CDABDAA4,
	GpuDeformationSystem_InitializeArrays_m11D24E9312112FFF8865054B240CB841B692CFEC,
	GpuDeformationSystem_Cleanup_m4B435152154381ADF0C4996F17C1E0E29A2C6464,
	GpuDeformationSystem_ResizeAndCopyArrays_mA2B9722DD234C19681F9EC2AE79BE69A0C61CFF5,
	GpuDeformationSystem_CleanupComputeResources_mC9201DFFB7DEFD7F2E1C8F13EF19EA50953E7937,
	GpuDeformationSystem_UpdateMaterial_mF2EA570443FFF632B62EF176D58559EF11258007,
	GpuDeformationSystem_AddSpriteSkin_m6D457B5688B6403FCA98783308C0F1D6E600201A,
	GpuDeformationSystem_Update_m2130F2105F1DEB71DACB19377CA661990D094CE7,
	GpuDeformationSystem_ResizeBuffers_m3D78DEE29B4777803F13BE127E361E59A17FD0D0,
	GpuDeformationSystem_CreateComputeBuffer_mB904E0EC55C0A56DDE940B0C7D62FD9508D993F1,
	GpuDeformationSystem_SetComputeBuffer_m17619798E1CCA81052BEE4BE870187886450C4ED,
	GpuDeformationSystem_ScheduleCopySpriteRendererBoneTransformBuffersJob_m71E403C330931D7EA163CAA47CD4B80DEE124A7A,
	GpuDeformationSystem__ctor_m95F25C60430CEC53B884C006BE4E5EA6F34D6497,
	TransformAccessJob__ctor_mD6EAD8E55DC39C30642951F0247FFA88B681583B,
	TransformAccessJob_Destroy_m7E55E78C9A1623D95B3FB7213459F570DC410779,
	TransformAccessJob_InitializeDataStructures_m020A60543A286A9EC487E6DC777FC034734F5465,
	TransformAccessJob_ClearDataStructures_m4AFA32B0BBA00ACAEC589AFEFC7CC6862CE88DBB,
	TransformAccessJob_ResetCache_m9DD6FA7692B7DD03131D2E9A9E6EF82F3C0FB065,
	TransformAccessJob_get_transformData_mA6B68A115FF7CED15402C2F21E9F69BDF8E19A3B,
	TransformAccessJob_get_transformMatrix_m0CC3B74850876DE7614AECF25BC8066778B1D00E,
	TransformAccessJob_AddTransform_mBDE033F954908A88B2EBCE1B36AA8B143C86C92F,
	NULL,
	NULL,
	TransformAccessJob_UpdateTransformIndex_m48C846177A7D51F436FC360CE75C3FAC3513D2BC,
	TransformAccessJob_StartLocalToWorldJob_m4D840C72D63F3FBC7C54D7E5B771B05AFB71D386,
	TransformAccessJob_StartWorldToLocalJob_m480EA003C27B312D9859A5FB4EB367D1666FA81C,
	TransformAccessJob_GetDebugLog_mB459C748C56775F1D4333B60892921C46EAE5F38,
	TransformAccessJob_RemoveTransformsIfNull_mACB02AB72B9DBFF24DD5CA93D63995FD48BDA0D5,
	TransformAccessJob_RemoveTransformsByIds_m551E8030E2E18DDAD63024323AB7F39C578AB5E3,
	TransformAccessJob_RemoveTransformById_m7E5E23AD603EEDAC55F4E44041BD91F25D892AEF,
	TransformData__ctor_mED230D1775060C4D1C0F02F7D757436387F20BD2,
	U3CU3Ec__cctor_m263CA5A2082ADE88BA99E13629D6F15368E5EDBD,
	U3CU3Ec__ctor_mAFCC3558764B6784F480CD970D3694414BAF495E,
	U3CU3Ec_U3CRemoveTransformsIfNullU3Eb__23_0_m27BAF4D3A88E142E4676BB5B5C71D256994251E4,
	U3CU3Ec_U3CRemoveTransformsIfNullU3Eb__23_1_mA852FF30AA5A85B02AF3EEB4A8864FE5261AB7D0,
	U3CU3Ec__DisplayClass24_0__ctor_m3E27CACAC8BAAD1F7AD9728819E22297D462F347,
	U3CU3Ec__DisplayClass24_0_U3CRemoveTransformsByIdsU3Eb__0_m4D0B9B8B51621FE0B666D7BA0CC2682BEC0F162F,
	U3CU3Ec__DisplayClass25_0__ctor_m24A1C6E3C75BCB024A71C6B21A7CFDD5DE8EBF77,
	U3CU3Ec__DisplayClass25_0_U3CRemoveTransformByIdU3Eb__0_mB9C07B9E9B096B185B12C59D074BB4C7A58A3F5F,
	LocalToWorldTransformAccessJob_Execute_mA0FF58AB3050E308CF060A21E25F5448F2C987FE,
	WorldToLocalTransformAccessJob_Execute_m35BD603238353EFDA8B1D3249BFB2B662F7FCED5,
	UpdateBoundJob_Execute_m21EBA97DE9CDABC2874AD8C34519A3B4CC83BD62,
	Bone_get_guid_m818AD823C67F82F6B1D2E23739495D3BA3B74ED7,
	Bone_set_guid_mECC3F2B1AFBFB75D5DA16EFC85884C674A46CC58,
	Bone__ctor_mDDEE3E72C5832F240207DE1839E77563E90EE846,
	VertexBuffer_get_bufferCount_mA9148EA31A3CE24C6FFF12ED50D845524865AC0F,
	VertexBuffer__ctor_mEA21AD068678A9D0801D30D129C9797EB9399B68,
	VertexBuffer_GetHashCode_m9C16F129B253B8703D3DEBE488837B7AA1C04E02,
	VertexBuffer_GetCurrentFrame_m4F0295E6AA5510CE7BFB93183ECEBC34BBCFD8D7,
	VertexBuffer_GetBuffer_m5C37AEDF559BFDBCA7688AA13E21C7B612A918DD,
	VertexBuffer_ResizeBuffer_m132FAA5F34E224593C1B388B2FE544A1C42BABF5,
	VertexBuffer_Deactivate_mAF5B69F208E2FF0B0B0AF555C8B9190CB2846230,
	VertexBuffer_Dispose_m85708897CAE4C5391F1E1AF1E61163E1E7A23FA1,
	VertexBuffer_IsSafeToDispose_m738EAB0318ECA75C4DE88C867DF23A47BD4C2A48,
	BufferManager_get_bufferCount_mF1802C500FD0AE5154977E98505E2594DF547CBA,
	BufferManager_get_needDoubleBuffering_m847E54EDC082F5C37FDDC5A222AFFEA08DFE8628,
	BufferManager_set_needDoubleBuffering_mD2D35186E0906DCF7481D12704E06E09666175D7,
	BufferManager_get_instance_m95AECA3426618550F12400165FF6347AA18B65D1,
	BufferManager_OnEnable_mD97F2D77ED2E9ACB415B40F5319B4FAE18573C88,
	BufferManager_OnDisable_mE5FB4A61B68A226026543BF094A0F0F151A4F41E,
	BufferManager_ForceClearBuffers_mA9D740563C66CB4D73FE063E52A4E9518CBB03C5,
	BufferManager_GetBuffer_mF012693FDFC7E1CC5A28B33AB18CEF630F43AAEC,
	BufferManager_CreateBuffer_mBBB61965DFCF31AF0EE60DA8BA6F4302C8140105,
	BufferManager_ReturnBuffer_m736DE3F45BEF786F616874A7C595E2016E394974,
	BufferManager_Update_mBDECC9C5FD62F9CE6C1D8C1D5E369819B171466D,
	BufferManager__ctor_m18C27C56D0CDB3F2B498D19F66562E887F4B3FDF,
	MeshUtilities_GetOutlineEdges_m06213B798862A3E5EE8AD144AAD6615A594C3D16,
	MeshUtilities_AddToEdgeMap_m420B959ECD9102D5B1ED0E93A9765C7ED6373E30,
	MeshUtilities_SortEdges_mE3EE6CD3012C39542E837B24CCC493C91737D5D7,
	MeshUtilities_GetFirstUnusedIndex_m77BD52B67B1266BD78E777DB9128850F404627A3,
	MeshUtilities_AddToEdgeMapU24BurstManaged_mDD61A7116DD6F8A96340F9A96C599D6D9866C482,
	MeshUtilities_SortEdgesU24BurstManaged_mFE2E298908DFC1D1F598D2A4170E1108CF8C77F4,
	MeshUtilities_GetFirstUnusedIndexU24BurstManaged_m09AED0689FF0E09501BE339074F341460C5D544B,
	AddToEdgeMap_00000089U24PostfixBurstDelegate__ctor_mEAE524D40CAB3E6847A988115374972F703698C2,
	AddToEdgeMap_00000089U24PostfixBurstDelegate_Invoke_m41BEE6E86843AEC29ADDF4DABA5A55ACAD5B63D5,
	AddToEdgeMap_00000089U24PostfixBurstDelegate_BeginInvoke_mEFAAB330E460B2EE3B0C550957BB9E60BF7572FD,
	AddToEdgeMap_00000089U24PostfixBurstDelegate_EndInvoke_m17EF1D6162F212FDD11CFF6EC3134D297BBC6E88,
	AddToEdgeMap_00000089U24BurstDirectCall_GetFunctionPointerDiscard_m521C50447DCA7A1131713E0843BFCE0968E2A649,
	AddToEdgeMap_00000089U24BurstDirectCall_GetFunctionPointer_m48E2DACB7D93AAB1AA5EAAE230BF0FF9EC97AD09,
	AddToEdgeMap_00000089U24BurstDirectCall_Invoke_m79F68298347573C323D27AF7CCDCA5858EBEFCB7,
	SortEdges_0000008AU24PostfixBurstDelegate__ctor_m5A1D647E1F5D38541B855E6D6BB33BBA63B3FACB,
	SortEdges_0000008AU24PostfixBurstDelegate_Invoke_m857A4F5C17C6A56A1B035ACD2A2DC4D3051CB1E2,
	SortEdges_0000008AU24PostfixBurstDelegate_BeginInvoke_m8D6F3A090C738D7E2646669C7C22BA7ADFE6D47F,
	SortEdges_0000008AU24PostfixBurstDelegate_EndInvoke_m78A9FA3F3B6718EB21CB65B63BD1DA985EF30AFE,
	SortEdges_0000008AU24BurstDirectCall_GetFunctionPointerDiscard_m5905FD953645041195AF23B2090B098967A05B1C,
	SortEdges_0000008AU24BurstDirectCall_GetFunctionPointer_m4FAA4CEABF8E902E4B2CA76B659855CA65C9A5EB,
	SortEdges_0000008AU24BurstDirectCall_Invoke_mE123FAFD29C75A735AFB20A6CBEAC348BA1D58B6,
	GetFirstUnusedIndex_0000008BU24PostfixBurstDelegate__ctor_mBF1794C82652699BE075E69D18F4AE5073292E49,
	GetFirstUnusedIndex_0000008BU24PostfixBurstDelegate_Invoke_m39656317B17BAC5C0111965DEDB72B5CEAC1A5D8,
	GetFirstUnusedIndex_0000008BU24PostfixBurstDelegate_BeginInvoke_m30DD0A8E357AE0D705FF12AB225DE2886B445106,
	GetFirstUnusedIndex_0000008BU24PostfixBurstDelegate_EndInvoke_mCFFB394AD9BF9C0E1046F7419EA903CE3F57DCA9,
	GetFirstUnusedIndex_0000008BU24BurstDirectCall_GetFunctionPointerDiscard_m51FC9528D91BA023256307E857597494CEFACB02,
	GetFirstUnusedIndex_0000008BU24BurstDirectCall_GetFunctionPointer_m6C0988D99DA70B49F8EC8D2BBBAC1861CD95DC30,
	GetFirstUnusedIndex_0000008BU24BurstDirectCall_Invoke_m8191DD31B5E2DB7771369DD143A7C4D119453F50,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SkeletonAsset_GetSpriteBones_mB5BEEFDA9FA91A705235D2365BF06CBA2748A9B1,
	SkeletonAsset_SetSpriteBones_m09911B5B1FFF5C4543E9390E6EA6A0F5B9F605D9,
	SkeletonAsset__ctor_mF10C7DA596BD26853FB8C7097B1652B32CFC71F2,
	NULL,
	NULL,
	NULL,
	NULL,
	SpriteLibrary_set_spriteLibraryAsset_mF680D67985B9B1728BF541DEE86B6AE6F05F458B,
	SpriteLibrary_get_spriteLibraryAsset_m9D80093A7507E7B5A38A35CA189841D255C58CFA,
	SpriteLibrary_OnEnable_mDE2E64C3D31D88F5B35613AAB359A3F2747EEA4A,
	SpriteLibrary_OnPreviewUpdate_m3F6F098FC434551AD6A804A1DCF04CA40466A84B,
	SpriteLibrary_GetSprite_m5C77C433511DA5BE2CAAF5B101E01B23FF8DC3C9,
	SpriteLibrary_GetSprite_mCCA7EFC5B4AA98F93CD16F8443F0B9F39FC14B63,
	SpriteLibrary_UpdateCacheOverridesIfNeeded_m676861DDDF9E45EDD095498C2AC988D1201B6DEB,
	SpriteLibrary_GetCategoryAndEntryNameFromHash_m0B1D65FE0944115D41BA1CBFE003430C49FEE66F,
	SpriteLibrary_GetHashForCategoryAndEntry_m4E20584B1788AB652371A7C648D9003B2E5B4871,
	SpriteLibrary_GetSpriteFromCategoryAndEntryHash_mB44061DF02FBA182EDB058B64E7FFDFDD78AFC3E,
	SpriteLibrary_GetEntries_m139AEC5801BA2B44525EBF02447CED8C9B3931EA,
	SpriteLibrary_GetEntry_m17D299BC2C1CBF68DD3E1F3335148F5735613161,
	SpriteLibrary_AddOverride_m700FE75CA3981C30B00F75F105EBE27C0A74B8E1,
	SpriteLibrary_AddOverride_m15621ADB67666F5C352DAC3A85557C7014BB3C2D,
	SpriteLibrary_AddOverride_m37A60FB18F9F037B7DE8F601983EE412F1146316,
	SpriteLibrary_RemoveOverride_mB15E1BF611EA39D9115D73C621D41167CDC67D4D,
	SpriteLibrary_RemoveOverride_mE77657B920C7303A0633B20DFD48BDB912799F53,
	SpriteLibrary_HasOverride_m8B0B705A58B82794056EDFF17AC151ABA809CF82,
	SpriteLibrary_RefreshSpriteResolvers_mD0866A099A97456E1EE0D07F45712EFA695AE0B3,
	SpriteLibrary_get_categoryNames_mEB52259DFC9FEE284E492964B2029978A549EB79,
	SpriteLibrary_GetEntryNames_m28E8ED9507751401104E3886AB94362EF6CDD2F6,
	SpriteLibrary_CacheOverrides_m59FC3CF018930CFD09BAAB93520291E59957A52A,
	SpriteLibrary__ctor_m33873F5DE5B7FD5B6A0906AFF0D48CFD74AB3C75,
	U3CU3Ec__DisplayClass18_0__ctor_mD7D88F1DD5B26197EDA8E4B190CDD38F72C27117,
	U3CU3Ec__DisplayClass18_0_U3CGetEntriesU3Eb__0_m2D1B2177E273A2ED25E6D39E46CA52247E764650,
	U3CU3Ec__DisplayClass19_0__ctor_m219436D5725E3100288824D6A924FB290865EC39,
	U3CU3Ec__DisplayClass19_0_U3CGetEntryU3Eb__0_mDF5C1426DFB598E77FE7514733B7B5B314BFF93C,
	U3CU3Ec__DisplayClass21_0__ctor_m2847E84FDC199E452F40D0A3030D20925C66F2A8,
	U3CU3Ec__DisplayClass21_0_U3CAddOverrideU3Eb__0_mD6C66DE4144F495C8454E13373664220751FDC2D,
	U3CU3Ec__DisplayClass23_0__ctor_m7994B72A1A84966296F8C335A3D21386A2F55D03,
	U3CU3Ec__DisplayClass23_0_U3CRemoveOverrideU3Eb__0_m9694B8FC076E116B98960B7BDFC120CC4B7724BE,
	U3CU3Ec__DisplayClass24_0__ctor_m1C01B71D76C984EE5C21B0E73BD8CD598C4FDC57,
	U3CU3Ec__DisplayClass24_0_U3CRemoveOverrideU3Eb__0_mD1CD99B272F301672AFA96020794742BAC121674,
	NULL,
	NULL,
	NULL,
	SpriteCategoryEntry_get_name_m66ACAEEC10BCCEE30D949CC399B55CA0C52120A5,
	SpriteCategoryEntry_set_name_mD7CB900F1FD90AE56EE92A2EF1540F85DC56B695,
	SpriteCategoryEntry_get_hash_m2A825DA45BF3FAFD2E082C91918E86F13888A1BF,
	SpriteCategoryEntry_get_sprite_m18219ADE134A806405B5C9E96C9C1552691A32A7,
	SpriteCategoryEntry_set_sprite_m119DC38091B5187315EEC2D2B2E5DB687EA2F15E,
	SpriteCategoryEntry_UpdateHash_m54A35B15FF1E60129599DF038406F44AA5897F9F,
	SpriteCategoryEntry__ctor_m28B160C4B16BA186FCF516C280DE63B057728D5A,
	SpriteLibCategory_get_name_m45C90D2115940247F0DB13F7564710280B6E4BE6,
	SpriteLibCategory_set_name_mEF802FFE9E78B67130C0A5F31A29DA79390B4653,
	SpriteLibCategory_get_hash_m2C0258EE015F53D8FF4912AAF9A735D2E8FDF88E,
	SpriteLibCategory_get_categoryList_mC734220C15AC1827A5986AB305EE50FC8F05D47B,
	SpriteLibCategory_set_categoryList_m7846E2C8DEF3F16803FF0AB50F58AD149F9E4D18,
	SpriteLibCategory_get_labels_mED08AC6160D594C38F542D9656434744D7AD2BD5,
	SpriteLibCategory_UpdateHash_mA2C536614AD08346369B01956A47539EA52978E2,
	SpriteLibCategory_ValidateLabels_mDECC12291A94847ACD565888E240535C4E364FF5,
	SpriteLibCategory__ctor_m10E30F11B96B0168EDB8A0AFB0BD6BB95B657137,
	U3CU3Ec__DisplayClass14_0__ctor_mBE0495632BE0F5B9C9C1736155CE3286F8E3EDBF,
	U3CU3Ec__DisplayClass14_0_U3CValidateLabelsU3Eb__0_m70F2F08CF214474DF45D044FB0A747C62781DC71,
	SpriteLibraryAsset_CreateAsset_m07E0AD1BEBB60FBE0789EAA4AAB7090A0E0C950C,
	SpriteLibraryAsset_get_categories_m805F6614A090CAF1A6A4F4FA43FCA6D0AD23B164,
	SpriteLibraryAsset_set_categories_m22CC07C954B274364246B358B502769CF77B5483,
	SpriteLibraryAsset_get_modificationHash_m98486690892C30EAA2D25CC182B602AFF41B2045,
	SpriteLibraryAsset_set_modificationHash_mD493105735F7191590EE86AA3F20C0E7367633D8,
	SpriteLibraryAsset_set_version_mB5B478A815C1605D380E8E1665DE0451787AF5A2,
	SpriteLibraryAsset_OnEnable_m90AB0E2D8463F567E5FB96039DD4F61F0E351E80,
	SpriteLibraryAsset_UpdateToVersionOne_mEDFDF05E4A10DE93C72FCB4AA0E7A1CA6B58AD97,
	SpriteLibraryAsset_GetSprite_m7745DBF8D7E54E5C96FA0958A1BCE8ED3E6A3707,
	SpriteLibraryAsset_GetSprite_m77BF394F0DBE95FCF6D11EA29EE216C7E364FF3C,
	SpriteLibraryAsset_GetSprite_m3CC5631691BB2D4A17C3AA69EDA39E29ADDC3117,
	SpriteLibraryAsset_GetCategoryNames_mDC1C7CE8EE93F40912E520C83E34AECB24132C67,
	SpriteLibraryAsset_GetCategorylabelNames_mECE4A546FA407607907AE7E4E6CB0E4E75B3176D,
	SpriteLibraryAsset_GetCategoryLabelNames_mE46A4E9A6F5C7787BD6C9E1DE05D894D6791F5BE,
	SpriteLibraryAsset_AddCategoryLabel_m7DADA570B01CE3F4E51E52938115763182EDCA74,
	SpriteLibraryAsset_RemoveCategoryLabel_mE7D5FB794EF48CCAA594B341F71DE3E6FDE9A982,
	SpriteLibraryAsset_UpdateHashes_mF4B36517B473EC071F2563EA85B4A24130349ECD,
	SpriteLibraryAsset_ValidateCategories_mB2D79C54047176864E6715A7915D4416A6177E77,
	SpriteLibraryAsset_RenameDuplicate_mCF8C5A8B5EEF7C245DD5D985ED05329AEC3042C0,
	SpriteLibraryAsset__ctor_m212C254CA1539AEB7F326C970CAD165BC2567E52,
	U3CU3Ec__cctor_m13C6A7D3054C7DA2A8F43B21715FE96A22A1DAEE,
	U3CU3Ec__ctor_m2A27E85D0CF4F1BD149847EF987CA2AC82EAE31A,
	U3CU3Ec_U3CGetCategoryNamesU3Eb__17_0_m1AF70B25CE197815DB5367646846B6B3CC5F6984,
	U3CU3Ec_U3CGetCategoryLabelNamesU3Eb__19_1_m8360D3D1EFC2F4468EECA0986E993DF7C1758DB2,
	U3CU3Ec__DisplayClass14_0__ctor_m2BB38B03421C1CCD8EA26C3C73296B8CF20004E4,
	U3CU3Ec__DisplayClass14_0_U3CGetSpriteU3Eb__0_m9AB0F17FC579FACBCF12339014701D06402227BF,
	U3CU3Ec__DisplayClass14_0_U3CGetSpriteU3Eb__1_m59F76293A88FDF97B30C2BF1B461B34C410E8E08,
	U3CU3Ec__DisplayClass19_0__ctor_mEA3B5B3F8540C52131188616AAFBB993CA6E18F7,
	U3CU3Ec__DisplayClass19_0_U3CGetCategoryLabelNamesU3Eb__0_m1C0BC7A7B264905F502400E16616FF8B1899AA87,
	U3CU3Ec__DisplayClass20_0__ctor_mD076FF66F48C69DB96AA2369E8DE201EACF086F2,
	U3CU3Ec__DisplayClass20_0_U3CAddCategoryLabelU3Eb__0_m97F7F63F1375CA7F87FCDEE5E4A90F763DD46A81,
	U3CU3Ec__DisplayClass20_1__ctor_m18EA61E3DD98E5896ABBB1867788CD648A552BAF,
	U3CU3Ec__DisplayClass20_1_U3CAddCategoryLabelU3Eb__1_m9CD7C22A923BD8E8AF96FED5C277AD95224ECCF2,
	U3CU3Ec__DisplayClass21_0__ctor_mA9EE18A5D2DEBC6B5545C4089C4972650102CA5A,
	U3CU3Ec__DisplayClass21_0_U3CRemoveCategoryLabelU3Eb__0_m38BF15789DC696F1F95197187815CCCAD44354E2,
	U3CU3Ec__DisplayClass21_0_U3CRemoveCategoryLabelU3Eb__2_m2BE89738D8BCE4D0BC0FE38076D08D109E8B8C16,
	U3CU3Ec__DisplayClass21_1__ctor_m304B37DF4528DB1E3916F871D6FA659A76425C54,
	U3CU3Ec__DisplayClass21_1_U3CRemoveCategoryLabelU3Eb__1_mF68EE2630C9C48894D5C946E3C28AE5C94E0A399,
	U3CU3Ec__DisplayClass23_0__ctor_m2468B81C090BF10DB882E9F6D70F458689B2C08C,
	U3CU3Ec__DisplayClass23_0_U3CValidateCategoriesU3Eb__0_mAD4E5E040D640ED83559697F58E7893F23DD43EC,
	U3CU3Ec__DisplayClass24_0__ctor_m3A888061943B8650679A99963D98C24B400F1011,
	U3CU3Ec__DisplayClass24_0_U3CRenameDuplicateU3Eb__0_m3EF463D99D9086E01DBFA5BCE5BED140B89E46D3,
	U3CU3Ec__DisplayClass24_1__ctor_m007D14B377E8CB7DB25E9CF5C97DD248171E3ABE,
	U3CU3Ec__DisplayClass24_2__ctor_m2F7BB0C664F257A13B3BDBA0C2D6AFDF83196FCF,
	U3CU3Ec__DisplayClass24_2_U3CRenameDuplicateU3Eb__1_m546BE213E0759E46BD8E4125CFC9E9294C5F8F1D,
	SpriteCategoryEntryOverride_get_fromMain_m1A72A3A0D8918C14F67675DD5D9BDA9E53643795,
	SpriteCategoryEntryOverride_set_fromMain_m645BA5FF8B8418DF6E8D4B98EDCB5BF6AEA45F9D,
	SpriteCategoryEntryOverride_get_spriteOverride_m72C69204374EDFC107495800DF8CA1A5FDB353E0,
	SpriteCategoryEntryOverride_set_spriteOverride_m2E7069C45FB24DC6C478D15D090BD467FA9F85EF,
	SpriteCategoryEntryOverride__ctor_m45663A814AB4FBA333389E08269E1A9F7EA499D3,
	SpriteLibCategoryOverride_get_fromMain_m8B74389DA47A85D96F0BFDB9744A26EE5BEF8E6A,
	SpriteLibCategoryOverride_set_fromMain_m68754BA07488A714A2F5374AF73BB1CCAA2055E7,
	SpriteLibCategoryOverride_get_entryOverrideCount_m47C53A728BF0189527C6F6D253318D31808B0447,
	SpriteLibCategoryOverride_set_entryOverrideCount_m54B9EC134267C1E75B02F4CA0BD116142C2FDCAA,
	SpriteLibCategoryOverride_get_overrideEntries_m685E64A7AC3A7905CDC4AC0ECE1F61AB68789437,
	SpriteLibCategoryOverride_set_overrideEntries_mBC1DF5FEEE1187B0576FFF6B8BEA952C54159D15,
	SpriteLibCategoryOverride_UpdateOverrideCount_m5286A9F13A045B7706F0DD861BA13DA7DE730068,
	SpriteLibCategoryOverride_RenameDuplicateOverrideEntries_mF0A118922C59E9365307866A26DD3201C82470D2,
	SpriteLibCategoryOverride__ctor_m646772A73EBAAA8C8F195B4F0D38292EDADF165B,
	U3CU3Ec__cctor_m9F6C212B1F14452D289EFAFED0A2FE90D06E6879,
	U3CU3Ec__ctor_m069B6644653BA574C1A1F8437032659E00E79C21,
	U3CU3Ec_U3CRenameDuplicateOverrideEntriesU3Eb__13_0_mB2DD873D90A91EA245F8CEFEBD3EFB3246DFCD52,
	SpriteLibrarySourceAsset_get_library_m96741941C63C9769E4402CD42C08FB9131603F29,
	SpriteLibrarySourceAsset_get_primaryLibraryGUID_mC4CA3914F378D4459C481B5CDEB5A8D4E1C6C52F,
	SpriteLibrarySourceAsset_get_modificationHash_mAE26BF2E1E03ED09BFF4AAFC90D775CFD1FC6593,
	SpriteLibrarySourceAsset_get_version_m1FCBACC69BED6061CE67E29C1E933614FA062BE5,
	SpriteLibrarySourceAsset_InitializeWithAsset_mC46EE140306FE7227422DC64C860D8AB4C6CD32A,
	SpriteLibrarySourceAsset_SetLibrary_m1885B3DE115CB86B9DAA295BF0C08DAA5F4AAD5A,
	SpriteLibrarySourceAsset_SetPrimaryLibraryGUID_mCC566DBF2C4FDCCFFE5B3330EA7A2BB58E8AF17D,
	SpriteLibrarySourceAsset_AddCategory_mEE8EFC4969198627428E6FE1B9DEEDBC91684458,
	SpriteLibrarySourceAsset_RemoveCategory_m42A98ADF5B0C9A316A27829AC26008577578B127,
	SpriteLibrarySourceAsset_ClearCategories_m7495F048F4F04DA916096A3AE1DE16D45DAD9690,
	SpriteLibrarySourceAsset_RemoveCategory_mBF27FB65C02578167D0DA94CFDEA6706566261A6,
	SpriteLibrarySourceAsset_UpdateModificationHash_m54A57D3C85EABB0F9EB7E21191654FAB50FC580B,
	SpriteLibrarySourceAsset__ctor_mC0690E2F4FDBCF4A9C25C2CA79F99FE4B49216CE,
	SpriteLibraryUtility_Convert32BitTo30BitHash_m1D416A50DFA53B54E09F9AEDF3CA12EEAA9DD40E,
	SpriteLibraryUtility_Bit30Hash_GetStringHash_mA295C62C1832B95858F3EEC077FD40C3C8CA9901,
	SpriteLibraryUtility_PreserveFirst30Bits_m9E5594A3CA7AC0DE58826845C3B6D64344FA6FD6,
	SpriteLibraryUtility_GenerateHash_mAA7ED0100867D1851451E7844C0E71CE6C997645,
	SpriteLibraryUtility__cctor_m114F9A7DC2AA90D59A60EF396C6B76BCFBC95B54,
	SpriteResolver_Reset_m40816C131437BA0E5A988A85F15EC39ABA78A9CA,
	SpriteResolver_SetSprite_mCAA900EAD3933CC681F2BC3CA4984495F8B99733,
	SpriteResolver_OnEnable_mB7614C61A30904A715380C82F5AA977DC172C053,
	SpriteResolver_InitializeSerializedData_m09CC228C2D760A0ECC46D1BE437AA38B6A0CE558,
	SpriteResolver_get_spriteRenderer_m56B5B46BDA69583C7CA2BE62163A7139FECF92C1,
	SpriteResolver_SetCategoryAndLabel_mF7A1451BAF1B4B6E6406C0FF817E43DE55BE47C9,
	SpriteResolver_GetCategory_mE85FF7205CDD9ACC3D33116C5D7CEE32837812B0,
	SpriteResolver_GetLabel_mDCC61753D0AC00153BC9F606DD0EE0A931C37134,
	SpriteResolver_get_spriteLibrary_m013ED298B4EF93D427312A0A0CA5EDA08AFFF3B9,
	SpriteResolver_OnPreviewUpdate_mA50A78C9BD39FE190EA3DEC1C1CDE4CD3C6D971B,
	SpriteResolver_IsInGUIUpdateLoop_m3AD92DE12C61D45FAC8D9CDB2AE724C74D89F96E,
	SpriteResolver_LateUpdate_m272EB3E4332D2C5967A9940449B73C32A62D95FA,
	SpriteResolver_ResolveUpdatedValue_m2311F2A314FE514BA4042C97C7A5183A2B9C6E78,
	SpriteResolver_ConvertCategoryLabelHashToSpriteKey_m95401386E8801B6E868EBC6E40A01D09B59A1892,
	SpriteResolver_GetSprite_mFFCBA23CE0B805478F38D6895D31732AD461881F,
	SpriteResolver_ResolveSpriteToSpriteRenderer_mA32F58B657B82237302157D11521A47180346462,
	SpriteResolver_OnTransformParentChanged_m0D7E5D476DC0D93A0897828A1CB713C4BCC9979C,
	SpriteResolver__ctor_mEE72E98F629568B1773423F089108BDED9F832C4,
	SpriteSkin_get_boneTransformId_mE1A8085BFC05772E0674888AA677DD505A8853A6,
	SpriteSkin_get_rootBoneTransformId_m5E71C241EB39B63C6574C22E4A8D3756F8471071,
	SpriteSkin_get_currentDeformationMethod_mC6ECB18C21A66DC0ECE49F9909DCAB3025445F3F,
	SpriteSkin_set_currentDeformationMethod_m397540722EEFD0105FAA39FB267E69BDEE34B998,
	SpriteSkin_get_deformationSystem_m0A177888E1EBBE3ECAC9813FE337821FF8D3A3D4,
	SpriteSkin_set_deformationSystem_mCDDEF1841F603869B17C7D58C3F56EBA5EB1ECA1,
	SpriteSkin_get_outlineIndices_m81C096423C1F78B36A3EB22E90E1B4BA0125A0C4,
	SpriteSkin_get_outlineVertices_mFA10B556E9F0CB7B3F3E64F10E4DA273FF7A0F29,
	SpriteSkin_get_vertexDeformationHash_m51F41C75A00FBE5A25CF97DE23E50B6A9B08DCE4,
	SpriteSkin_get_sprite_mF15B0997564F1606CF54BABBE096459267570BDA,
	SpriteSkin_get_spriteRenderer_m6BA6C5145F499FE6E718CE0C5BAE27450A9BDC80,
	SpriteSkin_get_spriteBoneWeights_m3F8CC9F85725ABD50DBB038E98869B86A53B0D73,
	SpriteSkin_get_dataIndex_m0254FD14560F5DBE6AF5D3F2C195E19C24A89FA6,
	SpriteSkin_SetDataIndex_m887214CCC9BDD1F1A65A6710A1071308BADFA67A,
	SpriteSkin_get_autoRebind_m95820D1D14C64B25C58D2781C2D25556C75B5968,
	SpriteSkin_set_autoRebind_mDEE587A61DA9E162B6DF4B00DD50948493A04ADE,
	SpriteSkin_get_boneTransforms_mD8CC46A5BAECECFEF8EE686CBE3C6E23569EE1D9,
	SpriteSkin_SetBoneTransforms_mF36BC9130E34C6EAAFFF2F183A2DB782F66DFBB7,
	SpriteSkin_get_rootBone_m68D6479EC449C6C0FD1F4D3C552C3D06480A6599,
	SpriteSkin_SetRootBone_mBF809803C1F8016AEB052A627064150503C8CB1F,
	SpriteSkin_get_bounds_mF17F41D61D122A6837D953521CE8F88ED45F163C,
	SpriteSkin_set_bounds_mE4789ADF55BD07D4824B4EB8150521C08C95AD26,
	SpriteSkin_get_alwaysUpdate_m06F0BCB5A2CC28CC60CDB215F75D753264D2D3DE,
	SpriteSkin_set_alwaysUpdate_m215C1C335A1E837281E2F194FFABE59465A87B7D,
	SpriteSkin_get_forceCpuDeformation_mC107F5E5B1A34D7B409F59E0EB7A18AFB3622F03,
	SpriteSkin_set_forceCpuDeformation_m27ED41A9A550E126ED9438CA839D9CCBD1C0ECD6,
	SpriteSkin_ResetBindPose_m3C817783DA7E7E445CC2761FBEE67A63584B079B,
	SpriteSkin_get_isValid_mE36B48E6DBC4AEEF21E73F6EC1FE5F01C35E4AF9,
	SpriteSkin_Awake_mF6D527563052A0C49F3E18D906B13C32DDB9C855,
	SpriteSkin_OnEnable_mC03816C41AF28D93A959C09C69A446824CBB5ADA,
	SpriteSkin_OnDisable_mB3730C1F02BC3C78CBCEA37BC5957513A958D1F2,
	SpriteSkin_RefreshBoneTransforms_mACF09172532DFD37E9933D8F51D12211DD8C9D2E,
	SpriteSkin_OnSpriteChanged_m1A73B3958E8FAB380FFF668314D4C6E8FDF5A79A,
	SpriteSkin_CacheBoneTransformIds_m2BAC9C1CF292F340B30E9015F1F67DBE87D1CB41,
	SpriteSkin_OnBoneTransformChanged_m9B1716380B1EAE284F29B44DA3CEE52C3C87BA84,
	SpriteSkin_OnBeforeSerialize_m89975D54F2D66674A64968A7A5705D6CB73DB758,
	SpriteSkin_OnAfterDeserialize_m4EB0A78A7FDD574FD12E092415EDBA462F44219E,
	SpriteSkin_OnBeforeSerializeBatch_m02C4FB69D476968C7ED6A6F25783735BD90FD74A,
	SpriteSkin_OnAfterSerializeBatch_m9CAB751D9E6A23B41F7725FFF0F26AED7477421C,
	SpriteSkin_OnEditorEnable_mA5B23CEB3CD53064F6FFDF750BEDD9DECAA20911,
	SpriteSkin_CacheValidFlag_m31EF3C3454A24531F688858F239D7FC5C9E2AE7A,
	SpriteSkin_BatchValidate_m2467FF2FDD5BEDD85C5B9798BD5CC4FC28B79394,
	SpriteSkin_Reset_mD2D7C8A74A75AA2977A25B498C30DE234E4CF4A0,
	SpriteSkin_ResetBoneTransformIdCache_mAE195FF805BCC26229DE618F3288B753E08BB69E,
	SpriteSkin_GetDeformedVertices_m02ABE856F01A596C406A71F9D64645783A23177C,
	SpriteSkin_HasCurrentDeformedVertices_m8B65CAF8F18F2B1263CECD6CA67E50587BE65AB3,
	SpriteSkin_GetCurrentDeformedVertices_m4B6CC2B08DCA4FFBA7DFDC7606CFE6C88B1EB8E2,
	SpriteSkin_GetCurrentDeformedVertexPositions_m3303010762D2A175E99257A463B8F4356D09F7B7,
	SpriteSkin_GetCurrentDeformedVertexPositionsAndTangents_m2F9A8FF3BFBAE465DB5FC870155EEC8103CCE995,
	SpriteSkin_GetDeformedVertexPositionData_m86AD284E8E717D3557DDAD8F735AD5412A076103,
	SpriteSkin_GetDeformedVertexTangentData_m272E4265DD563DD8572D2405C77EF9844D3029BA,
	SpriteSkin_DisposeOutlineCaches_m62915E89AEB6B9251CFB47936BD18B20DBA44A03,
	SpriteSkin_OnPreviewUpdate_m82A0E8A1917E38C891E71A9DD421A21B04FB2369,
	SpriteSkin_IsInGUIUpdateLoop_mB02E1A31856C8B26AA8D64730A261A802B8929AF,
	SpriteSkin_Deform_m507374A93373F84548AE95DDE450AD5539D3CCA6,
	SpriteSkin_PostDeform_mD3FFC2298A5DF1662F13DFEFEB4E6AC4502639CB,
	SpriteSkin_CacheCurrentSprite_mDAAC09263A952D94E544BD8A01DDDAF7142C682B,
	SpriteSkin_UpdateSpriteDeformationData_m4566B0074A11F7EAB13098D94668FE0766EC95E3,
	SpriteSkin_UpdateDeformedOutlineCache_m4C02FF6FDAD422DB30FC3625B69D17981C00C9A5,
	SpriteSkin_CacheSpriteOutline_mF4FE2F3EF5698D1DFD63BF2E973D641B251DD74D,
	SpriteSkin_CacheOutlineIndices_mD4ECE2DEC9551CF22A503870266CA5D663317088,
	SpriteSkin_CacheOutlineVertices_m268578A339E1676B1690ECC00F81EA4233013FD0,
	SpriteSkin_CopyToSpriteSkinData_mD059DF7D8F7695A6740798F7A5B8AFC083C98C8D,
	SpriteSkin_NeedToUpdateDeformationCache_mD0634471F66B27F0D502296F1C824269EFC786BF,
	SpriteSkin_CacheHierarchy_mC0A030A1A61012989B7E51086BAD22D95A56079B,
	SpriteSkin_DeactivateSkinning_mE3A93D904D7FD085827A85326A877E2CAAC7EE02,
	SpriteSkin_ResetSprite_m06E181F4DB7BFA9099DA1A453DC3CC6835D7F1F1,
	SpriteSkin_SetDeformationSystem_m6512B097A1FF4511AFDEAD456F0971E85AB4AEF3,
	SpriteSkin_CountChildren_mF12E34775BCD805D3F7090212A0AFB00B8EEF7AD,
	SpriteSkin_GetNewVertexDeformationHash_mCF8641139FA856721622F27CDB5FD3C884C3164B,
	SpriteSkin__ctor_m68477AF9394FB0E996C4A6208E327B3683FA6AC5,
	Profiling__cctor_m71142E4CC419BC908E7DCB53FB855DC6F1AA40B2,
	SpriteSkinContainer_add_onAddedSpriteSkin_mAA0920845F49D797080974592F7D69C0D844CE60,
	SpriteSkinContainer_remove_onAddedSpriteSkin_m680B6655B0191987E5510758805E947B1BED623C,
	SpriteSkinContainer_add_onRemovedSpriteSkin_m5EF7E123048AFA763F47E126092CCC5D5FF2C585,
	SpriteSkinContainer_remove_onRemovedSpriteSkin_mD8504216A4B3736732E99B44805DB5609FAB3060,
	SpriteSkinContainer_add_onBoneTransformChanged_m9F21B24CFF7B74185CBE9A08364C5347D4737A78,
	SpriteSkinContainer_remove_onBoneTransformChanged_mCDBDEB0E2BDE0A98F81B1C4F6A8480CA28B3E4EF,
	SpriteSkinContainer_get_instance_mA9D39D50D0CD9377802D2852FDED53BC366DA9B7,
	SpriteSkinContainer_get_spriteSkins_m3EB584921E931EB48328EE5DC94493658E76E3CE,
	SpriteSkinContainer_AddSpriteSkin_m428AAEB741AA1C442730A7451E66E2AB706737FA,
	SpriteSkinContainer_RemoveSpriteSkin_m8C42A7ED34E08FA91A0EFDD5EDE24DA4E1A53EF1,
	SpriteSkinContainer_BoneTransformsChanged_mCFA94288D68E29EB82F349E236E79590CC2C74E3,
	SpriteSkinContainer__ctor_mEC32A7C89375784FB475323F9C520A63FA650EBD,
	SpriteSkinHelpers_CacheChildren_mE6DF934082EEF2A4B6779885C6F7A4A710E4B5A8,
	SpriteSkinHelpers_GenerateTransformPath_mDC9A42EEBFAC4F7E67159740F8C53DB09DBAA063,
	SpriteSkinHelpers_GetSpriteBonesTransforms_m1EB9C5D67C66A91168CA2B580CD26608A262EF39,
	SpriteSkinHelpers_GetSpriteBonesTransformFromPath_m273A6B44C759665490D2E6A63E1736F137A9E55F,
	SpriteSkinHelpers_CalculateBoneTransformsPath_mC5CDED70F116BBDC82E10964DC7ECD0E23D3F4F1,
	U3CU3Ec__DisplayClass2_0__ctor_m5D3EE163C1900F1C79E0A396F3C982F1C0818F59,
	U3CU3Ec__DisplayClass2_0_U3CGetSpriteBonesTransformsU3Eb__0_m4BA4D4C40D9328B5998954A9F4DFB8F439400068,
	NativeByteArray_get_Length_mCBB70CAA06A6248216B787273A280F23B67707C7,
	NativeByteArray_get_IsCreated_mADB5E11A5FE061E98672AAC451A5AB294E051031,
	NativeByteArray_get_Item_m5A5D5F59F4B4BACE3123324D6A4D64D4249F9DD4,
	NativeByteArray_get_array_m6DD098B95665876510BC0FB925B47989B33D02DE,
	NativeByteArray__ctor_m279E3142238F49166ADCEBA75E17B6BA931BFA81,
	NativeByteArray_Dispose_m012B713339FDE3095C9C92F8C1A58827ED9129F1,
	SpriteSkinUtility_CanUseGpuDeformation_m1AFFD12E08C3AED27103C79B287151D48F049D25,
	SpriteSkinUtility_IsUsingGpuDeformation_mF50D05BFCC9B1AC8DDD49DA47A83708B2E041944,
	SpriteSkinUtility_IsGpuDeformationActive_mCCB150114CCFAF90F225B64C857E735D225978CC,
	SpriteSkinUtility_CanSpriteSkinUseGpuDeformation_mBC783FE2A8827BB255C26BA9376AA5134C6079F3,
	SpriteSkinUtility_Validate_mC31EB7E41D972DD369D497BA36E2E32CB5A4E369,
	SpriteSkinUtility_CreateBoneHierarchy_m5388397FC3E4D2A56B8D6534D9179B8CEBF3D85E,
	SpriteSkinUtility_GetVertexStreamSize_mA431E0E2846881F30F60D36D40D2E3B2ACE651F8,
	SpriteSkinUtility_GetVertexStreamOffset_m8B47F6747D2C64AFA32325A63962C52AE5150743,
	SpriteSkinUtility_CreateGameObject_mE070DBC6CE5E164E53FA6B3B8F69F226F534334E,
	SpriteSkinUtility_GetHash_m3EB3A276AB986508F0E6876045E363B185B60A4D,
	SpriteSkinUtility_CalculateTransformHash_mA3BE1DDA36921D2CFC68814E1215EB774E0CB3DE,
	SpriteSkinUtility_Deform_m559F3F0AEE4E775B0774706B643A4492DE6904B0,
	SpriteSkinUtility_Deform_mB62EB14E78CE661C661C59B5905AD92F265B8AB5,
	SpriteSkinUtility_Deform_m4A6CF4CFC4939F9BF405E008069E98AEB3D316FE,
	SpriteSkinUtility_Deform_m0DD5368630CA0379ACAEE40DAD4F68743B43F10C,
	SpriteSkinUtility_Bake_m5CF1EAAD01CB2B764CA39C649F66DA83A889F2CC,
	SpriteSkinUtility_CalculateBounds_m340DD259C2CCC217F1D2332AEB07E8D6E8F3A73E,
	SpriteSkinUtility_CalculateSpriteSkinBounds_m99D083830A48F235E94E56283A56848A0A9659B0,
	SpriteSkinUtility_UpdateBounds_m9AE2780487507192D3150091AD31C6BCBD78CCD2,
	BurstedSpriteSkinUtilities_ValidateBoneWeights_m5E7F9120E9D22F0A097DD5B296BE3E9336D45017,
	BurstedSpriteSkinUtilities_SetVertexPositionFromByteBuffer_m6D9016B821006BB68DA3C0FEDAD7238BB18A287C,
	BurstedSpriteSkinUtilities_ValidateBoneWeightsU24BurstManaged_m21CC2B3A2D0773D4D06FC7A380277502C103AC44,
	BurstedSpriteSkinUtilities_SetVertexPositionFromByteBufferU24BurstManaged_m6AE31BDEE33E5E3FD3E2EB37F37A400EBF4FF45C,
	ValidateBoneWeights_000001B0U24PostfixBurstDelegate__ctor_m4DA9B7B1554BAA889120835762BFE4FE3E590A04,
	ValidateBoneWeights_000001B0U24PostfixBurstDelegate_Invoke_m33E5BBEF21BB1C75A925DC7F07BB6C8E179EF024,
	ValidateBoneWeights_000001B0U24PostfixBurstDelegate_BeginInvoke_m46425994A8CC6A5B1915F907F74149971190BBCD,
	ValidateBoneWeights_000001B0U24PostfixBurstDelegate_EndInvoke_m702E9AA1472077E017794A1D060D489DD51B9CB4,
	ValidateBoneWeights_000001B0U24BurstDirectCall_GetFunctionPointerDiscard_mC1DADDCD3AA5364C1342E7F32DD31916D5076EDF,
	ValidateBoneWeights_000001B0U24BurstDirectCall_GetFunctionPointer_m1A223574762C317CAD0D5D9E92265C2A24BB130E,
	ValidateBoneWeights_000001B0U24BurstDirectCall_Invoke_mB65F58FC212A921DE77B913869F7B260A05F7DF2,
	SetVertexPositionFromByteBuffer_000001B1U24PostfixBurstDelegate__ctor_mBA084804138B82C11C3084C8095E4D2A5D3DCF32,
	SetVertexPositionFromByteBuffer_000001B1U24PostfixBurstDelegate_Invoke_mEFFEC74EB72A7888C1554F0B0E25A97BAF8BC865,
	SetVertexPositionFromByteBuffer_000001B1U24PostfixBurstDelegate_BeginInvoke_mCD0A93729E4E61708C646ACB8B74937669D9CE46,
	SetVertexPositionFromByteBuffer_000001B1U24PostfixBurstDelegate_EndInvoke_mC9AE252AB060AA87596E4826D9F735CB581F1B9E,
	SetVertexPositionFromByteBuffer_000001B1U24BurstDirectCall_GetFunctionPointerDiscard_mF064D520F7D256F7A555230CD67D5004F63E1C59,
	SetVertexPositionFromByteBuffer_000001B1U24BurstDirectCall_GetFunctionPointer_m4291F8D4F9D4B26DDADDBDED8A89F0298213E47A,
	SetVertexPositionFromByteBuffer_000001B1U24BurstDirectCall_Invoke_m5EBCEE5C013079B2531774411BE3EB46A6BEFB63,
	__JobReflectionRegistrationOutput__12640072059193112690_CreateJobReflectionData_mA063968188DB2EF135AC7DB46CC2EB45A360EBB8,
	__JobReflectionRegistrationOutput__12640072059193112690_EarlyInit_mCF112CAD6822389D903F18CEB4D464A1EDE4C3FC,
	U24BurstDirectCallInitializer_Initialize_m3D7699F95291919D3D61A7D414F04C3C9703A382,
};
extern void PrepareDeformJob_Execute_m371FE77DC77A71B801C2ED2F08BD76ECAD5D4333_AdjustorThunk (void);
extern void BoneDeformBatchedJob_Execute_mD24F84E1C367AC5B671D77CD67E961D395C28C61_AdjustorThunk (void);
extern void SkinDeformBatchedJob_Execute_m105DF36043A75DCAC5E0111DE74EE0087D64E136_AdjustorThunk (void);
extern void CalculateSpriteSkinAABBJob_Execute_mD93917686FF5B004B49DC055B42C9F4DB42EF756_AdjustorThunk (void);
extern void FillPerSkinJobSingleThread_Execute_mC5F785E6FA0AC412C792AF1FE03A8D8B08EA2C84_AdjustorThunk (void);
extern void CopySpriteRendererBuffersJob_Execute_m4EFBA68E2C6B03752414B97C768E2B80F688F643_AdjustorThunk (void);
extern void CopySpriteRendererBoneTransformBuffersJob_Execute_m386F8389F4159350BC846A57D1A88C7210B605D1_AdjustorThunk (void);
extern void TransformData__ctor_mED230D1775060C4D1C0F02F7D757436387F20BD2_AdjustorThunk (void);
extern void LocalToWorldTransformAccessJob_Execute_mA0FF58AB3050E308CF060A21E25F5448F2C987FE_AdjustorThunk (void);
extern void WorldToLocalTransformAccessJob_Execute_m35BD603238353EFDA8B1D3249BFB2B662F7FCED5_AdjustorThunk (void);
extern void UpdateBoundJob_Execute_m21EBA97DE9CDABC2874AD8C34519A3B4CC83BD62_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[11] = 
{
	{ 0x06000025, PrepareDeformJob_Execute_m371FE77DC77A71B801C2ED2F08BD76ECAD5D4333_AdjustorThunk },
	{ 0x06000026, BoneDeformBatchedJob_Execute_mD24F84E1C367AC5B671D77CD67E961D395C28C61_AdjustorThunk },
	{ 0x06000027, SkinDeformBatchedJob_Execute_m105DF36043A75DCAC5E0111DE74EE0087D64E136_AdjustorThunk },
	{ 0x06000028, CalculateSpriteSkinAABBJob_Execute_mD93917686FF5B004B49DC055B42C9F4DB42EF756_AdjustorThunk },
	{ 0x06000029, FillPerSkinJobSingleThread_Execute_mC5F785E6FA0AC412C792AF1FE03A8D8B08EA2C84_AdjustorThunk },
	{ 0x0600002A, CopySpriteRendererBuffersJob_Execute_m4EFBA68E2C6B03752414B97C768E2B80F688F643_AdjustorThunk },
	{ 0x0600002B, CopySpriteRendererBoneTransformBuffersJob_Execute_m386F8389F4159350BC846A57D1A88C7210B605D1_AdjustorThunk },
	{ 0x06000064, TransformData__ctor_mED230D1775060C4D1C0F02F7D757436387F20BD2_AdjustorThunk },
	{ 0x0600006D, LocalToWorldTransformAccessJob_Execute_mA0FF58AB3050E308CF060A21E25F5448F2C987FE_AdjustorThunk },
	{ 0x0600006E, WorldToLocalTransformAccessJob_Execute_m35BD603238353EFDA8B1D3249BFB2B662F7FCED5_AdjustorThunk },
	{ 0x0600006F, UpdateBoundJob_Execute_m21EBA97DE9CDABC2874AD8C34519A3B4CC83BD62_AdjustorThunk },
};
static const int32_t s_InvokerIndices[476] = 
{
	34298,
	21016,
	-1,
	15968,
	15968,
	15968,
	11681,
	15968,
	15968,
	20761,
	15903,
	21016,
	21016,
	21016,
	15903,
	21016,
	-1,
	6503,
	21016,
	11447,
	13690,
	6018,
	6018,
	6017,
	6017,
	21016,
	11681,
	8896,
	21016,
	34252,
	20694,
	21016,
	15968,
	21016,
	7288,
	21016,
	21016,
	15903,
	15903,
	15903,
	21016,
	15903,
	15903,
	34156,
	20761,
	20550,
	15757,
	21016,
	21016,
	21016,
	21016,
	15968,
	21016,
	21016,
	20550,
	21016,
	15968,
	15968,
	15968,
	21016,
	20761,
	15968,
	21016,
	21016,
	21016,
	34252,
	34252,
	20694,
	31561,
	31561,
	21016,
	21016,
	15903,
	21016,
	15968,
	11681,
	21016,
	7288,
	15903,
	21016,
	6017,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	18896,
	18883,
	15968,
	-1,
	-1,
	21016,
	20705,
	20705,
	20761,
	20694,
	15968,
	15903,
	15903,
	34252,
	21016,
	11681,
	11681,
	21016,
	11681,
	21016,
	11681,
	7621,
	7621,
	15903,
	20761,
	15968,
	21016,
	20694,
	3618,
	20694,
	34138,
	13811,
	7405,
	21016,
	21016,
	20550,
	20694,
	20550,
	15757,
	34156,
	21016,
	21016,
	21016,
	6075,
	6075,
	15903,
	21016,
	21016,
	30039,
	28910,
	28910,
	31768,
	28910,
	28910,
	31768,
	7988,
	6503,
	2232,
	15968,
	32748,
	34140,
	28910,
	7988,
	6503,
	2232,
	15968,
	32748,
	34140,
	28910,
	7988,
	13031,
	3277,
	13212,
	32748,
	34140,
	31768,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	20761,
	15968,
	21016,
	-1,
	-1,
	-1,
	-1,
	15968,
	20761,
	21016,
	21016,
	6094,
	13811,
	21016,
	3028,
	27855,
	6072,
	6087,
	25678,
	3763,
	7995,
	3763,
	15968,
	7995,
	5018,
	21016,
	20761,
	13820,
	21016,
	21016,
	21016,
	11681,
	21016,
	11681,
	21016,
	11681,
	21016,
	11681,
	21016,
	11681,
	-1,
	-1,
	-1,
	20761,
	15968,
	20694,
	20761,
	15968,
	21016,
	21016,
	20761,
	15968,
	20694,
	20761,
	15968,
	20761,
	21016,
	15757,
	21016,
	21016,
	7995,
	25681,
	20761,
	15968,
	20695,
	15904,
	15903,
	21016,
	21016,
	6075,
	3287,
	6094,
	20761,
	13820,
	13820,
	3763,
	3752,
	21016,
	15757,
	29176,
	21016,
	34252,
	21016,
	13820,
	13820,
	21016,
	11681,
	11681,
	21016,
	11681,
	21016,
	11681,
	21016,
	11681,
	21016,
	11681,
	11681,
	21016,
	11681,
	21016,
	7995,
	21016,
	11681,
	21016,
	21016,
	11681,
	20550,
	15757,
	20761,
	15968,
	21016,
	20550,
	15757,
	20694,
	15903,
	20761,
	15968,
	21016,
	21016,
	21016,
	34252,
	21016,
	7995,
	20761,
	20761,
	20695,
	20694,
	15968,
	15968,
	15968,
	15968,
	15968,
	21016,
	15903,
	21016,
	21016,
	31782,
	31787,
	31782,
	34139,
	34252,
	21016,
	15968,
	21016,
	21016,
	20761,
	5018,
	20761,
	20761,
	20761,
	21016,
	34103,
	21016,
	21016,
	25518,
	13792,
	20550,
	21016,
	21016,
	18846,
	20694,
	20694,
	15903,
	20761,
	15968,
	18846,
	18877,
	20694,
	20761,
	20761,
	18895,
	20694,
	15903,
	20550,
	15757,
	20761,
	13212,
	20761,
	13212,
	20547,
	15754,
	20550,
	15757,
	20550,
	15757,
	20550,
	20550,
	21016,
	21016,
	21016,
	21016,
	15968,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	20694,
	20550,
	21016,
	21016,
	13811,
	20550,
	18827,
	18914,
	18913,
	20761,
	20761,
	21016,
	21016,
	34103,
	21016,
	15757,
	15757,
	21016,
	21016,
	21016,
	15721,
	15903,
	15721,
	20550,
	21016,
	21016,
	21016,
	15968,
	31787,
	34138,
	21016,
	34252,
	32764,
	32764,
	32764,
	32764,
	32764,
	32764,
	34156,
	20761,
	15968,
	15968,
	15968,
	21016,
	29176,
	28070,
	27499,
	25327,
	26479,
	21016,
	11681,
	20694,
	20550,
	11619,
	18827,
	14780,
	21016,
	34103,
	34103,
	31561,
	31561,
	31787,
	32764,
	31787,
	27854,
	24573,
	31786,
	31787,
	21741,
	22138,
	21759,
	24640,
	28984,
	32764,
	31544,
	28984,
	27391,
	24460,
	27391,
	24460,
	7988,
	4836,
	2235,
	11681,
	32748,
	34140,
	27391,
	7988,
	2365,
	575,
	15968,
	32748,
	34140,
	24460,
	34252,
	34252,
	34252,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[5] = 
{
	{ 0x06000089, 20,  (void**)&MeshUtilities_AddToEdgeMap_m420B959ECD9102D5B1ED0E93A9765C7ED6373E30_RuntimeMethod_var, 0 },
	{ 0x0600008A, 22,  (void**)&MeshUtilities_SortEdges_mE3EE6CD3012C39542E837B24CCC493C91737D5D7_RuntimeMethod_var, 0 },
	{ 0x0600008B, 21,  (void**)&MeshUtilities_GetFirstUnusedIndex_m77BD52B67B1266BD78E777DB9128850F404627A3_RuntimeMethod_var, 0 },
	{ 0x060001C8, 3,  (void**)&BurstedSpriteSkinUtilities_ValidateBoneWeights_m5E7F9120E9D22F0A097DD5B296BE3E9336D45017_RuntimeMethod_var, 0 },
	{ 0x060001C9, 2,  (void**)&BurstedSpriteSkinUtilities_SetVertexPositionFromByteBuffer_m6D9016B821006BB68DA3C0FEDAD7238BB18A287C_RuntimeMethod_var, 0 },
};
static const Il2CppTokenRangePair s_rgctxIndices[8] = 
{
	{ 0x02000028, { 37, 8 } },
	{ 0x02000029, { 45, 11 } },
	{ 0x0600005B, { 0, 3 } },
	{ 0x0600005C, { 3, 7 } },
	{ 0x060000A4, { 10, 7 } },
	{ 0x060000A5, { 17, 8 } },
	{ 0x060000A6, { 25, 5 } },
	{ 0x060000A7, { 30, 7 } },
};
extern const uint32_t g_rgctx_TU5BU5DU26_t10F56A471E7BF8A60F540733ACEC2B8D937E3A2B;
extern const uint32_t g_rgctx_Array_Resize_TisT_t13F35ABA164B2548C3BAF6DBCDC2FB83F66259D1_m0F0ECC2557E3AA13F425979CCA7BF1E0CBE9819D;
extern const uint32_t g_rgctx_T_t13F35ABA164B2548C3BAF6DBCDC2FB83F66259D1;
extern const uint32_t g_rgctx_TU5BU5DU26_t4C8AF95B8042F6A529000DBB9EF3A1A200E90D50;
extern const uint32_t g_rgctx_List_1_tDD5B2147B57B069BBBC580957CC77A30BCC2638D;
extern const uint32_t g_rgctx_List_1__ctor_mA0A701D3F1D6689BD24F016E5854ED84FFFD2552;
extern const uint32_t g_rgctx_IEnumerable_1_tBA394427821AEF55E1F660F0F2BB14997BC6C971;
extern const uint32_t g_rgctx_List_1_RemoveAt_mE965464A24A1CF287722D0D16A2389B917BD7B54;
extern const uint32_t g_rgctx_List_1_ToArray_mEE3F810534DDDB0A2EBF18224B4C51C4B7833E4A;
extern const uint32_t g_rgctx_TU5BU5D_t6A9C5D92D962A66027AE03356E168ED6CDEBC645;
extern const uint32_t g_rgctx_NativeArray_1U26_tCF66F0736E54CFB857B555009C8668F1C575EE5C;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_mA908D3026F33807DEE3F56F6B270A1F81C60B066;
extern const uint32_t g_rgctx_NativeArray_1_t18C719B16A768BD9B6F72CFE5A6BBE572390D2C3;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m649FB6F6D64462DDDB1E038B5A1082AA56171E01;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_m8F08DD7AD4122E49DF943CE65E023B7DBEE3204F;
extern const uint32_t g_rgctx_NativeArray_1_t18C719B16A768BD9B6F72CFE5A6BBE572390D2C3;
extern const uint32_t g_rgctx_NativeArray_1__ctor_mDCB274EE8731EE527186453D0704DABDAFFE79B8;
extern const uint32_t g_rgctx_NativeArray_1U26_t0FFD8A50C15BDA6859516A57052ED5BFFEC0CEE9;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_mC35443FD8879262C49A5C010C446F1E53D7B035E;
extern const uint32_t g_rgctx_NativeArray_1_t308483D2E200A7927CD1E5A7DA10EB1BADCE671D;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m350BCACABB02F2703C59591A2728A8266C20FB55;
extern const uint32_t g_rgctx_NativeArray_1__ctor_m965C0442BF0F2C210EC0C0D184000234118EC682;
extern const uint32_t g_rgctx_NativeArray_1_t308483D2E200A7927CD1E5A7DA10EB1BADCE671D;
extern const uint32_t g_rgctx_NativeArray_1_Copy_mF91F5AE1E4E27D801BE27F0C8FB3C3C209698922;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_mAF6931A70E2F09C188683045086A88BB7E9A5871;
extern const uint32_t g_rgctx_NativeArray_1_tDC327DCE7E58E5E1EEA23B4AA3F6618ED07ED6B9;
extern const uint32_t g_rgctx_NativeArray_1_op_Inequality_mA75DBF90CD5A3C840275EDF386BA574DAD4E46C5;
extern const uint32_t g_rgctx_NativeArray_1_tDC327DCE7E58E5E1EEA23B4AA3F6618ED07ED6B9;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_m1AAAE7FCED35976F8BF84E2984CD542A5BAB1822;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_m4D7AB0B6D05292ABF6FA7D8E01409522AFA55DA1;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t3F2A62B40416EE5899ED8E5B4E4291B6474A8EEA_m5DCBB26E0A67A36E3F71452C0EA4083E4A67631F;
extern const uint32_t g_rgctx_NativeSlice_1_t722349B15FA383BF9DB4980B5C309EDC91C4538F;
extern const uint32_t g_rgctx_NativeSliceUnsafeUtility_GetUnsafeReadOnlyPtr_TisS_t2691BF9A101AF7E9E122F5B76483FB1748F8E67D_mC84F3FA0A135F6597C9D0ED8F63805952C587161;
extern const uint32_t g_rgctx_NativeArray_1_tB39E226E3B48F68F11EE512F2041C7E85C31E0C6;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_t3F2A62B40416EE5899ED8E5B4E4291B6474A8EEA_mBDEC5288EF490FAA18593BE7BB0EB3556B23E634;
extern const uint32_t g_rgctx_NativeSlice_1_get_Stride_m362CA47D93C603BCC530B027E25B37A37F10DAC1;
extern const uint32_t g_rgctx_NativeSlice_1_t722349B15FA383BF9DB4980B5C309EDC91C4538F;
extern const uint32_t g_rgctx_NativeCustomSlice_1_t9E18CBC72D9A55530C861D14DAE71CC955F5486B;
extern const uint32_t g_rgctx_NativeSlice_1_t2F35CCB134EAC30C89DBAE7B11D8C636DEF6811B;
extern const uint32_t g_rgctx_NativeSliceUnsafeUtility_GetUnsafeReadOnlyPtr_TisT_tD486A2E95D06FDF1696727C5D93E719BA9330ED1_m94D7CBC9099D4A9C2C14634DECDCCE6A55F4A904;
extern const uint32_t g_rgctx_NativeSlice_1_get_Length_m269F10F8B7540878F5DB6C63C14CAF8FF9F51D5F;
extern const uint32_t g_rgctx_NativeSlice_1_t2F35CCB134EAC30C89DBAE7B11D8C636DEF6811B;
extern const uint32_t g_rgctx_NativeSlice_1_get_Stride_mCD9D5C510907EEDB59E9E624A80330A5AD614B64;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElementWithStride_TisT_tD486A2E95D06FDF1696727C5D93E719BA9330ED1_m6EC1A3754BC3F1F3620A2A57806AEC0A75EDAC05;
extern const uint32_t g_rgctx_T_tD486A2E95D06FDF1696727C5D93E719BA9330ED1;
extern const uint32_t g_rgctx_NativeCustomSlice_1_t1D4A84799C24FDDF422579501FA8E552F55A5510;
extern const uint32_t g_rgctx_NativeCustomSlice_1__ctor_m45126DAD04B9D5D81E3FBF2F3A2C5D0B149F8000;
extern const uint32_t g_rgctx_NativeCustomSliceEnumerator_1_t5CC560EBE295DDF7F57299EAE5A5BEFCAFB48ED6;
extern const uint32_t g_rgctx_NativeCustomSliceEnumerator_1_Reset_mA16E234FA6829EF1C9DF06F6B5ACBD5990096366;
extern const uint32_t g_rgctx_NativeCustomSliceEnumerator_1_t5CC560EBE295DDF7F57299EAE5A5BEFCAFB48ED6;
extern const uint32_t g_rgctx_IEnumerator_1_tB80670A1D47FE01CFFCFD02782F961A02CA76020;
extern const uint32_t g_rgctx_NativeCustomSliceEnumerator_1_GetEnumerator_m48A35A90A749D996D81341BF9BC25CB1C50B8A0E;
extern const uint32_t g_rgctx_NativeCustomSlice_1_get_Item_mC6EBAC891726B561F1335A36FE7866E7D1FA6535;
extern const uint32_t g_rgctx_NativeCustomSlice_1_t1D4A84799C24FDDF422579501FA8E552F55A5510;
extern const uint32_t g_rgctx_T_tB74E1E04466A9759C60F4CC744B80D88E322203A;
extern const uint32_t g_rgctx_NativeCustomSliceEnumerator_1_get_Current_m92F631681B1755E0E50198FDA8A9E66E94944635;
static const Il2CppRGCTXDefinition s_rgctxValues[56] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t10F56A471E7BF8A60F540733ACEC2B8D937E3A2B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t13F35ABA164B2548C3BAF6DBCDC2FB83F66259D1_m0F0ECC2557E3AA13F425979CCA7BF1E0CBE9819D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t13F35ABA164B2548C3BAF6DBCDC2FB83F66259D1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t4C8AF95B8042F6A529000DBB9EF3A1A200E90D50 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tDD5B2147B57B069BBBC580957CC77A30BCC2638D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_mA0A701D3F1D6689BD24F016E5854ED84FFFD2552 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_tBA394427821AEF55E1F660F0F2BB14997BC6C971 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_RemoveAt_mE965464A24A1CF287722D0D16A2389B917BD7B54 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_ToArray_mEE3F810534DDDB0A2EBF18224B4C51C4B7833E4A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t6A9C5D92D962A66027AE03356E168ED6CDEBC645 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_tCF66F0736E54CFB857B555009C8668F1C575EE5C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_mA908D3026F33807DEE3F56F6B270A1F81C60B066 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t18C719B16A768BD9B6F72CFE5A6BBE572390D2C3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m649FB6F6D64462DDDB1E038B5A1082AA56171E01 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_m8F08DD7AD4122E49DF943CE65E023B7DBEE3204F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t18C719B16A768BD9B6F72CFE5A6BBE572390D2C3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_mDCB274EE8731EE527186453D0704DABDAFFE79B8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_t0FFD8A50C15BDA6859516A57052ED5BFFEC0CEE9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_mC35443FD8879262C49A5C010C446F1E53D7B035E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t308483D2E200A7927CD1E5A7DA10EB1BADCE671D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m350BCACABB02F2703C59591A2728A8266C20FB55 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_m965C0442BF0F2C210EC0C0D184000234118EC682 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t308483D2E200A7927CD1E5A7DA10EB1BADCE671D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Copy_mF91F5AE1E4E27D801BE27F0C8FB3C3C209698922 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_mAF6931A70E2F09C188683045086A88BB7E9A5871 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tDC327DCE7E58E5E1EEA23B4AA3F6618ED07ED6B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_op_Inequality_mA75DBF90CD5A3C840275EDF386BA574DAD4E46C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tDC327DCE7E58E5E1EEA23B4AA3F6618ED07ED6B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_m1AAAE7FCED35976F8BF84E2984CD542A5BAB1822 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_m4D7AB0B6D05292ABF6FA7D8E01409522AFA55DA1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t3F2A62B40416EE5899ED8E5B4E4291B6474A8EEA_m5DCBB26E0A67A36E3F71452C0EA4083E4A67631F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_t722349B15FA383BF9DB4980B5C309EDC91C4538F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSliceUnsafeUtility_GetUnsafeReadOnlyPtr_TisS_t2691BF9A101AF7E9E122F5B76483FB1748F8E67D_mC84F3FA0A135F6597C9D0ED8F63805952C587161 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tB39E226E3B48F68F11EE512F2041C7E85C31E0C6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_t3F2A62B40416EE5899ED8E5B4E4291B6474A8EEA_mBDEC5288EF490FAA18593BE7BB0EB3556B23E634 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_get_Stride_m362CA47D93C603BCC530B027E25B37A37F10DAC1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_t722349B15FA383BF9DB4980B5C309EDC91C4538F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeCustomSlice_1_t9E18CBC72D9A55530C861D14DAE71CC955F5486B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_t2F35CCB134EAC30C89DBAE7B11D8C636DEF6811B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSliceUnsafeUtility_GetUnsafeReadOnlyPtr_TisT_tD486A2E95D06FDF1696727C5D93E719BA9330ED1_m94D7CBC9099D4A9C2C14634DECDCCE6A55F4A904 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_get_Length_m269F10F8B7540878F5DB6C63C14CAF8FF9F51D5F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_t2F35CCB134EAC30C89DBAE7B11D8C636DEF6811B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_get_Stride_mCD9D5C510907EEDB59E9E624A80330A5AD614B64 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElementWithStride_TisT_tD486A2E95D06FDF1696727C5D93E719BA9330ED1_m6EC1A3754BC3F1F3620A2A57806AEC0A75EDAC05 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD486A2E95D06FDF1696727C5D93E719BA9330ED1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeCustomSlice_1_t1D4A84799C24FDDF422579501FA8E552F55A5510 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeCustomSlice_1__ctor_m45126DAD04B9D5D81E3FBF2F3A2C5D0B149F8000 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeCustomSliceEnumerator_1_t5CC560EBE295DDF7F57299EAE5A5BEFCAFB48ED6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeCustomSliceEnumerator_1_Reset_mA16E234FA6829EF1C9DF06F6B5ACBD5990096366 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeCustomSliceEnumerator_1_t5CC560EBE295DDF7F57299EAE5A5BEFCAFB48ED6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tB80670A1D47FE01CFFCFD02782F961A02CA76020 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeCustomSliceEnumerator_1_GetEnumerator_m48A35A90A749D996D81341BF9BC25CB1C50B8A0E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeCustomSlice_1_get_Item_mC6EBAC891726B561F1335A36FE7866E7D1FA6535 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeCustomSlice_1_t1D4A84799C24FDDF422579501FA8E552F55A5510 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB74E1E04466A9759C60F4CC744B80D88E322203A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeCustomSliceEnumerator_1_get_Current_m92F631681B1755E0E50198FDA8A9E66E94944635 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_2D_Animation_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_Unity_2D_Animation_Runtime_CodeGenModule = 
{
	"Unity.2D.Animation.Runtime.dll",
	476,
	s_methodPointers,
	11,
	s_adjustorThunks,
	s_InvokerIndices,
	5,
	s_reversePInvokeIndices,
	8,
	s_rgctxIndices,
	56,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
