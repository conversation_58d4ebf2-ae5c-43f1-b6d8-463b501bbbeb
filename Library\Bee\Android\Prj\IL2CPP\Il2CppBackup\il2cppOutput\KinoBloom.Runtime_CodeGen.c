﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m54B761E06F7894CBA5A268FAE1F889D9A7ED653F (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mF7AE4FDE3C30127968C3EA8EA92D1D04A504ADB0 (void);
extern void Bloom_get_thresholdGamma_m0B91BAD6344981D8E6F23872F82F0661BD86ACDE (void);
extern void Bloom_set_thresholdGamma_m6815A4F1C92ED38364AECFBE8FB3B9B13536F661 (void);
extern void Bloom_get_thresholdLinear_mC4BAE80A96C574D2DD4889F70791A0A886362CB5 (void);
extern void Bloom_set_thresholdLinear_m7BBC09CA8ECA8279F5033CAE0EC1855625BBB93C (void);
extern void Bloom_get_softKnee_m7FE308F6881EEB607B4B9E90CFA555118EF78CF0 (void);
extern void Bloom_set_softKnee_m0662BE9493EE34E744ED437A2BC5E14C2BFB2987 (void);
extern void Bloom_get_radius_mDAC194446B02A7522F4E8B20919D0DA0A252CAFB (void);
extern void Bloom_set_radius_mBAD5FE4B71E4FC4E475BA5CA602356A5E2874CCA (void);
extern void Bloom_get_intensity_m62468D24C9D0155C778FEDFFD909A7846668D09A (void);
extern void Bloom_set_intensity_m4715BD004C291DD33A0DC0DC6D4DE0C42DE4F4E2 (void);
extern void Bloom_get_highQuality_m11E16391564428D74D1E5C8B76F8CF8300BCBC59 (void);
extern void Bloom_set_highQuality_m076A62131453AF7FFA9866AF7463D3BE46A18A0B (void);
extern void Bloom_get_antiFlicker_m5DC8868C04E8C19E85D2A6DCAED4B2447F9EEF68 (void);
extern void Bloom_set_antiFlicker_m881F189D848A953C7DF8E0053D76BED98154D61C (void);
extern void Bloom_LinearToGamma_mC8BBA7DAC0B85588382025A2A9EAA866AEA07EF2 (void);
extern void Bloom_GammaToLinear_mE7A60D9869AA3CA47DD8AB6B03918DCF15C3F3DA (void);
extern void Bloom_OnEnable_mFC995393B810C1696187D7E1B9779AAAAED4CBE3 (void);
extern void Bloom_OnDisable_m24ECC6E1E4258545857EFDB8110DAB76B3FBA8EB (void);
extern void Bloom_OnRenderImage_m2FA867D44BD6651C9723F93D4650DE16EF3058B5 (void);
extern void Bloom__ctor_mE7129E735B78AEBDDF8F13B35CD2B88A834BAB17 (void);
static Il2CppMethodPointer s_methodPointers[22] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m54B761E06F7894CBA5A268FAE1F889D9A7ED653F,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mF7AE4FDE3C30127968C3EA8EA92D1D04A504ADB0,
	Bloom_get_thresholdGamma_m0B91BAD6344981D8E6F23872F82F0661BD86ACDE,
	Bloom_set_thresholdGamma_m6815A4F1C92ED38364AECFBE8FB3B9B13536F661,
	Bloom_get_thresholdLinear_mC4BAE80A96C574D2DD4889F70791A0A886362CB5,
	Bloom_set_thresholdLinear_m7BBC09CA8ECA8279F5033CAE0EC1855625BBB93C,
	Bloom_get_softKnee_m7FE308F6881EEB607B4B9E90CFA555118EF78CF0,
	Bloom_set_softKnee_m0662BE9493EE34E744ED437A2BC5E14C2BFB2987,
	Bloom_get_radius_mDAC194446B02A7522F4E8B20919D0DA0A252CAFB,
	Bloom_set_radius_mBAD5FE4B71E4FC4E475BA5CA602356A5E2874CCA,
	Bloom_get_intensity_m62468D24C9D0155C778FEDFFD909A7846668D09A,
	Bloom_set_intensity_m4715BD004C291DD33A0DC0DC6D4DE0C42DE4F4E2,
	Bloom_get_highQuality_m11E16391564428D74D1E5C8B76F8CF8300BCBC59,
	Bloom_set_highQuality_m076A62131453AF7FFA9866AF7463D3BE46A18A0B,
	Bloom_get_antiFlicker_m5DC8868C04E8C19E85D2A6DCAED4B2447F9EEF68,
	Bloom_set_antiFlicker_m881F189D848A953C7DF8E0053D76BED98154D61C,
	Bloom_LinearToGamma_mC8BBA7DAC0B85588382025A2A9EAA866AEA07EF2,
	Bloom_GammaToLinear_mE7A60D9869AA3CA47DD8AB6B03918DCF15C3F3DA,
	Bloom_OnEnable_mFC995393B810C1696187D7E1B9779AAAAED4CBE3,
	Bloom_OnDisable_m24ECC6E1E4258545857EFDB8110DAB76B3FBA8EB,
	Bloom_OnRenderImage_m2FA867D44BD6651C9723F93D4650DE16EF3058B5,
	Bloom__ctor_mE7129E735B78AEBDDF8F13B35CD2B88A834BAB17,
};
static const int32_t s_InvokerIndices[22] = 
{
	34290,
	21016,
	20873,
	16071,
	20873,
	16071,
	20873,
	16071,
	20873,
	16071,
	20873,
	16071,
	20550,
	15757,
	20550,
	15757,
	14026,
	14026,
	21016,
	21016,
	7995,
	21016,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_KinoBloom_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_KinoBloom_Runtime_CodeGenModule = 
{
	"KinoBloom.Runtime.dll",
	22,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
