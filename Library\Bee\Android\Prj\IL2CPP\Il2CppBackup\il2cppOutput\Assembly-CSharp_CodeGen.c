﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void FPSMeter_OnEnable_m282EC52AEAC8D57EE9FD69B31BD4F2A15FE759A2 (void);
extern void FPSMeter_Update_m6EC34DC7F1FC4A13E45F177327D2A3587933C1C7 (void);
extern void FPSMeter__ctor_m81BF8F0E163DBC4AEE672E95860810D98B083F79 (void);
extern void AdConfig_get_AppKey_m6DA4AC42AAD3F7D858522804DB62387EB186EB8A (void);
extern void AdConfig_get_BannerAdUnitId_m22EAC9E61BB55AE9446A94F5735F00894AAFF3B0 (void);
extern void AdConfig_get_InterstitalAdUnitId_mD920BEFA215FD9C65AB7A6712F30F3DDF37BE05A (void);
extern void AdConfig_get_RewardedVideoAdUnitId_m44D12E1509DBE402094A4E4D5E77E0F069008197 (void);
extern void AdConfig_GetAppKey_m69298F64CCC5201F0EDDEBA124D78B75C256E66E (void);
extern void AdConfig_GetBannerAdUnitId_m54C810FD18C939970F42117CD039FDAD08AE44AA (void);
extern void AdConfig_GetInterstitialAdUnitId_mA058379ECCFA113C2DC1D49576C958DB112E86D7 (void);
extern void AdConfig_GetRewardedVideoAdUnitId_mF45C7CC2D3510849656C068E1283E63D9F062C4C (void);
extern void LevelPlaySample_Start_mF8FEBB5FCF9E647542F23C2DFB42F08431BEFB25 (void);
extern void LevelPlaySample_EnableAds_mD107E91A7162E886A03721C44A3065AD36F0439C (void);
extern void LevelPlaySample_OnGUI_m98229292EFDC32DFCEB00C42615AB7E99AF7C534 (void);
extern void LevelPlaySample_SdkInitializationCompletedEvent_m6A23269AF1AA1E7E85FD9C4D43CDB7D58377D95E (void);
extern void LevelPlaySample_SdkInitializationFailedEvent_mCF6D2EDE19BF491F1722216994540F1736082B5F (void);
extern void LevelPlaySample_RewardedVideoOnLoadedEvent_m6F7635FBC65AFA0BA2C0454F8D1A3FAD8522D25B (void);
extern void LevelPlaySample_RewardedVideoOnAdLoadFailedEvent_m7A212DB9706E91065E18DE92FE9828219B8C7DF4 (void);
extern void LevelPlaySample_RewardedVideoOnAdDisplayedEvent_mA73C685B895CBD21F9D3E0E3B4C01566F5EDF4EA (void);
extern void LevelPlaySample_RewardedVideoOnAdDisplayedFailedEvent_m7C8BA504D3149BD5160CA3253D077FABD4A3185F (void);
extern void LevelPlaySample_RewardedVideoOnAdRewardedEvent_mBF963CDDD55E70886568CB62E8A4AD8076AF4551 (void);
extern void LevelPlaySample_RewardedVideoOnAdClickedEvent_mC5EECC2AEB4DE526E85F1E91A085B26023686346 (void);
extern void LevelPlaySample_RewardedVideoOnAdClosedEvent_mE172BB7F1F813B191C7B41887C6770153AC8FD0A (void);
extern void LevelPlaySample_RewardedVideoOnAdInfoChangedEvent_m3685D46596D4247B2A1F11088B51A470D6DBD71F (void);
extern void LevelPlaySample_InterstitialOnAdLoadedEvent_m2155C7AF75158EA07C3DCF5FDAEC1C88A97ADDC3 (void);
extern void LevelPlaySample_InterstitialOnAdLoadFailedEvent_m6F5F5C3E0745F5D18836F72DAB5AD9969B1C71E0 (void);
extern void LevelPlaySample_InterstitialOnAdDisplayedEvent_m36130DA8C20A73015B786071C1392D508AE3EFBB (void);
extern void LevelPlaySample_InterstitialOnAdDisplayFailedEvent_mCBAC25BF9DA95862B82027708DFEE9311BB36F4C (void);
extern void LevelPlaySample_InterstitialOnAdClickedEvent_mB8567497EACD9F56BCAA2FD340226E26B9CB40E2 (void);
extern void LevelPlaySample_InterstitialOnAdClosedEvent_mB9EC434D33B3604B4CACEDC58AE49820245BE8E5 (void);
extern void LevelPlaySample_InterstitialOnAdInfoChangedEvent_m1C80797731683CE46B8C52641911E8BA93A5C18A (void);
extern void LevelPlaySample_BannerOnAdLoadedEvent_mE3228F53EA5845FF7012B90564BBF780511D968D (void);
extern void LevelPlaySample_BannerOnAdLoadFailedEvent_mBC5EFE967CABDF5E763219792117D28D4670090C (void);
extern void LevelPlaySample_BannerOnAdClickedEvent_m1C0BA242D5E3392CA76941BADEB7C79623642534 (void);
extern void LevelPlaySample_BannerOnAdDisplayedEvent_mCC1DA608D4341D5EFDB89C823918F460390C1EC5 (void);
extern void LevelPlaySample_BannerOnAdDisplayFailedEvent_m944A73E95D28CC426875B905E9D2AF142BBBC8D1 (void);
extern void LevelPlaySample_BannerOnAdCollapsedEvent_m14A4B71E37F6EB86785535C7550E0042D178439A (void);
extern void LevelPlaySample_BannerOnAdLeftApplicationEvent_mD77E9C82286B5FA62C7D934AE2F33C920274BAB7 (void);
extern void LevelPlaySample_BannerOnAdExpandedEvent_m62AB29A94709D17605F74A452367B41B01BD7F32 (void);
extern void LevelPlaySample_ImpressionDataReadyEvent_m53FCB7ECD3373143DB2E3C0F67150FAF39EB4256 (void);
extern void LevelPlaySample_OnDisable_m989FD6F1FA1C8ED9EE5A771D87D66911632DE325 (void);
extern void LevelPlaySample__ctor_mC55DD77FAAC44605B746576C01C0C8C6E83C81C4 (void);
extern void ZStringExample_Start_m85D621DB211F7AB6B615E3D1DEA49A8D0C157DA0 (void);
extern void ZStringExample_OnDestroy_mFFA3D21D9462A7F83B4FF8B67170DE21C8E2CC76 (void);
extern void ZStringExample_RunAllExamples_m925707EDDEA6B409ACA90528BC6C6300B8B96216 (void);
extern void ZStringExample_Example1_BasicStringBuilding_m2B6F87707442B9085BC365003E3FCEB2B6AABA7E (void);
extern void ZStringExample_Example2_StringFormatting_m9D701C1C59B1CDBD92B816D43EA8EDE6C0964A9F (void);
extern void ZStringExample_Example3_NumberFormatting_m03AA5CB57087D1E0FF3F5D9FE0D310B4B7A04A32 (void);
extern void ZStringExample_Example4_StringConcatenation_m15A493D2F18B94DCB64A52C751231CB41A3A3636 (void);
extern void ZStringExample_Example5_ComplexStringBuilding_mB20A65A4F0AF426F65A4113FD3B434FB39C3F5BE (void);
extern void ZStringExample_Example6_PerformanceComparison_mCBD05A5C340407D1ACC55F3F68BA7FB5EDE81491 (void);
extern void ZStringExample_Example7_ThreadStaticUsage_mAFD9D7A08E3AE171C402F98DCFD1313B8C86650D (void);
extern void ZStringExample_Example8_ReusableStringBuilder_m77FB0EB3EBC6BB0250177759398D73220E0C3F54 (void);
extern void ZStringExample_Update_m4A5AF6DA0DE2CFDFE4DE6BCB2FA11E2766A10705 (void);
extern void ZStringExample_BuildDebugString_m556AE922FF4A15D49B9FED62F59E46FDCF5675B1 (void);
extern void ZStringExample_BuildUIText_m16B69A59680D8727AFCC6CD34AF37E0FC8DA6212 (void);
extern void ZStringExample_BuildLogMessage_mB0ECB6F19C377D745829708DFAC0AB2488EEFAF6 (void);
extern void ZStringExample__ctor_m4BFBDC491B27F82CCFA61F5542B898373B71C890 (void);
extern void ZStringExample_U3CStartU3Eb__3_0_m636DFEF6490C32EB881F61B75CE6DD13DD3E8E6D (void);
extern void ZStringExample_U3COnDestroyU3Eb__4_0_m6417D89D9D9716EEC29534312572DDA57D5EB51A (void);
extern void ZStringGameExamples_BuildScoreText_m71AE99948DB1AB25087809DDA42B89592F3776A2 (void);
extern void ZStringGameExamples_BuildStatusText_mD378FF2005B1C9F48268C677B229F4367EA978F1 (void);
extern void ZStringGameExamples_BuildDebugInfo_m2F9E2C377D17E00333428509EAF333F138BA4C4E (void);
extern void ZStringGameExamples_BuildAchievementText_m6970A551C5C32596464B787CE8E06877AE5511DE (void);
extern void ZStringGameExamples_BuildInventoryItemText_m0B2ECC9F01026B1E64907516A680D3FE5087D149 (void);
extern void ZStringGameExamples_BuildLevelSummary_mB804C01CE8E06E995F1891B0D57AFE7D08DC356C (void);
extern void ZStringGameExamples_BuildErrorMessage_m1C7799C7B73EB942954AD342BA90908FFE3FE439 (void);
extern void ZStringGameExamples_BuildNetworkPacket_m119E6D65C13ADEA4054CB6E5CA937DA721B30684 (void);
extern void ZStringGameExamples_BuildCSVRow_m4B03258957C1FA0B77360462E0D29A9879D5DF2C (void);
extern void ZStringGameExamples_BuildSimpleJSON_m7DFEE614C5EE400D6FE2182D22AA51466ABDF0BB (void);
extern void ZStringGameExamples_BuildTooltipText_mFA5CBC3045A67A05E69E3831426FB2A583EBF80F (void);
extern void ZStringGameExamples_BuildLeaderboardEntry_m1E577DCD77A3F5A14DE92B0A1B3984DE66079665 (void);
extern void ZStringGameExamples_BuildSaveData_m58CAAA2A89EA0FFD27C224DE888E4B6B2AAD4CB3 (void);
extern void ZStringGameExamples_BuildPerformanceMetrics_mC99A9497B3AEA4EDBE75C4F645B996C3539B525A (void);
extern void ZStringGameExamples_BuildGameStateLog_mF91846A47403585916FCE44546B2E052039F6EDE (void);
extern void ZStringGameExamples_BuildConfigString_m40171D8B625447AF8A3CAF18D6718A61C17C07EB (void);
extern void ZStringGameExamples_BuildVersionString_m0B0E4C14B202358AE8997160DC2D447D5F346C51 (void);
extern void ZStringGameExamples_BuildProgressText_mAEBD7CD732E4D2CF800C88CDCBAF8AF19E263F23 (void);
extern void ZStringGameExamples_BuildTimeRemainingText_m7F4964DA02EDD2726B8021D17CD5BBE9F8E50044 (void);
extern void ZStringGameExamples_BuildComboText_m74EF17BDD848C56286BC90D8054A2211097D466F (void);
extern void ZStringGameExamples_Start_m4870E3C3BC6845016A5577AF32DB711C78C79426 (void);
extern void ZStringGameExamples_TestAllExamples_mC534A616C976B84836A8970E0471B778B8AAA33F (void);
extern void ZStringGameExamples__ctor_m412B73BBAC082058E96F3C1F6F1F6FFD448A76B7 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBEB95BEB954BB63E9710BBC7AD5E78C4CB0A0033 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE70FB23ACC1EA12ABC948AA22C2E78B2D0AA39B1 (void);
extern void GemPlacerTile_GetTileData_mD03D6A7C928D2554E2A5164484F08B159C2EF3B6 (void);
extern void GemPlacerTile_StartUp_m736E544809E2930E3358C415B74CFF75A2A3CD41 (void);
extern void GemPlacerTile__ctor_m67288FB545CA9CFDD53427EF9F553B98A8E20326 (void);
extern void GemSpawner_GetTileData_m7F9E2F7E8FCF5B18AA3EC49E1133A5B49FD69EA8 (void);
extern void GemSpawner_StartUp_m88585914BBE796386DE1DE4642FD392823A858B4 (void);
extern void GemSpawner__ctor_m5AA6E842A519DEA522AC1C73AB5FA518DB1AD2A4 (void);
extern void ObstaclePlacer_GetTileData_m136410E54FC76752C28A42871807E9C9C5A937AC (void);
extern void ObstaclePlacer_StartUp_m9D08DC9E0CABEED8EE2BD6E87B754BBB27FBEB6B (void);
extern void ObstaclePlacer__ctor_mB1500F51638A29AAFEEFA6563CFFA6771734B25D (void);
extern void Board_get_Bounds_m04FC2F12A137C4D1C3B38B00090B99B7A3D90EFB (void);
extern void Board_get_Grid_mC2AAC6E3956F18E81BB859CEEBBCD4420D456865 (void);
extern void Board_Awake_m8CA5CE36331C6CA5C3181B843567E8951823ABFF (void);
extern void Board_Start_mF9266E493FC5AA5BD3276E1825D035E79670818A (void);
extern void Board_OnDestroy_m093AB152ADFE4DE7537B6C41BD49EA0F70DF99C3 (void);
extern void Board_GetReference_m142837CDC47B9210F8DD623BF2C257240D61CB49 (void);
extern void Board_ToggleInput_m9BE9C75C3C47EDC4324AFA33DF0792BFFFBF390A (void);
extern void Board_TriggerFinalStretch_m9C1D39D478B6A6DC2C83AEA58E727AC1E42BC28D (void);
extern void Board_LockMovement_m90C1B5D8C178461BCF5FA585B8DAB212A2240EEA (void);
extern void Board_UnlockMovement_m73F4709EDF8E1B01B01A9FDC25279E909D4BC6C2 (void);
extern void Board_Init_m29BC5943AC84E65C4C2CE0CDBF177A3B7F99E10F (void);
extern void Board_WaitToFadeIn_mA49F46A034788DCB0C13AE8DE7ADBC12294C8811 (void);
extern void Board_RegisterCell_m257A6C0AA8CAEF1B3F2B2F98977693EFB7F867A5 (void);
extern void Board_AddObstacle_m1D27E3E161341C0B59DDC6A944670CF2DC5E4906 (void);
extern void Board_AddObstacleGem_mDF41AFBA77FF7B31CD3BAC43469BC4E2C92A57A7 (void);
extern void Board_ChangeLock_m4D0B915BF6254BBD077F1F9099112F967E6A5A8F (void);
extern void Board_RegisterDeletedCallback_mA3DC9FB6FD61B022C9EC59D121B51AEF317FFB8C (void);
extern void Board_UnregisterDeletedCallback_mE2E178038A0E81DBC16B30BD1D80E394BAC55A52 (void);
extern void Board_RegisterMatchedCallback_m8BF54145B1C77313FE6613C19ED5BB9D4A74AA61 (void);
extern void Board_UnregisterMatchedCallback_m60AA9DE5D07C88C2FB17CDDBFFE720B0066D9D18 (void);
extern void Board_RegisterSpawner_mA2B614E97C70C045DBF6E3591A92F442C99FD283 (void);
extern void Board_GenerateBoard_m79B9B52FFEB2AD251CDDF35D6AB93F3AF664F7AF (void);
extern void Board_Update_m8CE4CD52588E4E9CDC6B189AF06BECC01EEAAA6B (void);
extern void Board_MoveGems_m1FA360E3D97AACD0F45260542EEB3C99E1470CF3 (void);
extern void Board_MatchTicking_mA415B3FDFF3282C2A73276534FC12795EB2676B3 (void);
extern void Board_DestroyGem_m3B4DD1B4E6DD7A6E282AC11D1F3DFD612A2BA441 (void);
extern void Board_GetCellCenter_m08D36E21536522D2B36E85B2359C39B3E0F1657F (void);
extern void Board_WorldToCell_m3D8516CD911C62CDC8F40FCCAB0AA23E51A7A64D (void);
extern void Board_AddNewBoardAction_m4AD6876AF1E271F2CF0AAC20D0F3C1B9C15BBAAD (void);
extern void Board_CreateCustomMatch_m298742B68971969AB02EE7E004F6DB8F0E7B8022 (void);
extern void Board_EmptyCheck_m45B4510F14C04C30C79CD7B22E93ACDBD7560C88 (void);
extern void Board_DoMatchCheck_mC0C0794EC19D46ED1D7E1C7F64BB3BB97FD5D022 (void);
extern void Board_DrawDebugCross_mB670C9DDD6BE87D027AA9AB7F67E9A50A175E846 (void);
extern void Board_NewGemAt_m9AD629527703C18592F9C9C004A606E081B06FD5 (void);
extern void Board_ActivateSpawnerAt_m34942108B8EF243E4B11A8968FD223D00B657893 (void);
extern void Board_TickSwap_m4AB5EA4F5C08C7296E6D7146078789065C3B6FA6 (void);
extern void Board_DoCheck_mD77484E1B2502C4500063AD3D12E532AD48DEB71 (void);
extern void Board_CheckInput_mA56C4EDCF523236704CA1DC836A398E989C5A810 (void);
extern void Board_ActivateBonusItem_mFE0CD53F7C0385E99E795D81FEFAFFC914569DC2 (void);
extern void Board_FindAllPossibleMatch_m957F4F5F8C46FDF0354ED83229F4DBF5FD091DD5 (void);
extern void Board__ctor_m78FB8DAC1D25C9DB91544B7D865969943507C7C1 (void);
extern void Board_U3CWaitToFadeInU3Eb__54_0_m51A8025547F3D02C420BA6E6D06D28C8EA885AFB (void);
extern void PossibleSwap__ctor_m9596C364B0A454547AC727A30731213C822B0A75 (void);
extern void U3CU3Ec__cctor_m6035E6BF35334A8CFF0B2D0E42ED9EBCFC94211B (void);
extern void U3CU3Ec__ctor_m9FA9FC438A44B5D2C3A8525F5FCF673163B0E3DB (void);
extern void U3CU3Ec_U3CMoveGemsU3Eb__66_0_m00EC83C37CE6A7101914E36649C478D8D3D31DA3 (void);
extern void U3CWaitToFadeInU3Ed__54__ctor_mA902964A0D0A4CEC174F647C804A4B09EC40BD15 (void);
extern void U3CWaitToFadeInU3Ed__54_System_IDisposable_Dispose_mD05D38F8EA8954423A06F2151222AF33C479BA4B (void);
extern void U3CWaitToFadeInU3Ed__54_MoveNext_m53D63053FC01E6A1146F8FE7C69D06B1DF34DF87 (void);
extern void U3CWaitToFadeInU3Ed__54_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEAC52759F38996FBFD7E9E53C579797F88AC6B9C (void);
extern void U3CWaitToFadeInU3Ed__54_System_Collections_IEnumerator_Reset_mC68E2D8E1049B58FD9F193E4DC0AC7433C748D45 (void);
extern void U3CWaitToFadeInU3Ed__54_System_Collections_IEnumerator_get_Current_m5359C279E5E2B67204BBE9A996FDE79261CD92AD (void);
extern void BoardCell_get_CanFall_m31C8304D9ACE066771B4A0CD24C948E21AB4E5C1 (void);
extern void BoardCell_get_BlockFall_m98F76C34F7DF4F011CB379E1B1F1AC7098189667 (void);
extern void BoardCell_get_CanBeMoved_m8C5CA8C2B1AC5BFAB354492C42BD3A7833B55B20 (void);
extern void BoardCell_CanMatch_m2A66DE8AB448CF041A010C7E227FB14FF0E36B37 (void);
extern void BoardCell_CanDelete_mDE03D0B34616B59A4CE9A1E3FFF4B07F373A9486 (void);
extern void BoardCell_IsEmpty_m8814AEB278850BFFCA9008D81A12CABEE03AF455 (void);
extern void BoardCell__ctor_mBBF557B9CDE72A40CD39A33818ED98C02029D17B (void);
extern void BoardCell__cctor_m430770A3AB894A179CCD367D211B32D957FE0084 (void);
extern void BonusGem_Awake_m3F071C1577B25A0D0F49BC97D0324047F942B049 (void);
extern void BonusGem_HandleContent_mB0B65284A34A5E58CB6713A5D409AA24BFA07488 (void);
extern void BonusGem_BonusTriggerEffect_mB6D8BE80C93E54247445C28F570BA2CFBFFAF0D6 (void);
extern void BonusGem__ctor_m724832E114D1776BF07283C6C8DB7FFCF5C96DC8 (void);
extern void MatchShape_OnBeforeSerialize_mC0FACE6BBADF7797D21892C45CC97074CDDF4E75 (void);
extern void MatchShape_OnAfterDeserialize_mC3A7E9C45A567F84DC79E9722C7D036D02BD59E7 (void);
extern void MatchShape_FitIn_m6BD3A69C363CB6792DA74F3FFCE6FFD1C3D58C75 (void);
extern void MatchShape_GetRotation_m7595E508531F955EE420132B1BA546B8857DF5FB (void);
extern void MatchShape_GetBoundOf_m8D1B56A385C05F04E3AF2FAAD194B00F048C5A34 (void);
extern void MatchShape__ctor_m97829DFF477D067EC548B6C7E15F4C3C9D8D0026 (void);
extern void ColorClean_Awake_mFB240B40875301626163A334C0ADB48FCB2D7D3B (void);
extern void ColorClean_Use_m570A06DA0C76E157B818BE268327628B5C1D15A7 (void);
extern void ColorClean__ctor_mE322CE7C60FFC06636E7A39068B495B0D63CD108 (void);
extern void JumpingFish_Awake_m544FBB6A4E6162EC84A977404A8B8EE260C445B2 (void);
extern void JumpingFish_Use_m19B4F00FCEADB949A735E8B95E388FBE6A45F308 (void);
extern void JumpingFish_FindTarget_mAA7A71A316D7CF3437653B79C2AEB4D6C656971B (void);
extern void JumpingFish__ctor_m1C96BF21C4ACD22227C1160FDA0A0C75CE686487 (void);
extern void U3CU3Ec__cctor_m98239FB053406C79E80873717CC8F7DD5CEA900B (void);
extern void U3CU3Ec__ctor_m808CECCB576D1A8E096208761C9D0A97312A7F13 (void);
extern void U3CU3Ec_U3CFindTargetU3Eb__3_0_m1470C6396A72799CDAC8926DA2989B21EC1302E0 (void);
extern void U3CU3Ec_U3CFindTargetU3Eb__3_1_mF41A035ACB40CFD130B6806450369963A20854AF (void);
extern void U3CU3Ec_U3CFindTargetU3Eb__3_2_mBAC6628E793872A2EB1D7261AEE3712A2C43E870 (void);
extern void U3CU3Ec_U3CFindTargetU3Eb__3_3_mFEFA1485E6CF598E37E4AB487791F9FD5F30AD5D (void);
extern void LargeBomb_Awake_m9E022B53626932F5EBBC4289AA13500644B38E00 (void);
extern void LargeBomb_Use_m04232BDB87AD9083F2F1A62436F66453F06497E1 (void);
extern void LargeBomb__ctor_m2D23173FC25AE98814EFFA8BFA0281608FE02C90 (void);
extern void LineRocket_Awake_m0F931FEC5C6F7072EB8F591ADBCA8D5395DEF651 (void);
extern void LineRocket_Use_mCC002F8B0D1EAAF15A3B72EE6938A2C35164268A (void);
extern void LineRocket__ctor_mCBA76320AF6AB22EB74C8F65E773A507E4174DFC (void);
extern void RocketAction__ctor_m6DFDD28F9E0A12C34A9D613006180872D2BDECDF (void);
extern void RocketAction_Tick_mAA4CABE7038A99043BB40EED4027539CF83C204B (void);
extern void SmallBomb_Awake_m82CD30B81EDC5F4B94ACE00E3DB4200C3C79E2FC (void);
extern void SmallBomb_Use_m9A7FCD1AB02AED321678CAD013C2A9C39B5143AD (void);
extern void SmallBomb__ctor_m8FED95D2B3E42DC633CF5B85AEDD7E2BA71432D6 (void);
extern void BonusGemBonusItem_Use_m72049CECE392148F9215274EEAC682D3ECD70F6D (void);
extern void BonusGemBonusItem__ctor_m60CB622FE77FE6ED170BE74044D41629D6DCA23C (void);
extern void BonusItem__ctor_m51ACA2AA94F8A8651F8293B0784261B8B9A7AB66 (void);
extern void Crate_Awake_m7BA0B26B1D85171EB3FFB8705CA05C9732715645 (void);
extern void Crate_Init_mAC38FF7BCBAA05CFD2CFD44E0908ACC49DF94343 (void);
extern void Crate_Damage_m352056E99AF9F18755A5B1DA2797A8DEADBE2425 (void);
extern void Crate_OnDestroy_mCBF286EF162A5FD0BCF645EAF799466793A0AB1A (void);
extern void Crate_AdjacentMatch_m26B785CAF4C690A8F91D5AB9FBD591C94F059834 (void);
extern void Crate_UpdateState_m85F4767E26713917E994C40AB2A3A5640B0B5AE5 (void);
extern void Crate__ctor_m1A2D4C85DDDB2E981FBAFB9954C7658603E29724 (void);
extern void FrozenIce_Awake_m6B2830E2C934620414BD7A1DEF2F7898E1B34703 (void);
extern void FrozenIce_Init_m1B5ECEA7009F7B9ED0B68793E9B15BF25C63BE69 (void);
extern void FrozenIce_Damage_m150D187914EF462885F6100F8A5EE84672F6B5A2 (void);
extern void FrozenIce_OnDestroy_m41B257AA32001EFDFA68D6B523CDDA8445E6C10A (void);
extern void FrozenIce_AdjacentMatch_mD18AB2C3E95258C7E528EA287159C81DC7AA9949 (void);
extern void FrozenIce_Clear_mBFE3F7335B59259EBFC45E15C74BE24782CC33E2 (void);
extern void FrozenIce_UpdateState_mBBCA03E0BE8F4FD24FA7E1237A279BA96828ECCF (void);
extern void FrozenIce__ctor_mB97F6230F9F5E2839F2B6AAEB7A62F47286F6C17 (void);
extern void GameManager_get_Instance_mCA164312C1A40D4A73B8E35C23E297CDE085E33A (void);
extern void GameManager_set_Instance_m4DE067BA347C2EFCB41C80F437E2E8C143480BB7 (void);
extern void GameManager_IsShuttingDown_m0DED12FDEA496799F33DA10BE678250966B2F03A (void);
extern void GameManager_get_Coins_m544A17D9DD8DDC906DF720A628C6AD6E130DFD59 (void);
extern void GameManager_set_Coins_mF1FB9B188FA838859607F7ED400D9547E147AAC0 (void);
extern void GameManager_get_Stars_mE9620FFA33A5330EF0F5DBF1DADFDD7FD775FB80 (void);
extern void GameManager_set_Stars_mA66E43C741FED26F76C11EF91797C50CF2223F8C (void);
extern void GameManager_get_Lives_m0D96CBF5C1E60453D4F884445FC9BD70809EDCF4 (void);
extern void GameManager_set_Lives_m5AEFA782B8FC4F6F8E5CD1A08DB4CBBB1584BD7F (void);
extern void GameManager_get_Volumes_m76041CB9A3F6DCEA0800A815078B8E59492AD15C (void);
extern void GameManager_get_PoolSystem_m376567F99B2FB070096C0D7F1C6DA1C664D9B526 (void);
extern void GameManager_set_PoolSystem_m60BD89820E9DF067996DDD0E50C264CCC803F5EA (void);
extern void GameManager_Awake_m1E35BC392975868D0F46CCF6B207805C6E89514A (void);
extern void GameManager_OnDestroy_mEADD1F2BC76160633100F3730FDF8DE1A3F39B45 (void);
extern void GameManager_GetReferences_m0550A3C20F4F52C61C283A499B81DE7742F0F196 (void);
extern void GameManager_StartLevel_mFE2E9C3AA5ACF5C4B240EABEECD4090B03A9D717 (void);
extern void GameManager_DelayedInit_m1D82F0256B4DB7D453B9FF3E352D8939070854E0 (void);
extern void GameManager_ComputeCamera_m9874806A9BC3D0FF3E7640476057E0F4A3759FD8 (void);
extern void GameManager_MainMenuOpened_m0C49686D3F34BF490B0C829581AB08291D321D3C (void);
extern void GameManager_Update_mD24FAA83FF824E6ED4D95920D5142A28E2E1776D (void);
extern void GameManager_ChangeCoins_m9B56F93CBEF90A3C04D87338FEBFC440F195D0A3 (void);
extern void GameManager_WinStar_m0F6DFA6E9926843C1AB33306647CA4214B5B888C (void);
extern void GameManager_AddLive_mA11F8BD135703FBFCBC703CF0A74C12AB9952E57 (void);
extern void GameManager_LoseLife_m4079E60499EB43ABFFBE9F717B69E81F24C3AE13 (void);
extern void GameManager_UpdateVolumes_m8A468784C0F5D8F28968CE848D8B5FA43488C8CE (void);
extern void GameManager_SaveSoundData_m8011F3D2CB32324172F065514926B84DB2397A4E (void);
extern void GameManager_LoadSoundData_mF56328C023E3B9461F75395EF83B6AF1DB7F24B3 (void);
extern void GameManager_AddBonusItem_mFA2B9B9EE504A1206C47C1E38BED8EA2DBD25F65 (void);
extern void GameManager_ActivateBonusItem_mAA54AA343E995757AF760A51BF0155C7CCC1F168 (void);
extern void GameManager_UseBonusItem_m28C28799A1D6D91040F44F18EC0EB96E01775248 (void);
extern void GameManager_PlaySFX_m390BC781FCA313C914E3E25BB43EB7C1290D3074 (void);
extern void GameManager_WinTriggered_m8348C22E371CB4FA4E0381E82F54D2315C7FBA49 (void);
extern void GameManager_LooseTriggered_m716738164009EFDB14E3A9E2CA086BED329A0A3A (void);
extern void GameManager_SwitchMusic_m833AAF266851BB2FBFC91F3716D97D7A32608783 (void);
extern void GameManager__ctor_m6F1066273623D009E120B0A2262E6096CA3C0CB5 (void);
extern void SoundData__ctor_m4F510CEB58B97C4E3F6468B9824F5DF0AED7797B (void);
extern void BonusItemEntry__ctor_mF72770D80F00FCFBC3BD5310407098315FD0617D (void);
extern void U3CU3Ec__cctor_m8B0C43E3CD46F22268BB1772FFC2C74B1A847CB5 (void);
extern void U3CU3Ec__ctor_mCFBE66007F7BE8C0D973DE1CCF5945AC95E66223 (void);
extern void U3CU3Ec_U3CStartLevelU3Eb__41_0_m95C765F9EFF893906101D65E62E6C470E0DB2C8C (void);
extern void U3CU3Ec_U3CStartLevelU3Eb__41_1_m2DDC63DA9C6787F0BCDAEA3D51325D9861C4511D (void);
extern void U3CU3Ec__DisplayClass53_0__ctor_m4240F42E5D0865A12ED55D55CA4C1EECDB0767B3 (void);
extern void U3CU3Ec__DisplayClass53_0_U3CAddBonusItemU3Eb__0_mBADF4787DE8D81C7C1D74962E71BFB1F30982884 (void);
extern void U3CU3Ec__DisplayClass55_0__ctor_m44923FC12D13841BCA1A829A33E6DD87F5FA35EA (void);
extern void U3CU3Ec__DisplayClass55_0_U3CUseBonusItemU3Eb__0_m1E0A0A1F439FF9E7FB32EB5FF6375455FC2EF2B6 (void);
extern void U3CDelayedInitU3Ed__42__ctor_m1AA3194D6499356A70C838901E785E0E72A3FF0E (void);
extern void U3CDelayedInitU3Ed__42_System_IDisposable_Dispose_m5CE9BCEDFCF8F874B080E13FADFC7B9C8F7D5857 (void);
extern void U3CDelayedInitU3Ed__42_MoveNext_m9BC7643E13B2285497CB35EBC1718B58ECA313DB (void);
extern void U3CDelayedInitU3Ed__42_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mAB2C511D586FF5B660CF44D4EE599B651D46471E (void);
extern void U3CDelayedInitU3Ed__42_System_Collections_IEnumerator_Reset_mC429487BCEC4BB4B1AF4255D34489A2D89F5A897 (void);
extern void U3CDelayedInitU3Ed__42_System_Collections_IEnumerator_get_Current_mB1DB4EE5BED2D161F97E952127EEA8B52BF32E72 (void);
extern void GameSettings__ctor_mD4BBFCF986E62F57548EF60613165BD75E7E6656 (void);
extern void VisualSetting__ctor_mCBB360C42E97066B0CE267FEA2DFC36CAA6642C0 (void);
extern void BonusSetting__ctor_m1ECAE3C657119E32D0CF51B9EDE27A05440E16B5 (void);
extern void ShopSetting__ctor_m3F4D109C9E5694A93513B488EF7DB78E78CEE8F7 (void);
extern void ShopItem_CanBeBought_m65F0F8DD62A68C30D2204BC88715191988F96807 (void);
extern void ShopItem__ctor_mE40A7EA9FA4625AC77D964CCD8DD83670A9C9222 (void);
extern void SoundSetting__ctor_mEB9E78CB93B72BE449ACAD84D43322F5E3C93603 (void);
extern void Gem_get_CanMove_m181BBEC3D3E6D65BF91E57B3FFFB9481312DCB69 (void);
extern void Gem_get_CurrentIndex_m4275FDEA5BCB4B78E172E5982FD66A1DA1DB585D (void);
extern void Gem_get_Usable_mA7DC1191799E6DD31E423EB078AB6C47C52D56EC (void);
extern void Gem_get_Used_mA10615BA440E34372D1B17097A684E3B805DF38E (void);
extern void Gem_get_FallTime_m951916E72DF38D530CC5338C5B84CBE62E09077D (void);
extern void Gem_get_CurrentState_mCF2914BC9EC6305F1D4EF18D9B4D883AD2963659 (void);
extern void Gem_get_HitPoint_mE72E5E059F06560C70C3B38A1B5124EDFF7D948F (void);
extern void Gem_Init_m6AEDEDE244DE9BC08B685B54EC65CFF6F8D1F120 (void);
extern void Gem_Use_m69BC2EE54CE8BA530CDB719F7DD0DDCC600F10F8 (void);
extern void Gem_Damage_mE95D726C82691387C4F6B57975CDA8E17EDA15B5 (void);
extern void Gem_MoveTo_m665D31B183B21A0BC9DBC649DB0DBC5C7A9C0D1B (void);
extern void Gem_StartMoveTimer_mC67003CE6076A4848DAEBE971E430BBD42C6BCA3 (void);
extern void Gem_TickMoveTimer_mAE139BD15CF6ECD123354ACB16EE1773CF91504F (void);
extern void Gem_StopFalling_m66767912CFD945895D8128DB48A8703873281FF4 (void);
extern void Gem_StopBouncing_m03FE2CF3D02D8158DA6203CA7B903466E7DF3CF5 (void);
extern void Gem_Clear_m9F31E1EA2FF04515990766AB3BDDFF5600011437 (void);
extern void Gem_Destroyed_m2FF7F17FB8437B178AED19354C7BC5BE01ACDCBC (void);
extern void Gem__ctor_m2DFEE06668BB2042086F2C18083A5A3D3738D06D (void);
extern void InitLoader_Awake_mE5B7BD9B076ED09590017D74A1BA02873C8840F0 (void);
extern void InitLoader__ctor_m5D2C1B321A0AC4B8DDB4169BAF0C7C3DEA64067E (void);
extern void LevelData_get_Instance_mFA261197A16973C48DD986D278B381B06C304DB9 (void);
extern void LevelData_set_Instance_m919E3DF275123B18E93466F255397CEACE397986 (void);
extern void LevelData_get_RemainingMove_m75B91D05B0F1F86FD13C10F3BB880C35A37F939E (void);
extern void LevelData_set_RemainingMove_m3FDB90324169DD4E4700D070877B23DD569468BB (void);
extern void LevelData_get_GoalLeft_mB851E9360E927BD59E1077D372F0687859DF0D7C (void);
extern void LevelData_set_GoalLeft_m358E3AE124A668DF4E80F302B07C1E6A7B997E64 (void);
extern void LevelData_Awake_m9A604CA17535772326B70B75C0A008846B67BF60 (void);
extern void LevelData_Start_mC91F551B8C73EFC1A7C4E5D08EF0489DBD191341 (void);
extern void LevelData_Update_m6A3942BF346BCA069B6945388A39E0F79FDDD869 (void);
extern void LevelData_Matched_m25ED9F6E934A0CB6E9416C8036A0D7C4CDA48AFD (void);
extern void LevelData_DarkenBackground_mA6B80594C67969E805106005A04480B7744C091F (void);
extern void LevelData_Moved_m43E7BCFE1054CB8CA126F60B5FCB0B20E74CCDD7 (void);
extern void LevelData__ctor_m4772DEAC476C2F0BF68489509A3EDA114CEC8C58 (void);
extern void GemGoal__ctor_m7C6C5E7751217B721AA134BE30F14EDFAED74A59 (void);
extern void GoalChangeDelegate__ctor_mC2DCE39E160A61AE940EE9DEFFCCD670534BAFD3 (void);
extern void GoalChangeDelegate_Invoke_m8B7145436ABA68A5B668976FA93A90D21FFC3D7A (void);
extern void GoalChangeDelegate_BeginInvoke_mA9BCA119B6BFB09543A8E5BFD1588B0AD85714E2 (void);
extern void GoalChangeDelegate_EndInvoke_m2B82B418E75AA7FE39DD08119169154BE64B865C (void);
extern void MoveNotificationDelegate__ctor_m64A9E75FDEEFC7B7BA6300ACE1A54358B536D81E (void);
extern void MoveNotificationDelegate_Invoke_m496F973661A13BAF8DCD6DADD2AB6EF3177174D4 (void);
extern void MoveNotificationDelegate_BeginInvoke_mA5F1EC9579DBF4512C350754EDB3B343C2300728 (void);
extern void MoveNotificationDelegate_EndInvoke_mE62F950AAE163E64B2C2CB8A6E644860CDE6B01C (void);
extern void LevelList_get_SceneCount_m69E5DDE0197A14AF900D45103BF3C760703C8800 (void);
extern void LevelList_LoadLevel_m1D6F96A7644733CB01F9523E58AA2BF1ECA990DF (void);
extern void LevelList__ctor_m260BD9B19227F6E54A2A8A6CBBC758A2A947A317 (void);
extern void LightManager_Update_m32C2A8E30EB5FDF19A7320B715CA840945EF60A4 (void);
extern void LightManager_map_mE7032657080DB0D23058D2269DDBB5B07DAD858D (void);
extern void LightManager__ctor_m90A8AFF31C68C1753C5CACF538447B7056F08B56 (void);
extern void Match_AddGem_m0E6874A0A39ECDAC3B8C63E1F87AA2288084FEA9 (void);
extern void Match__ctor_mAC32C9E36C8F641B6714693295F3CB8411000F8A (void);
extern void Obstacle_Init_m63566D49E36BAE2BDE7E059D9EE920F30BB2380C (void);
extern void Obstacle_Clear_mED851B2BAAA93F8FF2E15110FDC3E10EF48ADE19 (void);
extern void Obstacle_Damage_m349A45805734E4475D6BCBEA0A4968EE76538521 (void);
extern void Obstacle_ChangeState_m1F26D149B0BD52CC14DE9B47E42476310C7F5F7A (void);
extern void Obstacle__ctor_m7FE76689F0E7B93CA96927CE5409DE93E46B9BCC (void);
extern void LockStateData__ctor_m58905CE70997AD2ACF6C9915B68D7BE4AA256938 (void);
extern void ShopItemBonusItem_Buy_mA9706A743DC7B394015221EE559676FE8EBA541E (void);
extern void ShopItemBonusItem__ctor_mF6C4C25971A237BA3789B79644F03964A2456D37 (void);
extern void ShopItemCoin_Buy_mC5D2232F390C073D3D46F481544F6ED459A8AA23 (void);
extern void ShopItemCoin__ctor_m62FC02E1BF2032E838CFEB86F5103AB05CAAA2B2 (void);
extern void ShopItemLive_Buy_m30BDB539A77A83D226432BE651BF678518E0C6C4 (void);
extern void ShopItemLive__ctor_mC41CB82306AD9F6BE89BBDCE5B3E06E4B399837D (void);
extern void TieBlocker_Init_m6B39B565F2609B504CE9E25391530A94C40B6BC4 (void);
extern void TieBlocker_Clear_m409C5211B3ABEF7362BA987061A6A07E710EE779 (void);
extern void TieBlocker_CellMatch_m2FEC24566FB7CC13CF3DB0BD3298666042C7369E (void);
extern void TieBlocker__ctor_mB2DA0208D1563E021860926F0FF6CD8F5393A6E4 (void);
extern void MainMenu_Start_mEB804965A99525AEAD83B6464BEEA9388CEE02F6 (void);
extern void MainMenu_FadeIn_mE8E6D8FD5600485C8569DCA60B6002AD3299B041 (void);
extern void MainMenu_FadeOut_m9DAFED7676DF879FA555A177ED28A0E71969DFC0 (void);
extern void MainMenu__ctor_m4F43E2347FAC1AF5C99B983E2497453D6C504B12 (void);
extern void MainMenu_U3CStartU3Eb__5_0_mE278F1415B5E0947B36058D6FC431129278DE181 (void);
extern void U3CU3Ec__DisplayClass5_0__ctor_m62AE472BEA5418EAF1F5AE30CBFD4DEAFF40619F (void);
extern void U3CU3Ec__DisplayClass5_0_U3CStartU3Eb__1_mAA72AF3133E692AEAF39617B146F25BFFB509503 (void);
extern void U3CFadeInU3Ed__6__ctor_m65340D8533768F1D8E9D6EDBDD4E10B31716656E (void);
extern void U3CFadeInU3Ed__6_System_IDisposable_Dispose_mE9B0A0E6D810CB940C8A6EC8C41C5361D24CECAB (void);
extern void U3CFadeInU3Ed__6_MoveNext_m9DA1CD924D5691FC89534D7A064E4E38DAD38E7C (void);
extern void U3CFadeInU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2FB709C15A60D2B0EE86398E604B26E690434764 (void);
extern void U3CFadeInU3Ed__6_System_Collections_IEnumerator_Reset_m0CF4C3727A78C5DFE817805960774320CB9CE3B8 (void);
extern void U3CFadeInU3Ed__6_System_Collections_IEnumerator_get_Current_m148CD0E53EC1E6EBF57B2E07E4D29E6C431F2D4A (void);
extern void UIHandler_get_Instance_m7EAC0B10A2DAD3142DCAB7A68B07398AAAD8201A (void);
extern void UIHandler_set_Instance_m0F7A8F583A3838936A201D67CBAB3C2ECE933254 (void);
extern void UIHandler_Awake_m00AB8C1ED88A7493F31BD4DBEFF96DC93ED310B1 (void);
extern void UIHandler_Start_m07B4278EA7DA905097C3868AB823739A676D84DF (void);
extern void UIHandler_Init_mE8410DA9FF8002FB9F2BE5AC350C1EDA36C7DF9A (void);
extern void UIHandler_Display_m49B4E4D357DC115D2FD1C476383691C605A8E0BE (void);
extern void UIHandler_ApplySafeArea_m4E3348CE281A5598F09D520EEB3D1F9A93FC6636 (void);
extern void UIHandler_ShowEnd_m3F42A6598939C4FFE20CACB02CC73D7CFE0B4775 (void);
extern void UIHandler_ShowWin_m190F503D9ED5D6E7C2462B7B349C209BAC6DDB4D (void);
extern void UIHandler_ShowLose_m7FAB94A63C403B3389B30B0DEEF76B1C5B9E4098 (void);
extern void UIHandler_ShowEndControl_m871B2FDD56ED59961F46C8B25808D3411B361B83 (void);
extern void UIHandler_ToggleSettingMenu_mB912E71A647331CEC90436818B2C729793859EB3 (void);
extern void UIHandler_FadeIn_m2972E57F1EA8A3C0CF8632740CA2E58A417D5DB5 (void);
extern void UIHandler_FadeOut_m852994403EE640198683C27D3EF355E416EC9C1A (void);
extern void UIHandler_SetCoverOpacityNoTransition_m5FFBE55A5F87CD4945F46C369FD4ABE13E478BD2 (void);
extern void UIHandler_AddMatchEffect_m0A30138ADADCE2C2DA9868543DDF1BAAEA82E9F1 (void);
extern void UIHandler_AddCoin_m1BC7AA489635DC625F6F323310FE320E0F3D1F6C (void);
extern void UIHandler_ShowShop_m51073040056D4ABEF05E8D91756E3980F9427D41 (void);
extern void UIHandler_Update_mDF52AEF331BD4E7EE85540980D590B8881837DFD (void);
extern void UIHandler_UpdateTopBarData_mB2AC1C94A3A20AC125888F9202C9277CA70AF004 (void);
extern void UIHandler_CreateBottomBar_mCB01EF0E4B3371D18329D95AF049BA16C0B45315 (void);
extern void UIHandler_UpdateBottomBar_m521FB1FB49290FA2B8AFA49385FD805C117DEF3D (void);
extern void UIHandler_DeselectBonusItem_m3BA80744CE55901B93195ABDD51BD19DAA073156 (void);
extern void UIHandler_UpdateShopEntry_m65B233A263607F1C61F4514882D367F3BC2EC519 (void);
extern void UIHandler_TriggerCharacterAnimation_mEB082453FDB2D912DB39722B06935C9B1E1EE941 (void);
extern void UIHandler__ctor_m9CE8770BB638EBA1B34ECEB2075D6755B8A9515D (void);
extern void UIHandler_U3CInitU3Eb__48_0_m75D3D7CF153D509FEBFFBA3BFB39AD70A294E2CA (void);
extern void UIHandler_U3CInitU3Eb__48_1_m4E12D795F6B1591EC9AF48B6EEDFD3F774C61F13 (void);
extern void UIAnimationEntry__ctor_m54B1CD7C5DFB60EACBD3F42965C07B443B2DA117 (void);
extern void ShopEntry_UpdateButtonState_m0949D285920EE8A56E3427D42157EC7CEABA92F5 (void);
extern void ShopEntry__ctor_m3D6D367D80AF8A28016F1898EC67B33C1DC0C8EB (void);
extern void U3CU3Ec__cctor_m3674B2604B7EAFB77512EF93544AE91FC59E018E (void);
extern void U3CU3Ec__ctor_mF0DCBDCB21D723695F4B0C943C2BF5724560C18D (void);
extern void U3CU3Ec_U3CStartU3Eb__47_11_m74105AB672B0D89959AA1FA5E9CF6AAEA69E7C01 (void);
extern void U3CU3Ec_U3CStartU3Eb__47_12_mE521FDB4C60FA65E73F3723AC1CD8032D82610EA (void);
extern void U3CU3Ec__DisplayClass47_0__ctor_m8066EB94ED205953E73A3BF3A56C581482801D57 (void);
extern void U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__0_m55F5DD8708AE19E1C6B35E37A02DF8F5D7938DBE (void);
extern void U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__1_mF6465761730F05BB87C0EC7D7CC0B1185EB183D8 (void);
extern void U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__2_mABA7EDEFA9D3DE8ED459589BEEA19369139EC1A1 (void);
extern void U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__3_m75BAA356682A51A72D0B6E5AF6320E88EBCAD43D (void);
extern void U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__13_m55BAED00E638E4129CF4FB84F014B1EFF329F8F3 (void);
extern void U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__4_m65418ED6A343AF195F97CE8BDF719C6158ED28DA (void);
extern void U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__5_mC652D6A0C85F83A58035D002CBF6C0A2BC9238A1 (void);
extern void U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__6_m12252C446310EFA2FD1B75160C108B78275CCE5A (void);
extern void U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__7_mE0EA0E5DCFDD527BB78C529503F44FAB29A9FC16 (void);
extern void U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__8_m6D450E79BD154F20FE81F27E3E1953F8E13DAB4A (void);
extern void U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__9_m704DC1F909660465E030323040575F916E5A9DBE (void);
extern void U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__10_m6C4077F98A0DD053799DE5BFC44611AEBD4B34C6 (void);
extern void U3CU3Ec__DisplayClass47_1__ctor_mAEE94283CB826A92C82ACFA0905DE8306D71021E (void);
extern void U3CU3Ec__DisplayClass47_1_U3CStartU3Eb__14_m60C62B579DCD15E03EA7BD687555F1825092D391 (void);
extern void U3CU3Ec__DisplayClass64_0__ctor_mA93D59362F1A19416E5BFB974A5E9CE37450E435 (void);
extern void U3CU3Ec__DisplayClass64_0_U3CCreateBottomBarU3Eb__0_m3925600501FD7EA69E25E2B80B3273F94800BEC4 (void);
extern void U3CShowEndControlU3Ed__54__ctor_mAE1541B291041D9A8FA8D7C4C14F3ED4920698FB (void);
extern void U3CShowEndControlU3Ed__54_System_IDisposable_Dispose_m4BA4C4B4ADB06F468868A917FDE21B6144A5462D (void);
extern void U3CShowEndControlU3Ed__54_MoveNext_mD9E96415AF76B2912DC98DBBEFB8FE1D71ADA4F2 (void);
extern void U3CShowEndControlU3Ed__54_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m97C6301E85046F481808DCB003B61DD5CCA5DEB5 (void);
extern void U3CShowEndControlU3Ed__54_System_Collections_IEnumerator_Reset_m4083C9C7BC627616568F199E9680F670A8A78BCF (void);
extern void U3CShowEndControlU3Ed__54_System_Collections_IEnumerator_get_Current_mC92B724AC2D5795FD63A86DCBEA4497C6ED0F851 (void);
extern void VFXPoolSystem_Clean_m202E61C451ACDB00DBFB5C7E51EA256DB8493B48 (void);
extern void VFXPoolSystem_Update_m4A8F4DF6C5ED1DEAA4BBCEDB99D4ACEF6400B309 (void);
extern void VFXPoolSystem_AddNewInstance_mF86C09581AB27EEDAE03AA9725C5B486DB5B1797 (void);
extern void VFXPoolSystem_PlayInstanceAt_m529B72909F50DB3C663B69A666B8B34D5570C978 (void);
extern void VFXPoolSystem_GetInstance_mD9235827A1EF6660DD29F7037B53BA8D43542FC8 (void);
extern void VFXPoolSystem__ctor_mD4A2291A9A60CE601109904CC149C625B3889835 (void);
extern void VFXInstance__ctor_mE82BCD982A617CAC5F29FB0291E80DF7555D7B9E (void);
static Il2CppMethodPointer s_methodPointers[411] = 
{
	FPSMeter_OnEnable_m282EC52AEAC8D57EE9FD69B31BD4F2A15FE759A2,
	FPSMeter_Update_m6EC34DC7F1FC4A13E45F177327D2A3587933C1C7,
	FPSMeter__ctor_m81BF8F0E163DBC4AEE672E95860810D98B083F79,
	AdConfig_get_AppKey_m6DA4AC42AAD3F7D858522804DB62387EB186EB8A,
	AdConfig_get_BannerAdUnitId_m22EAC9E61BB55AE9446A94F5735F00894AAFF3B0,
	AdConfig_get_InterstitalAdUnitId_mD920BEFA215FD9C65AB7A6712F30F3DDF37BE05A,
	AdConfig_get_RewardedVideoAdUnitId_m44D12E1509DBE402094A4E4D5E77E0F069008197,
	AdConfig_GetAppKey_m69298F64CCC5201F0EDDEBA124D78B75C256E66E,
	AdConfig_GetBannerAdUnitId_m54C810FD18C939970F42117CD039FDAD08AE44AA,
	AdConfig_GetInterstitialAdUnitId_mA058379ECCFA113C2DC1D49576C958DB112E86D7,
	AdConfig_GetRewardedVideoAdUnitId_mF45C7CC2D3510849656C068E1283E63D9F062C4C,
	LevelPlaySample_Start_mF8FEBB5FCF9E647542F23C2DFB42F08431BEFB25,
	LevelPlaySample_EnableAds_mD107E91A7162E886A03721C44A3065AD36F0439C,
	LevelPlaySample_OnGUI_m98229292EFDC32DFCEB00C42615AB7E99AF7C534,
	LevelPlaySample_SdkInitializationCompletedEvent_m6A23269AF1AA1E7E85FD9C4D43CDB7D58377D95E,
	LevelPlaySample_SdkInitializationFailedEvent_mCF6D2EDE19BF491F1722216994540F1736082B5F,
	LevelPlaySample_RewardedVideoOnLoadedEvent_m6F7635FBC65AFA0BA2C0454F8D1A3FAD8522D25B,
	LevelPlaySample_RewardedVideoOnAdLoadFailedEvent_m7A212DB9706E91065E18DE92FE9828219B8C7DF4,
	LevelPlaySample_RewardedVideoOnAdDisplayedEvent_mA73C685B895CBD21F9D3E0E3B4C01566F5EDF4EA,
	LevelPlaySample_RewardedVideoOnAdDisplayedFailedEvent_m7C8BA504D3149BD5160CA3253D077FABD4A3185F,
	LevelPlaySample_RewardedVideoOnAdRewardedEvent_mBF963CDDD55E70886568CB62E8A4AD8076AF4551,
	LevelPlaySample_RewardedVideoOnAdClickedEvent_mC5EECC2AEB4DE526E85F1E91A085B26023686346,
	LevelPlaySample_RewardedVideoOnAdClosedEvent_mE172BB7F1F813B191C7B41887C6770153AC8FD0A,
	LevelPlaySample_RewardedVideoOnAdInfoChangedEvent_m3685D46596D4247B2A1F11088B51A470D6DBD71F,
	LevelPlaySample_InterstitialOnAdLoadedEvent_m2155C7AF75158EA07C3DCF5FDAEC1C88A97ADDC3,
	LevelPlaySample_InterstitialOnAdLoadFailedEvent_m6F5F5C3E0745F5D18836F72DAB5AD9969B1C71E0,
	LevelPlaySample_InterstitialOnAdDisplayedEvent_m36130DA8C20A73015B786071C1392D508AE3EFBB,
	LevelPlaySample_InterstitialOnAdDisplayFailedEvent_mCBAC25BF9DA95862B82027708DFEE9311BB36F4C,
	LevelPlaySample_InterstitialOnAdClickedEvent_mB8567497EACD9F56BCAA2FD340226E26B9CB40E2,
	LevelPlaySample_InterstitialOnAdClosedEvent_mB9EC434D33B3604B4CACEDC58AE49820245BE8E5,
	LevelPlaySample_InterstitialOnAdInfoChangedEvent_m1C80797731683CE46B8C52641911E8BA93A5C18A,
	LevelPlaySample_BannerOnAdLoadedEvent_mE3228F53EA5845FF7012B90564BBF780511D968D,
	LevelPlaySample_BannerOnAdLoadFailedEvent_mBC5EFE967CABDF5E763219792117D28D4670090C,
	LevelPlaySample_BannerOnAdClickedEvent_m1C0BA242D5E3392CA76941BADEB7C79623642534,
	LevelPlaySample_BannerOnAdDisplayedEvent_mCC1DA608D4341D5EFDB89C823918F460390C1EC5,
	LevelPlaySample_BannerOnAdDisplayFailedEvent_m944A73E95D28CC426875B905E9D2AF142BBBC8D1,
	LevelPlaySample_BannerOnAdCollapsedEvent_m14A4B71E37F6EB86785535C7550E0042D178439A,
	LevelPlaySample_BannerOnAdLeftApplicationEvent_mD77E9C82286B5FA62C7D934AE2F33C920274BAB7,
	LevelPlaySample_BannerOnAdExpandedEvent_m62AB29A94709D17605F74A452367B41B01BD7F32,
	LevelPlaySample_ImpressionDataReadyEvent_m53FCB7ECD3373143DB2E3C0F67150FAF39EB4256,
	LevelPlaySample_OnDisable_m989FD6F1FA1C8ED9EE5A771D87D66911632DE325,
	LevelPlaySample__ctor_mC55DD77FAAC44605B746576C01C0C8C6E83C81C4,
	ZStringExample_Start_m85D621DB211F7AB6B615E3D1DEA49A8D0C157DA0,
	ZStringExample_OnDestroy_mFFA3D21D9462A7F83B4FF8B67170DE21C8E2CC76,
	ZStringExample_RunAllExamples_m925707EDDEA6B409ACA90528BC6C6300B8B96216,
	ZStringExample_Example1_BasicStringBuilding_m2B6F87707442B9085BC365003E3FCEB2B6AABA7E,
	ZStringExample_Example2_StringFormatting_m9D701C1C59B1CDBD92B816D43EA8EDE6C0964A9F,
	ZStringExample_Example3_NumberFormatting_m03AA5CB57087D1E0FF3F5D9FE0D310B4B7A04A32,
	ZStringExample_Example4_StringConcatenation_m15A493D2F18B94DCB64A52C751231CB41A3A3636,
	ZStringExample_Example5_ComplexStringBuilding_mB20A65A4F0AF426F65A4113FD3B434FB39C3F5BE,
	ZStringExample_Example6_PerformanceComparison_mCBD05A5C340407D1ACC55F3F68BA7FB5EDE81491,
	ZStringExample_Example7_ThreadStaticUsage_mAFD9D7A08E3AE171C402F98DCFD1313B8C86650D,
	ZStringExample_Example8_ReusableStringBuilder_m77FB0EB3EBC6BB0250177759398D73220E0C3F54,
	ZStringExample_Update_m4A5AF6DA0DE2CFDFE4DE6BCB2FA11E2766A10705,
	ZStringExample_BuildDebugString_m556AE922FF4A15D49B9FED62F59E46FDCF5675B1,
	ZStringExample_BuildUIText_m16B69A59680D8727AFCC6CD34AF37E0FC8DA6212,
	ZStringExample_BuildLogMessage_mB0ECB6F19C377D745829708DFAC0AB2488EEFAF6,
	ZStringExample__ctor_m4BFBDC491B27F82CCFA61F5542B898373B71C890,
	ZStringExample_U3CStartU3Eb__3_0_m636DFEF6490C32EB881F61B75CE6DD13DD3E8E6D,
	ZStringExample_U3COnDestroyU3Eb__4_0_m6417D89D9D9716EEC29534312572DDA57D5EB51A,
	ZStringGameExamples_BuildScoreText_m71AE99948DB1AB25087809DDA42B89592F3776A2,
	ZStringGameExamples_BuildStatusText_mD378FF2005B1C9F48268C677B229F4367EA978F1,
	ZStringGameExamples_BuildDebugInfo_m2F9E2C377D17E00333428509EAF333F138BA4C4E,
	ZStringGameExamples_BuildAchievementText_m6970A551C5C32596464B787CE8E06877AE5511DE,
	ZStringGameExamples_BuildInventoryItemText_m0B2ECC9F01026B1E64907516A680D3FE5087D149,
	ZStringGameExamples_BuildLevelSummary_mB804C01CE8E06E995F1891B0D57AFE7D08DC356C,
	ZStringGameExamples_BuildErrorMessage_m1C7799C7B73EB942954AD342BA90908FFE3FE439,
	ZStringGameExamples_BuildNetworkPacket_m119E6D65C13ADEA4054CB6E5CA937DA721B30684,
	ZStringGameExamples_BuildCSVRow_m4B03258957C1FA0B77360462E0D29A9879D5DF2C,
	ZStringGameExamples_BuildSimpleJSON_m7DFEE614C5EE400D6FE2182D22AA51466ABDF0BB,
	ZStringGameExamples_BuildTooltipText_mFA5CBC3045A67A05E69E3831426FB2A583EBF80F,
	ZStringGameExamples_BuildLeaderboardEntry_m1E577DCD77A3F5A14DE92B0A1B3984DE66079665,
	ZStringGameExamples_BuildSaveData_m58CAAA2A89EA0FFD27C224DE888E4B6B2AAD4CB3,
	ZStringGameExamples_BuildPerformanceMetrics_mC99A9497B3AEA4EDBE75C4F645B996C3539B525A,
	ZStringGameExamples_BuildGameStateLog_mF91846A47403585916FCE44546B2E052039F6EDE,
	ZStringGameExamples_BuildConfigString_m40171D8B625447AF8A3CAF18D6718A61C17C07EB,
	ZStringGameExamples_BuildVersionString_m0B0E4C14B202358AE8997160DC2D447D5F346C51,
	ZStringGameExamples_BuildProgressText_mAEBD7CD732E4D2CF800C88CDCBAF8AF19E263F23,
	ZStringGameExamples_BuildTimeRemainingText_m7F4964DA02EDD2726B8021D17CD5BBE9F8E50044,
	ZStringGameExamples_BuildComboText_m74EF17BDD848C56286BC90D8054A2211097D466F,
	ZStringGameExamples_Start_m4870E3C3BC6845016A5577AF32DB711C78C79426,
	ZStringGameExamples_TestAllExamples_mC534A616C976B84836A8970E0471B778B8AAA33F,
	ZStringGameExamples__ctor_m412B73BBAC082058E96F3C1F6F1F6FFD448A76B7,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBEB95BEB954BB63E9710BBC7AD5E78C4CB0A0033,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE70FB23ACC1EA12ABC948AA22C2E78B2D0AA39B1,
	GemPlacerTile_GetTileData_mD03D6A7C928D2554E2A5164484F08B159C2EF3B6,
	GemPlacerTile_StartUp_m736E544809E2930E3358C415B74CFF75A2A3CD41,
	GemPlacerTile__ctor_m67288FB545CA9CFDD53427EF9F553B98A8E20326,
	GemSpawner_GetTileData_m7F9E2F7E8FCF5B18AA3EC49E1133A5B49FD69EA8,
	GemSpawner_StartUp_m88585914BBE796386DE1DE4642FD392823A858B4,
	GemSpawner__ctor_m5AA6E842A519DEA522AC1C73AB5FA518DB1AD2A4,
	ObstaclePlacer_GetTileData_m136410E54FC76752C28A42871807E9C9C5A937AC,
	ObstaclePlacer_StartUp_m9D08DC9E0CABEED8EE2BD6E87B754BBB27FBEB6B,
	ObstaclePlacer__ctor_mB1500F51638A29AAFEEFA6563CFFA6771734B25D,
	Board_get_Bounds_m04FC2F12A137C4D1C3B38B00090B99B7A3D90EFB,
	Board_get_Grid_mC2AAC6E3956F18E81BB859CEEBBCD4420D456865,
	Board_Awake_m8CA5CE36331C6CA5C3181B843567E8951823ABFF,
	Board_Start_mF9266E493FC5AA5BD3276E1825D035E79670818A,
	Board_OnDestroy_m093AB152ADFE4DE7537B6C41BD49EA0F70DF99C3,
	Board_GetReference_m142837CDC47B9210F8DD623BF2C257240D61CB49,
	Board_ToggleInput_m9BE9C75C3C47EDC4324AFA33DF0792BFFFBF390A,
	Board_TriggerFinalStretch_m9C1D39D478B6A6DC2C83AEA58E727AC1E42BC28D,
	Board_LockMovement_m90C1B5D8C178461BCF5FA585B8DAB212A2240EEA,
	Board_UnlockMovement_m73F4709EDF8E1B01B01A9FDC25279E909D4BC6C2,
	Board_Init_m29BC5943AC84E65C4C2CE0CDBF177A3B7F99E10F,
	Board_WaitToFadeIn_mA49F46A034788DCB0C13AE8DE7ADBC12294C8811,
	Board_RegisterCell_m257A6C0AA8CAEF1B3F2B2F98977693EFB7F867A5,
	Board_AddObstacle_m1D27E3E161341C0B59DDC6A944670CF2DC5E4906,
	Board_AddObstacleGem_mDF41AFBA77FF7B31CD3BAC43469BC4E2C92A57A7,
	Board_ChangeLock_m4D0B915BF6254BBD077F1F9099112F967E6A5A8F,
	Board_RegisterDeletedCallback_mA3DC9FB6FD61B022C9EC59D121B51AEF317FFB8C,
	Board_UnregisterDeletedCallback_mE2E178038A0E81DBC16B30BD1D80E394BAC55A52,
	Board_RegisterMatchedCallback_m8BF54145B1C77313FE6613C19ED5BB9D4A74AA61,
	Board_UnregisterMatchedCallback_m60AA9DE5D07C88C2FB17CDDBFFE720B0066D9D18,
	Board_RegisterSpawner_mA2B614E97C70C045DBF6E3591A92F442C99FD283,
	Board_GenerateBoard_m79B9B52FFEB2AD251CDDF35D6AB93F3AF664F7AF,
	Board_Update_m8CE4CD52588E4E9CDC6B189AF06BECC01EEAAA6B,
	Board_MoveGems_m1FA360E3D97AACD0F45260542EEB3C99E1470CF3,
	Board_MatchTicking_mA415B3FDFF3282C2A73276534FC12795EB2676B3,
	Board_DestroyGem_m3B4DD1B4E6DD7A6E282AC11D1F3DFD612A2BA441,
	Board_GetCellCenter_m08D36E21536522D2B36E85B2359C39B3E0F1657F,
	Board_WorldToCell_m3D8516CD911C62CDC8F40FCCAB0AA23E51A7A64D,
	Board_AddNewBoardAction_m4AD6876AF1E271F2CF0AAC20D0F3C1B9C15BBAAD,
	Board_CreateCustomMatch_m298742B68971969AB02EE7E004F6DB8F0E7B8022,
	Board_EmptyCheck_m45B4510F14C04C30C79CD7B22E93ACDBD7560C88,
	Board_DoMatchCheck_mC0C0794EC19D46ED1D7E1C7F64BB3BB97FD5D022,
	Board_DrawDebugCross_mB670C9DDD6BE87D027AA9AB7F67E9A50A175E846,
	Board_NewGemAt_m9AD629527703C18592F9C9C004A606E081B06FD5,
	Board_ActivateSpawnerAt_m34942108B8EF243E4B11A8968FD223D00B657893,
	Board_TickSwap_m4AB5EA4F5C08C7296E6D7146078789065C3B6FA6,
	Board_DoCheck_mD77484E1B2502C4500063AD3D12E532AD48DEB71,
	Board_CheckInput_mA56C4EDCF523236704CA1DC836A398E989C5A810,
	Board_ActivateBonusItem_mFE0CD53F7C0385E99E795D81FEFAFFC914569DC2,
	Board_FindAllPossibleMatch_m957F4F5F8C46FDF0354ED83229F4DBF5FD091DD5,
	Board__ctor_m78FB8DAC1D25C9DB91544B7D865969943507C7C1,
	Board_U3CWaitToFadeInU3Eb__54_0_m51A8025547F3D02C420BA6E6D06D28C8EA885AFB,
	NULL,
	PossibleSwap__ctor_m9596C364B0A454547AC727A30731213C822B0A75,
	U3CU3Ec__cctor_m6035E6BF35334A8CFF0B2D0E42ED9EBCFC94211B,
	U3CU3Ec__ctor_m9FA9FC438A44B5D2C3A8525F5FCF673163B0E3DB,
	U3CU3Ec_U3CMoveGemsU3Eb__66_0_m00EC83C37CE6A7101914E36649C478D8D3D31DA3,
	U3CWaitToFadeInU3Ed__54__ctor_mA902964A0D0A4CEC174F647C804A4B09EC40BD15,
	U3CWaitToFadeInU3Ed__54_System_IDisposable_Dispose_mD05D38F8EA8954423A06F2151222AF33C479BA4B,
	U3CWaitToFadeInU3Ed__54_MoveNext_m53D63053FC01E6A1146F8FE7C69D06B1DF34DF87,
	U3CWaitToFadeInU3Ed__54_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEAC52759F38996FBFD7E9E53C579797F88AC6B9C,
	U3CWaitToFadeInU3Ed__54_System_Collections_IEnumerator_Reset_mC68E2D8E1049B58FD9F193E4DC0AC7433C748D45,
	U3CWaitToFadeInU3Ed__54_System_Collections_IEnumerator_get_Current_m5359C279E5E2B67204BBE9A996FDE79261CD92AD,
	BoardCell_get_CanFall_m31C8304D9ACE066771B4A0CD24C948E21AB4E5C1,
	BoardCell_get_BlockFall_m98F76C34F7DF4F011CB379E1B1F1AC7098189667,
	BoardCell_get_CanBeMoved_m8C5CA8C2B1AC5BFAB354492C42BD3A7833B55B20,
	BoardCell_CanMatch_m2A66DE8AB448CF041A010C7E227FB14FF0E36B37,
	BoardCell_CanDelete_mDE03D0B34616B59A4CE9A1E3FFF4B07F373A9486,
	BoardCell_IsEmpty_m8814AEB278850BFFCA9008D81A12CABEE03AF455,
	BoardCell__ctor_mBBF557B9CDE72A40CD39A33818ED98C02029D17B,
	BoardCell__cctor_m430770A3AB894A179CCD367D211B32D957FE0084,
	BonusGem_Awake_m3F071C1577B25A0D0F49BC97D0324047F942B049,
	BonusGem_HandleContent_mB0B65284A34A5E58CB6713A5D409AA24BFA07488,
	BonusGem_BonusTriggerEffect_mB6D8BE80C93E54247445C28F570BA2CFBFFAF0D6,
	BonusGem__ctor_m724832E114D1776BF07283C6C8DB7FFCF5C96DC8,
	MatchShape_OnBeforeSerialize_mC0FACE6BBADF7797D21892C45CC97074CDDF4E75,
	MatchShape_OnAfterDeserialize_mC3A7E9C45A567F84DC79E9722C7D036D02BD59E7,
	MatchShape_FitIn_m6BD3A69C363CB6792DA74F3FFCE6FFD1C3D58C75,
	MatchShape_GetRotation_m7595E508531F955EE420132B1BA546B8857DF5FB,
	MatchShape_GetBoundOf_m8D1B56A385C05F04E3AF2FAAD194B00F048C5A34,
	MatchShape__ctor_m97829DFF477D067EC548B6C7E15F4C3C9D8D0026,
	ColorClean_Awake_mFB240B40875301626163A334C0ADB48FCB2D7D3B,
	ColorClean_Use_m570A06DA0C76E157B818BE268327628B5C1D15A7,
	ColorClean__ctor_mE322CE7C60FFC06636E7A39068B495B0D63CD108,
	JumpingFish_Awake_m544FBB6A4E6162EC84A977404A8B8EE260C445B2,
	JumpingFish_Use_m19B4F00FCEADB949A735E8B95E388FBE6A45F308,
	JumpingFish_FindTarget_mAA7A71A316D7CF3437653B79C2AEB4D6C656971B,
	JumpingFish__ctor_m1C96BF21C4ACD22227C1160FDA0A0C75CE686487,
	U3CU3Ec__cctor_m98239FB053406C79E80873717CC8F7DD5CEA900B,
	U3CU3Ec__ctor_m808CECCB576D1A8E096208761C9D0A97312A7F13,
	U3CU3Ec_U3CFindTargetU3Eb__3_0_m1470C6396A72799CDAC8926DA2989B21EC1302E0,
	U3CU3Ec_U3CFindTargetU3Eb__3_1_mF41A035ACB40CFD130B6806450369963A20854AF,
	U3CU3Ec_U3CFindTargetU3Eb__3_2_mBAC6628E793872A2EB1D7261AEE3712A2C43E870,
	U3CU3Ec_U3CFindTargetU3Eb__3_3_mFEFA1485E6CF598E37E4AB487791F9FD5F30AD5D,
	LargeBomb_Awake_m9E022B53626932F5EBBC4289AA13500644B38E00,
	LargeBomb_Use_m04232BDB87AD9083F2F1A62436F66453F06497E1,
	LargeBomb__ctor_m2D23173FC25AE98814EFFA8BFA0281608FE02C90,
	LineRocket_Awake_m0F931FEC5C6F7072EB8F591ADBCA8D5395DEF651,
	LineRocket_Use_mCC002F8B0D1EAAF15A3B72EE6938A2C35164268A,
	LineRocket__ctor_mCBA76320AF6AB22EB74C8F65E773A507E4174DFC,
	RocketAction__ctor_m6DFDD28F9E0A12C34A9D613006180872D2BDECDF,
	RocketAction_Tick_mAA4CABE7038A99043BB40EED4027539CF83C204B,
	SmallBomb_Awake_m82CD30B81EDC5F4B94ACE00E3DB4200C3C79E2FC,
	SmallBomb_Use_m9A7FCD1AB02AED321678CAD013C2A9C39B5143AD,
	SmallBomb__ctor_m8FED95D2B3E42DC633CF5B85AEDD7E2BA71432D6,
	BonusGemBonusItem_Use_m72049CECE392148F9215274EEAC682D3ECD70F6D,
	BonusGemBonusItem__ctor_m60CB622FE77FE6ED170BE74044D41629D6DCA23C,
	NULL,
	BonusItem__ctor_m51ACA2AA94F8A8651F8293B0784261B8B9A7AB66,
	Crate_Awake_m7BA0B26B1D85171EB3FFB8705CA05C9732715645,
	Crate_Init_mAC38FF7BCBAA05CFD2CFD44E0908ACC49DF94343,
	Crate_Damage_m352056E99AF9F18755A5B1DA2797A8DEADBE2425,
	Crate_OnDestroy_mCBF286EF162A5FD0BCF645EAF799466793A0AB1A,
	Crate_AdjacentMatch_m26B785CAF4C690A8F91D5AB9FBD591C94F059834,
	Crate_UpdateState_m85F4767E26713917E994C40AB2A3A5640B0B5AE5,
	Crate__ctor_m1A2D4C85DDDB2E981FBAFB9954C7658603E29724,
	FrozenIce_Awake_m6B2830E2C934620414BD7A1DEF2F7898E1B34703,
	FrozenIce_Init_m1B5ECEA7009F7B9ED0B68793E9B15BF25C63BE69,
	FrozenIce_Damage_m150D187914EF462885F6100F8A5EE84672F6B5A2,
	FrozenIce_OnDestroy_m41B257AA32001EFDFA68D6B523CDDA8445E6C10A,
	FrozenIce_AdjacentMatch_mD18AB2C3E95258C7E528EA287159C81DC7AA9949,
	FrozenIce_Clear_mBFE3F7335B59259EBFC45E15C74BE24782CC33E2,
	FrozenIce_UpdateState_mBBCA03E0BE8F4FD24FA7E1237A279BA96828ECCF,
	FrozenIce__ctor_mB97F6230F9F5E2839F2B6AAEB7A62F47286F6C17,
	GameManager_get_Instance_mCA164312C1A40D4A73B8E35C23E297CDE085E33A,
	GameManager_set_Instance_m4DE067BA347C2EFCB41C80F437E2E8C143480BB7,
	GameManager_IsShuttingDown_m0DED12FDEA496799F33DA10BE678250966B2F03A,
	GameManager_get_Coins_m544A17D9DD8DDC906DF720A628C6AD6E130DFD59,
	GameManager_set_Coins_mF1FB9B188FA838859607F7ED400D9547E147AAC0,
	GameManager_get_Stars_mE9620FFA33A5330EF0F5DBF1DADFDD7FD775FB80,
	GameManager_set_Stars_mA66E43C741FED26F76C11EF91797C50CF2223F8C,
	GameManager_get_Lives_m0D96CBF5C1E60453D4F884445FC9BD70809EDCF4,
	GameManager_set_Lives_m5AEFA782B8FC4F6F8E5CD1A08DB4CBBB1584BD7F,
	GameManager_get_Volumes_m76041CB9A3F6DCEA0800A815078B8E59492AD15C,
	GameManager_get_PoolSystem_m376567F99B2FB070096C0D7F1C6DA1C664D9B526,
	GameManager_set_PoolSystem_m60BD89820E9DF067996DDD0E50C264CCC803F5EA,
	GameManager_Awake_m1E35BC392975868D0F46CCF6B207805C6E89514A,
	GameManager_OnDestroy_mEADD1F2BC76160633100F3730FDF8DE1A3F39B45,
	GameManager_GetReferences_m0550A3C20F4F52C61C283A499B81DE7742F0F196,
	GameManager_StartLevel_mFE2E9C3AA5ACF5C4B240EABEECD4090B03A9D717,
	GameManager_DelayedInit_m1D82F0256B4DB7D453B9FF3E352D8939070854E0,
	GameManager_ComputeCamera_m9874806A9BC3D0FF3E7640476057E0F4A3759FD8,
	GameManager_MainMenuOpened_m0C49686D3F34BF490B0C829581AB08291D321D3C,
	GameManager_Update_mD24FAA83FF824E6ED4D95920D5142A28E2E1776D,
	GameManager_ChangeCoins_m9B56F93CBEF90A3C04D87338FEBFC440F195D0A3,
	GameManager_WinStar_m0F6DFA6E9926843C1AB33306647CA4214B5B888C,
	GameManager_AddLive_mA11F8BD135703FBFCBC703CF0A74C12AB9952E57,
	GameManager_LoseLife_m4079E60499EB43ABFFBE9F717B69E81F24C3AE13,
	GameManager_UpdateVolumes_m8A468784C0F5D8F28968CE848D8B5FA43488C8CE,
	GameManager_SaveSoundData_m8011F3D2CB32324172F065514926B84DB2397A4E,
	GameManager_LoadSoundData_mF56328C023E3B9461F75395EF83B6AF1DB7F24B3,
	GameManager_AddBonusItem_mFA2B9B9EE504A1206C47C1E38BED8EA2DBD25F65,
	GameManager_ActivateBonusItem_mAA54AA343E995757AF760A51BF0155C7CCC1F168,
	GameManager_UseBonusItem_m28C28799A1D6D91040F44F18EC0EB96E01775248,
	GameManager_PlaySFX_m390BC781FCA313C914E3E25BB43EB7C1290D3074,
	GameManager_WinTriggered_m8348C22E371CB4FA4E0381E82F54D2315C7FBA49,
	GameManager_LooseTriggered_m716738164009EFDB14E3A9E2CA086BED329A0A3A,
	GameManager_SwitchMusic_m833AAF266851BB2FBFC91F3716D97D7A32608783,
	GameManager__ctor_m6F1066273623D009E120B0A2262E6096CA3C0CB5,
	SoundData__ctor_m4F510CEB58B97C4E3F6468B9824F5DF0AED7797B,
	BonusItemEntry__ctor_mF72770D80F00FCFBC3BD5310407098315FD0617D,
	U3CU3Ec__cctor_m8B0C43E3CD46F22268BB1772FFC2C74B1A847CB5,
	U3CU3Ec__ctor_mCFBE66007F7BE8C0D973DE1CCF5945AC95E66223,
	U3CU3Ec_U3CStartLevelU3Eb__41_0_m95C765F9EFF893906101D65E62E6C470E0DB2C8C,
	U3CU3Ec_U3CStartLevelU3Eb__41_1_m2DDC63DA9C6787F0BCDAEA3D51325D9861C4511D,
	U3CU3Ec__DisplayClass53_0__ctor_m4240F42E5D0865A12ED55D55CA4C1EECDB0767B3,
	U3CU3Ec__DisplayClass53_0_U3CAddBonusItemU3Eb__0_mBADF4787DE8D81C7C1D74962E71BFB1F30982884,
	U3CU3Ec__DisplayClass55_0__ctor_m44923FC12D13841BCA1A829A33E6DD87F5FA35EA,
	U3CU3Ec__DisplayClass55_0_U3CUseBonusItemU3Eb__0_m1E0A0A1F439FF9E7FB32EB5FF6375455FC2EF2B6,
	U3CDelayedInitU3Ed__42__ctor_m1AA3194D6499356A70C838901E785E0E72A3FF0E,
	U3CDelayedInitU3Ed__42_System_IDisposable_Dispose_m5CE9BCEDFCF8F874B080E13FADFC7B9C8F7D5857,
	U3CDelayedInitU3Ed__42_MoveNext_m9BC7643E13B2285497CB35EBC1718B58ECA313DB,
	U3CDelayedInitU3Ed__42_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mAB2C511D586FF5B660CF44D4EE599B651D46471E,
	U3CDelayedInitU3Ed__42_System_Collections_IEnumerator_Reset_mC429487BCEC4BB4B1AF4255D34489A2D89F5A897,
	U3CDelayedInitU3Ed__42_System_Collections_IEnumerator_get_Current_mB1DB4EE5BED2D161F97E952127EEA8B52BF32E72,
	GameSettings__ctor_mD4BBFCF986E62F57548EF60613165BD75E7E6656,
	VisualSetting__ctor_mCBB360C42E97066B0CE267FEA2DFC36CAA6642C0,
	BonusSetting__ctor_m1ECAE3C657119E32D0CF51B9EDE27A05440E16B5,
	ShopSetting__ctor_m3F4D109C9E5694A93513B488EF7DB78E78CEE8F7,
	ShopItem_CanBeBought_m65F0F8DD62A68C30D2204BC88715191988F96807,
	NULL,
	ShopItem__ctor_mE40A7EA9FA4625AC77D964CCD8DD83670A9C9222,
	SoundSetting__ctor_mEB9E78CB93B72BE449ACAD84D43322F5E3C93603,
	Gem_get_CanMove_m181BBEC3D3E6D65BF91E57B3FFFB9481312DCB69,
	Gem_get_CurrentIndex_m4275FDEA5BCB4B78E172E5982FD66A1DA1DB585D,
	Gem_get_Usable_mA7DC1191799E6DD31E423EB078AB6C47C52D56EC,
	Gem_get_Used_mA10615BA440E34372D1B17097A684E3B805DF38E,
	Gem_get_FallTime_m951916E72DF38D530CC5338C5B84CBE62E09077D,
	Gem_get_CurrentState_mCF2914BC9EC6305F1D4EF18D9B4D883AD2963659,
	Gem_get_HitPoint_mE72E5E059F06560C70C3B38A1B5124EDFF7D948F,
	Gem_Init_m6AEDEDE244DE9BC08B685B54EC65CFF6F8D1F120,
	Gem_Use_m69BC2EE54CE8BA530CDB719F7DD0DDCC600F10F8,
	Gem_Damage_mE95D726C82691387C4F6B57975CDA8E17EDA15B5,
	Gem_MoveTo_m665D31B183B21A0BC9DBC649DB0DBC5C7A9C0D1B,
	Gem_StartMoveTimer_mC67003CE6076A4848DAEBE971E430BBD42C6BCA3,
	Gem_TickMoveTimer_mAE139BD15CF6ECD123354ACB16EE1773CF91504F,
	Gem_StopFalling_m66767912CFD945895D8128DB48A8703873281FF4,
	Gem_StopBouncing_m03FE2CF3D02D8158DA6203CA7B903466E7DF3CF5,
	Gem_Clear_m9F31E1EA2FF04515990766AB3BDDFF5600011437,
	Gem_Destroyed_m2FF7F17FB8437B178AED19354C7BC5BE01ACDCBC,
	Gem__ctor_m2DFEE06668BB2042086F2C18083A5A3D3738D06D,
	InitLoader_Awake_mE5B7BD9B076ED09590017D74A1BA02873C8840F0,
	InitLoader__ctor_m5D2C1B321A0AC4B8DDB4169BAF0C7C3DEA64067E,
	LevelData_get_Instance_mFA261197A16973C48DD986D278B381B06C304DB9,
	LevelData_set_Instance_m919E3DF275123B18E93466F255397CEACE397986,
	LevelData_get_RemainingMove_m75B91D05B0F1F86FD13C10F3BB880C35A37F939E,
	LevelData_set_RemainingMove_m3FDB90324169DD4E4700D070877B23DD569468BB,
	LevelData_get_GoalLeft_mB851E9360E927BD59E1077D372F0687859DF0D7C,
	LevelData_set_GoalLeft_m358E3AE124A668DF4E80F302B07C1E6A7B997E64,
	LevelData_Awake_m9A604CA17535772326B70B75C0A008846B67BF60,
	LevelData_Start_mC91F551B8C73EFC1A7C4E5D08EF0489DBD191341,
	LevelData_Update_m6A3942BF346BCA069B6945388A39E0F79FDDD869,
	LevelData_Matched_m25ED9F6E934A0CB6E9416C8036A0D7C4CDA48AFD,
	LevelData_DarkenBackground_mA6B80594C67969E805106005A04480B7744C091F,
	LevelData_Moved_m43E7BCFE1054CB8CA126F60B5FCB0B20E74CCDD7,
	LevelData__ctor_m4772DEAC476C2F0BF68489509A3EDA114CEC8C58,
	GemGoal__ctor_m7C6C5E7751217B721AA134BE30F14EDFAED74A59,
	GoalChangeDelegate__ctor_mC2DCE39E160A61AE940EE9DEFFCCD670534BAFD3,
	GoalChangeDelegate_Invoke_m8B7145436ABA68A5B668976FA93A90D21FFC3D7A,
	GoalChangeDelegate_BeginInvoke_mA9BCA119B6BFB09543A8E5BFD1588B0AD85714E2,
	GoalChangeDelegate_EndInvoke_m2B82B418E75AA7FE39DD08119169154BE64B865C,
	MoveNotificationDelegate__ctor_m64A9E75FDEEFC7B7BA6300ACE1A54358B536D81E,
	MoveNotificationDelegate_Invoke_m496F973661A13BAF8DCD6DADD2AB6EF3177174D4,
	MoveNotificationDelegate_BeginInvoke_mA5F1EC9579DBF4512C350754EDB3B343C2300728,
	MoveNotificationDelegate_EndInvoke_mE62F950AAE163E64B2C2CB8A6E644860CDE6B01C,
	LevelList_get_SceneCount_m69E5DDE0197A14AF900D45103BF3C760703C8800,
	LevelList_LoadLevel_m1D6F96A7644733CB01F9523E58AA2BF1ECA990DF,
	LevelList__ctor_m260BD9B19227F6E54A2A8A6CBBC758A2A947A317,
	LightManager_Update_m32C2A8E30EB5FDF19A7320B715CA840945EF60A4,
	LightManager_map_mE7032657080DB0D23058D2269DDBB5B07DAD858D,
	LightManager__ctor_m90A8AFF31C68C1753C5CACF538447B7056F08B56,
	Match_AddGem_m0E6874A0A39ECDAC3B8C63E1F87AA2288084FEA9,
	Match__ctor_mAC32C9E36C8F641B6714693295F3CB8411000F8A,
	Obstacle_Init_m63566D49E36BAE2BDE7E059D9EE920F30BB2380C,
	Obstacle_Clear_mED851B2BAAA93F8FF2E15110FDC3E10EF48ADE19,
	Obstacle_Damage_m349A45805734E4475D6BCBEA0A4968EE76538521,
	Obstacle_ChangeState_m1F26D149B0BD52CC14DE9B47E42476310C7F5F7A,
	Obstacle__ctor_m7FE76689F0E7B93CA96927CE5409DE93E46B9BCC,
	LockStateData__ctor_m58905CE70997AD2ACF6C9915B68D7BE4AA256938,
	ShopItemBonusItem_Buy_mA9706A743DC7B394015221EE559676FE8EBA541E,
	ShopItemBonusItem__ctor_mF6C4C25971A237BA3789B79644F03964A2456D37,
	ShopItemCoin_Buy_mC5D2232F390C073D3D46F481544F6ED459A8AA23,
	ShopItemCoin__ctor_m62FC02E1BF2032E838CFEB86F5103AB05CAAA2B2,
	ShopItemLive_Buy_m30BDB539A77A83D226432BE651BF678518E0C6C4,
	ShopItemLive__ctor_mC41CB82306AD9F6BE89BBDCE5B3E06E4B399837D,
	TieBlocker_Init_m6B39B565F2609B504CE9E25391530A94C40B6BC4,
	TieBlocker_Clear_m409C5211B3ABEF7362BA987061A6A07E710EE779,
	TieBlocker_CellMatch_m2FEC24566FB7CC13CF3DB0BD3298666042C7369E,
	TieBlocker__ctor_mB2DA0208D1563E021860926F0FF6CD8F5393A6E4,
	MainMenu_Start_mEB804965A99525AEAD83B6464BEEA9388CEE02F6,
	MainMenu_FadeIn_mE8E6D8FD5600485C8569DCA60B6002AD3299B041,
	MainMenu_FadeOut_m9DAFED7676DF879FA555A177ED28A0E71969DFC0,
	MainMenu__ctor_m4F43E2347FAC1AF5C99B983E2497453D6C504B12,
	MainMenu_U3CStartU3Eb__5_0_mE278F1415B5E0947B36058D6FC431129278DE181,
	U3CU3Ec__DisplayClass5_0__ctor_m62AE472BEA5418EAF1F5AE30CBFD4DEAFF40619F,
	U3CU3Ec__DisplayClass5_0_U3CStartU3Eb__1_mAA72AF3133E692AEAF39617B146F25BFFB509503,
	U3CFadeInU3Ed__6__ctor_m65340D8533768F1D8E9D6EDBDD4E10B31716656E,
	U3CFadeInU3Ed__6_System_IDisposable_Dispose_mE9B0A0E6D810CB940C8A6EC8C41C5361D24CECAB,
	U3CFadeInU3Ed__6_MoveNext_m9DA1CD924D5691FC89534D7A064E4E38DAD38E7C,
	U3CFadeInU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2FB709C15A60D2B0EE86398E604B26E690434764,
	U3CFadeInU3Ed__6_System_Collections_IEnumerator_Reset_m0CF4C3727A78C5DFE817805960774320CB9CE3B8,
	U3CFadeInU3Ed__6_System_Collections_IEnumerator_get_Current_m148CD0E53EC1E6EBF57B2E07E4D29E6C431F2D4A,
	UIHandler_get_Instance_m7EAC0B10A2DAD3142DCAB7A68B07398AAAD8201A,
	UIHandler_set_Instance_m0F7A8F583A3838936A201D67CBAB3C2ECE933254,
	UIHandler_Awake_m00AB8C1ED88A7493F31BD4DBEFF96DC93ED310B1,
	UIHandler_Start_m07B4278EA7DA905097C3868AB823739A676D84DF,
	UIHandler_Init_mE8410DA9FF8002FB9F2BE5AC350C1EDA36C7DF9A,
	UIHandler_Display_m49B4E4D357DC115D2FD1C476383691C605A8E0BE,
	UIHandler_ApplySafeArea_m4E3348CE281A5598F09D520EEB3D1F9A93FC6636,
	UIHandler_ShowEnd_m3F42A6598939C4FFE20CACB02CC73D7CFE0B4775,
	UIHandler_ShowWin_m190F503D9ED5D6E7C2462B7B349C209BAC6DDB4D,
	UIHandler_ShowLose_m7FAB94A63C403B3389B30B0DEEF76B1C5B9E4098,
	UIHandler_ShowEndControl_m871B2FDD56ED59961F46C8B25808D3411B361B83,
	UIHandler_ToggleSettingMenu_mB912E71A647331CEC90436818B2C729793859EB3,
	UIHandler_FadeIn_m2972E57F1EA8A3C0CF8632740CA2E58A417D5DB5,
	UIHandler_FadeOut_m852994403EE640198683C27D3EF355E416EC9C1A,
	UIHandler_SetCoverOpacityNoTransition_m5FFBE55A5F87CD4945F46C369FD4ABE13E478BD2,
	UIHandler_AddMatchEffect_m0A30138ADADCE2C2DA9868543DDF1BAAEA82E9F1,
	UIHandler_AddCoin_m1BC7AA489635DC625F6F323310FE320E0F3D1F6C,
	UIHandler_ShowShop_m51073040056D4ABEF05E8D91756E3980F9427D41,
	UIHandler_Update_mDF52AEF331BD4E7EE85540980D590B8881837DFD,
	UIHandler_UpdateTopBarData_mB2AC1C94A3A20AC125888F9202C9277CA70AF004,
	UIHandler_CreateBottomBar_mCB01EF0E4B3371D18329D95AF049BA16C0B45315,
	UIHandler_UpdateBottomBar_m521FB1FB49290FA2B8AFA49385FD805C117DEF3D,
	UIHandler_DeselectBonusItem_m3BA80744CE55901B93195ABDD51BD19DAA073156,
	UIHandler_UpdateShopEntry_m65B233A263607F1C61F4514882D367F3BC2EC519,
	UIHandler_TriggerCharacterAnimation_mEB082453FDB2D912DB39722B06935C9B1E1EE941,
	UIHandler__ctor_m9CE8770BB638EBA1B34ECEB2075D6755B8A9515D,
	UIHandler_U3CInitU3Eb__48_0_m75D3D7CF153D509FEBFFBA3BFB39AD70A294E2CA,
	UIHandler_U3CInitU3Eb__48_1_m4E12D795F6B1591EC9AF48B6EEDFD3F774C61F13,
	UIAnimationEntry__ctor_m54B1CD7C5DFB60EACBD3F42965C07B443B2DA117,
	ShopEntry_UpdateButtonState_m0949D285920EE8A56E3427D42157EC7CEABA92F5,
	ShopEntry__ctor_m3D6D367D80AF8A28016F1898EC67B33C1DC0C8EB,
	U3CU3Ec__cctor_m3674B2604B7EAFB77512EF93544AE91FC59E018E,
	U3CU3Ec__ctor_mF0DCBDCB21D723695F4B0C943C2BF5724560C18D,
	U3CU3Ec_U3CStartU3Eb__47_11_m74105AB672B0D89959AA1FA5E9CF6AAEA69E7C01,
	U3CU3Ec_U3CStartU3Eb__47_12_mE521FDB4C60FA65E73F3723AC1CD8032D82610EA,
	U3CU3Ec__DisplayClass47_0__ctor_m8066EB94ED205953E73A3BF3A56C581482801D57,
	U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__0_m55F5DD8708AE19E1C6B35E37A02DF8F5D7938DBE,
	U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__1_mF6465761730F05BB87C0EC7D7CC0B1185EB183D8,
	U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__2_mABA7EDEFA9D3DE8ED459589BEEA19369139EC1A1,
	U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__3_m75BAA356682A51A72D0B6E5AF6320E88EBCAD43D,
	U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__13_m55BAED00E638E4129CF4FB84F014B1EFF329F8F3,
	U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__4_m65418ED6A343AF195F97CE8BDF719C6158ED28DA,
	U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__5_mC652D6A0C85F83A58035D002CBF6C0A2BC9238A1,
	U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__6_m12252C446310EFA2FD1B75160C108B78275CCE5A,
	U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__7_mE0EA0E5DCFDD527BB78C529503F44FAB29A9FC16,
	U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__8_m6D450E79BD154F20FE81F27E3E1953F8E13DAB4A,
	U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__9_m704DC1F909660465E030323040575F916E5A9DBE,
	U3CU3Ec__DisplayClass47_0_U3CStartU3Eb__10_m6C4077F98A0DD053799DE5BFC44611AEBD4B34C6,
	U3CU3Ec__DisplayClass47_1__ctor_mAEE94283CB826A92C82ACFA0905DE8306D71021E,
	U3CU3Ec__DisplayClass47_1_U3CStartU3Eb__14_m60C62B579DCD15E03EA7BD687555F1825092D391,
	U3CU3Ec__DisplayClass64_0__ctor_mA93D59362F1A19416E5BFB974A5E9CE37450E435,
	U3CU3Ec__DisplayClass64_0_U3CCreateBottomBarU3Eb__0_m3925600501FD7EA69E25E2B80B3273F94800BEC4,
	U3CShowEndControlU3Ed__54__ctor_mAE1541B291041D9A8FA8D7C4C14F3ED4920698FB,
	U3CShowEndControlU3Ed__54_System_IDisposable_Dispose_m4BA4C4B4ADB06F468868A917FDE21B6144A5462D,
	U3CShowEndControlU3Ed__54_MoveNext_mD9E96415AF76B2912DC98DBBEFB8FE1D71ADA4F2,
	U3CShowEndControlU3Ed__54_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m97C6301E85046F481808DCB003B61DD5CCA5DEB5,
	U3CShowEndControlU3Ed__54_System_Collections_IEnumerator_Reset_m4083C9C7BC627616568F199E9680F670A8A78BCF,
	U3CShowEndControlU3Ed__54_System_Collections_IEnumerator_get_Current_mC92B724AC2D5795FD63A86DCBEA4497C6ED0F851,
	VFXPoolSystem_Clean_m202E61C451ACDB00DBFB5C7E51EA256DB8493B48,
	VFXPoolSystem_Update_m4A8F4DF6C5ED1DEAA4BBCEDB99D4ACEF6400B309,
	VFXPoolSystem_AddNewInstance_mF86C09581AB27EEDAE03AA9725C5B486DB5B1797,
	VFXPoolSystem_PlayInstanceAt_m529B72909F50DB3C663B69A666B8B34D5570C978,
	VFXPoolSystem_GetInstance_mD9235827A1EF6660DD29F7037B53BA8D43542FC8,
	VFXPoolSystem__ctor_mD4A2291A9A60CE601109904CC149C625B3889835,
	VFXInstance__ctor_mE82BCD982A617CAC5F29FB0291E80DF7555D7B9E,
};
static const int32_t s_InvokerIndices[411] = 
{
	21016,
	21016,
	21016,
	34156,
	34156,
	34156,
	34156,
	34156,
	34156,
	34156,
	34156,
	21016,
	21016,
	21016,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	7995,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	15968,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	3294,
	3318,
	21016,
	16348,
	16348,
	6075,
	3301,
	3347,
	3313,
	3315,
	1093,
	3323,
	3333,
	2267,
	3318,
	3324,
	2242,
	2241,
	3337,
	3330,
	13820,
	2239,
	3292,
	13828,
	6075,
	21016,
	21016,
	21016,
	34281,
	21016,
	3899,
	3168,
	21016,
	3899,
	3168,
	21016,
	3899,
	3168,
	21016,
	20548,
	20761,
	21016,
	21016,
	21016,
	21016,
	15757,
	21016,
	21016,
	21016,
	21016,
	20761,
	29234,
	29234,
	29234,
	29233,
	29234,
	29234,
	29234,
	29234,
	32787,
	21016,
	21016,
	21016,
	21016,
	8202,
	14280,
	14297,
	15968,
	13843,
	21016,
	21016,
	16199,
	6137,
	16200,
	21016,
	5187,
	21016,
	15968,
	21016,
	21016,
	21016,
	-1,
	21016,
	34252,
	21016,
	5829,
	15903,
	21016,
	20550,
	20761,
	21016,
	20761,
	20550,
	20550,
	20550,
	20550,
	20550,
	20550,
	21016,
	34252,
	21016,
	7995,
	21016,
	21016,
	21016,
	21016,
	5008,
	1341,
	32182,
	21016,
	21016,
	7968,
	21016,
	21016,
	7968,
	21005,
	21016,
	34252,
	21016,
	10553,
	14284,
	10553,
	14284,
	21016,
	7968,
	21016,
	21016,
	7968,
	21016,
	2927,
	20550,
	21016,
	7968,
	21016,
	16200,
	21016,
	-1,
	21016,
	21016,
	16200,
	11619,
	21016,
	21016,
	21016,
	21016,
	21016,
	16200,
	11619,
	21016,
	21016,
	21016,
	21016,
	21016,
	34156,
	32764,
	34103,
	20694,
	15903,
	20694,
	15903,
	20694,
	15903,
	20761,
	20761,
	15968,
	21016,
	21016,
	21016,
	21016,
	20761,
	21016,
	21016,
	21016,
	15903,
	21016,
	15903,
	21016,
	21016,
	21016,
	21016,
	15968,
	15968,
	8051,
	13820,
	21016,
	21016,
	15968,
	21016,
	21016,
	21016,
	34252,
	21016,
	21016,
	21016,
	21016,
	11681,
	21016,
	11681,
	15903,
	21016,
	20550,
	20761,
	21016,
	20761,
	21016,
	21016,
	21016,
	21016,
	20550,
	-1,
	21016,
	21016,
	20550,
	21005,
	20550,
	20550,
	20873,
	20694,
	20694,
	16200,
	7968,
	11619,
	16200,
	21016,
	16071,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	34156,
	32764,
	20694,
	15903,
	20694,
	15903,
	21016,
	21016,
	21016,
	11681,
	15757,
	21016,
	21016,
	21016,
	7988,
	7405,
	2240,
	15968,
	7988,
	15903,
	3300,
	15968,
	20694,
	15903,
	21016,
	21016,
	22848,
	21016,
	15968,
	21016,
	16200,
	21016,
	15903,
	11619,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	16200,
	21016,
	21016,
	21016,
	21016,
	20761,
	21016,
	21016,
	15968,
	21016,
	21016,
	15903,
	21016,
	20550,
	20761,
	21016,
	20761,
	34156,
	32764,
	21016,
	21016,
	21016,
	15757,
	32764,
	21016,
	21016,
	21016,
	13820,
	15757,
	15968,
	15968,
	16071,
	15968,
	16199,
	15757,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	15903,
	21016,
	7405,
	15903,
	21016,
	21016,
	21016,
	34252,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	15968,
	15968,
	15968,
	21016,
	21016,
	15968,
	21016,
	21016,
	21016,
	21016,
	15903,
	21016,
	20550,
	20761,
	21016,
	20761,
	21016,
	21016,
	7985,
	6099,
	13820,
	21016,
	21016,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule;
const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule = 
{
	"Assembly-CSharp.dll",
	411,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
