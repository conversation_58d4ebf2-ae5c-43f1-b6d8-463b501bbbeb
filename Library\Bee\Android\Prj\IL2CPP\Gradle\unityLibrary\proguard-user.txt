-keep class com.google.games.** { *; }
-keep interface com.google.games.** { *; }
-keep class com.google.unity.** { *; }
 
 -keep class com.unity3d.plugin.lvl.ServiceBinder { *; }
 -keep class com.google.android.gms.games.multiplayer.** { *; }
 -keep class com.google.android.gms.games.leaderboard.** { *; }
 -keep class com.google.android.gms.games.snapshot.** { *; }
 -keep class com.google.android.gms.games.achievement.** { *; }
 -keep class com.google.android.gms.games.event.** { *; }
 -keep class com.google.android.gms.games.stats.** { *; }
 -keep class com.google.android.gms.games.video.** { *; }
 -keep class com.google.android.gms.games.* { *; }
 -keep class com.google.android.gms.common.api.ResultCallback { *; }
 -keep class com.google.android.gms.signin.** { *; }
 -keep class com.google.android.gms.dynamic.** { *; }
 -keep class com.google.android.gms.dynamite.** { *; }
 -keep class com.google.android.play.core.review.** { *; }
 -keep class com.google.android.play.core.appupdate.** { *; }
 -keep class com.google.android.gms.tasks.** { *; }
 -keep class com.google.android.gms.security.** { *; }
 -keep class com.google.android.gms.base.** { *; }
 -keep class com.google.android.gms.actions.** { *; }
 -keep class com.google.games.bridge.** { *; }
 
 -keep class com.google.android.gms.common.ConnectionResult { *; }
 -keep class com.google.android.gms.common.GooglePlayServicesUtil { *; }
 -keep class com.google.android.gms.common.api.** { *; }
 -keep class com.google.android.gms.common.data.DataBufferUtils { *; }
 -keep class com.google.android.gms.games.quest.** { *; }
 -keep class com.google.android.gms.nearby.** { *; }

 -keep class com.google.android.play.core.appupdate.AppUpdateInfo {
    public java.lang.String packageName();
    public int availableVersionCode();
    public int updateAvailability();
    public int installStatus();
    public boolean isUpdateTypeAllowed(com.google.android.play.core.appupdate.AppUpdateOptions);
    public boolean isUpdateTypeAllowed(int);
    public int updatePriority();
    public long bytesDownloaded();
    public long totalBytesToDownload();
    public java.lang.Integer clientVersionStalenessDays();
}

-keep class com.google.android.play.core.appupdate.AppUpdateManager {
    public abstract void registerListener(com.google.android.play.core.install.InstallStateUpdatedListener);
    public abstract void unregisterListener(com.google.android.play.core.install.InstallStateUpdatedListener);

    public abstract com.google.android.play.core.tasks.Task completeUpdate();
    public abstract com.google.android.play.core.tasks.Task getAppUpdateInfo();
    public com.google.android.play.core.tasks.Task startUpdateFlow(com.google.android.play.core.appupdate.AppUpdateInfo, android.app.Activity, com.google.android.play.core.appupdate.AppUpdateOptions);
    public boolean startUpdateFlowForResult(com.google.android.play.core.appupdate.AppUpdateInfo, int, android.app.Activity, int);
    public boolean startUpdateFlowForResult(com.google.android.play.core.appupdate.AppUpdateInfo, int, com.google.android.play.core.common.IntentSenderForResultStarter, int);
    public boolean startUpdateFlowForResult(com.google.android.play.core.appupdate.AppUpdateInfo, android.app.Activity, com.google.android.play.core.appupdate.AppUpdateOptions, int);
    public boolean startUpdateFlowForResult(com.google.android.play.core.appupdate.AppUpdateInfo, com.google.android.play.core.common.IntentSenderForResultStarter, com.google.android.play.core.appupdate.AppUpdateOptions, int);
}

-keep class com.google.android.play.core.appupdate.AppUpdateManagerFactory {
    <init>();

    public static com.google.android.play.core.appupdate.AppUpdateManager create(android.content.Context);
}

-keep class com.google.android.play.core.appupdate.AppUpdateOptions {
    public abstract boolean allowAssetPackDeletion();
    public abstract int appUpdateType();
    public static com.google.android.play.core.appupdate.AppUpdateOptions$Builder newBuilder(int);
    public static com.google.android.play.core.appupdate.AppUpdateOptions defaultOptions(int);
}

-keep class com.google.android.play.core.appupdate.AppUpdateOptions$Builder {
    public abstract com.google.android.play.core.appupdate.AppUpdateOptions$Builder setAllowAssetPackDeletion(boolean);
    public abstract com.google.android.play.core.appupdate.AppUpdateOptions$Builder setAppUpdateType(int);
    public abstract com.google.android.play.core.appupdate.AppUpdateOptions build();
}

-keep class com.google.android.play.core.appupdate.testing.FakeAppUpdateManager {
    public <init>(android.content.Context);

    public void registerListener(com.google.android.play.core.install.InstallStateUpdatedListener);
    public void unregisterListener(com.google.android.play.core.install.InstallStateUpdatedListener);

    public com.google.android.play.core.tasks.Task completeUpdate();
    public com.google.android.play.core.tasks.Task getAppUpdateInfo();
    public boolean startUpdateFlowForResult(com.google.android.play.core.appupdate.AppUpdateInfo, int, android.app.Activity, int);
    public boolean startUpdateFlowForResult(com.google.android.play.core.appupdate.AppUpdateInfo, int, com.google.android.play.core.common.IntentSenderForResultStarter, int);

    public void setUpdateAvailable(int);
    public void setUpdateAvailable(int, int);
    public void setUpdateNotAvailable();
    public void setInstallErrorCode(int);

    public void userAcceptsUpdate();
    public void userRejectsUpdate();

    public void downloadStarts();
    public void downloadCompletes();
    public void userCancelsDownload();
    public void downloadFails();

    public void installCompletes();
    public void installFails();

    public boolean isConfirmationDialogVisible();
    public boolean isImmediateFlowVisible();
    public boolean isInstallSplashScreenVisible();
    public java.lang.Integer getTypeForUpdateInProgress();

    public void setClientVersionStalenessDays(java.lang.Integer);
    public void setTotalBytesToDownload(long);
    public void setBytesDownloaded(long);
    public void setUpdatePriority(int);
}

-keep class com.google.android.play.core.install.InstallException {
    public int getErrorCode();
}

-keep class com.google.android.play.core.install.InstallState {
    public int installErrorCode();
    public int installStatus();
    public java.lang.String packageName();
    public long bytesDownloaded();
    public long totalBytesToDownload();
}

-keep class com.google.android.play.core.install.InstallStateUpdatedListener {
   <init>();

   public void onStateUpdate(com.google.android.play.core.install.InstallState);
}

-keep class com.google.android.play.core.install.NativeInstallStateUpdateListener {
    <init>();

    public void onStateUpdate(com.google.android.play.core.install.InstallState);
}

-keep class com.google.android.play.core.install.model.ActivityResult {
    public static int RESULT_IN_APP_UPDATE_FAILED;
}

-keep class com.google.android.play.core.install.model.AppUpdateType {
    public static int FLEXIBLE;
    public static int IMMEDIATE;
}

-keep class com.google.android.play.core.install.model.InstallErrorCode {
    public static int NO_ERROR;
    public static int NO_ERROR_PARTIALLY_ALLOWED;
    public static int ERROR_API_NOT_AVAILABLE;
    public static int ERROR_APP_NOT_OWNED;
    public static int ERROR_DOWNLOAD_NOT_PRESENT;
    public static int ERROR_INSTALL_NOT_ALLOWED;
    public static int ERROR_INSTALL_UNAVAILABLE;
    public static int ERROR_INTERNAL_ERROR;
    public static int ERROR_INVALID_REQUEST;
    public static int ERROR_PLAY_STORE_NOT_FOUND;
    public static int ERROR_UNKNOWN;
}

-keep class com.google.android.play.core.install.model.InstallStatus {
    public static int CANCELED;
    public static int DOWNLOADED;
    public static int DOWNLOADING;
    public static int FAILED;
    public static int INSTALLED;
    public static int INSTALLING;
    public static int PENDING;
    public static int REQUIRES_UI_INTENT;
    public static int UNKNOWN;
}

-keep class com.google.android.play.core.install.model.UpdateAvailability {
    public static int UNKNOWN;
    public static int UPDATE_AVAILABLE;
    public static int UPDATE_NOT_AVAILABLE;
    public static int DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS;
}

 -keepclassmembers class fqcn.of.javascript.interface.for.webview { public *;}
 -target 1.6
 -optimizationpasses 2
 -dontusemixedcaseclassnames
 -dontskipnonpubliclibraryclasses
 -dontpreverify
 -keepattributes InnerClasses,EnclosingMethod

 -optimizations !code/simplification/arithmetic

 -keep class * { public <methods>; !private *; }

  -dontobfuscate