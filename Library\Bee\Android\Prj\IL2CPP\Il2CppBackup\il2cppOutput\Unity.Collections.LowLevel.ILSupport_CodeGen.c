﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m5AEC11BEDCF402372741582A926E9D7E5C979AC6 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m0D7FC8D9CBAF4951124B56FA51165015F41B67A4 (void);
static Il2CppMethodPointer s_methodPointers[4] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m5AEC11BEDCF402372741582A926E9D7E5C979AC6,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m0D7FC8D9CBAF4951124B56FA51165015F41B67A4,
	NULL,
	NULL,
};
static const int32_t s_InvokerIndices[4] = 
{
	34307,
	21016,
	-1,
	-1,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x06000003, { 0, 1 } },
	{ 0x06000004, { 1, 1 } },
};
extern const uint32_t g_rgctx_TU26_t81D2AD553E963FAF8292CE458EABC39F2E248307;
extern const uint32_t g_rgctx_TU26_t0A0F9E5BDE8E7598AF00903A15E266DCF04E9678;
static const Il2CppRGCTXDefinition s_rgctxValues[2] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t81D2AD553E963FAF8292CE458EABC39F2E248307 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t0A0F9E5BDE8E7598AF00903A15E266DCF04E9678 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Collections_LowLevel_ILSupport_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Collections_LowLevel_ILSupport_CodeGenModule = 
{
	"Unity.Collections.LowLevel.ILSupport.dll",
	4,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	2,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
