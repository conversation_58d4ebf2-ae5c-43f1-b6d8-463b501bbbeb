﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBBDBE07677B9861F9DDE72B54BE93CE4AB8FCD16 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mFF59B50408C73DF474D1CC985BE40673CA6C7366 (void);
extern void PlayCoreEventHandler_CreateInScene_mEAD0812DABD584CE53E16A912A40D31291428D01 (void);
extern void PlayCoreEventHandler_HandleEvent_m11290881148344FD48D5E4B9F52047FD0BF8B58A (void);
extern void PlayCoreEventHandler_Awake_m96A238A1CEE92F4ADB8E37BA0C907B27B2C8F9AF (void);
extern void PlayCoreEventHandler_HandleEventInternal_mBD78948B254E283CAF4C3AF7A954B8BBF2BDBCAD (void);
extern void PlayCoreEventHandler_Update_m062AE02CDAD001C022A025A78440A096AF00F1D3 (void);
extern void PlayCoreEventHandler__ctor_m5CDC8F15F1E7549412362974B8DF493FEB0BED16 (void);
extern void PlayCoreHelper_ConvertJavaString_m9AA409E09CC8B5651AA9A65B320FDF428F402337 (void);
extern void PlayCoreHelper_IsNull_m2D9E2A843202A853E42F5E0A3579DFA9078CCAA2 (void);
extern void PlayCoreOnFailureListener_add_OnTaskFailed_mA8A6D505A5C4DE0EE899A6ECF8C974F56EFF39FD (void);
extern void PlayCoreOnFailureListener_remove_OnTaskFailed_mB1C755B2B5D25EF55F31464026350C04693FCCDA (void);
extern void PlayCoreOnFailureListener__ctor_mE8DAA18EE8B95F321D14809657CBD7119BCB0FF1 (void);
extern void PlayCoreOnFailureListener_onFailure_m7B1D7DA088A5664BEC2C43C4DAF48B2EA9406DA1 (void);
extern void U3CU3Ec__cctor_m27A12959E503839A9C72E03A83149AF1A0543225 (void);
extern void U3CU3Ec__ctor_mF126A16036EE9D168F868E20E9788A18537C6319 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__3_0_mCF85D2D900A409FFFDCA5D1D6C27938E2700300A (void);
extern void U3CU3Ec__DisplayClass4_0__ctor_m61888034D6248032698BE7D895218213DB5AD61E (void);
extern void U3CU3Ec__DisplayClass4_0_U3ConFailureU3Eb__0_m6E5BDBDB629DB6DDD4DEB0D91219D2027F79477F (void);
extern void TaskOnFailureListener_add_OnTaskFailed_m14EA123D4C451DC9DF240BB3E91B87BE0A05E709 (void);
extern void TaskOnFailureListener_remove_OnTaskFailed_mC0D692EE9C0FBE6411E031C5F4F8EA5948D41FA1 (void);
extern void TaskOnFailureListener__ctor_m2C0A4697F03EE32677A51A3C5F3230A1BABCBD3D (void);
extern void TaskOnFailureListener_onFailure_mBA7C90373F5D6EC099CDC6B780C0CDAC6C4E2010 (void);
extern void U3CU3Ec__cctor_m104AA475D159AE5EEE9B99C5FFEE69B4BD6F1E71 (void);
extern void U3CU3Ec__ctor_m4A5A150E01899D94A9F46FF91BD44A30114EF61F (void);
extern void U3CU3Ec_U3C_ctorU3Eb__3_0_m98CE383D1FAFB3083ADCCD50E2615516F33D9613 (void);
extern void U3CU3Ec__DisplayClass4_0__ctor_m55574BB58F31A6A1AC660017D0295CF6DE08CD19 (void);
extern void U3CU3Ec__DisplayClass4_0_U3ConFailureU3Eb__0_mF61FFA0F5ECDB185832BF267FAB1E370E92C0506 (void);
static Il2CppMethodPointer s_methodPointers[54] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBBDBE07677B9861F9DDE72B54BE93CE4AB8FCD16,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mFF59B50408C73DF474D1CC985BE40673CA6C7366,
	PlayCoreEventHandler_CreateInScene_mEAD0812DABD584CE53E16A912A40D31291428D01,
	PlayCoreEventHandler_HandleEvent_m11290881148344FD48D5E4B9F52047FD0BF8B58A,
	PlayCoreEventHandler_Awake_m96A238A1CEE92F4ADB8E37BA0C907B27B2C8F9AF,
	PlayCoreEventHandler_HandleEventInternal_mBD78948B254E283CAF4C3AF7A954B8BBF2BDBCAD,
	PlayCoreEventHandler_Update_m062AE02CDAD001C022A025A78440A096AF00F1D3,
	PlayCoreEventHandler__ctor_m5CDC8F15F1E7549412362974B8DF493FEB0BED16,
	NULL,
	NULL,
	PlayCoreHelper_ConvertJavaString_m9AA409E09CC8B5651AA9A65B320FDF428F402337,
	PlayCoreHelper_IsNull_m2D9E2A843202A853E42F5E0A3579DFA9078CCAA2,
	PlayCoreOnFailureListener_add_OnTaskFailed_mA8A6D505A5C4DE0EE899A6ECF8C974F56EFF39FD,
	PlayCoreOnFailureListener_remove_OnTaskFailed_mB1C755B2B5D25EF55F31464026350C04693FCCDA,
	PlayCoreOnFailureListener__ctor_mE8DAA18EE8B95F321D14809657CBD7119BCB0FF1,
	PlayCoreOnFailureListener_onFailure_m7B1D7DA088A5664BEC2C43C4DAF48B2EA9406DA1,
	U3CU3Ec__cctor_m27A12959E503839A9C72E03A83149AF1A0543225,
	U3CU3Ec__ctor_mF126A16036EE9D168F868E20E9788A18537C6319,
	U3CU3Ec_U3C_ctorU3Eb__3_0_mCF85D2D900A409FFFDCA5D1D6C27938E2700300A,
	U3CU3Ec__DisplayClass4_0__ctor_m61888034D6248032698BE7D895218213DB5AD61E,
	U3CU3Ec__DisplayClass4_0_U3ConFailureU3Eb__0_m6E5BDBDB629DB6DDD4DEB0D91219D2027F79477F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TaskOnFailureListener_add_OnTaskFailed_m14EA123D4C451DC9DF240BB3E91B87BE0A05E709,
	TaskOnFailureListener_remove_OnTaskFailed_mC0D692EE9C0FBE6411E031C5F4F8EA5948D41FA1,
	TaskOnFailureListener__ctor_m2C0A4697F03EE32677A51A3C5F3230A1BABCBD3D,
	TaskOnFailureListener_onFailure_mBA7C90373F5D6EC099CDC6B780C0CDAC6C4E2010,
	U3CU3Ec__cctor_m104AA475D159AE5EEE9B99C5FFEE69B4BD6F1E71,
	U3CU3Ec__ctor_m4A5A150E01899D94A9F46FF91BD44A30114EF61F,
	U3CU3Ec_U3C_ctorU3Eb__3_0_m98CE383D1FAFB3083ADCCD50E2615516F33D9613,
	U3CU3Ec__DisplayClass4_0__ctor_m55574BB58F31A6A1AC660017D0295CF6DE08CD19,
	U3CU3Ec__DisplayClass4_0_U3ConFailureU3Eb__0_mF61FFA0F5ECDB185832BF267FAB1E370E92C0506,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
static const int32_t s_InvokerIndices[54] = 
{
	34288,
	21016,
	34252,
	32764,
	21016,
	15968,
	21016,
	21016,
	-1,
	-1,
	32090,
	31561,
	15968,
	15968,
	21016,
	15968,
	34252,
	21016,
	7985,
	21016,
	21016,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	15968,
	15968,
	21016,
	15968,
	34252,
	21016,
	7985,
	21016,
	21016,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
};
static const Il2CppTokenRangePair s_rgctxIndices[9] = 
{
	{ 0x0200000B, { 12, 11 } },
	{ 0x0200000C, { 23, 3 } },
	{ 0x0200000D, { 26, 5 } },
	{ 0x0200000E, { 31, 7 } },
	{ 0x02000012, { 38, 11 } },
	{ 0x02000013, { 49, 3 } },
	{ 0x02000014, { 52, 5 } },
	{ 0x06000009, { 0, 5 } },
	{ 0x0600000A, { 5, 7 } },
};
extern const uint32_t g_rgctx_List_1_tC7BE6748E8CCD35415AF9A778F8F7A16B55F089F;
extern const uint32_t g_rgctx_List_1__ctor_m6B1D6D535936971512AF5C510169F073469AEACA;
extern const uint32_t g_rgctx_AndroidJavaObject_Call_TisTAndroidJava_t3144D6CDC93A26AAB21DE6D2C6142D869907B20F_m1D314C58574A6DE2C7140AD701AFE89A94D69AA3;
extern const uint32_t g_rgctx_TAndroidJava_t3144D6CDC93A26AAB21DE6D2C6142D869907B20F;
extern const uint32_t g_rgctx_List_1_Add_m8AEA20866E37C6C5967DECB9E987832924E55BFF;
extern const uint32_t g_rgctx_Dictionary_2_t755FF4BA9FFF7B98AE27BA0C19BAAD4ECD55279A;
extern const uint32_t g_rgctx_Dictionary_2__ctor_m5454EC359E412944DE52D0126B976CB1EC8A06B9;
extern const uint32_t g_rgctx_AndroidJavaObject_Call_TisTAndroidJavaKey_t80177B56B03A7FE41C0D662AC3ABBEE5AE2CBB40_m1459A3BF1D8E0CF64DF8A6076D1A469FAD3783DD;
extern const uint32_t g_rgctx_TAndroidJavaKey_t80177B56B03A7FE41C0D662AC3ABBEE5AE2CBB40;
extern const uint32_t g_rgctx_AndroidJavaObject_Call_TisTAndroidJavaValue_t5FA26E19471BEE9D34311B0E8BF86408837B99F6_mF3B7F7A9A09F7D0D3EE2DEDED4E7FAF501168372;
extern const uint32_t g_rgctx_TAndroidJavaValue_t5FA26E19471BEE9D34311B0E8BF86408837B99F6;
extern const uint32_t g_rgctx_Dictionary_2_Add_m5655497F5466BEC98F6E74AEDF8427D2B0E4C63F;
extern const uint32_t g_rgctx_PlayCoreOnSuccessListener_1_tDD1F798FA06DF397F9BC985605C9F29C5CCF3FBA;
extern const uint32_t g_rgctx_Action_1_tB844C51AB481CDDA488F4C0A102BDFA3B8717E91;
extern const uint32_t g_rgctx_Action_1U26_t45CD87D12A984CBF3E7CD371E866CAD3659CC75A;
extern const uint32_t g_rgctx_U3CU3Ec_tD20A8D2543CF7B4BAC5FFB171F1373F27EC62940;
extern const uint32_t g_rgctx_U3CU3Ec_tD20A8D2543CF7B4BAC5FFB171F1373F27EC62940;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_ctorU3Eb__3_0_m5A94DC60F2C0C4989A4E86E5AF581D950F6B3177;
extern const uint32_t g_rgctx_Action_1__ctor_mC601303614CC644D2272F62B883BC4C27ACE4580;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass4_0_t3B65DF74727F6E0F4FA531E319C4BEBA02B99A62;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass4_0__ctor_m4FE5DC626A4A192731463230D7B7606382C66D0F;
extern const uint32_t g_rgctx_TAndroidJava_tF38A3A70F5A51198A94DF7048978F0A9FEFD3492;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass4_0_U3ConSuccessU3Eb__0_m92E6E859BF4D5B761ED29C68D0FFD6EF0EDAD653;
extern const uint32_t g_rgctx_U3CU3Ec_t90ECDDEF7EC900AB4107D21F5A352EF83BF45E14;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_m357C65FA4C3CF3B19310232FB091FA39FF76274E;
extern const uint32_t g_rgctx_U3CU3Ec_t90ECDDEF7EC900AB4107D21F5A352EF83BF45E14;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass4_0_tA212F9A9A84BA8817304149793CB1303B7E581D3;
extern const uint32_t g_rgctx_PlayCoreOnSuccessListener_1_t7A4193876B612E86077071B6313D60EBA2ED8A11;
extern const uint32_t g_rgctx_Action_1_tE1E6A3BB0429CDF72E1E23D34DCF87CC6F567C23;
extern const uint32_t g_rgctx_TAndroidJava_t6A1C95B1FC03278C57CBA6144364CCED7DE17016;
extern const uint32_t g_rgctx_Action_1_Invoke_mFBD4C4878512FED23368E154648058164552809B;
extern const uint32_t g_rgctx_PlayServicesTask_1_tBDAD8FA3EBDF665484222B7D0D6CE05ADF5B3576;
extern const uint32_t g_rgctx_TaskOnSuccessListener_1_tBB6969AAB524F504224F2184D7D12777D3746021;
extern const uint32_t g_rgctx_TaskOnSuccessListener_1__ctor_m446FB2E123A297FE5ACB27580C83DFE6C2039C0E;
extern const uint32_t g_rgctx_Action_1_tD000634351901AB3067555D4E9B02F8754AFD561;
extern const uint32_t g_rgctx_TaskOnSuccessListener_1_add_OnTaskSucceeded_m9F33133886CF3AD391BC365335C84E890AD92B7D;
extern const uint32_t g_rgctx_PlayServicesTask_1_AddOnSuccessListener_m684E32848F44ABA6F8A8DA667B1AD7BBBF4D9085;
extern const uint32_t g_rgctx_PlayServicesTask_1_AddOnFailureListener_m8398EB2AC808B775F6F031E03862DB3C5F9DD272;
extern const uint32_t g_rgctx_TaskOnSuccessListener_1_t2ED44F84778FEB7F5C8C07AA7B8BB18E797E1327;
extern const uint32_t g_rgctx_Action_1_t42A119F55461E3F93B0B6532DE5AF3E42CE1C71B;
extern const uint32_t g_rgctx_Action_1U26_tC6C3515DA72844B751987878F32D9C5C9465ED95;
extern const uint32_t g_rgctx_U3CU3Ec_t63E1B6047E6FEDAF1D0D11337FD8EEC7DA93F251;
extern const uint32_t g_rgctx_U3CU3Ec_t63E1B6047E6FEDAF1D0D11337FD8EEC7DA93F251;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_ctorU3Eb__3_0_m0EE69A8BA74874F49A651A0736AFAC95ACCE555C;
extern const uint32_t g_rgctx_Action_1__ctor_mFB6D0B3A1DB8399A40A76CB17273B1C2ED633A1B;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass4_0_tD8F658D1C9621FD656C67AC9AC7AC4F7193C3DCB;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass4_0__ctor_mA355F5B11FCE3B085EE20CB8E14ECF55F836F780;
extern const uint32_t g_rgctx_TAndroidJava_t3C7F640078D225151D07AE07ED50A511104016A8;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass4_0_U3ConSuccessU3Eb__0_mB0A210DF19578DE2B5F0F505604F177D5E44A146;
extern const uint32_t g_rgctx_U3CU3Ec_tDBFCA0A125356520D6D980613245A398559D5ACE;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_mAD506D196B1C0D727A3772539D6B2E51F735E671;
extern const uint32_t g_rgctx_U3CU3Ec_tDBFCA0A125356520D6D980613245A398559D5ACE;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass4_0_t9B59CF41EA45E24624B41F3A38D4A003F1BF8520;
extern const uint32_t g_rgctx_TaskOnSuccessListener_1_t508BBDF3BBE8E8D0F90B2EE6D7A28A023F19E951;
extern const uint32_t g_rgctx_Action_1_tD1AE3AFCFF2754E669EB3901C860ED94B485C4BE;
extern const uint32_t g_rgctx_TAndroidJava_tF77ADE322D71100ADCB05627CEFD629004198E97;
extern const uint32_t g_rgctx_Action_1_Invoke_m0B6076119D93FEB3D477A2175435EFF6BEEEC1AF;
static const Il2CppRGCTXDefinition s_rgctxValues[57] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tC7BE6748E8CCD35415AF9A778F8F7A16B55F089F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m6B1D6D535936971512AF5C510169F073469AEACA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject_Call_TisTAndroidJava_t3144D6CDC93A26AAB21DE6D2C6142D869907B20F_m1D314C58574A6DE2C7140AD701AFE89A94D69AA3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAndroidJava_t3144D6CDC93A26AAB21DE6D2C6142D869907B20F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m8AEA20866E37C6C5967DECB9E987832924E55BFF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t755FF4BA9FFF7B98AE27BA0C19BAAD4ECD55279A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_m5454EC359E412944DE52D0126B976CB1EC8A06B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject_Call_TisTAndroidJavaKey_t80177B56B03A7FE41C0D662AC3ABBEE5AE2CBB40_m1459A3BF1D8E0CF64DF8A6076D1A469FAD3783DD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAndroidJavaKey_t80177B56B03A7FE41C0D662AC3ABBEE5AE2CBB40 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject_Call_TisTAndroidJavaValue_t5FA26E19471BEE9D34311B0E8BF86408837B99F6_mF3B7F7A9A09F7D0D3EE2DEDED4E7FAF501168372 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAndroidJavaValue_t5FA26E19471BEE9D34311B0E8BF86408837B99F6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Add_m5655497F5466BEC98F6E74AEDF8427D2B0E4C63F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PlayCoreOnSuccessListener_1_tDD1F798FA06DF397F9BC985605C9F29C5CCF3FBA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tB844C51AB481CDDA488F4C0A102BDFA3B8717E91 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1U26_t45CD87D12A984CBF3E7CD371E866CAD3659CC75A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tD20A8D2543CF7B4BAC5FFB171F1373F27EC62940 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tD20A8D2543CF7B4BAC5FFB171F1373F27EC62940 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_ctorU3Eb__3_0_m5A94DC60F2C0C4989A4E86E5AF581D950F6B3177 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_mC601303614CC644D2272F62B883BC4C27ACE4580 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass4_0_t3B65DF74727F6E0F4FA531E319C4BEBA02B99A62 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass4_0__ctor_m4FE5DC626A4A192731463230D7B7606382C66D0F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAndroidJava_tF38A3A70F5A51198A94DF7048978F0A9FEFD3492 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass4_0_U3ConSuccessU3Eb__0_m92E6E859BF4D5B761ED29C68D0FFD6EF0EDAD653 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t90ECDDEF7EC900AB4107D21F5A352EF83BF45E14 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_m357C65FA4C3CF3B19310232FB091FA39FF76274E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t90ECDDEF7EC900AB4107D21F5A352EF83BF45E14 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass4_0_tA212F9A9A84BA8817304149793CB1303B7E581D3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PlayCoreOnSuccessListener_1_t7A4193876B612E86077071B6313D60EBA2ED8A11 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tE1E6A3BB0429CDF72E1E23D34DCF87CC6F567C23 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAndroidJava_t6A1C95B1FC03278C57CBA6144364CCED7DE17016 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_mFBD4C4878512FED23368E154648058164552809B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PlayServicesTask_1_tBDAD8FA3EBDF665484222B7D0D6CE05ADF5B3576 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskOnSuccessListener_1_tBB6969AAB524F504224F2184D7D12777D3746021 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskOnSuccessListener_1__ctor_m446FB2E123A297FE5ACB27580C83DFE6C2039C0E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tD000634351901AB3067555D4E9B02F8754AFD561 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskOnSuccessListener_1_add_OnTaskSucceeded_m9F33133886CF3AD391BC365335C84E890AD92B7D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PlayServicesTask_1_AddOnSuccessListener_m684E32848F44ABA6F8A8DA667B1AD7BBBF4D9085 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PlayServicesTask_1_AddOnFailureListener_m8398EB2AC808B775F6F031E03862DB3C5F9DD272 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskOnSuccessListener_1_t2ED44F84778FEB7F5C8C07AA7B8BB18E797E1327 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t42A119F55461E3F93B0B6532DE5AF3E42CE1C71B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1U26_tC6C3515DA72844B751987878F32D9C5C9465ED95 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t63E1B6047E6FEDAF1D0D11337FD8EEC7DA93F251 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t63E1B6047E6FEDAF1D0D11337FD8EEC7DA93F251 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_ctorU3Eb__3_0_m0EE69A8BA74874F49A651A0736AFAC95ACCE555C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_mFB6D0B3A1DB8399A40A76CB17273B1C2ED633A1B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass4_0_tD8F658D1C9621FD656C67AC9AC7AC4F7193C3DCB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass4_0__ctor_mA355F5B11FCE3B085EE20CB8E14ECF55F836F780 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAndroidJava_t3C7F640078D225151D07AE07ED50A511104016A8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass4_0_U3ConSuccessU3Eb__0_mB0A210DF19578DE2B5F0F505604F177D5E44A146 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tDBFCA0A125356520D6D980613245A398559D5ACE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_mAD506D196B1C0D727A3772539D6B2E51F735E671 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tDBFCA0A125356520D6D980613245A398559D5ACE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass4_0_t9B59CF41EA45E24624B41F3A38D4A003F1BF8520 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskOnSuccessListener_1_t508BBDF3BBE8E8D0F90B2EE6D7A28A023F19E951 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tD1AE3AFCFF2754E669EB3901C860ED94B485C4BE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAndroidJava_tF77ADE322D71100ADCB05627CEFD629004198E97 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_m0B6076119D93FEB3D477A2175435EFF6BEEEC1AF },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Google_Play_Core_CodeGenModule;
const Il2CppCodeGenModule g_Google_Play_Core_CodeGenModule = 
{
	"Google.Play.Core.dll",
	54,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	9,
	s_rgctxIndices,
	57,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
