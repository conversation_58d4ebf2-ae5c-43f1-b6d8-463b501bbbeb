﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void VisualEffectActivationBehaviour__ctor_m65E4CD17A18691729EA7828682BCD258D637F18E (void);
extern void VisualEffectActivationClip_get_clipCaps_m8F28DEE46549E79C5304F6DBA33D669BF67ADC11 (void);
extern void VisualEffectActivationClip_CreatePlayable_m4814F182C51EEF28575E8778C1878869826412FF (void);
extern void VisualEffectActivationClip__ctor_mBA01A34328A0CF217AE901CDFD1DBCC894FA8B62 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m1A0F97D1C26E60CB50CCFEC8C0EB54142437073F (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m660B9B680F16025E4FBA89662402F4D7A4161AF4 (void);
extern void IncrementStripIndexOnStart_OnPlay_mC0FFFC6BF19D2183B2CF20A6F5BD84ECB517ABB1 (void);
extern void IncrementStripIndexOnStart_OnStop_m501883B139058CDA61B84BE9829D7C76155B020A (void);
extern void IncrementStripIndexOnStart_OnUpdate_m92C00CAEC2C39033FCEB36D3E953421D8197B33F (void);
extern void IncrementStripIndexOnStart__ctor_mD5AC51266B8B4C455B08323C80BF1326C5F2AC24 (void);
extern void IncrementStripIndexOnStart__cctor_m7BAE26202B9622EC6696C7F712338036672F7344 (void);
extern void InputProperties__ctor_mC58B1AA00C2A3ECC0CE232C7BEF2D917498CA527 (void);
extern void LoopAndDelay_OnPlay_m5C7E9B52858DF05EB83E325CE74DAE580784C8DE (void);
extern void LoopAndDelay_OnUpdate_mF618CDEDADBF1A4AE6F8D72308B7A4878811C9AF (void);
extern void LoopAndDelay_OnStop_mB4D8429F935FAC6E6FF3CC3AD08CB04EADBDDDAE (void);
extern void LoopAndDelay__ctor_mFA1ADB24F0572B3FBBF66F7664E762DCF834E995 (void);
extern void LoopAndDelay__cctor_mA0AB3A7F64A528FF436812537438B3C04817EB18 (void);
extern void InputProperties__ctor_m338F6549D0C496DBE3024AF9C9AE77A109563BFA (void);
extern void SetSpawnTime_OnPlay_m494EDFC1FE73146B6C487FDD4A07D42E91159660 (void);
extern void SetSpawnTime_OnUpdate_m9B8FDFEC517518E5BC71C6B728DD731B3BC92E26 (void);
extern void SetSpawnTime_OnStop_mFA93FBC917BC1DB9C1ADCB0396D51A991A939868 (void);
extern void SetSpawnTime__ctor_m0A6C3292B3E92EE9AB41174552778E52EC365B02 (void);
extern void SetSpawnTime__cctor_m7645785D0D869207055935A7FDDD29A9330E053D (void);
extern void SpawnOverDistance_OnPlay_m8CD1A43AE9201B150ECA3F11B8D3626E1D9C3202 (void);
extern void SpawnOverDistance_OnUpdate_mAD7E92251CD686D43412F078BC3FC2F24833D1CE (void);
extern void SpawnOverDistance_OnStop_m433A53C7DE34C595F610840DBE4FDF6D504AC620 (void);
extern void SpawnOverDistance__ctor_m609C74C15136C08BC9DAFF29F063D8E3D6FAFD87 (void);
extern void SpawnOverDistance__cctor_m9DB320C3EA82984EA751B5DE8F4F502DB217D554 (void);
extern void InputProperties__ctor_m66051F750DCE103C62E11E0FED58833AEB98FD5F (void);
extern void VFXTypeAttribute__ctor_mBEDA075ED10124DE0B91759EE19986A24B779F30 (void);
extern void VFXTypeAttribute_get_usages_m426B7B073E3E45D3D9A51CA64167689D1DA15EA3 (void);
extern void VFXTypeAttribute_set_usages_m5FA9D043AC5C3B09A588C36382E204A49025AF54 (void);
extern void VFXTypeAttribute_get_name_m95DF2E6B041C7F60B2CD1CEEEA0B1DF72B1E7D7A (void);
extern void VFXTypeAttribute_set_name_mA22E40168BCDBEC01144E4D6A3D114E821FC669E (void);
extern void VisualEffectControlClip_get_clipCaps_mDA4C416CD5848FAAE07975B087E8EABB21267341 (void);
extern void VisualEffectControlClip_get_clipStart_m93DAEA00AA3F13ECF2B6804918E7D56F21EDD615 (void);
extern void VisualEffectControlClip_set_clipStart_m6492AB7FB6F37A24D0091A1AC6500D5CFA4CE3ED (void);
extern void VisualEffectControlClip_get_clipEnd_mC4E239AAC1D5EB370AEF75AD0A74B5E24EF65C25 (void);
extern void VisualEffectControlClip_set_clipEnd_m104DEDD83157E9555030BACA648ED87F18E49CCA (void);
extern void VisualEffectControlClip_CreatePlayable_m24517EF9D0AC0F71ED375BB491C44C6735CF2E8C (void);
extern void VisualEffectControlClip__ctor_m6D2F2738A0576C9895CDB9A0323585062BAE047F (void);
extern void ClipEvent__cctor_m81D0577A55BB6F80CC63C992BF386096AF979A8E (void);
extern void EventAttribute__ctor_m5117CE33C14C44625311D78A276BAD7C0E6F70B7 (void);
extern void EventAttributeFloat__ctor_m79EDE6A6C5F1ACCD07D77131D123D40297DF373C (void);
extern void U3CU3Ec__cctor_m468B6F148EAE1F0F735CB086D52C26B670AA08BF (void);
extern void U3CU3Ec__ctor_m089625E761BD440013E5B879BC173F8697D8B02E (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_0_m91F140B0A595A23333531CC1687A8EEA1B2D9725 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_1_m91CBCB1D42D1A9D83CE287077E5E9946CED49AE6 (void);
extern void EventAttributeVector2__ctor_mE405FDB1A0AE5DE93A38EF678A0FDDD5C6DB6682 (void);
extern void U3CU3Ec__cctor_m68D5AB824B40D21B675464791FCC893B386C09DB (void);
extern void U3CU3Ec__ctor_m4B240E328F7124697AA262246269825681A64E02 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_0_m0FD894A71C567CF4C87FF7E8D29E4C9EE9ACD4D9 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_1_m4B680295768F7545572ECD3A899A0944FAA2822C (void);
extern void EventAttributeVector3__ctor_m4D79D78CCD84E4B1E823AE5F25180219685E5823 (void);
extern void U3CU3Ec__cctor_mCD045471CC9DB34636302AED0E75FAF6EF1107B3 (void);
extern void U3CU3Ec__ctor_m7C568651AB7F6B256E09202A608036C4BF20D6B6 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_0_m03C2049FB8E660B0C84476A24553F6BF02AF4013 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_1_m9FAEBFF57B9683E72B29B64B918AB64FD109ED49 (void);
extern void EventAttributeColor__ctor_m1CE6C6C33E4FBFB825DCA668999DD18562DAD2FB (void);
extern void EventAttributeVector4__ctor_m8D616CBBBDDF790881E64F1E66400F827CC5ADDA (void);
extern void U3CU3Ec__cctor_m804A41F35134CA49F6532A89FA0293556C5B2A1B (void);
extern void U3CU3Ec__ctor_m4EAB14E3DEF3EEC0E11D133A39CB41B88F400DC6 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_0_m4A5843374FEB5A925912A6D02EF055498D88E1F5 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_1_m0E08413C13AE1CD26DB708C607AB37012F7DAA0C (void);
extern void EventAttributeInt__ctor_mE53F8D3731E8524DAA6D5EB8CFBF6C1D40D2B5E3 (void);
extern void U3CU3Ec__cctor_mB926AC936F78A6C0F25F34913DDB771513021231 (void);
extern void U3CU3Ec__ctor_mABC1862D1263E14227904CF9D33BBC2A7B084345 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_0_m2A9EC3BAB45EC9EB2D4A0F9D5EA697503B8A85E0 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_1_m2808603C9031DC7BDAE707F3404BA2EAF2229D97 (void);
extern void EventAttributeUInt__ctor_m3B8E10C5AD2AE1A16E53D057224FE48ED5625C0D (void);
extern void U3CU3Ec__cctor_m0CBEAB7F81298EBFFE59B752C13D364578C75B05 (void);
extern void U3CU3Ec__ctor_m3BBA6C33AF3CF2427FC1F6E32DDB916302357D0C (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_0_mA2B580F8E48AD406D1C8147E60DB8D12379BFBA2 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_1_mAFB884B374D2DF3CF97ED7B159ED5DB859B2FA8B (void);
extern void EventAttributeBool__ctor_m8F97B13EC89497D630A3A753C6EE9FD05CC4DE9E (void);
extern void U3CU3Ec__cctor_m28737E59045A9CE9B73971B84DB3420397AA0B0A (void);
extern void U3CU3Ec__ctor_mAF6B7CF5EA37A9E0CCDB40E26AE832FCCF333CF4 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_0_m00A50E06725F9E7E695B1E0CE5005514F21044C7 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_1_m9D884E09719B1957A7A248573E9AED449AAB0309 (void);
extern void VFXTimeSpaceHelper_GetEventNormalizedSpace_mE5EF0B4148168D7302DAB402A278104783BDC68F (void);
extern void VFXTimeSpaceHelper_CollectClipEvents_mE265C738EBD5A0B9AC32C77FB67EA9C9C36CC6C1 (void);
extern void VFXTimeSpaceHelper_GetEventNormalizedSpace_m5B3EC5FE8E496F70D98F9687F11A2B341B571EC7 (void);
extern void VFXTimeSpaceHelper_GetEventNormalizedSpace_m2B1165647C19A2A827749F029C51B53399B0576B (void);
extern void VFXTimeSpaceHelper_GetTimeInSpace_m18A26B4BC5C83F5F1543ABA43309B778C3D3AB8C (void);
extern void U3CCollectClipEventsU3Ed__1__ctor_m2CD3C3FAF9770FF837068A76D4A6844CE20F86DC (void);
extern void U3CCollectClipEventsU3Ed__1_System_IDisposable_Dispose_mE421A5DEAFDC8A8C7865542A9E7CB2156414D97F (void);
extern void U3CCollectClipEventsU3Ed__1_MoveNext_m4E36F5D97B3C4E0FA29EACF44C5073FF0C296AD5 (void);
extern void U3CCollectClipEventsU3Ed__1_U3CU3Em__Finally1_m43C29A88D48A86DA5ECFDF09D4359ABF218CBFDB (void);
extern void U3CCollectClipEventsU3Ed__1_System_Collections_Generic_IEnumeratorU3CUnityEngine_VFX_VisualEffectPlayableSerializedEventU3E_get_Current_mD802A2E1D86FF0A2949F219CB66F55E78A9508D4 (void);
extern void U3CCollectClipEventsU3Ed__1_System_Collections_IEnumerator_Reset_m1386508BFD5C1C003F1A5C9B7B739E4960B7662B (void);
extern void U3CCollectClipEventsU3Ed__1_System_Collections_IEnumerator_get_Current_mD57E3DB12874EF4CB6BF4D50992615572EA9A90B (void);
extern void U3CCollectClipEventsU3Ed__1_System_Collections_Generic_IEnumerableU3CUnityEngine_VFX_VisualEffectPlayableSerializedEventU3E_GetEnumerator_m1CD6AA3483A3803BDFC48F1CAB1D6F4C9657C4B1 (void);
extern void U3CCollectClipEventsU3Ed__1_System_Collections_IEnumerable_GetEnumerator_m1DB190A75B66369E8ED4D4B949B819B1131825C7 (void);
extern void U3CGetEventNormalizedSpaceU3Ed__3__ctor_mBDEBCD19B0D5DB4083AA38A9B52300153A549679 (void);
extern void U3CGetEventNormalizedSpaceU3Ed__3_System_IDisposable_Dispose_mF37FCA0FABB1DD6531BC8DF0ECD4BECBD8AFF7C8 (void);
extern void U3CGetEventNormalizedSpaceU3Ed__3_MoveNext_m43A52BFEA923E4681812E40363C122F2FED23184 (void);
extern void U3CGetEventNormalizedSpaceU3Ed__3_U3CU3Em__Finally1_m338E4D46114C61E5F073E6D900F7C3245584FDD0 (void);
extern void U3CGetEventNormalizedSpaceU3Ed__3_System_Collections_Generic_IEnumeratorU3CUnityEngine_VFX_VisualEffectPlayableSerializedEventU3E_get_Current_m251309432105DAB00635EE065228C133836A96D1 (void);
extern void U3CGetEventNormalizedSpaceU3Ed__3_System_Collections_IEnumerator_Reset_m60928E0623B0199133E340DD8CBF3727D8117F52 (void);
extern void U3CGetEventNormalizedSpaceU3Ed__3_System_Collections_IEnumerator_get_Current_m51DFA7B77CA706E9FD5D7369901C841485FF44C1 (void);
extern void U3CGetEventNormalizedSpaceU3Ed__3_System_Collections_Generic_IEnumerableU3CUnityEngine_VFX_VisualEffectPlayableSerializedEventU3E_GetEnumerator_m52F0B166CA12BDC43A8480077468F3F3B364DD2A (void);
extern void U3CGetEventNormalizedSpaceU3Ed__3_System_Collections_IEnumerable_GetEnumerator_m723616A0EE5BEC85DF12A9E2C72C30FF56C39667 (void);
extern void VisualEffectPlayableSerializedEventNoColor_op_Implicit_m1DD4365ECC9960280DCCF5340F877C73B94B99E3 (void);
extern void VisualEffectControlPlayableBehaviour_get_clipStart_m689F60FE2D816D84F198C58E1C08966B6787B7B5 (void);
extern void VisualEffectControlPlayableBehaviour_set_clipStart_m3DDB3D020EC92A72E210977E798C89A4D1891891 (void);
extern void VisualEffectControlPlayableBehaviour_get_clipEnd_m859839E532CB36444C1D195900FEBE750109A4ED (void);
extern void VisualEffectControlPlayableBehaviour_set_clipEnd_m729BCB207E6BA29584A733A96C28FA2713B71A7F (void);
extern void VisualEffectControlPlayableBehaviour_get_scrubbing_m8D4FA50C990002FBBF232405E60B6AAD169B4627 (void);
extern void VisualEffectControlPlayableBehaviour_set_scrubbing_m9AA0B61DDA4505B005D14B84B16A8C0EA93DDA34 (void);
extern void VisualEffectControlPlayableBehaviour_get_reinitEnter_mB375023DEA53AE4018E20D4E5DBBF96F312893BF (void);
extern void VisualEffectControlPlayableBehaviour_set_reinitEnter_m9BA52EC21ACA4EC37703A36D10D5DB71012BBE31 (void);
extern void VisualEffectControlPlayableBehaviour_get_reinitExit_m11F5C5677AC6886BC56AE50E34E02C4C3ACC2338 (void);
extern void VisualEffectControlPlayableBehaviour_set_reinitExit_m4B9F2701246584D549E1558D4AA53E352324082D (void);
extern void VisualEffectControlPlayableBehaviour_get_startSeed_m309C2E082A7918619C89639C8912864F0078C2D6 (void);
extern void VisualEffectControlPlayableBehaviour_set_startSeed_m32D38B7DABC4E2506C6D658588821533E7D544B6 (void);
extern void VisualEffectControlPlayableBehaviour_get_events_m216202F0555371ED4ABE49900451524905B94E8E (void);
extern void VisualEffectControlPlayableBehaviour_set_events_mF84DC8C0EF99A946F55B8376CCD90C6C026B7875 (void);
extern void VisualEffectControlPlayableBehaviour_get_clipEventsCount_m63BC2B9B266086181CCA7B2CCEB1F07468EEB7CB (void);
extern void VisualEffectControlPlayableBehaviour_set_clipEventsCount_m7B86254EA658D79246D9686ED4D9E16ECB50D7E1 (void);
extern void VisualEffectControlPlayableBehaviour_get_prewarmStepCount_m149D92B8048A4E4C2D8A39780CBB1B6A582FD2AC (void);
extern void VisualEffectControlPlayableBehaviour_set_prewarmStepCount_mC75C0EEE33184750B55CB87F61B6FE8BEFCCE915 (void);
extern void VisualEffectControlPlayableBehaviour_get_prewarmDeltaTime_mAEA02EF7E7DC4661867E73CD58A6F6AD0AEC883B (void);
extern void VisualEffectControlPlayableBehaviour_set_prewarmDeltaTime_mA4F76757F58DEE820705774C931F769192017847 (void);
extern void VisualEffectControlPlayableBehaviour_get_prewarmEvent_m20C789CA605D15AAFAAF08BB18DBA93B8BDE7203 (void);
extern void VisualEffectControlPlayableBehaviour_set_prewarmEvent_m454D61B3B33E1406FFABD5E529B346CA35104B3D (void);
extern void VisualEffectControlPlayableBehaviour__ctor_m606A9669F7622C624F0E5AB0B3453AE71D6146E7 (void);
extern void VisualEffectControlTrack_IsUpToDate_m84152EA8B902320074FD268B37CFEC1E5E8B7D0B (void);
extern void VisualEffectControlTrack_OnBeforeTrackSerialize_m218B10FF8EAA709F50705E28CD1E2F002D778BC2 (void);
extern void VisualEffectControlTrack_CreateTrackMixer_mAD3AE69BC2DA79C47DB9152AF2458203382241D4 (void);
extern void VisualEffectControlTrack_GatherProperties_m674F98807F63DFC615DF44739CE17C163CE6A3C7 (void);
extern void VisualEffectControlTrack__ctor_mE6CE6E9843BD35463D3873A4A082AFD44593B5F7 (void);
extern void U3CU3Ec__cctor_m134E48ED7703FD6FFD37EE2A635BBCDD09B603CA (void);
extern void U3CU3Ec__ctor_m620D5C0181A6F1C741E9611642A6EDD74E76EDCC (void);
extern void U3CU3Ec_U3COnBeforeTrackSerializeU3Eb__5_0_mC05756E2A3520F8A36CF89CCEB9FA9F10CABA367 (void);
extern void VisualEffectControlTrackController_OnEnterChunk_m1EF3EFAB913389BCEFBB34F618C643D458774CBF (void);
extern void VisualEffectControlTrackController_OnLeaveChunk_m035AB7792D5A01AF1B5378A85A383A93A5B028F3 (void);
extern void VisualEffectControlTrackController_IsTimeInChunk_m7841D0327B1CDC57A2D8350C7841BE6229125F6F (void);
extern void VisualEffectControlTrackController_Update_m0448870C6E19DA191DCA159812902F0F3440605F (void);
extern void VisualEffectControlTrackController_ProcessNoScrubbingEvents_m9849F1E801C0EDC94F87AEAADCE608CFA5E103EF (void);
extern void VisualEffectControlTrackController_ProcessEvent_m4F164A5B6A7EEE48A03CEFA157B1B077D9DC6209 (void);
extern void VisualEffectControlTrackController_GetEventsIndex_mF6CC8543A83F9EC003535FC7E8E3E861D10742E1 (void);
extern void VisualEffectControlTrackController_ComputeAttribute_mA080BEA66245A3CE3FEF1A728F9A504623259A2A (void);
extern void VisualEffectControlTrackController_ComputeRuntimeEvent_m5363BC4838D69C045CA5D428FCCE5409AFD86070 (void);
extern void VisualEffectControlTrackController_RestoreVFXState_m52F3DB809AB721A54D501B4ECFD8378884336CE8 (void);
extern void VisualEffectControlTrackController_Init_m606209AB01A38BE71FEB14698E815C03AC06964D (void);
extern void VisualEffectControlTrackController_Release_mBBB2834DDED2610732776ED3D41BC68C90899438 (void);
extern void VisualEffectControlTrackController__ctor_m2252CB4F240A02FC5E4389078F00C709D26335AE (void);
extern void VisualEffectControlTrackController__cctor_mF5D1E66700B00DE5520B7636989EA98286D8CCAE (void);
extern void VisualEffectControlPlayableBehaviourComparer_Compare_m836B0BAEA95F48013884D0AD7649B7B9B3583BE3 (void);
extern void VisualEffectControlPlayableBehaviourComparer__ctor_m91628EB0522619DB513B57189B9987805B4A0C06 (void);
extern void U3CU3Ec__cctor_m8784A4CD4C1575C8D8DFC54CAF57A9445EDA00BC (void);
extern void U3CU3Ec__ctor_m3AB440EF9B6381AC8CE4729A22524CFBB607547C (void);
extern void U3CU3Ec_U3CInitU3Eb__24_1_m35C4DB62613D6668B45503367830E444CE4C8210 (void);
extern void U3CU3Ec_U3CInitU3Eb__24_0_m979D61DB422D4D1D07C2498708FE6D95413F90E5 (void);
extern void U3CComputeRuntimeEventU3Ed__21__ctor_mE041EBA4A2D0442A63A980A1EE2C62B1AB81F5B1 (void);
extern void U3CComputeRuntimeEventU3Ed__21_System_IDisposable_Dispose_m7E9B8070D3F729AB5AAECFD93175AB4FF6B0C016 (void);
extern void U3CComputeRuntimeEventU3Ed__21_MoveNext_m8915BD704DFC3DFFF80548ABF495DDC60EE592D4 (void);
extern void U3CComputeRuntimeEventU3Ed__21_U3CU3Em__Finally1_m5C9213B974F92D2C874DB608553F66AA5EEDDE37 (void);
extern void U3CComputeRuntimeEventU3Ed__21_System_Collections_Generic_IEnumeratorU3CUnityEngine_VFX_VisualEffectControlTrackController_EventU3E_get_Current_m83DD7CB8991557ADCDC42AF7876DB63B9A70636A (void);
extern void U3CComputeRuntimeEventU3Ed__21_System_Collections_IEnumerator_Reset_mC11E1922A6FA2BD006D22D931A5F2523CF14A1FB (void);
extern void U3CComputeRuntimeEventU3Ed__21_System_Collections_IEnumerator_get_Current_mCCD5A35C7D39AA7F254133DD5BBE988081CD19FB (void);
extern void U3CComputeRuntimeEventU3Ed__21_System_Collections_Generic_IEnumerableU3CUnityEngine_VFX_VisualEffectControlTrackController_EventU3E_GetEnumerator_m2AF3BE79E0F9537E5A9356B2C01BE9A8C81F6A07 (void);
extern void U3CComputeRuntimeEventU3Ed__21_System_Collections_IEnumerable_GetEnumerator_mF6BF251A31ADBC1C3F8CFFE59982F8E0C85F7CC9 (void);
extern void VisualEffectControlTrackMixerBehaviour_Init_mB018DC52B5255F530C1AE00753EEBEA63CED5470 (void);
extern void VisualEffectControlTrackMixerBehaviour_ApplyFrame_m39EB85F9B2E3CFE1B9A6699A1477D967B10F500F (void);
extern void VisualEffectControlTrackMixerBehaviour_BindVFX_mC52448E6F722DD2BE8D0FCFBD42B47D975F35845 (void);
extern void VisualEffectControlTrackMixerBehaviour_UnbindVFX_m0CDA5E7EACEF4A995E7C6ED2A3F3C91BF29B2145 (void);
extern void VisualEffectControlTrackMixerBehaviour_PrepareFrame_m2C91428E2CF68E9D5BCD686950D2AF6F03890B0D (void);
extern void VisualEffectControlTrackMixerBehaviour_OnBehaviourPause_m8C8C1BFE79631C37F5C6DFC11024B79917D1D29D (void);
extern void VisualEffectControlTrackMixerBehaviour_InvalidateScrubbingHelper_mF9A450E411E0A07ED25092C33C9CA00D2B776924 (void);
extern void VisualEffectControlTrackMixerBehaviour_OnPlayableCreate_m3D5F80B1CB8E80E9B2493E315DB1971E04E78FF9 (void);
extern void VisualEffectControlTrackMixerBehaviour_OnPlayableDestroy_mA2A628856853A146CC640BF078C94DF6C2CE532F (void);
extern void VisualEffectControlTrackMixerBehaviour__ctor_m559E28AC3A23DC45F6972B33377A01D407BF5CDE (void);
extern void VFXRuntimeResources_get_sdfRayMapCS_m43E786271B4E4CF84FC9ECEA228F7BF930E682E6 (void);
extern void VFXRuntimeResources_set_sdfRayMapCS_mCBD5CA21B07EBD35FB7A6ACFAD090CCFE5F5952F (void);
extern void VFXRuntimeResources_get_sdfNormalsCS_m81CB5B4AB07311A120C846EF7EEF4AF40B5F1646 (void);
extern void VFXRuntimeResources_set_sdfNormalsCS_m60625AD04277C6E36C13D19763CD75588BDEBF70 (void);
extern void VFXRuntimeResources_get_sdfRayMapShader_mD3063E53C627E4648861A7A290BE4896FAA053EE (void);
extern void VFXRuntimeResources_set_sdfRayMapShader_mF54C5E9F9539D34A35C866DB13CA8B183A6446AE (void);
extern void VFXRuntimeResources_get_runtimeResources_mF3D34F7B1B8525C819200E600B1916E7F8AB8A31 (void);
extern void VFXRuntimeResources__ctor_m12274CA3BDCC9014C4AE5B0DF58CD716C800BE9D (void);
extern void MeshToSDFBaker_get_SdfTexture_mECBAA9DA0DD9590FDD99C86263194A9063FD80C2 (void);
extern void MeshToSDFBaker_InitMeshFromList_mA070F5AC1A7F3AEBD917E7EF8602D0A7FEFC871F (void);
extern void MeshToSDFBaker_InitCommandBuffer_m31AEFDFD74DFAFF10B13B010042F7F3D053858DC (void);
extern void MeshToSDFBaker_GetTotalVoxelCount_m358CBAF6FBB88594584F12917985C2D4E6C51A52 (void);
extern void MeshToSDFBaker_InitSizeBox_m5FB97C78F43AF057E1D28CD071BA1FCF5FC4BCEF (void);
extern void MeshToSDFBaker_GetGridSize_m2E238B4C95C3B9115C76E340FB26B6C462E3F4B3 (void);
extern void MeshToSDFBaker_GetActualBoxSize_m2D78F4D810A2FAC2886ED6A7079E0A00C638796D (void);
extern void MeshToSDFBaker__ctor_m4382050A450DB8A7FB584D855326DA2C63D72EBE (void);
extern void MeshToSDFBaker__ctor_mB3EEFBBA0BD7A8A449BF256C9B33EC80A3E638CC (void);
extern void MeshToSDFBaker_Finalize_m7E3DF440D57DBFE6F146FEB83A0B6B52E57F5A52 (void);
extern void MeshToSDFBaker_Reinit_m33B5E0BDD51F81D1F7AA9F84CC4060D0E44E463F (void);
extern void MeshToSDFBaker_Reinit_mCE929D673905E852CE0CE993EBB223F0ED0C74E5 (void);
extern void MeshToSDFBaker_SetParameters_m38C6EBE0FCD3A9985399A834E2D430680B305C38 (void);
extern void MeshToSDFBaker_LoadRuntimeResources_mC0C91894CC56731379D3253E9CF47E51A024D6C5 (void);
extern void MeshToSDFBaker_InitTextures_m2C2CCC8540291CFB603D4ACB98AC8EC7D968AF4C (void);
extern void MeshToSDFBaker_Init_mC664261A480639E3A4A65CE49B6D6595ECB0FD29 (void);
extern void MeshToSDFBaker_UpdateCameras_m36ECE1DC89606DD9B337B7B279A9948C47378A3E (void);
extern void MeshToSDFBaker_ComputeOrthographicWorldToClip_mBA143A16FA0C34CFE7CDC7D416B6C105ACC9DE16 (void);
extern void MeshToSDFBaker_iDivUp_mC187EDD662E2BF3F32D5AA674F0A47346C55F359 (void);
extern void MeshToSDFBaker_GetThreadGroupsCount_m4DCF1F0B29A933D29411CD4E2D83327C8604B36C (void);
extern void MeshToSDFBaker_PrefixSumCount_m3C9E9E5FA7337DC001DADF50AE621923911A43A5 (void);
extern void MeshToSDFBaker_SurfaceClosing_m1712DAB3C5D6359A7C7CE215FEC187404BED5E4D (void);
extern void MeshToSDFBaker_GetTextureVoxelPrincipal_m908BF61AEE91EA0438B7E447DD4CF0177BD229D6 (void);
extern void MeshToSDFBaker_GetTextureVoxelBis_m7F150C086B55E188BEBCD59957874431B8B8F920 (void);
extern void MeshToSDFBaker_JFA_mA31A6D93A96EE3CB887528E686D7919A4FE3D25C (void);
extern void MeshToSDFBaker_GenerateRayMap_m9014A08AB40B5DC585A4882D8CCB7C4ADDA9D48D (void);
extern void MeshToSDFBaker_GetRayMapPrincipal_m2DE01FBB3F68FBAE1451FEFBB367022085096A76 (void);
extern void MeshToSDFBaker_GetRayMapBis_m31B5184905348AD94BEC5FB7114CE380F2EF8DA1 (void);
extern void MeshToSDFBaker_GetSignMapPrincipal_mCF2B4EF2B8B685CA57E51377C0C49E8EA2703875 (void);
extern void MeshToSDFBaker_GetSignMapBis_mBABEB9223BEE531DAF10C7FBD79C9ACC85C5F69D (void);
extern void MeshToSDFBaker_SignPass_mB3D3AED7713AB3B7DDB0C77B76C3781D59A1C9B4 (void);
extern void MeshToSDFBaker_BakeSDF_mF0737A425E08581920C2D1034E619D566B5FC16A (void);
extern void MeshToSDFBaker_InitMeshBuffers_mA5D107F3721F38DBB916532A3B9B6E84A24DCA09 (void);
extern void MeshToSDFBaker_FirstDraw_m024B2A34A8A90EBD5ADA9FCCE20F8BF66DF161B5 (void);
extern void MeshToSDFBaker_SecondDraw_m2BBE4CFB156E114AE6832D84A089978536699771 (void);
extern void MeshToSDFBaker_BuildGeometry_m4344ACEE2B820B63DCDCFF508E767175B6E1C817 (void);
extern void MeshToSDFBaker_InitGeometryBuffers_m3221F0474C3F6D9097FEB7E9F50CE665CC7883AE (void);
extern void MeshToSDFBaker_InitPrefixSumBuffers_mAF8F4996FFE7B0C96AD1C38BF3BC25A0B765958F (void);
extern void MeshToSDFBaker_ClearRenderTexturesAndBuffers_mFB6D2B7C2EF1CC4A9ECACB769621D461899E622D (void);
extern void MeshToSDFBaker_PerformDistanceTransformWinding_mF0A58664A163A68C095AA04BDAD6E15106E9C83D (void);
extern void MeshToSDFBaker_ReleaseBuffersAndTextures_mF9E5FFB2308C6E800DF8C6B72173766E3B61C2C5 (void);
extern void MeshToSDFBaker_Dispose_m161EE2DA63BB7958CE022908C3FFEAAB92F513C6 (void);
extern void MeshToSDFBaker_CreateGraphicsBufferIfNeeded_m025EF26D06E44974CC07D783035CCC8F0DAFE61F (void);
extern void MeshToSDFBaker_ReleaseGraphicsBuffer_m9892BFD6274E1492BE48240BDCE2A9D7286719F0 (void);
extern void MeshToSDFBaker_CreateRenderTextureIfNeeded_m5B5D546AD320BFB4B76BEB24A62B914BB2A7A1DC (void);
extern void MeshToSDFBaker_ReleaseRenderTexture_mD362FCB0A017FF60CF58888B272D36A7833F92E5 (void);
extern void MeshToSDFBaker__cctor_mF617DF3F43D12F5C260DE4EF5362E0FD2EE8B959 (void);
extern void ShaderProperties__cctor_m154A681C6FE4887FA2C8681CACBC45F22CF1E5D3 (void);
extern void Kernels__ctor_mAA8D1C254238AF617EC6C9D4EC508962EEFF3C56 (void);
extern void VFXMouseEventBinder_SetEventAttribute_m1B3CB78F8479F2CC6B557BA3F9D11491FB2E9A5A (void);
extern void VFXMouseEventBinder_Awake_m4874D86408A47F0260FA827FD2AF8C2554916DEB (void);
extern void VFXMouseEventBinder_RaycastMainCamera_m3B068FB3A911122EE5B531B03B64BF1885A2F5DF (void);
extern void VFXMouseEventBinder_RayCastDrag_mA48A78CD33F70568B5C5B7D2CDEB2B3D474CE76A (void);
extern void VFXMouseEventBinder_RayCastAndTriggerEvent_mE5CC4749C8EC6914625220934276B2AC29D1C520 (void);
extern void VFXMouseEventBinder_OnEnable_mC4E0DC107FD9FAD395002CBF5F36A58F766A398E (void);
extern void VFXMouseEventBinder_OnDisable_mE638C97759BC680F7BAF72D74F4E6258E6C0E26A (void);
extern void VFXMouseEventBinder_GetMousePosition_mBBBD1C179D116151B9680061E049323C43CFA071 (void);
extern void VFXMouseEventBinder_DoOnMouseDown_mB5F4554ED7949B6D6BFCA0E5B501F87F80B52694 (void);
extern void VFXMouseEventBinder_DoOnMouseUp_m3F48CD8795EB94D37B86275815D44A3284BC33BA (void);
extern void VFXMouseEventBinder_DoOnMouseDrag_m0310246449E70E1DE580B869E4B90124D00C3837 (void);
extern void VFXMouseEventBinder_DoOnMouseOver_m4BE7BFA06C6B0FD42688F747ACF0BDA7D82765F5 (void);
extern void VFXMouseEventBinder_DoOnMouseEnter_mEDCA5B540678906577F24882A09AD8E08E03D55E (void);
extern void VFXMouseEventBinder_DoOnMouseExit_m3460D1E2C017D2F39FF8280A830125F1692814DB (void);
extern void VFXMouseEventBinder__ctor_m269FBD4E25512D754602BA4322D36979B9B4C991 (void);
extern void VFXMouseEventBinder_U3CAwakeU3Eb__12_0_m32B96CDA0C466364249D8AD0597A12B067E00513 (void);
extern void VFXMouseEventBinder_U3CAwakeU3Eb__12_1_m969BEE4465D53C7F39A4510668D1A418687CA062 (void);
extern void VFXRigidBodyCollisionEventBinder_SetEventAttribute_m418F73D31B12BEEA39C810335B4D1FBADAA2F499 (void);
extern void VFXRigidBodyCollisionEventBinder_OnCollisionEnter_mBC06344BAB974CC3104ADFFFDDBC13805AD7CD29 (void);
extern void VFXRigidBodyCollisionEventBinder__ctor_m0A4CEA80864876A7B38C26DDFB83715944486D6F (void);
extern void VFXTriggerEventBinder_SetEventAttribute_m30552173CF7D7CA17B5BB76B6088756002056DF3 (void);
extern void VFXTriggerEventBinder_OnTriggerEnter_m7B530756703BE1C37DA3206D81D4D6FA2C378327 (void);
extern void VFXTriggerEventBinder_OnTriggerExit_m098634E694226818276DF5430088B893B548EE1E (void);
extern void VFXTriggerEventBinder_OnTriggerStay_mF2D6604ABF9323C20333F482F8B8CCF6523683E1 (void);
extern void VFXTriggerEventBinder__ctor_mB7AEE8C9686AD0E31068AD18CAE614982215500C (void);
extern void VFXVisibilityEventBinder_SetEventAttribute_mBDE795116F6A7E9BF54C8AE080D83FD614D42724 (void);
extern void VFXVisibilityEventBinder_OnBecameVisible_m46F052C58FDE520ECDA612E3B5D5B3BD56401641 (void);
extern void VFXVisibilityEventBinder_OnBecameInvisible_m4D980D9A9E75AEF34A9B3492FDC8D67BC0005527 (void);
extern void VFXVisibilityEventBinder__ctor_m84E91F879D507B7E47B3D239D7158D9E4E019B5F (void);
extern void VFXEventBinderBase_OnEnable_mF6262A11BD09CA75E1AE89A5D362C15861A9B68D (void);
extern void VFXEventBinderBase_OnValidate_m137196E91DD0412C813823130B79D0F04DFEB0FC (void);
extern void VFXEventBinderBase_UpdateCacheEventAttribute_m3FD1CFD6B8BE81E246534D127FB175554C2D1CD2 (void);
extern void VFXEventBinderBase_SendEventToVisualEffect_mC7CAA713AF7BD533AFD1A6663753141221493690 (void);
extern void VFXEventBinderBase__ctor_m9CD7C6460EC05578FF0E20CA29B4459D00DB6E53 (void);
extern void VFXOutputEventAbstractHandler_set_m_VisualEffect_mB7CEA6FD627A192FCC5B1BB72A3FD034FB40D209 (void);
extern void VFXOutputEventAbstractHandler_get_m_VisualEffect_m5DE6F44EA40481C9A196614B97B68E4B1DEC59B3 (void);
extern void VFXOutputEventAbstractHandler_OnEnable_m9DA1E28C8FD0B621DEE7E0D055B467436E2AC29E (void);
extern void VFXOutputEventAbstractHandler_OnDisable_mA95FD62310C790E2FC47EBEED06C73315F3EFCC5 (void);
extern void VFXOutputEventAbstractHandler_OnOutputEventRecieved_m744F5D9DE68857E8B83ABE29DA28A63DD4746BD3 (void);
extern void VFXOutputEventAbstractHandler__ctor_m4E2D7BE313EA0EC6CD4BE214184BDAC7899F18E9 (void);
extern void ExposedProperty_op_Implicit_m9CEB3D09454745C8E450BAB7C266D15ED28B44FB (void);
extern void ExposedProperty_op_Explicit_m8F7C257466E30C5602C4F12AEA2588B7093424F3 (void);
extern void ExposedProperty_op_Implicit_mA6E7C425842E5FA0B783360A77558C2D45A1970F (void);
extern void ExposedProperty_op_Addition_m2FBBFB0417DEA6D43A730115FA3B9F39A3892C93 (void);
extern void ExposedProperty__ctor_m92A33E6D7AD2239105318F03D95351BCC16D739A (void);
extern void ExposedProperty__ctor_m20517E8CD365AFE0803F9F8A632BFE09B34C3E65 (void);
extern void ExposedProperty_ToString_m56C2D2ACA650A07737B9D8815A3ED1DEAC6EF6A9 (void);
extern void VFXAudioSpectrumBinder_get_CountProperty_m5DF677930AD099959C3A5532E54C4F874126DCD8 (void);
extern void VFXAudioSpectrumBinder_set_CountProperty_m268EEE2B83A4C3AFD7B21B2D1BF46BF44DAD15E9 (void);
extern void VFXAudioSpectrumBinder_get_TextureProperty_mE5D3C308903A7A372D524FA235F828D03451BE54 (void);
extern void VFXAudioSpectrumBinder_set_TextureProperty_m839466D803689AC9DA97636117987E3C734D5F67 (void);
extern void VFXAudioSpectrumBinder_IsValid_m30BE575435B6E5E745DD0784DC9F3C7A83C1F053 (void);
extern void VFXAudioSpectrumBinder_UpdateTexture_mE59FAFFCA67F721F8C4F61F5382C4512AC1F2D3F (void);
extern void VFXAudioSpectrumBinder_UpdateBinding_mA14547ECB53ED7582BEA59BF8322D45A8F03B1F7 (void);
extern void VFXAudioSpectrumBinder_ToString_mAC1E82DB90BFF1E5082D8568D243146DDAB5133F (void);
extern void VFXAudioSpectrumBinder__ctor_m6AE18A43116D28E1E3C0442125DD2EAC81D2481D (void);
extern void VFXEnabledBinder_get_Property_m9E546CBA700214100BF8950EBCE931F7636447D4 (void);
extern void VFXEnabledBinder_set_Property_m3FEC0F9E00145E12EF6BEC5F4B7CB0B6B42F6930 (void);
extern void VFXEnabledBinder_IsValid_m18B425AA9785071B34E9DD6B7EE9D96049609CEE (void);
extern void VFXEnabledBinder_UpdateBinding_m7317EA9F03EBCA97B04C4A09A6D4F9EA9810DE91 (void);
extern void VFXEnabledBinder_ToString_m0494C5F154394FE7DD3B111500E732C12116D2BD (void);
extern void VFXEnabledBinder__ctor_mDE6F21D9110687AE183840A8518F2E98F5C4B658 (void);
extern void VFXHierarchyAttributeMapBinder_OnEnable_m38918F3AE086A49B9128184E3E96E8CE96D974FC (void);
extern void VFXHierarchyAttributeMapBinder_OnValidate_mA060E7B9E068BC61735610221CB6E0840AA557F4 (void);
extern void VFXHierarchyAttributeMapBinder_UpdateHierarchy_m47224F693D4241AA2756D12E51501FF19879F326 (void);
extern void VFXHierarchyAttributeMapBinder_ChildrenOf_m7CE1D7B085ECC08E6FDD5ECD616B8830475E0292 (void);
extern void VFXHierarchyAttributeMapBinder_UpdateData_mACEC1F8DE36468C5AEB4991740DB49D038C92DF5 (void);
extern void VFXHierarchyAttributeMapBinder_IsValid_m6D606850EC453BA8D59586905C7D24AB936AFA46 (void);
extern void VFXHierarchyAttributeMapBinder_UpdateBinding_m6F39ADB3F03839FCA851407C321E801940D108F5 (void);
extern void VFXHierarchyAttributeMapBinder_ToString_m2F6AABC4EF6170107A24A0811C0EEDE7BF3EB18E (void);
extern void VFXHierarchyAttributeMapBinder__ctor_mE25F5BE296E8CBB0351ED58614EE060EFBA8FE24 (void);
extern void VFXInputAxisBinder_get_AxisProperty_m41BA17CCFBC250949E08A54FE410D77B80499C44 (void);
extern void VFXInputAxisBinder_set_AxisProperty_m49DA6F0B7AB5C8CD1142DB51012E77DABBAE826A (void);
extern void VFXInputAxisBinder_IsValid_m8D2B723CE3DB06D4C828C99B4605DC6A3A47D77D (void);
extern void VFXInputAxisBinder_UpdateBinding_m6548BAD730D4F2702993C26567E17F5F8B09BA67 (void);
extern void VFXInputAxisBinder_ToString_m8E72E56666E8793464814B58E66074A5FD982607 (void);
extern void VFXInputAxisBinder__ctor_m3A8598E21956D3A4513C0D49ACF0C0DAD1634916 (void);
extern void VFXInputButtonBinder_get_ButtonProperty_mA522DC8AF508A7BBCABC5D2429B034CD42092294 (void);
extern void VFXInputButtonBinder_set_ButtonProperty_m10BC47EB9D8FE15F09DA10B59ABF0638F3803C7D (void);
extern void VFXInputButtonBinder_get_ButtonSmoothProperty_mE317FFB3A849B1C1FB39F25107CA9D7750D94B2A (void);
extern void VFXInputButtonBinder_set_ButtonSmoothProperty_mF65F9ED724538801BA94C8F54044740DC385EB4D (void);
extern void VFXInputButtonBinder_IsValid_m9CA18F74ED3A211B72C827BD7778542B1C951754 (void);
extern void VFXInputButtonBinder_Start_m6118535A6B5A2941B70D64172D08DC341CA33E59 (void);
extern void VFXInputButtonBinder_UpdateBinding_mBC295D5045F481605C5EC7C0EC5448D76F8D1356 (void);
extern void VFXInputButtonBinder_ToString_m3369304609737827924CE154EE18EB7D29E59C06 (void);
extern void VFXInputButtonBinder__ctor_m1964F20A0E02493DAE4425BE15CC94D1DDB013DB (void);
extern void VFXInputKeyBinder_get_KeyProperty_mED5F959B3D088D9FCB7994D328328D30A234AD9A (void);
extern void VFXInputKeyBinder_set_KeyProperty_m74531680A817E42585D994459F13E7C05627C252 (void);
extern void VFXInputKeyBinder_get_KeySmoothProperty_m76F68335C1429F2C0E47D320BBFE0CDDEFA4D233 (void);
extern void VFXInputKeyBinder_set_KeySmoothProperty_mB75547AE164AF5C49EF91E1657E0E4FD81FF7560 (void);
extern void VFXInputKeyBinder_IsValid_mFCFF34FAFC061CF5BFCBA17F1F06A4B3AD3E1ED2 (void);
extern void VFXInputKeyBinder_Start_m4104697A270AD61FD19A0BB650908EC03CAA4BA1 (void);
extern void VFXInputKeyBinder_UpdateBinding_m0A405ECC914FAEC6DBA071C51DBFB7245FF5E872 (void);
extern void VFXInputKeyBinder_ToString_mAB3ED436EBA2E2FBEED6AC4C2B4D833B30ABABF0 (void);
extern void VFXInputKeyBinder__ctor_m0ACB3319F4FF129AC6DBD52BFA33B84E1ABCDFF4 (void);
extern void VFXInputMouseBinder_get_MouseLeftClickProperty_mC69F12902664403234F3F6833857B145AA280E01 (void);
extern void VFXInputMouseBinder_set_MouseLeftClickProperty_mD7F27520CF9D3374FD010F64BC7A067BF587487A (void);
extern void VFXInputMouseBinder_get_MouseRightClickProperty_m607B8729BD1AD905996BBD1D81BA6364CB50D950 (void);
extern void VFXInputMouseBinder_set_MouseRightClickProperty_mA594A77F11DF628FD69423C32AC1C71EB1761C00 (void);
extern void VFXInputMouseBinder_get_PositionProperty_m4510B50390BF4D86C0F754CF433B5496F11E9FB0 (void);
extern void VFXInputMouseBinder_set_PositionProperty_m65C3FEFE063F15943C15A923F19B1E727C4AB836 (void);
extern void VFXInputMouseBinder_get_VelocityProperty_m2E5913FF1F6A8D9780191BC8BC92F10DE7F0CC25 (void);
extern void VFXInputMouseBinder_set_VelocityProperty_mEFD15CA2774BD57920668603BA3C3C86B9E21B35 (void);
extern void VFXInputMouseBinder_IsValid_mBB3B7F2ACE4F7DCECF48DBF403D534722B3474F9 (void);
extern void VFXInputMouseBinder_UpdateBinding_m69E9420D08180E52EBB3605BE0580C37C8D545F0 (void);
extern void VFXInputMouseBinder_IsRightClickPressed_m0C582DEB1E7B7A5701F38E309A7B4EBA05FCF6EC (void);
extern void VFXInputMouseBinder_IsLeftClickPressed_m17F8F9BA1B555FC41046F2A311DDA6CEF6C7DADE (void);
extern void VFXInputMouseBinder_GetMousePosition_m5630931394578D6D2BA9E817C69FADF7D53B229C (void);
extern void VFXInputMouseBinder_ToString_mCD428D57FA2CBA8B62143A421A39E3AF79DB71B1 (void);
extern void VFXInputMouseBinder__ctor_m827C8ED1DDFB31459335FA71CA19DC484DBC7A62 (void);
extern void VFXInputTouchBinder_get_TouchEnabledProperty_m105A858EE0EECFD8737C36D72BCF579C6A2BAC8D (void);
extern void VFXInputTouchBinder_set_TouchEnabledProperty_m78A63DFA1A097040AADB6B4084ABE45A7C7F3053 (void);
extern void VFXInputTouchBinder_get_Parameter_mE98BC45B23C7C6FA1FA268D506EB4F7BF9AA06F5 (void);
extern void VFXInputTouchBinder_set_Parameter_m1F3487772C022265BDB7A931F49C9DD9318D4EBE (void);
extern void VFXInputTouchBinder_get_VelocityParameter_m00EC882744202BC3C09D5FE1E75416F3F455FCED (void);
extern void VFXInputTouchBinder_set_VelocityParameter_m1BBAE312F27FFE87B9F9396F4A3F8DF06722430A (void);
extern void VFXInputTouchBinder_IsValid_m6B86A4123CA4D2810F22DCDB8A0BAF9BE1291DA5 (void);
extern void VFXInputTouchBinder_UpdateBinding_mF21A05757A5200052BF16E2B1985D2A1A4EC84F8 (void);
extern void VFXInputTouchBinder_GetTouchCount_m6F85B121C3D99D4FC9B515CBC3C8CE84EC2A30B2 (void);
extern void VFXInputTouchBinder_GetTouchPosition_mA0555635D0689F84EF0929162BFE644E996C36C5 (void);
extern void VFXInputTouchBinder_ToString_m5AF94A7C372FADD51886F7462851C236B09288FF (void);
extern void VFXInputTouchBinder__ctor_mFE6C77657C9A6F1F34DEA017A22A8653C02437E6 (void);
extern void U3CU3Ec__cctor_mCF1AC0A123D5214071C87B14B9BEFA9F3E165D06 (void);
extern void U3CU3Ec__ctor_m9AB57595F56A5AD38DD8DDFF11254F497A21AF07 (void);
extern void U3CU3Ec_U3CGetTouchCountU3Eb__20_0_m1C7DAEF04F30D57567824E2E97A7DE426E36462F (void);
extern void VFXLightBinder_get_ColorProperty_m985EDD04353195BBD32B94DF66317A170C93CAFD (void);
extern void VFXLightBinder_set_ColorProperty_m202E67AB1B8A2265BBB8D4C1DB75646977469158 (void);
extern void VFXLightBinder_get_BrightnessProperty_mF1E9B4E4B53714CAF652F56B563BFD1E2C794AD1 (void);
extern void VFXLightBinder_set_BrightnessProperty_mBBC16547EA68167DA5D72CBE1D92A85D3F19C0D5 (void);
extern void VFXLightBinder_get_RadiusProperty_m0373D3B6410CA134EB6A4C3F5B9B7538583D5A20 (void);
extern void VFXLightBinder_set_RadiusProperty_m399CFB05F9BFE7B45B9B07E94F837B2DF77500E6 (void);
extern void VFXLightBinder_IsValid_m18DC2B463BD5382C3E278CDC87F54BAD0DB2D602 (void);
extern void VFXLightBinder_UpdateBinding_m745CD70F467410D2FEEA727CF58580F4692B304F (void);
extern void VFXLightBinder_ToString_mD8BC48524A247DF67DA7670B8D3E6857A70DEACB (void);
extern void VFXLightBinder__ctor_m526A3FA1F645F0A8EFC83A2B07184CD3D57661E2 (void);
extern void VFXMultiplePositionBinder_OnEnable_m81DA3CE28B764E868656402A55399AA93A84E289 (void);
extern void VFXMultiplePositionBinder_IsValid_m6B2D25A10A35F604BB649B554D00FEAB93ECED44 (void);
extern void VFXMultiplePositionBinder_UpdateBinding_m4D1544BAB488ECC4ADDA4B5914AC1793C683147D (void);
extern void VFXMultiplePositionBinder_UpdateTexture_m08801CB93727EA213155BAC676BE9437E489B5E3 (void);
extern void VFXMultiplePositionBinder_ToString_mEDB13894DBEAF2F31994A72310A23231A6A7BDCA (void);
extern void VFXMultiplePositionBinder__ctor_mE710FDA9E568A663416CB22750A80995D7169FEF (void);
extern void VFXPlaneBinder_get_Property_m54C30C4D74B21178FCB92E920F2B0B004E611CB7 (void);
extern void VFXPlaneBinder_set_Property_m91934DD97598848E257D6841DF8E6583AA71E57C (void);
extern void VFXPlaneBinder_OnEnable_m7367BBAC66F8ACCA856E6FE63D3F320C79C0C2BC (void);
extern void VFXPlaneBinder_OnValidate_m7950C4FB69814A74319778D4DECBB657C686E2FD (void);
extern void VFXPlaneBinder_UpdateSubProperties_m9AEB13B4B517C1DBFD1DDA34390F682959FD909A (void);
extern void VFXPlaneBinder_IsValid_m4F681A04FB92422F01268AE5E4D943A5BFB0B505 (void);
extern void VFXPlaneBinder_UpdateBinding_mE2DD9576ADF5DC76AEE00656785F77B05B2E6B1C (void);
extern void VFXPlaneBinder_ToString_mE44F0899E57BE384413B7B7955F5E6BC6E683F52 (void);
extern void VFXPlaneBinder__ctor_mED11CF344484C1B27051B2E34270AE1FB45765C3 (void);
extern void VFXPositionBinder_get_Property_m2D7DD39EC9D12CFD373412BB123D8072B96DA611 (void);
extern void VFXPositionBinder_set_Property_m3E5DF8C12D71D8DF8585AC6B60D72CFE6144FA2C (void);
extern void VFXPositionBinder_IsValid_m35061E5E219677356561587A4D135AE338694DEA (void);
extern void VFXPositionBinder_UpdateBinding_mD01D10DACA584A345656478A1573D7B1359C6BC5 (void);
extern void VFXPositionBinder_ToString_m78DB45D14627C4B494AE4AE39C19A516F554B92F (void);
extern void VFXPositionBinder__ctor_m5CF6F416503FA2FFEFEF87579C815966DA13206A (void);
extern void VFXPreviousPositionBinder_OnEnable_mE04D045DBCEF3C2A8939400C8238B1824104411D (void);
extern void VFXPreviousPositionBinder_IsValid_mB7EB49FEBF47D5DD3137CC3FB077CCAE1DA62C76 (void);
extern void VFXPreviousPositionBinder_UpdateBinding_mC5F3579B2C52ECBC389AB59BD446B6FE2119B7A6 (void);
extern void VFXPreviousPositionBinder_ToString_m9CE4B7425D39CC7001D4345EB23DAF871DB4E81C (void);
extern void VFXPreviousPositionBinder__ctor_m08FD8E72C2B94BACB7B3C6765A4EB267889DA429 (void);
extern void VFXRaycastBinder_get_TargetPosition_mB1DA401AC6AE1AAE043647B165928BA37624F56D (void);
extern void VFXRaycastBinder_set_TargetPosition_mA7F2AB642781472A6E17C0C9EF1A0B9CE5623C55 (void);
extern void VFXRaycastBinder_get_TargetNormal_mF80F5F53B038FE6044A3B3BC2B05A3C7A0D26A09 (void);
extern void VFXRaycastBinder_set_TargetNormal_mC914237651B9C4FACDBAC0013BE1F06DE7AD3A58 (void);
extern void VFXRaycastBinder_get_TargetHit_mCCE5712B9F489C7E060B1E7753D3F441852E8F1F (void);
extern void VFXRaycastBinder_set_TargetHit_m14E7AE924CD526CF36C41DB84BF94E07706CE5CE (void);
extern void VFXRaycastBinder_OnEnable_m3970D4A7E5262CE38A895CAE3CBB22E9FE801678 (void);
extern void VFXRaycastBinder_OnValidate_m946F4F1F8F9ED30E1657D07CD472330C83C8D0D2 (void);
extern void VFXRaycastBinder_UpdateSubProperties_m984FC69B108C955BA03E05B08DEC583AAFFDE01D (void);
extern void VFXRaycastBinder_IsValid_m90C04243E0957E1BC9EF550A17844A6DA60E6960 (void);
extern void VFXRaycastBinder_UpdateBinding_m16F89A49C9B46B21A4891CF80A6A61268C5E0188 (void);
extern void VFXRaycastBinder_ToString_mA78937DE31C9774C3FC1ED4EA455DAC05F90937A (void);
extern void VFXRaycastBinder__ctor_mDD2986E1D00C5C668849ED855384DA39638DDFB0 (void);
extern void VFXSpaceableBinder_GetTargetSpace_m0137508DC0E097C46C7EAA1ECC89398947D10D58 (void);
extern void VFXSpaceableBinder_ApplySpacePositionNormal_m57A07B73ED57359C970485FF3EBA907964822089 (void);
extern void VFXSpaceableBinder_ApplySpaceTS_m3C7E145FA7A6D8B485B8992B7992FA984D76977C (void);
extern void VFXSpaceableBinder_ApplySpaceTRS_m385C4C16AB3E5A84044D1406394ABC9B1A9BB748 (void);
extern void VFXSpaceableBinder_ApplySpacePosition_mA1B55C45A80F225F7FC4562E74E306E90F95D12D (void);
extern void VFXSpaceableBinder__ctor_m22AD01FED423E4B640D31DC8DC13AD85422E1A04 (void);
extern void VFXSphereBinder_get_Property_m4BD037B82D29852C959D5A1FEF723D9A23A00C1F (void);
extern void VFXSphereBinder_set_Property_m2463716FB48677C077197E308EA17763202BAC56 (void);
extern void VFXSphereBinder_OnEnable_m6B394405C5822497D82D45FD2C688F4D1498C157 (void);
extern void VFXSphereBinder_OnValidate_mC678A407430AE4D4EF88B406FDAF3CA847249E33 (void);
extern void VFXSphereBinder_UpdateSubProperties_mEDEF8CCE8D1B0CDE488F0A113CCF9D3699B4C4A6 (void);
extern void VFXSphereBinder_IsValid_mDDE887FEA870AF1ED13EDED131F97C76FDCA8332 (void);
extern void VFXSphereBinder_UpdateBinding_mC6A1B76D0164FB80EA507DC77B0CBF57729333A0 (void);
extern void VFXSphereBinder_GetSphereColliderScale_mFF7014EA91C2D8244BD6F925F0FC267C908696D1 (void);
extern void VFXSphereBinder_ToString_m6E5BAF459D5303CA7A1E0077F558F260605C3BCA (void);
extern void VFXSphereBinder__ctor_m69CA4A3B5BF7AAA4F1EAFB16982232AC84FBE3EB (void);
extern void VFXTerrainBinder_get_Property_m11076487A8F242D0C7569747D702BDEA81F122B8 (void);
extern void VFXTerrainBinder_set_Property_mFA12D45C44255006830071146EF6E2B71DE5CC10 (void);
extern void VFXTerrainBinder_OnEnable_mDFBE56E0010C7316828223EA34F3E72F111F0660 (void);
extern void VFXTerrainBinder_OnValidate_m2792C13EB0EC52B41CA022DE47A2ED676D8F5109 (void);
extern void VFXTerrainBinder_UpdateSubProperties_m4BC6A8E8C029DD3BD310534ABC857E2EF6FEE282 (void);
extern void VFXTerrainBinder_IsValid_m9ADEB583B6516C697F5AE5B37BD77BE0FD64897E (void);
extern void VFXTerrainBinder_UpdateBinding_m51D293C9821AA974ECCC7D56960952E16146C005 (void);
extern void VFXTerrainBinder_ToString_m6BC233FED85CE059387C328F979B8C731467F4EF (void);
extern void VFXTerrainBinder__ctor_m1D29723C84DE383CA3430EC4F17495705D5F1A0B (void);
extern void VFXTransformBinder_get_Property_mDDA731FAD1367EF045268409CDED96BFEB180184 (void);
extern void VFXTransformBinder_set_Property_m7D20FC04182B2015BFB94C2EFBD33AE32D54F3A7 (void);
extern void VFXTransformBinder_OnEnable_m99FD4C7A36F4D6828151CDA42D7228199F395952 (void);
extern void VFXTransformBinder_OnValidate_mE7FF581C03E4BAC5D4802770FE8EE7F37B40DFD1 (void);
extern void VFXTransformBinder_UpdateSubProperties_mBF83D3F5E36B080C6CA43DF75A6C51B7A8AE6530 (void);
extern void VFXTransformBinder_IsValid_m7C1ADF405797D11C4B58B07CDB9E9C42A011319A (void);
extern void VFXTransformBinder_UpdateBinding_mC78FFF864A6EBCDC4F4BE86912D06B0276DC22A0 (void);
extern void VFXTransformBinder_ToString_m6446F68FCBEE2476A0B3896C743D125DA37FAC1C (void);
extern void VFXTransformBinder__ctor_mAFB4C41C58EAA855D89425DE74CA282313D31A90 (void);
extern void VFXUIDropdownBinder_get_Property_mA312792ECADF9C6FC60D90F817069A49ACCB38C2 (void);
extern void VFXUIDropdownBinder_set_Property_m3ED3BB346E2D25DBE202427337FAC7295D029CF8 (void);
extern void VFXUIDropdownBinder_IsValid_m2242EB079F7B03379C451F7E0EE893A05B22CE72 (void);
extern void VFXUIDropdownBinder_UpdateBinding_mA2D693A069DB8B8E46B94998168B5BA55AE3AAEB (void);
extern void VFXUIDropdownBinder_ToString_mD9EB109542CCBE72A2A1B2C0D1ABAF1E888C9525 (void);
extern void VFXUIDropdownBinder__ctor_mFE6887A7B2147AB264BD55C93D3276BB962ACF2B (void);
extern void VFXUISliderBinder_get_Property_m1337B1C947ADE3A5165034AC0CEBEC6BF5E7D6F5 (void);
extern void VFXUISliderBinder_set_Property_m64411E8C290FC676ED685978BE0EFA7222703AFC (void);
extern void VFXUISliderBinder_IsValid_mC9747D1AA84FC9C01A2013D817845BD56FF3AA7A (void);
extern void VFXUISliderBinder_UpdateBinding_mB5228548515D6D956FAF8FF7065DB79450711AF5 (void);
extern void VFXUISliderBinder_ToString_m783D8F7A1C66611D2921F3CADD972A28C6554C92 (void);
extern void VFXUISliderBinder__ctor_mE12081296E2ADF34102FC1059A762C714EEF2B46 (void);
extern void VFXUIToggleBinder_get_Property_mD8909CE48307CA7F8218CFFFABE4478B4B76FAED (void);
extern void VFXUIToggleBinder_set_Property_m0D1AD5FC6784FDD9B02A50E4ECF627A46082BC7E (void);
extern void VFXUIToggleBinder_IsValid_m8CE38454205A6E0CAE24F652D990B61115A4AA59 (void);
extern void VFXUIToggleBinder_UpdateBinding_m540D27870616173E0F74275B324162E132EDDE44 (void);
extern void VFXUIToggleBinder_ToString_m5F9673F0E3DB2AD4BB1EF915F3FE1545E5D08F23 (void);
extern void VFXUIToggleBinder__ctor_mF32CC67443A5FCD342046325703CB346AAA66811 (void);
extern void VFXVelocityBinder_get_Property_m1A1D476AFF3A3D71861361F157C3648BE4E13376 (void);
extern void VFXVelocityBinder_set_Property_m962BAC85948E45D56FD6AC427AF4644617906847 (void);
extern void VFXVelocityBinder_IsValid_m733A7FFC62AE73C5EC2718C579318EEE889A6BB2 (void);
extern void VFXVelocityBinder_Reset_mDACCE514C2B72E4B47F809BCDD65B976A7F232CD (void);
extern void VFXVelocityBinder_UpdateBinding_m09ED97F0F480345359CA957026989E7E020D3FDD (void);
extern void VFXVelocityBinder_ToString_m1F67119485CFD21DB8903023EB78BB7D7123332F (void);
extern void VFXVelocityBinder__ctor_m90ADA6EBDCAF56890C4078630ED7E2FF1519596B (void);
extern void VFXVelocityBinder__cctor_m1DCF8B74965367AADAED91267964D319EB6C532F (void);
extern void VFXBinderAttribute__ctor_m5685F22B447E9F6F9F0AD314B115F9ACAB27EED1 (void);
extern void VFXBinderBase_Reset_m7C64FFDCB71B077590384C512E0776B33B23477E (void);
extern void VFXBinderBase_Awake_mB78C136536D663C6188EA4575AC9F971F512B915 (void);
extern void VFXBinderBase_OnEnable_mA026DDFCBD31D8800C70F6A291A8DB6387F2041E (void);
extern void VFXBinderBase_OnDisable_mC6D17B49F6277356AD6F67652EE80B122920B577 (void);
extern void VFXBinderBase_ToString_m7FA1FF8BC36F8140D98A49BEF82EA8CCAF2E8DB0 (void);
extern void VFXBinderBase__ctor_m4C0835B8FFF0968BC5378A1D579ABD0168ECF7D3 (void);
extern void VFXPropertyBinder_OnEnable_m867A5FEA5EDE524F0F7DF47EB86BF80128A6F298 (void);
extern void VFXPropertyBinder_OnValidate_m31AF250AFDCBBEAEEA73D29AF8793E25CEDF478D (void);
extern void VFXPropertyBinder_SafeDestroy_m4BC4B6A481989E373832AB7449F231ACB640F1AA (void);
extern void VFXPropertyBinder_Reload_mC20E8C114F88956652BCFE1D1AB7701BA946CA4A (void);
extern void VFXPropertyBinder_Reset_mEE67C9046525E83EB402D542891D80149008D9D2 (void);
extern void VFXPropertyBinder_LateUpdate_mBF71A331AE3C80F8CFE288BBCF1CBF7B20A505BC (void);
extern void VFXPropertyBinder_ClearPropertyBinders_m0D6B59D73389A5A83E46E87D05B24ADE479CF13F (void);
extern void VFXPropertyBinder_ClearParameterBinders_mFD3FCF79B5B335810B8A754D503702EF15A06F6D (void);
extern void VFXPropertyBinder_RemovePropertyBinder_mBFC783DFBF28ED3BB9ACC607CECCF9EF4661E863 (void);
extern void VFXPropertyBinder_RemoveParameterBinder_mCD45A1A2731CCE0B9B4946F22C21794719E534D6 (void);
extern void VFXPropertyBinder__ctor_mAC0E583E26FEA725E8C676DD293C791173AA8A90 (void);
extern void VFXPropertyBindingAttribute__ctor_m3619150CC621FD0D074BD7482574633A3F64ACB9 (void);
static Il2CppMethodPointer s_methodPointers[506] = 
{
	VisualEffectActivationBehaviour__ctor_m65E4CD17A18691729EA7828682BCD258D637F18E,
	VisualEffectActivationClip_get_clipCaps_m8F28DEE46549E79C5304F6DBA33D669BF67ADC11,
	VisualEffectActivationClip_CreatePlayable_m4814F182C51EEF28575E8778C1878869826412FF,
	VisualEffectActivationClip__ctor_mBA01A34328A0CF217AE901CDFD1DBCC894FA8B62,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m1A0F97D1C26E60CB50CCFEC8C0EB54142437073F,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m660B9B680F16025E4FBA89662402F4D7A4161AF4,
	IncrementStripIndexOnStart_OnPlay_mC0FFFC6BF19D2183B2CF20A6F5BD84ECB517ABB1,
	IncrementStripIndexOnStart_OnStop_m501883B139058CDA61B84BE9829D7C76155B020A,
	IncrementStripIndexOnStart_OnUpdate_m92C00CAEC2C39033FCEB36D3E953421D8197B33F,
	IncrementStripIndexOnStart__ctor_mD5AC51266B8B4C455B08323C80BF1326C5F2AC24,
	IncrementStripIndexOnStart__cctor_m7BAE26202B9622EC6696C7F712338036672F7344,
	InputProperties__ctor_mC58B1AA00C2A3ECC0CE232C7BEF2D917498CA527,
	LoopAndDelay_OnPlay_m5C7E9B52858DF05EB83E325CE74DAE580784C8DE,
	LoopAndDelay_OnUpdate_mF618CDEDADBF1A4AE6F8D72308B7A4878811C9AF,
	LoopAndDelay_OnStop_mB4D8429F935FAC6E6FF3CC3AD08CB04EADBDDDAE,
	LoopAndDelay__ctor_mFA1ADB24F0572B3FBBF66F7664E762DCF834E995,
	LoopAndDelay__cctor_mA0AB3A7F64A528FF436812537438B3C04817EB18,
	InputProperties__ctor_m338F6549D0C496DBE3024AF9C9AE77A109563BFA,
	SetSpawnTime_OnPlay_m494EDFC1FE73146B6C487FDD4A07D42E91159660,
	SetSpawnTime_OnUpdate_m9B8FDFEC517518E5BC71C6B728DD731B3BC92E26,
	SetSpawnTime_OnStop_mFA93FBC917BC1DB9C1ADCB0396D51A991A939868,
	SetSpawnTime__ctor_m0A6C3292B3E92EE9AB41174552778E52EC365B02,
	SetSpawnTime__cctor_m7645785D0D869207055935A7FDDD29A9330E053D,
	SpawnOverDistance_OnPlay_m8CD1A43AE9201B150ECA3F11B8D3626E1D9C3202,
	SpawnOverDistance_OnUpdate_mAD7E92251CD686D43412F078BC3FC2F24833D1CE,
	SpawnOverDistance_OnStop_m433A53C7DE34C595F610840DBE4FDF6D504AC620,
	SpawnOverDistance__ctor_m609C74C15136C08BC9DAFF29F063D8E3D6FAFD87,
	SpawnOverDistance__cctor_m9DB320C3EA82984EA751B5DE8F4F502DB217D554,
	InputProperties__ctor_m66051F750DCE103C62E11E0FED58833AEB98FD5F,
	VFXTypeAttribute__ctor_mBEDA075ED10124DE0B91759EE19986A24B779F30,
	VFXTypeAttribute_get_usages_m426B7B073E3E45D3D9A51CA64167689D1DA15EA3,
	VFXTypeAttribute_set_usages_m5FA9D043AC5C3B09A588C36382E204A49025AF54,
	VFXTypeAttribute_get_name_m95DF2E6B041C7F60B2CD1CEEEA0B1DF72B1E7D7A,
	VFXTypeAttribute_set_name_mA22E40168BCDBEC01144E4D6A3D114E821FC669E,
	VisualEffectControlClip_get_clipCaps_mDA4C416CD5848FAAE07975B087E8EABB21267341,
	VisualEffectControlClip_get_clipStart_m93DAEA00AA3F13ECF2B6804918E7D56F21EDD615,
	VisualEffectControlClip_set_clipStart_m6492AB7FB6F37A24D0091A1AC6500D5CFA4CE3ED,
	VisualEffectControlClip_get_clipEnd_mC4E239AAC1D5EB370AEF75AD0A74B5E24EF65C25,
	VisualEffectControlClip_set_clipEnd_m104DEDD83157E9555030BACA648ED87F18E49CCA,
	VisualEffectControlClip_CreatePlayable_m24517EF9D0AC0F71ED375BB491C44C6735CF2E8C,
	VisualEffectControlClip__ctor_m6D2F2738A0576C9895CDB9A0323585062BAE047F,
	ClipEvent__cctor_m81D0577A55BB6F80CC63C992BF386096AF979A8E,
	NULL,
	EventAttribute__ctor_m5117CE33C14C44625311D78A276BAD7C0E6F70B7,
	NULL,
	NULL,
	EventAttributeFloat__ctor_m79EDE6A6C5F1ACCD07D77131D123D40297DF373C,
	U3CU3Ec__cctor_m468B6F148EAE1F0F735CB086D52C26B670AA08BF,
	U3CU3Ec__ctor_m089625E761BD440013E5B879BC173F8697D8B02E,
	U3CU3Ec_U3C_ctorU3Eb__0_0_m91F140B0A595A23333531CC1687A8EEA1B2D9725,
	U3CU3Ec_U3C_ctorU3Eb__0_1_m91CBCB1D42D1A9D83CE287077E5E9946CED49AE6,
	EventAttributeVector2__ctor_mE405FDB1A0AE5DE93A38EF678A0FDDD5C6DB6682,
	U3CU3Ec__cctor_m68D5AB824B40D21B675464791FCC893B386C09DB,
	U3CU3Ec__ctor_m4B240E328F7124697AA262246269825681A64E02,
	U3CU3Ec_U3C_ctorU3Eb__0_0_m0FD894A71C567CF4C87FF7E8D29E4C9EE9ACD4D9,
	U3CU3Ec_U3C_ctorU3Eb__0_1_m4B680295768F7545572ECD3A899A0944FAA2822C,
	EventAttributeVector3__ctor_m4D79D78CCD84E4B1E823AE5F25180219685E5823,
	U3CU3Ec__cctor_mCD045471CC9DB34636302AED0E75FAF6EF1107B3,
	U3CU3Ec__ctor_m7C568651AB7F6B256E09202A608036C4BF20D6B6,
	U3CU3Ec_U3C_ctorU3Eb__0_0_m03C2049FB8E660B0C84476A24553F6BF02AF4013,
	U3CU3Ec_U3C_ctorU3Eb__0_1_m9FAEBFF57B9683E72B29B64B918AB64FD109ED49,
	EventAttributeColor__ctor_m1CE6C6C33E4FBFB825DCA668999DD18562DAD2FB,
	EventAttributeVector4__ctor_m8D616CBBBDDF790881E64F1E66400F827CC5ADDA,
	U3CU3Ec__cctor_m804A41F35134CA49F6532A89FA0293556C5B2A1B,
	U3CU3Ec__ctor_m4EAB14E3DEF3EEC0E11D133A39CB41B88F400DC6,
	U3CU3Ec_U3C_ctorU3Eb__0_0_m4A5843374FEB5A925912A6D02EF055498D88E1F5,
	U3CU3Ec_U3C_ctorU3Eb__0_1_m0E08413C13AE1CD26DB708C607AB37012F7DAA0C,
	EventAttributeInt__ctor_mE53F8D3731E8524DAA6D5EB8CFBF6C1D40D2B5E3,
	U3CU3Ec__cctor_mB926AC936F78A6C0F25F34913DDB771513021231,
	U3CU3Ec__ctor_mABC1862D1263E14227904CF9D33BBC2A7B084345,
	U3CU3Ec_U3C_ctorU3Eb__0_0_m2A9EC3BAB45EC9EB2D4A0F9D5EA697503B8A85E0,
	U3CU3Ec_U3C_ctorU3Eb__0_1_m2808603C9031DC7BDAE707F3404BA2EAF2229D97,
	EventAttributeUInt__ctor_m3B8E10C5AD2AE1A16E53D057224FE48ED5625C0D,
	U3CU3Ec__cctor_m0CBEAB7F81298EBFFE59B752C13D364578C75B05,
	U3CU3Ec__ctor_m3BBA6C33AF3CF2427FC1F6E32DDB916302357D0C,
	U3CU3Ec_U3C_ctorU3Eb__0_0_mA2B580F8E48AD406D1C8147E60DB8D12379BFBA2,
	U3CU3Ec_U3C_ctorU3Eb__0_1_mAFB884B374D2DF3CF97ED7B159ED5DB859B2FA8B,
	EventAttributeBool__ctor_m8F97B13EC89497D630A3A753C6EE9FD05CC4DE9E,
	U3CU3Ec__cctor_m28737E59045A9CE9B73971B84DB3420397AA0B0A,
	U3CU3Ec__ctor_mAF6B7CF5EA37A9E0CCDB40E26AE832FCCF333CF4,
	U3CU3Ec_U3C_ctorU3Eb__0_0_m00A50E06725F9E7E695B1E0CE5005514F21044C7,
	U3CU3Ec_U3C_ctorU3Eb__0_1_m9D884E09719B1957A7A248573E9AED449AAB0309,
	VFXTimeSpaceHelper_GetEventNormalizedSpace_mE5EF0B4148168D7302DAB402A278104783BDC68F,
	VFXTimeSpaceHelper_CollectClipEvents_mE265C738EBD5A0B9AC32C77FB67EA9C9C36CC6C1,
	VFXTimeSpaceHelper_GetEventNormalizedSpace_m5B3EC5FE8E496F70D98F9687F11A2B341B571EC7,
	VFXTimeSpaceHelper_GetEventNormalizedSpace_m2B1165647C19A2A827749F029C51B53399B0576B,
	VFXTimeSpaceHelper_GetTimeInSpace_m18A26B4BC5C83F5F1543ABA43309B778C3D3AB8C,
	U3CCollectClipEventsU3Ed__1__ctor_m2CD3C3FAF9770FF837068A76D4A6844CE20F86DC,
	U3CCollectClipEventsU3Ed__1_System_IDisposable_Dispose_mE421A5DEAFDC8A8C7865542A9E7CB2156414D97F,
	U3CCollectClipEventsU3Ed__1_MoveNext_m4E36F5D97B3C4E0FA29EACF44C5073FF0C296AD5,
	U3CCollectClipEventsU3Ed__1_U3CU3Em__Finally1_m43C29A88D48A86DA5ECFDF09D4359ABF218CBFDB,
	U3CCollectClipEventsU3Ed__1_System_Collections_Generic_IEnumeratorU3CUnityEngine_VFX_VisualEffectPlayableSerializedEventU3E_get_Current_mD802A2E1D86FF0A2949F219CB66F55E78A9508D4,
	U3CCollectClipEventsU3Ed__1_System_Collections_IEnumerator_Reset_m1386508BFD5C1C003F1A5C9B7B739E4960B7662B,
	U3CCollectClipEventsU3Ed__1_System_Collections_IEnumerator_get_Current_mD57E3DB12874EF4CB6BF4D50992615572EA9A90B,
	U3CCollectClipEventsU3Ed__1_System_Collections_Generic_IEnumerableU3CUnityEngine_VFX_VisualEffectPlayableSerializedEventU3E_GetEnumerator_m1CD6AA3483A3803BDFC48F1CAB1D6F4C9657C4B1,
	U3CCollectClipEventsU3Ed__1_System_Collections_IEnumerable_GetEnumerator_m1DB190A75B66369E8ED4D4B949B819B1131825C7,
	U3CGetEventNormalizedSpaceU3Ed__3__ctor_mBDEBCD19B0D5DB4083AA38A9B52300153A549679,
	U3CGetEventNormalizedSpaceU3Ed__3_System_IDisposable_Dispose_mF37FCA0FABB1DD6531BC8DF0ECD4BECBD8AFF7C8,
	U3CGetEventNormalizedSpaceU3Ed__3_MoveNext_m43A52BFEA923E4681812E40363C122F2FED23184,
	U3CGetEventNormalizedSpaceU3Ed__3_U3CU3Em__Finally1_m338E4D46114C61E5F073E6D900F7C3245584FDD0,
	U3CGetEventNormalizedSpaceU3Ed__3_System_Collections_Generic_IEnumeratorU3CUnityEngine_VFX_VisualEffectPlayableSerializedEventU3E_get_Current_m251309432105DAB00635EE065228C133836A96D1,
	U3CGetEventNormalizedSpaceU3Ed__3_System_Collections_IEnumerator_Reset_m60928E0623B0199133E340DD8CBF3727D8117F52,
	U3CGetEventNormalizedSpaceU3Ed__3_System_Collections_IEnumerator_get_Current_m51DFA7B77CA706E9FD5D7369901C841485FF44C1,
	U3CGetEventNormalizedSpaceU3Ed__3_System_Collections_Generic_IEnumerableU3CUnityEngine_VFX_VisualEffectPlayableSerializedEventU3E_GetEnumerator_m52F0B166CA12BDC43A8480077468F3F3B364DD2A,
	U3CGetEventNormalizedSpaceU3Ed__3_System_Collections_IEnumerable_GetEnumerator_m723616A0EE5BEC85DF12A9E2C72C30FF56C39667,
	VisualEffectPlayableSerializedEventNoColor_op_Implicit_m1DD4365ECC9960280DCCF5340F877C73B94B99E3,
	VisualEffectControlPlayableBehaviour_get_clipStart_m689F60FE2D816D84F198C58E1C08966B6787B7B5,
	VisualEffectControlPlayableBehaviour_set_clipStart_m3DDB3D020EC92A72E210977E798C89A4D1891891,
	VisualEffectControlPlayableBehaviour_get_clipEnd_m859839E532CB36444C1D195900FEBE750109A4ED,
	VisualEffectControlPlayableBehaviour_set_clipEnd_m729BCB207E6BA29584A733A96C28FA2713B71A7F,
	VisualEffectControlPlayableBehaviour_get_scrubbing_m8D4FA50C990002FBBF232405E60B6AAD169B4627,
	VisualEffectControlPlayableBehaviour_set_scrubbing_m9AA0B61DDA4505B005D14B84B16A8C0EA93DDA34,
	VisualEffectControlPlayableBehaviour_get_reinitEnter_mB375023DEA53AE4018E20D4E5DBBF96F312893BF,
	VisualEffectControlPlayableBehaviour_set_reinitEnter_m9BA52EC21ACA4EC37703A36D10D5DB71012BBE31,
	VisualEffectControlPlayableBehaviour_get_reinitExit_m11F5C5677AC6886BC56AE50E34E02C4C3ACC2338,
	VisualEffectControlPlayableBehaviour_set_reinitExit_m4B9F2701246584D549E1558D4AA53E352324082D,
	VisualEffectControlPlayableBehaviour_get_startSeed_m309C2E082A7918619C89639C8912864F0078C2D6,
	VisualEffectControlPlayableBehaviour_set_startSeed_m32D38B7DABC4E2506C6D658588821533E7D544B6,
	VisualEffectControlPlayableBehaviour_get_events_m216202F0555371ED4ABE49900451524905B94E8E,
	VisualEffectControlPlayableBehaviour_set_events_mF84DC8C0EF99A946F55B8376CCD90C6C026B7875,
	VisualEffectControlPlayableBehaviour_get_clipEventsCount_m63BC2B9B266086181CCA7B2CCEB1F07468EEB7CB,
	VisualEffectControlPlayableBehaviour_set_clipEventsCount_m7B86254EA658D79246D9686ED4D9E16ECB50D7E1,
	VisualEffectControlPlayableBehaviour_get_prewarmStepCount_m149D92B8048A4E4C2D8A39780CBB1B6A582FD2AC,
	VisualEffectControlPlayableBehaviour_set_prewarmStepCount_mC75C0EEE33184750B55CB87F61B6FE8BEFCCE915,
	VisualEffectControlPlayableBehaviour_get_prewarmDeltaTime_mAEA02EF7E7DC4661867E73CD58A6F6AD0AEC883B,
	VisualEffectControlPlayableBehaviour_set_prewarmDeltaTime_mA4F76757F58DEE820705774C931F769192017847,
	VisualEffectControlPlayableBehaviour_get_prewarmEvent_m20C789CA605D15AAFAAF08BB18DBA93B8BDE7203,
	VisualEffectControlPlayableBehaviour_set_prewarmEvent_m454D61B3B33E1406FFABD5E529B346CA35104B3D,
	VisualEffectControlPlayableBehaviour__ctor_m606A9669F7622C624F0E5AB0B3453AE71D6146E7,
	VisualEffectControlTrack_IsUpToDate_m84152EA8B902320074FD268B37CFEC1E5E8B7D0B,
	VisualEffectControlTrack_OnBeforeTrackSerialize_m218B10FF8EAA709F50705E28CD1E2F002D778BC2,
	VisualEffectControlTrack_CreateTrackMixer_mAD3AE69BC2DA79C47DB9152AF2458203382241D4,
	VisualEffectControlTrack_GatherProperties_m674F98807F63DFC615DF44739CE17C163CE6A3C7,
	VisualEffectControlTrack__ctor_mE6CE6E9843BD35463D3873A4A082AFD44593B5F7,
	U3CU3Ec__cctor_m134E48ED7703FD6FFD37EE2A635BBCDD09B603CA,
	U3CU3Ec__ctor_m620D5C0181A6F1C741E9611642A6EDD74E76EDCC,
	U3CU3Ec_U3COnBeforeTrackSerializeU3Eb__5_0_mC05756E2A3520F8A36CF89CCEB9FA9F10CABA367,
	VisualEffectControlTrackController_OnEnterChunk_m1EF3EFAB913389BCEFBB34F618C643D458774CBF,
	VisualEffectControlTrackController_OnLeaveChunk_m035AB7792D5A01AF1B5378A85A383A93A5B028F3,
	VisualEffectControlTrackController_IsTimeInChunk_m7841D0327B1CDC57A2D8350C7841BE6229125F6F,
	VisualEffectControlTrackController_Update_m0448870C6E19DA191DCA159812902F0F3440605F,
	VisualEffectControlTrackController_ProcessNoScrubbingEvents_m9849F1E801C0EDC94F87AEAADCE608CFA5E103EF,
	VisualEffectControlTrackController_ProcessEvent_m4F164A5B6A7EEE48A03CEFA157B1B077D9DC6209,
	VisualEffectControlTrackController_GetEventsIndex_mF6CC8543A83F9EC003535FC7E8E3E861D10742E1,
	VisualEffectControlTrackController_ComputeAttribute_mA080BEA66245A3CE3FEF1A728F9A504623259A2A,
	VisualEffectControlTrackController_ComputeRuntimeEvent_m5363BC4838D69C045CA5D428FCCE5409AFD86070,
	VisualEffectControlTrackController_RestoreVFXState_m52F3DB809AB721A54D501B4ECFD8378884336CE8,
	VisualEffectControlTrackController_Init_m606209AB01A38BE71FEB14698E815C03AC06964D,
	VisualEffectControlTrackController_Release_mBBB2834DDED2610732776ED3D41BC68C90899438,
	VisualEffectControlTrackController__ctor_m2252CB4F240A02FC5E4389078F00C709D26335AE,
	VisualEffectControlTrackController__cctor_mF5D1E66700B00DE5520B7636989EA98286D8CCAE,
	VisualEffectControlPlayableBehaviourComparer_Compare_m836B0BAEA95F48013884D0AD7649B7B9B3583BE3,
	VisualEffectControlPlayableBehaviourComparer__ctor_m91628EB0522619DB513B57189B9987805B4A0C06,
	U3CU3Ec__cctor_m8784A4CD4C1575C8D8DFC54CAF57A9445EDA00BC,
	U3CU3Ec__ctor_m3AB440EF9B6381AC8CE4729A22524CFBB607547C,
	U3CU3Ec_U3CInitU3Eb__24_1_m35C4DB62613D6668B45503367830E444CE4C8210,
	U3CU3Ec_U3CInitU3Eb__24_0_m979D61DB422D4D1D07C2498708FE6D95413F90E5,
	U3CComputeRuntimeEventU3Ed__21__ctor_mE041EBA4A2D0442A63A980A1EE2C62B1AB81F5B1,
	U3CComputeRuntimeEventU3Ed__21_System_IDisposable_Dispose_m7E9B8070D3F729AB5AAECFD93175AB4FF6B0C016,
	U3CComputeRuntimeEventU3Ed__21_MoveNext_m8915BD704DFC3DFFF80548ABF495DDC60EE592D4,
	U3CComputeRuntimeEventU3Ed__21_U3CU3Em__Finally1_m5C9213B974F92D2C874DB608553F66AA5EEDDE37,
	U3CComputeRuntimeEventU3Ed__21_System_Collections_Generic_IEnumeratorU3CUnityEngine_VFX_VisualEffectControlTrackController_EventU3E_get_Current_m83DD7CB8991557ADCDC42AF7876DB63B9A70636A,
	U3CComputeRuntimeEventU3Ed__21_System_Collections_IEnumerator_Reset_mC11E1922A6FA2BD006D22D931A5F2523CF14A1FB,
	U3CComputeRuntimeEventU3Ed__21_System_Collections_IEnumerator_get_Current_mCCD5A35C7D39AA7F254133DD5BBE988081CD19FB,
	U3CComputeRuntimeEventU3Ed__21_System_Collections_Generic_IEnumerableU3CUnityEngine_VFX_VisualEffectControlTrackController_EventU3E_GetEnumerator_m2AF3BE79E0F9537E5A9356B2C01BE9A8C81F6A07,
	U3CComputeRuntimeEventU3Ed__21_System_Collections_IEnumerable_GetEnumerator_mF6BF251A31ADBC1C3F8CFFE59982F8E0C85F7CC9,
	VisualEffectControlTrackMixerBehaviour_Init_mB018DC52B5255F530C1AE00753EEBEA63CED5470,
	VisualEffectControlTrackMixerBehaviour_ApplyFrame_m39EB85F9B2E3CFE1B9A6699A1477D967B10F500F,
	VisualEffectControlTrackMixerBehaviour_BindVFX_mC52448E6F722DD2BE8D0FCFBD42B47D975F35845,
	VisualEffectControlTrackMixerBehaviour_UnbindVFX_m0CDA5E7EACEF4A995E7C6ED2A3F3C91BF29B2145,
	VisualEffectControlTrackMixerBehaviour_PrepareFrame_m2C91428E2CF68E9D5BCD686950D2AF6F03890B0D,
	VisualEffectControlTrackMixerBehaviour_OnBehaviourPause_m8C8C1BFE79631C37F5C6DFC11024B79917D1D29D,
	VisualEffectControlTrackMixerBehaviour_InvalidateScrubbingHelper_mF9A450E411E0A07ED25092C33C9CA00D2B776924,
	VisualEffectControlTrackMixerBehaviour_OnPlayableCreate_m3D5F80B1CB8E80E9B2493E315DB1971E04E78FF9,
	VisualEffectControlTrackMixerBehaviour_OnPlayableDestroy_mA2A628856853A146CC640BF078C94DF6C2CE532F,
	VisualEffectControlTrackMixerBehaviour__ctor_m559E28AC3A23DC45F6972B33377A01D407BF5CDE,
	VFXRuntimeResources_get_sdfRayMapCS_m43E786271B4E4CF84FC9ECEA228F7BF930E682E6,
	VFXRuntimeResources_set_sdfRayMapCS_mCBD5CA21B07EBD35FB7A6ACFAD090CCFE5F5952F,
	VFXRuntimeResources_get_sdfNormalsCS_m81CB5B4AB07311A120C846EF7EEF4AF40B5F1646,
	VFXRuntimeResources_set_sdfNormalsCS_m60625AD04277C6E36C13D19763CD75588BDEBF70,
	VFXRuntimeResources_get_sdfRayMapShader_mD3063E53C627E4648861A7A290BE4896FAA053EE,
	VFXRuntimeResources_set_sdfRayMapShader_mF54C5E9F9539D34A35C866DB13CA8B183A6446AE,
	VFXRuntimeResources_get_runtimeResources_mF3D34F7B1B8525C819200E600B1916E7F8AB8A31,
	VFXRuntimeResources__ctor_m12274CA3BDCC9014C4AE5B0DF58CD716C800BE9D,
	MeshToSDFBaker_get_SdfTexture_mECBAA9DA0DD9590FDD99C86263194A9063FD80C2,
	MeshToSDFBaker_InitMeshFromList_mA070F5AC1A7F3AEBD917E7EF8602D0A7FEFC871F,
	MeshToSDFBaker_InitCommandBuffer_m31AEFDFD74DFAFF10B13B010042F7F3D053858DC,
	MeshToSDFBaker_GetTotalVoxelCount_m358CBAF6FBB88594584F12917985C2D4E6C51A52,
	MeshToSDFBaker_InitSizeBox_m5FB97C78F43AF057E1D28CD071BA1FCF5FC4BCEF,
	MeshToSDFBaker_GetGridSize_m2E238B4C95C3B9115C76E340FB26B6C462E3F4B3,
	MeshToSDFBaker_GetActualBoxSize_m2D78F4D810A2FAC2886ED6A7079E0A00C638796D,
	MeshToSDFBaker__ctor_m4382050A450DB8A7FB584D855326DA2C63D72EBE,
	MeshToSDFBaker__ctor_mB3EEFBBA0BD7A8A449BF256C9B33EC80A3E638CC,
	MeshToSDFBaker_Finalize_m7E3DF440D57DBFE6F146FEB83A0B6B52E57F5A52,
	MeshToSDFBaker_Reinit_m33B5E0BDD51F81D1F7AA9F84CC4060D0E44E463F,
	MeshToSDFBaker_Reinit_mCE929D673905E852CE0CE993EBB223F0ED0C74E5,
	MeshToSDFBaker_SetParameters_m38C6EBE0FCD3A9985399A834E2D430680B305C38,
	MeshToSDFBaker_LoadRuntimeResources_mC0C91894CC56731379D3253E9CF47E51A024D6C5,
	MeshToSDFBaker_InitTextures_m2C2CCC8540291CFB603D4ACB98AC8EC7D968AF4C,
	MeshToSDFBaker_Init_mC664261A480639E3A4A65CE49B6D6595ECB0FD29,
	MeshToSDFBaker_UpdateCameras_m36ECE1DC89606DD9B337B7B279A9948C47378A3E,
	MeshToSDFBaker_ComputeOrthographicWorldToClip_mBA143A16FA0C34CFE7CDC7D416B6C105ACC9DE16,
	MeshToSDFBaker_iDivUp_mC187EDD662E2BF3F32D5AA674F0A47346C55F359,
	MeshToSDFBaker_GetThreadGroupsCount_m4DCF1F0B29A933D29411CD4E2D83327C8604B36C,
	MeshToSDFBaker_PrefixSumCount_m3C9E9E5FA7337DC001DADF50AE621923911A43A5,
	MeshToSDFBaker_SurfaceClosing_m1712DAB3C5D6359A7C7CE215FEC187404BED5E4D,
	MeshToSDFBaker_GetTextureVoxelPrincipal_m908BF61AEE91EA0438B7E447DD4CF0177BD229D6,
	MeshToSDFBaker_GetTextureVoxelBis_m7F150C086B55E188BEBCD59957874431B8B8F920,
	MeshToSDFBaker_JFA_mA31A6D93A96EE3CB887528E686D7919A4FE3D25C,
	MeshToSDFBaker_GenerateRayMap_m9014A08AB40B5DC585A4882D8CCB7C4ADDA9D48D,
	MeshToSDFBaker_GetRayMapPrincipal_m2DE01FBB3F68FBAE1451FEFBB367022085096A76,
	MeshToSDFBaker_GetRayMapBis_m31B5184905348AD94BEC5FB7114CE380F2EF8DA1,
	MeshToSDFBaker_GetSignMapPrincipal_mCF2B4EF2B8B685CA57E51377C0C49E8EA2703875,
	MeshToSDFBaker_GetSignMapBis_mBABEB9223BEE531DAF10C7FBD79C9ACC85C5F69D,
	MeshToSDFBaker_SignPass_mB3D3AED7713AB3B7DDB0C77B76C3781D59A1C9B4,
	MeshToSDFBaker_BakeSDF_mF0737A425E08581920C2D1034E619D566B5FC16A,
	MeshToSDFBaker_InitMeshBuffers_mA5D107F3721F38DBB916532A3B9B6E84A24DCA09,
	MeshToSDFBaker_FirstDraw_m024B2A34A8A90EBD5ADA9FCCE20F8BF66DF161B5,
	MeshToSDFBaker_SecondDraw_m2BBE4CFB156E114AE6832D84A089978536699771,
	MeshToSDFBaker_BuildGeometry_m4344ACEE2B820B63DCDCFF508E767175B6E1C817,
	MeshToSDFBaker_InitGeometryBuffers_m3221F0474C3F6D9097FEB7E9F50CE665CC7883AE,
	MeshToSDFBaker_InitPrefixSumBuffers_mAF8F4996FFE7B0C96AD1C38BF3BC25A0B765958F,
	MeshToSDFBaker_ClearRenderTexturesAndBuffers_mFB6D2B7C2EF1CC4A9ECACB769621D461899E622D,
	MeshToSDFBaker_PerformDistanceTransformWinding_mF0A58664A163A68C095AA04BDAD6E15106E9C83D,
	MeshToSDFBaker_ReleaseBuffersAndTextures_mF9E5FFB2308C6E800DF8C6B72173766E3B61C2C5,
	MeshToSDFBaker_Dispose_m161EE2DA63BB7958CE022908C3FFEAAB92F513C6,
	MeshToSDFBaker_CreateGraphicsBufferIfNeeded_m025EF26D06E44974CC07D783035CCC8F0DAFE61F,
	MeshToSDFBaker_ReleaseGraphicsBuffer_m9892BFD6274E1492BE48240BDCE2A9D7286719F0,
	MeshToSDFBaker_CreateRenderTextureIfNeeded_m5B5D546AD320BFB4B76BEB24A62B914BB2A7A1DC,
	MeshToSDFBaker_ReleaseRenderTexture_mD362FCB0A017FF60CF58888B272D36A7833F92E5,
	MeshToSDFBaker__cctor_mF617DF3F43D12F5C260DE4EF5362E0FD2EE8B959,
	ShaderProperties__cctor_m154A681C6FE4887FA2C8681CACBC45F22CF1E5D3,
	Kernels__ctor_mAA8D1C254238AF617EC6C9D4EC508962EEFF3C56,
	VFXMouseEventBinder_SetEventAttribute_m1B3CB78F8479F2CC6B557BA3F9D11491FB2E9A5A,
	VFXMouseEventBinder_Awake_m4874D86408A47F0260FA827FD2AF8C2554916DEB,
	VFXMouseEventBinder_RaycastMainCamera_m3B068FB3A911122EE5B531B03B64BF1885A2F5DF,
	VFXMouseEventBinder_RayCastDrag_mA48A78CD33F70568B5C5B7D2CDEB2B3D474CE76A,
	VFXMouseEventBinder_RayCastAndTriggerEvent_mE5CC4749C8EC6914625220934276B2AC29D1C520,
	VFXMouseEventBinder_OnEnable_mC4E0DC107FD9FAD395002CBF5F36A58F766A398E,
	VFXMouseEventBinder_OnDisable_mE638C97759BC680F7BAF72D74F4E6258E6C0E26A,
	VFXMouseEventBinder_GetMousePosition_mBBBD1C179D116151B9680061E049323C43CFA071,
	VFXMouseEventBinder_DoOnMouseDown_mB5F4554ED7949B6D6BFCA0E5B501F87F80B52694,
	VFXMouseEventBinder_DoOnMouseUp_m3F48CD8795EB94D37B86275815D44A3284BC33BA,
	VFXMouseEventBinder_DoOnMouseDrag_m0310246449E70E1DE580B869E4B90124D00C3837,
	VFXMouseEventBinder_DoOnMouseOver_m4BE7BFA06C6B0FD42688F747ACF0BDA7D82765F5,
	VFXMouseEventBinder_DoOnMouseEnter_mEDCA5B540678906577F24882A09AD8E08E03D55E,
	VFXMouseEventBinder_DoOnMouseExit_m3460D1E2C017D2F39FF8280A830125F1692814DB,
	VFXMouseEventBinder__ctor_m269FBD4E25512D754602BA4322D36979B9B4C991,
	VFXMouseEventBinder_U3CAwakeU3Eb__12_0_m32B96CDA0C466364249D8AD0597A12B067E00513,
	VFXMouseEventBinder_U3CAwakeU3Eb__12_1_m969BEE4465D53C7F39A4510668D1A418687CA062,
	VFXRigidBodyCollisionEventBinder_SetEventAttribute_m418F73D31B12BEEA39C810335B4D1FBADAA2F499,
	VFXRigidBodyCollisionEventBinder_OnCollisionEnter_mBC06344BAB974CC3104ADFFFDDBC13805AD7CD29,
	VFXRigidBodyCollisionEventBinder__ctor_m0A4CEA80864876A7B38C26DDFB83715944486D6F,
	VFXTriggerEventBinder_SetEventAttribute_m30552173CF7D7CA17B5BB76B6088756002056DF3,
	VFXTriggerEventBinder_OnTriggerEnter_m7B530756703BE1C37DA3206D81D4D6FA2C378327,
	VFXTriggerEventBinder_OnTriggerExit_m098634E694226818276DF5430088B893B548EE1E,
	VFXTriggerEventBinder_OnTriggerStay_mF2D6604ABF9323C20333F482F8B8CCF6523683E1,
	VFXTriggerEventBinder__ctor_mB7AEE8C9686AD0E31068AD18CAE614982215500C,
	VFXVisibilityEventBinder_SetEventAttribute_mBDE795116F6A7E9BF54C8AE080D83FD614D42724,
	VFXVisibilityEventBinder_OnBecameVisible_m46F052C58FDE520ECDA612E3B5D5B3BD56401641,
	VFXVisibilityEventBinder_OnBecameInvisible_m4D980D9A9E75AEF34A9B3492FDC8D67BC0005527,
	VFXVisibilityEventBinder__ctor_m84E91F879D507B7E47B3D239D7158D9E4E019B5F,
	VFXEventBinderBase_OnEnable_mF6262A11BD09CA75E1AE89A5D362C15861A9B68D,
	VFXEventBinderBase_OnValidate_m137196E91DD0412C813823130B79D0F04DFEB0FC,
	VFXEventBinderBase_UpdateCacheEventAttribute_m3FD1CFD6B8BE81E246534D127FB175554C2D1CD2,
	NULL,
	VFXEventBinderBase_SendEventToVisualEffect_mC7CAA713AF7BD533AFD1A6663753141221493690,
	VFXEventBinderBase__ctor_m9CD7C6460EC05578FF0E20CA29B4459D00DB6E53,
	NULL,
	VFXOutputEventAbstractHandler_set_m_VisualEffect_mB7CEA6FD627A192FCC5B1BB72A3FD034FB40D209,
	VFXOutputEventAbstractHandler_get_m_VisualEffect_m5DE6F44EA40481C9A196614B97B68E4B1DEC59B3,
	VFXOutputEventAbstractHandler_OnEnable_m9DA1E28C8FD0B621DEE7E0D055B467436E2AC29E,
	VFXOutputEventAbstractHandler_OnDisable_mA95FD62310C790E2FC47EBEED06C73315F3EFCC5,
	VFXOutputEventAbstractHandler_OnOutputEventRecieved_m744F5D9DE68857E8B83ABE29DA28A63DD4746BD3,
	NULL,
	VFXOutputEventAbstractHandler__ctor_m4E2D7BE313EA0EC6CD4BE214184BDAC7899F18E9,
	ExposedProperty_op_Implicit_m9CEB3D09454745C8E450BAB7C266D15ED28B44FB,
	ExposedProperty_op_Explicit_m8F7C257466E30C5602C4F12AEA2588B7093424F3,
	ExposedProperty_op_Implicit_mA6E7C425842E5FA0B783360A77558C2D45A1970F,
	ExposedProperty_op_Addition_m2FBBFB0417DEA6D43A730115FA3B9F39A3892C93,
	ExposedProperty__ctor_m92A33E6D7AD2239105318F03D95351BCC16D739A,
	ExposedProperty__ctor_m20517E8CD365AFE0803F9F8A632BFE09B34C3E65,
	ExposedProperty_ToString_m56C2D2ACA650A07737B9D8815A3ED1DEAC6EF6A9,
	VFXAudioSpectrumBinder_get_CountProperty_m5DF677930AD099959C3A5532E54C4F874126DCD8,
	VFXAudioSpectrumBinder_set_CountProperty_m268EEE2B83A4C3AFD7B21B2D1BF46BF44DAD15E9,
	VFXAudioSpectrumBinder_get_TextureProperty_mE5D3C308903A7A372D524FA235F828D03451BE54,
	VFXAudioSpectrumBinder_set_TextureProperty_m839466D803689AC9DA97636117987E3C734D5F67,
	VFXAudioSpectrumBinder_IsValid_m30BE575435B6E5E745DD0784DC9F3C7A83C1F053,
	VFXAudioSpectrumBinder_UpdateTexture_mE59FAFFCA67F721F8C4F61F5382C4512AC1F2D3F,
	VFXAudioSpectrumBinder_UpdateBinding_mA14547ECB53ED7582BEA59BF8322D45A8F03B1F7,
	VFXAudioSpectrumBinder_ToString_mAC1E82DB90BFF1E5082D8568D243146DDAB5133F,
	VFXAudioSpectrumBinder__ctor_m6AE18A43116D28E1E3C0442125DD2EAC81D2481D,
	VFXEnabledBinder_get_Property_m9E546CBA700214100BF8950EBCE931F7636447D4,
	VFXEnabledBinder_set_Property_m3FEC0F9E00145E12EF6BEC5F4B7CB0B6B42F6930,
	VFXEnabledBinder_IsValid_m18B425AA9785071B34E9DD6B7EE9D96049609CEE,
	VFXEnabledBinder_UpdateBinding_m7317EA9F03EBCA97B04C4A09A6D4F9EA9810DE91,
	VFXEnabledBinder_ToString_m0494C5F154394FE7DD3B111500E732C12116D2BD,
	VFXEnabledBinder__ctor_mDE6F21D9110687AE183840A8518F2E98F5C4B658,
	VFXHierarchyAttributeMapBinder_OnEnable_m38918F3AE086A49B9128184E3E96E8CE96D974FC,
	VFXHierarchyAttributeMapBinder_OnValidate_mA060E7B9E068BC61735610221CB6E0840AA557F4,
	VFXHierarchyAttributeMapBinder_UpdateHierarchy_m47224F693D4241AA2756D12E51501FF19879F326,
	VFXHierarchyAttributeMapBinder_ChildrenOf_m7CE1D7B085ECC08E6FDD5ECD616B8830475E0292,
	VFXHierarchyAttributeMapBinder_UpdateData_mACEC1F8DE36468C5AEB4991740DB49D038C92DF5,
	VFXHierarchyAttributeMapBinder_IsValid_m6D606850EC453BA8D59586905C7D24AB936AFA46,
	VFXHierarchyAttributeMapBinder_UpdateBinding_m6F39ADB3F03839FCA851407C321E801940D108F5,
	VFXHierarchyAttributeMapBinder_ToString_m2F6AABC4EF6170107A24A0811C0EEDE7BF3EB18E,
	VFXHierarchyAttributeMapBinder__ctor_mE25F5BE296E8CBB0351ED58614EE060EFBA8FE24,
	VFXInputAxisBinder_get_AxisProperty_m41BA17CCFBC250949E08A54FE410D77B80499C44,
	VFXInputAxisBinder_set_AxisProperty_m49DA6F0B7AB5C8CD1142DB51012E77DABBAE826A,
	VFXInputAxisBinder_IsValid_m8D2B723CE3DB06D4C828C99B4605DC6A3A47D77D,
	VFXInputAxisBinder_UpdateBinding_m6548BAD730D4F2702993C26567E17F5F8B09BA67,
	VFXInputAxisBinder_ToString_m8E72E56666E8793464814B58E66074A5FD982607,
	VFXInputAxisBinder__ctor_m3A8598E21956D3A4513C0D49ACF0C0DAD1634916,
	VFXInputButtonBinder_get_ButtonProperty_mA522DC8AF508A7BBCABC5D2429B034CD42092294,
	VFXInputButtonBinder_set_ButtonProperty_m10BC47EB9D8FE15F09DA10B59ABF0638F3803C7D,
	VFXInputButtonBinder_get_ButtonSmoothProperty_mE317FFB3A849B1C1FB39F25107CA9D7750D94B2A,
	VFXInputButtonBinder_set_ButtonSmoothProperty_mF65F9ED724538801BA94C8F54044740DC385EB4D,
	VFXInputButtonBinder_IsValid_m9CA18F74ED3A211B72C827BD7778542B1C951754,
	VFXInputButtonBinder_Start_m6118535A6B5A2941B70D64172D08DC341CA33E59,
	VFXInputButtonBinder_UpdateBinding_mBC295D5045F481605C5EC7C0EC5448D76F8D1356,
	VFXInputButtonBinder_ToString_m3369304609737827924CE154EE18EB7D29E59C06,
	VFXInputButtonBinder__ctor_m1964F20A0E02493DAE4425BE15CC94D1DDB013DB,
	VFXInputKeyBinder_get_KeyProperty_mED5F959B3D088D9FCB7994D328328D30A234AD9A,
	VFXInputKeyBinder_set_KeyProperty_m74531680A817E42585D994459F13E7C05627C252,
	VFXInputKeyBinder_get_KeySmoothProperty_m76F68335C1429F2C0E47D320BBFE0CDDEFA4D233,
	VFXInputKeyBinder_set_KeySmoothProperty_mB75547AE164AF5C49EF91E1657E0E4FD81FF7560,
	VFXInputKeyBinder_IsValid_mFCFF34FAFC061CF5BFCBA17F1F06A4B3AD3E1ED2,
	VFXInputKeyBinder_Start_m4104697A270AD61FD19A0BB650908EC03CAA4BA1,
	VFXInputKeyBinder_UpdateBinding_m0A405ECC914FAEC6DBA071C51DBFB7245FF5E872,
	VFXInputKeyBinder_ToString_mAB3ED436EBA2E2FBEED6AC4C2B4D833B30ABABF0,
	VFXInputKeyBinder__ctor_m0ACB3319F4FF129AC6DBD52BFA33B84E1ABCDFF4,
	VFXInputMouseBinder_get_MouseLeftClickProperty_mC69F12902664403234F3F6833857B145AA280E01,
	VFXInputMouseBinder_set_MouseLeftClickProperty_mD7F27520CF9D3374FD010F64BC7A067BF587487A,
	VFXInputMouseBinder_get_MouseRightClickProperty_m607B8729BD1AD905996BBD1D81BA6364CB50D950,
	VFXInputMouseBinder_set_MouseRightClickProperty_mA594A77F11DF628FD69423C32AC1C71EB1761C00,
	VFXInputMouseBinder_get_PositionProperty_m4510B50390BF4D86C0F754CF433B5496F11E9FB0,
	VFXInputMouseBinder_set_PositionProperty_m65C3FEFE063F15943C15A923F19B1E727C4AB836,
	VFXInputMouseBinder_get_VelocityProperty_m2E5913FF1F6A8D9780191BC8BC92F10DE7F0CC25,
	VFXInputMouseBinder_set_VelocityProperty_mEFD15CA2774BD57920668603BA3C3C86B9E21B35,
	VFXInputMouseBinder_IsValid_mBB3B7F2ACE4F7DCECF48DBF403D534722B3474F9,
	VFXInputMouseBinder_UpdateBinding_m69E9420D08180E52EBB3605BE0580C37C8D545F0,
	VFXInputMouseBinder_IsRightClickPressed_m0C582DEB1E7B7A5701F38E309A7B4EBA05FCF6EC,
	VFXInputMouseBinder_IsLeftClickPressed_m17F8F9BA1B555FC41046F2A311DDA6CEF6C7DADE,
	VFXInputMouseBinder_GetMousePosition_m5630931394578D6D2BA9E817C69FADF7D53B229C,
	VFXInputMouseBinder_ToString_mCD428D57FA2CBA8B62143A421A39E3AF79DB71B1,
	VFXInputMouseBinder__ctor_m827C8ED1DDFB31459335FA71CA19DC484DBC7A62,
	VFXInputTouchBinder_get_TouchEnabledProperty_m105A858EE0EECFD8737C36D72BCF579C6A2BAC8D,
	VFXInputTouchBinder_set_TouchEnabledProperty_m78A63DFA1A097040AADB6B4084ABE45A7C7F3053,
	VFXInputTouchBinder_get_Parameter_mE98BC45B23C7C6FA1FA268D506EB4F7BF9AA06F5,
	VFXInputTouchBinder_set_Parameter_m1F3487772C022265BDB7A931F49C9DD9318D4EBE,
	VFXInputTouchBinder_get_VelocityParameter_m00EC882744202BC3C09D5FE1E75416F3F455FCED,
	VFXInputTouchBinder_set_VelocityParameter_m1BBAE312F27FFE87B9F9396F4A3F8DF06722430A,
	VFXInputTouchBinder_IsValid_m6B86A4123CA4D2810F22DCDB8A0BAF9BE1291DA5,
	VFXInputTouchBinder_UpdateBinding_mF21A05757A5200052BF16E2B1985D2A1A4EC84F8,
	VFXInputTouchBinder_GetTouchCount_m6F85B121C3D99D4FC9B515CBC3C8CE84EC2A30B2,
	VFXInputTouchBinder_GetTouchPosition_mA0555635D0689F84EF0929162BFE644E996C36C5,
	VFXInputTouchBinder_ToString_m5AF94A7C372FADD51886F7462851C236B09288FF,
	VFXInputTouchBinder__ctor_mFE6C77657C9A6F1F34DEA017A22A8653C02437E6,
	U3CU3Ec__cctor_mCF1AC0A123D5214071C87B14B9BEFA9F3E165D06,
	U3CU3Ec__ctor_m9AB57595F56A5AD38DD8DDFF11254F497A21AF07,
	U3CU3Ec_U3CGetTouchCountU3Eb__20_0_m1C7DAEF04F30D57567824E2E97A7DE426E36462F,
	VFXLightBinder_get_ColorProperty_m985EDD04353195BBD32B94DF66317A170C93CAFD,
	VFXLightBinder_set_ColorProperty_m202E67AB1B8A2265BBB8D4C1DB75646977469158,
	VFXLightBinder_get_BrightnessProperty_mF1E9B4E4B53714CAF652F56B563BFD1E2C794AD1,
	VFXLightBinder_set_BrightnessProperty_mBBC16547EA68167DA5D72CBE1D92A85D3F19C0D5,
	VFXLightBinder_get_RadiusProperty_m0373D3B6410CA134EB6A4C3F5B9B7538583D5A20,
	VFXLightBinder_set_RadiusProperty_m399CFB05F9BFE7B45B9B07E94F837B2DF77500E6,
	VFXLightBinder_IsValid_m18DC2B463BD5382C3E278CDC87F54BAD0DB2D602,
	VFXLightBinder_UpdateBinding_m745CD70F467410D2FEEA727CF58580F4692B304F,
	VFXLightBinder_ToString_mD8BC48524A247DF67DA7670B8D3E6857A70DEACB,
	VFXLightBinder__ctor_m526A3FA1F645F0A8EFC83A2B07184CD3D57661E2,
	VFXMultiplePositionBinder_OnEnable_m81DA3CE28B764E868656402A55399AA93A84E289,
	VFXMultiplePositionBinder_IsValid_m6B2D25A10A35F604BB649B554D00FEAB93ECED44,
	VFXMultiplePositionBinder_UpdateBinding_m4D1544BAB488ECC4ADDA4B5914AC1793C683147D,
	VFXMultiplePositionBinder_UpdateTexture_m08801CB93727EA213155BAC676BE9437E489B5E3,
	VFXMultiplePositionBinder_ToString_mEDB13894DBEAF2F31994A72310A23231A6A7BDCA,
	VFXMultiplePositionBinder__ctor_mE710FDA9E568A663416CB22750A80995D7169FEF,
	VFXPlaneBinder_get_Property_m54C30C4D74B21178FCB92E920F2B0B004E611CB7,
	VFXPlaneBinder_set_Property_m91934DD97598848E257D6841DF8E6583AA71E57C,
	VFXPlaneBinder_OnEnable_m7367BBAC66F8ACCA856E6FE63D3F320C79C0C2BC,
	VFXPlaneBinder_OnValidate_m7950C4FB69814A74319778D4DECBB657C686E2FD,
	VFXPlaneBinder_UpdateSubProperties_m9AEB13B4B517C1DBFD1DDA34390F682959FD909A,
	VFXPlaneBinder_IsValid_m4F681A04FB92422F01268AE5E4D943A5BFB0B505,
	VFXPlaneBinder_UpdateBinding_mE2DD9576ADF5DC76AEE00656785F77B05B2E6B1C,
	VFXPlaneBinder_ToString_mE44F0899E57BE384413B7B7955F5E6BC6E683F52,
	VFXPlaneBinder__ctor_mED11CF344484C1B27051B2E34270AE1FB45765C3,
	VFXPositionBinder_get_Property_m2D7DD39EC9D12CFD373412BB123D8072B96DA611,
	VFXPositionBinder_set_Property_m3E5DF8C12D71D8DF8585AC6B60D72CFE6144FA2C,
	VFXPositionBinder_IsValid_m35061E5E219677356561587A4D135AE338694DEA,
	VFXPositionBinder_UpdateBinding_mD01D10DACA584A345656478A1573D7B1359C6BC5,
	VFXPositionBinder_ToString_m78DB45D14627C4B494AE4AE39C19A516F554B92F,
	VFXPositionBinder__ctor_m5CF6F416503FA2FFEFEF87579C815966DA13206A,
	VFXPreviousPositionBinder_OnEnable_mE04D045DBCEF3C2A8939400C8238B1824104411D,
	VFXPreviousPositionBinder_IsValid_mB7EB49FEBF47D5DD3137CC3FB077CCAE1DA62C76,
	VFXPreviousPositionBinder_UpdateBinding_mC5F3579B2C52ECBC389AB59BD446B6FE2119B7A6,
	VFXPreviousPositionBinder_ToString_m9CE4B7425D39CC7001D4345EB23DAF871DB4E81C,
	VFXPreviousPositionBinder__ctor_m08FD8E72C2B94BACB7B3C6765A4EB267889DA429,
	VFXRaycastBinder_get_TargetPosition_mB1DA401AC6AE1AAE043647B165928BA37624F56D,
	VFXRaycastBinder_set_TargetPosition_mA7F2AB642781472A6E17C0C9EF1A0B9CE5623C55,
	VFXRaycastBinder_get_TargetNormal_mF80F5F53B038FE6044A3B3BC2B05A3C7A0D26A09,
	VFXRaycastBinder_set_TargetNormal_mC914237651B9C4FACDBAC0013BE1F06DE7AD3A58,
	VFXRaycastBinder_get_TargetHit_mCCE5712B9F489C7E060B1E7753D3F441852E8F1F,
	VFXRaycastBinder_set_TargetHit_m14E7AE924CD526CF36C41DB84BF94E07706CE5CE,
	VFXRaycastBinder_OnEnable_m3970D4A7E5262CE38A895CAE3CBB22E9FE801678,
	VFXRaycastBinder_OnValidate_m946F4F1F8F9ED30E1657D07CD472330C83C8D0D2,
	VFXRaycastBinder_UpdateSubProperties_m984FC69B108C955BA03E05B08DEC583AAFFDE01D,
	VFXRaycastBinder_IsValid_m90C04243E0957E1BC9EF550A17844A6DA60E6960,
	VFXRaycastBinder_UpdateBinding_m16F89A49C9B46B21A4891CF80A6A61268C5E0188,
	VFXRaycastBinder_ToString_mA78937DE31C9774C3FC1ED4EA455DAC05F90937A,
	VFXRaycastBinder__ctor_mDD2986E1D00C5C668849ED855384DA39638DDFB0,
	VFXSpaceableBinder_GetTargetSpace_m0137508DC0E097C46C7EAA1ECC89398947D10D58,
	VFXSpaceableBinder_ApplySpacePositionNormal_m57A07B73ED57359C970485FF3EBA907964822089,
	VFXSpaceableBinder_ApplySpaceTS_m3C7E145FA7A6D8B485B8992B7992FA984D76977C,
	VFXSpaceableBinder_ApplySpaceTRS_m385C4C16AB3E5A84044D1406394ABC9B1A9BB748,
	VFXSpaceableBinder_ApplySpacePosition_mA1B55C45A80F225F7FC4562E74E306E90F95D12D,
	VFXSpaceableBinder__ctor_m22AD01FED423E4B640D31DC8DC13AD85422E1A04,
	VFXSphereBinder_get_Property_m4BD037B82D29852C959D5A1FEF723D9A23A00C1F,
	VFXSphereBinder_set_Property_m2463716FB48677C077197E308EA17763202BAC56,
	VFXSphereBinder_OnEnable_m6B394405C5822497D82D45FD2C688F4D1498C157,
	VFXSphereBinder_OnValidate_mC678A407430AE4D4EF88B406FDAF3CA847249E33,
	VFXSphereBinder_UpdateSubProperties_mEDEF8CCE8D1B0CDE488F0A113CCF9D3699B4C4A6,
	VFXSphereBinder_IsValid_mDDE887FEA870AF1ED13EDED131F97C76FDCA8332,
	VFXSphereBinder_UpdateBinding_mC6A1B76D0164FB80EA507DC77B0CBF57729333A0,
	VFXSphereBinder_GetSphereColliderScale_mFF7014EA91C2D8244BD6F925F0FC267C908696D1,
	VFXSphereBinder_ToString_m6E5BAF459D5303CA7A1E0077F558F260605C3BCA,
	VFXSphereBinder__ctor_m69CA4A3B5BF7AAA4F1EAFB16982232AC84FBE3EB,
	VFXTerrainBinder_get_Property_m11076487A8F242D0C7569747D702BDEA81F122B8,
	VFXTerrainBinder_set_Property_mFA12D45C44255006830071146EF6E2B71DE5CC10,
	VFXTerrainBinder_OnEnable_mDFBE56E0010C7316828223EA34F3E72F111F0660,
	VFXTerrainBinder_OnValidate_m2792C13EB0EC52B41CA022DE47A2ED676D8F5109,
	VFXTerrainBinder_UpdateSubProperties_m4BC6A8E8C029DD3BD310534ABC857E2EF6FEE282,
	VFXTerrainBinder_IsValid_m9ADEB583B6516C697F5AE5B37BD77BE0FD64897E,
	VFXTerrainBinder_UpdateBinding_m51D293C9821AA974ECCC7D56960952E16146C005,
	VFXTerrainBinder_ToString_m6BC233FED85CE059387C328F979B8C731467F4EF,
	VFXTerrainBinder__ctor_m1D29723C84DE383CA3430EC4F17495705D5F1A0B,
	VFXTransformBinder_get_Property_mDDA731FAD1367EF045268409CDED96BFEB180184,
	VFXTransformBinder_set_Property_m7D20FC04182B2015BFB94C2EFBD33AE32D54F3A7,
	VFXTransformBinder_OnEnable_m99FD4C7A36F4D6828151CDA42D7228199F395952,
	VFXTransformBinder_OnValidate_mE7FF581C03E4BAC5D4802770FE8EE7F37B40DFD1,
	VFXTransformBinder_UpdateSubProperties_mBF83D3F5E36B080C6CA43DF75A6C51B7A8AE6530,
	VFXTransformBinder_IsValid_m7C1ADF405797D11C4B58B07CDB9E9C42A011319A,
	VFXTransformBinder_UpdateBinding_mC78FFF864A6EBCDC4F4BE86912D06B0276DC22A0,
	VFXTransformBinder_ToString_m6446F68FCBEE2476A0B3896C743D125DA37FAC1C,
	VFXTransformBinder__ctor_mAFB4C41C58EAA855D89425DE74CA282313D31A90,
	VFXUIDropdownBinder_get_Property_mA312792ECADF9C6FC60D90F817069A49ACCB38C2,
	VFXUIDropdownBinder_set_Property_m3ED3BB346E2D25DBE202427337FAC7295D029CF8,
	VFXUIDropdownBinder_IsValid_m2242EB079F7B03379C451F7E0EE893A05B22CE72,
	VFXUIDropdownBinder_UpdateBinding_mA2D693A069DB8B8E46B94998168B5BA55AE3AAEB,
	VFXUIDropdownBinder_ToString_mD9EB109542CCBE72A2A1B2C0D1ABAF1E888C9525,
	VFXUIDropdownBinder__ctor_mFE6887A7B2147AB264BD55C93D3276BB962ACF2B,
	VFXUISliderBinder_get_Property_m1337B1C947ADE3A5165034AC0CEBEC6BF5E7D6F5,
	VFXUISliderBinder_set_Property_m64411E8C290FC676ED685978BE0EFA7222703AFC,
	VFXUISliderBinder_IsValid_mC9747D1AA84FC9C01A2013D817845BD56FF3AA7A,
	VFXUISliderBinder_UpdateBinding_mB5228548515D6D956FAF8FF7065DB79450711AF5,
	VFXUISliderBinder_ToString_m783D8F7A1C66611D2921F3CADD972A28C6554C92,
	VFXUISliderBinder__ctor_mE12081296E2ADF34102FC1059A762C714EEF2B46,
	VFXUIToggleBinder_get_Property_mD8909CE48307CA7F8218CFFFABE4478B4B76FAED,
	VFXUIToggleBinder_set_Property_m0D1AD5FC6784FDD9B02A50E4ECF627A46082BC7E,
	VFXUIToggleBinder_IsValid_m8CE38454205A6E0CAE24F652D990B61115A4AA59,
	VFXUIToggleBinder_UpdateBinding_m540D27870616173E0F74275B324162E132EDDE44,
	VFXUIToggleBinder_ToString_m5F9673F0E3DB2AD4BB1EF915F3FE1545E5D08F23,
	VFXUIToggleBinder__ctor_mF32CC67443A5FCD342046325703CB346AAA66811,
	VFXVelocityBinder_get_Property_m1A1D476AFF3A3D71861361F157C3648BE4E13376,
	VFXVelocityBinder_set_Property_m962BAC85948E45D56FD6AC427AF4644617906847,
	VFXVelocityBinder_IsValid_m733A7FFC62AE73C5EC2718C579318EEE889A6BB2,
	VFXVelocityBinder_Reset_mDACCE514C2B72E4B47F809BCDD65B976A7F232CD,
	VFXVelocityBinder_UpdateBinding_m09ED97F0F480345359CA957026989E7E020D3FDD,
	VFXVelocityBinder_ToString_m1F67119485CFD21DB8903023EB78BB7D7123332F,
	VFXVelocityBinder__ctor_m90ADA6EBDCAF56890C4078630ED7E2FF1519596B,
	VFXVelocityBinder__cctor_m1DCF8B74965367AADAED91267964D319EB6C532F,
	VFXBinderAttribute__ctor_m5685F22B447E9F6F9F0AD314B115F9ACAB27EED1,
	NULL,
	VFXBinderBase_Reset_m7C64FFDCB71B077590384C512E0776B33B23477E,
	VFXBinderBase_Awake_mB78C136536D663C6188EA4575AC9F971F512B915,
	VFXBinderBase_OnEnable_mA026DDFCBD31D8800C70F6A291A8DB6387F2041E,
	VFXBinderBase_OnDisable_mC6D17B49F6277356AD6F67652EE80B122920B577,
	NULL,
	VFXBinderBase_ToString_m7FA1FF8BC36F8140D98A49BEF82EA8CCAF2E8DB0,
	VFXBinderBase__ctor_m4C0835B8FFF0968BC5378A1D579ABD0168ECF7D3,
	VFXPropertyBinder_OnEnable_m867A5FEA5EDE524F0F7DF47EB86BF80128A6F298,
	VFXPropertyBinder_OnValidate_m31AF250AFDCBBEAEEA73D29AF8793E25CEDF478D,
	VFXPropertyBinder_SafeDestroy_m4BC4B6A481989E373832AB7449F231ACB640F1AA,
	VFXPropertyBinder_Reload_mC20E8C114F88956652BCFE1D1AB7701BA946CA4A,
	VFXPropertyBinder_Reset_mEE67C9046525E83EB402D542891D80149008D9D2,
	VFXPropertyBinder_LateUpdate_mBF71A331AE3C80F8CFE288BBCF1CBF7B20A505BC,
	NULL,
	NULL,
	VFXPropertyBinder_ClearPropertyBinders_m0D6B59D73389A5A83E46E87D05B24ADE479CF13F,
	VFXPropertyBinder_ClearParameterBinders_mFD3FCF79B5B335810B8A754D503702EF15A06F6D,
	VFXPropertyBinder_RemovePropertyBinder_mBFC783DFBF28ED3BB9ACC607CECCF9EF4661E863,
	VFXPropertyBinder_RemoveParameterBinder_mCD45A1A2731CCE0B9B4946F22C21794719E534D6,
	NULL,
	NULL,
	NULL,
	NULL,
	VFXPropertyBinder__ctor_mAC0E583E26FEA725E8C676DD293C791173AA8A90,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	VFXPropertyBindingAttribute__ctor_m3619150CC621FD0D074BD7482574633A3F64ACB9,
};
static const int32_t s_InvokerIndices[506] = 
{
	21016,
	20694,
	6145,
	21016,
	34321,
	21016,
	3763,
	3763,
	3763,
	21016,
	34252,
	21016,
	3763,
	3763,
	3763,
	21016,
	34252,
	21016,
	3763,
	3763,
	3763,
	21016,
	34252,
	3763,
	3763,
	3763,
	21016,
	34252,
	21016,
	7461,
	20694,
	15903,
	20761,
	15968,
	20694,
	20593,
	15804,
	20593,
	15804,
	6145,
	21016,
	34252,
	-1,
	21016,
	-1,
	-1,
	21016,
	34252,
	21016,
	5016,
	3730,
	21016,
	34252,
	21016,
	5016,
	3736,
	21016,
	34252,
	21016,
	5016,
	3737,
	21016,
	21016,
	34252,
	21016,
	5016,
	3738,
	21016,
	34252,
	21016,
	5016,
	3722,
	21016,
	34252,
	21016,
	5016,
	3735,
	21016,
	34252,
	21016,
	5016,
	3715,
	28038,
	32090,
	25646,
	24303,
	22278,
	15903,
	21016,
	20550,
	21016,
	21014,
	21016,
	20761,
	20761,
	20761,
	15903,
	21016,
	20550,
	21016,
	21014,
	21016,
	20761,
	20761,
	20761,
	32719,
	20593,
	15804,
	20593,
	15804,
	20550,
	15757,
	20550,
	15757,
	20550,
	15757,
	20984,
	16179,
	20761,
	15968,
	20984,
	16179,
	20984,
	16179,
	20873,
	16071,
	20761,
	15968,
	21016,
	20550,
	21016,
	3361,
	7995,
	21016,
	34252,
	21016,
	11681,
	15903,
	7312,
	4886,
	6737,
	3943,
	7857,
	23564,
	28061,
	28070,
	6689,
	3829,
	21016,
	21016,
	34252,
	5704,
	21016,
	34252,
	21016,
	5558,
	5960,
	15903,
	21016,
	20550,
	21016,
	21322,
	21016,
	20761,
	20761,
	20761,
	3695,
	8071,
	15968,
	21016,
	8071,
	8071,
	21016,
	15993,
	15993,
	21016,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	34156,
	21016,
	20761,
	28070,
	21016,
	20694,
	21016,
	21005,
	21004,
	340,
	219,
	21016,
	492,
	341,
	804,
	21016,
	21016,
	21016,
	21016,
	267,
	5649,
	6254,
	21016,
	21016,
	13811,
	13811,
	21016,
	21016,
	13811,
	13811,
	13811,
	13811,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	15903,
	21016,
	21016,
	21016,
	21016,
	21016,
	3521,
	15721,
	6522,
	15721,
	34252,
	34252,
	15968,
	15968,
	21016,
	21016,
	21016,
	15968,
	21016,
	21016,
	34247,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	16348,
	16348,
	15968,
	15968,
	21016,
	15968,
	15968,
	15968,
	15968,
	21016,
	15968,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	-1,
	15968,
	21016,
	-1,
	15968,
	20761,
	21016,
	21016,
	16193,
	-1,
	21016,
	32090,
	32090,
	31787,
	28070,
	21016,
	15968,
	20761,
	20761,
	15968,
	20761,
	15968,
	11681,
	21016,
	15968,
	20761,
	21016,
	20761,
	15968,
	11681,
	15968,
	20761,
	21016,
	21016,
	21016,
	21016,
	6098,
	21016,
	11681,
	15968,
	20761,
	21016,
	20761,
	15968,
	11681,
	15968,
	20761,
	21016,
	20761,
	15968,
	20761,
	15968,
	11681,
	21016,
	15968,
	20761,
	21016,
	20761,
	15968,
	20761,
	15968,
	11681,
	21016,
	15968,
	20761,
	21016,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	11681,
	15968,
	20550,
	20550,
	21002,
	20761,
	21016,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	11681,
	15968,
	20694,
	14261,
	20761,
	21016,
	34252,
	21016,
	11681,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	11681,
	15968,
	20761,
	21016,
	21016,
	11681,
	15968,
	21016,
	20761,
	21016,
	20761,
	15968,
	21016,
	21016,
	21016,
	11681,
	15968,
	20761,
	21016,
	20761,
	15968,
	11681,
	15968,
	20761,
	21016,
	21016,
	11681,
	15968,
	20761,
	21016,
	20761,
	15968,
	20761,
	15968,
	20761,
	15968,
	21016,
	21016,
	21016,
	11681,
	15968,
	20761,
	21016,
	5704,
	1281,
	1281,
	746,
	3439,
	21016,
	20761,
	15968,
	21016,
	21016,
	21016,
	11681,
	15968,
	14032,
	20761,
	21016,
	20761,
	15968,
	21016,
	21016,
	21016,
	11681,
	15968,
	20761,
	21016,
	20761,
	15968,
	21016,
	21016,
	21016,
	11681,
	15968,
	20761,
	21016,
	20761,
	15968,
	11681,
	15968,
	20761,
	21016,
	20761,
	15968,
	11681,
	15968,
	20761,
	21016,
	20761,
	15968,
	11681,
	15968,
	20761,
	21016,
	20761,
	15968,
	11681,
	21016,
	15968,
	20761,
	21016,
	34252,
	15968,
	-1,
	21016,
	21016,
	21016,
	21016,
	-1,
	20761,
	21016,
	21016,
	21016,
	32764,
	21016,
	21016,
	21016,
	-1,
	-1,
	21016,
	21016,
	15968,
	15968,
	-1,
	-1,
	-1,
	-1,
	21016,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	15968,
};
static const Il2CppTokenRangePair s_rgctxIndices[8] = 
{
	{ 0x02000017, { 0, 4 } },
	{ 0x02000068, { 15, 7 } },
	{ 0x060001E6, { 4, 2 } },
	{ 0x060001E7, { 6, 2 } },
	{ 0x060001EC, { 8, 1 } },
	{ 0x060001ED, { 9, 1 } },
	{ 0x060001EE, { 10, 3 } },
	{ 0x060001EF, { 13, 2 } },
};
extern const uint32_t g_rgctx_EventAttributeValue_1_t855302F4424EAD5DB166DF3C523848286032C7F9;
extern const uint32_t g_rgctx_Action_3_tBD843CF5BC8402FF901C3B1DC146151927DCD971;
extern const uint32_t g_rgctx_T_t825031F921224E4B55F1ED51347E1A32EC5FABFE;
extern const uint32_t g_rgctx_Action_3_Invoke_mCFF499E952F90256A7975CFE7C701F91FABAAA47;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisT_tFAEA91CA1ACEC8F6A91141D3637947892D96BBB0_mDF058388EE983F4ACCE73E5EB60AC3D934A76A1D;
extern const uint32_t g_rgctx_T_tFAEA91CA1ACEC8F6A91141D3637947892D96BBB0;
extern const uint32_t g_rgctx_VFXPropertyBinder_AddPropertyBinder_TisT_t83DBF61A5ADA9E2C60986AC36E3F33B00AFE76BC_m2BE8428571F42FF1E62A5B038E0C43CB45C2E7F7;
extern const uint32_t g_rgctx_T_t83DBF61A5ADA9E2C60986AC36E3F33B00AFE76BC;
extern const uint32_t g_rgctx_T_t7089D0FC6FC343C3A6A8956F3054736B781F9E54;
extern const uint32_t g_rgctx_VFXPropertyBinder_RemovePropertyBinders_TisT_t9D1940C1EE1AED226017D083B1F65DB56FEA8770_m8491E03EAE32B7386EA41FCCDACC52906F8D321D;
extern const uint32_t g_rgctx_U3CGetPropertyBindersU3Ed__17_1_t80EA748007BD2A21916B327D667F4E5B0DD6A764;
extern const uint32_t g_rgctx_U3CGetPropertyBindersU3Ed__17_1__ctor_m7AF463E18810F44E56C747D2B600BD9074A31E4F;
extern const uint32_t g_rgctx_IEnumerable_1_t6232759638B8AE3DEF6B4326A782573B53F24DD8;
extern const uint32_t g_rgctx_VFXPropertyBinder_GetPropertyBinders_TisT_tC27A0D05FC1D7CAB9915A9BA03F3299D142BD145_mEE1E9241ED0437969EDD27232097B128058AE38E;
extern const uint32_t g_rgctx_IEnumerable_1_tAF62CCA75AA286B809107C152023C4F1FD7E330A;
extern const uint32_t g_rgctx_U3CGetPropertyBindersU3Ed__17_1_t14101C9BC5F8AF07DB91DAFE51F19E5032EF971E;
extern const uint32_t g_rgctx_U3CGetPropertyBindersU3Ed__17_1_U3CU3Em__Finally1_mD76EAD05E4CAF2C4BCB980196F938F074D8D448D;
extern const uint32_t g_rgctx_T_t5E852F4C449B6155BEA2543AE06162010B0FEF6E;
extern const uint32_t g_rgctx_U3CGetPropertyBindersU3Ed__17_1_System_IDisposable_Dispose_m6E80FB7ACF96FF0C2C600C9D4D033FC0A74F14B3;
extern const uint32_t g_rgctx_U3CGetPropertyBindersU3Ed__17_1__ctor_m391AE979B3CB9F8915AFF837554685907F42D5C1;
extern const uint32_t g_rgctx_IEnumerator_1_tEB2A401DC537AD1CD1E9432108F693929CA92DF3;
extern const uint32_t g_rgctx_U3CGetPropertyBindersU3Ed__17_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m54DCC688F2581FE56B84F7875704012434FFBDA5;
static const Il2CppRGCTXDefinition s_rgctxValues[22] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EventAttributeValue_1_t855302F4424EAD5DB166DF3C523848286032C7F9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3_tBD843CF5BC8402FF901C3B1DC146151927DCD971 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t825031F921224E4B55F1ED51347E1A32EC5FABFE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_3_Invoke_mCFF499E952F90256A7975CFE7C701F91FABAAA47 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisT_tFAEA91CA1ACEC8F6A91141D3637947892D96BBB0_mDF058388EE983F4ACCE73E5EB60AC3D934A76A1D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFAEA91CA1ACEC8F6A91141D3637947892D96BBB0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_VFXPropertyBinder_AddPropertyBinder_TisT_t83DBF61A5ADA9E2C60986AC36E3F33B00AFE76BC_m2BE8428571F42FF1E62A5B038E0C43CB45C2E7F7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t83DBF61A5ADA9E2C60986AC36E3F33B00AFE76BC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7089D0FC6FC343C3A6A8956F3054736B781F9E54 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_VFXPropertyBinder_RemovePropertyBinders_TisT_t9D1940C1EE1AED226017D083B1F65DB56FEA8770_m8491E03EAE32B7386EA41FCCDACC52906F8D321D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CGetPropertyBindersU3Ed__17_1_t80EA748007BD2A21916B327D667F4E5B0DD6A764 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CGetPropertyBindersU3Ed__17_1__ctor_m7AF463E18810F44E56C747D2B600BD9074A31E4F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t6232759638B8AE3DEF6B4326A782573B53F24DD8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_VFXPropertyBinder_GetPropertyBinders_TisT_tC27A0D05FC1D7CAB9915A9BA03F3299D142BD145_mEE1E9241ED0437969EDD27232097B128058AE38E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_tAF62CCA75AA286B809107C152023C4F1FD7E330A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CGetPropertyBindersU3Ed__17_1_t14101C9BC5F8AF07DB91DAFE51F19E5032EF971E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CGetPropertyBindersU3Ed__17_1_U3CU3Em__Finally1_mD76EAD05E4CAF2C4BCB980196F938F074D8D448D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5E852F4C449B6155BEA2543AE06162010B0FEF6E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CGetPropertyBindersU3Ed__17_1_System_IDisposable_Dispose_m6E80FB7ACF96FF0C2C600C9D4D033FC0A74F14B3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CGetPropertyBindersU3Ed__17_1__ctor_m391AE979B3CB9F8915AFF837554685907F42D5C1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tEB2A401DC537AD1CD1E9432108F693929CA92DF3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CGetPropertyBindersU3Ed__17_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m54DCC688F2581FE56B84F7875704012434FFBDA5 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_VisualEffectGraph_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_Unity_VisualEffectGraph_Runtime_CodeGenModule = 
{
	"Unity.VisualEffectGraph.Runtime.dll",
	506,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	8,
	s_rgctxIndices,
	22,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
