﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBB4FDEABAA002AD98D9A2B28FB8605502C990502 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m9DEDF85C8BFEA23248A12BD5B40978400DF1007C (void);
extern void PlanarGraph_RemoveDuplicateEdges_m4BD4BCB21678A1ACF9CAA2D7C1CCEA846DEA607E (void);
extern void PlanarGraph_CheckCollinear_m2C20CF3CBD2F6C1598261C0D2F07A77B52DACF26 (void);
extern void PlanarGraph_LineLineIntersection_m6A19D4B59781E13C978432AFAD7D233879D3EEBC (void);
extern void PlanarGraph_LineLineIntersection_m4A8A7E8A3A7F6783ED000E66F573B4F6FD700AFB (void);
extern void PlanarGraph_CalculateEdgeIntersections_m1C01C196ED8AE622F14AC3F6380650D5DDC35B06 (void);
extern void PlanarGraph_CalculateTJunctions_m1644F4F05010E0F99BF4E212729750B4E2EB2EA4 (void);
extern void PlanarGraph_CutEdges_m97A6AB066AE8E9FD110209F2370306C34CE5740D (void);
extern void PlanarGraph_RemoveDuplicatePoints_mAF205F732573BA4C5AB76533AE3526CBD0115972 (void);
extern void PlanarGraph_Validate_mB9C1CC7BF7AA0DB3A5AA6358C99B6CF539EA44BB (void);
extern void PlanarGraph__cctor_m237102EAFF388BCF5B853FB5A810FF295CA02E7F (void);
extern void Refinery_RequiresRefining_m4EA65D4D6DC9952D1929E4137B766D381D50AAEC (void);
extern void Refinery_FetchEncroachedSegments_m121F47268DE44EC2B1CD13283FABF82B4EA85842 (void);
extern void Refinery_InsertVertex_mCA9503EA509CE393850604C957C00C51AFE48F6E (void);
extern void Refinery_SplitSegments_m0757F012E06D755761CEF82647C757CB26A735CC (void);
extern void Refinery_Condition_m6C3A1C309B279D858D172E5E0593673A5912D98E (void);
extern void Refinery__cctor_m8CC81D621E197C54AFDE164722945ACE75F06624 (void);
extern void Smoothen_RefineEdges_m914F0B0D1F7193F66F410668153ECBC3E7BA6BBB (void);
extern void Smoothen_GetAffectingEdges_mE425C25391BA739301127B8403F38CE0985040FA (void);
extern void Smoothen_CentroidByPoints_mD181064D3B258FDBC8BB36711C67869DFF719FD8 (void);
extern void Smoothen_CentroidByPolygon_mBFA60C01A996E080A9C37EF3B4D6C07B9F815004 (void);
extern void Smoothen_ConnectTriangles_m356CE5E58898A0C0018CD8A45997AA97817981D1 (void);
extern void Smoothen_Condition_m6DA3DC27448AE26EC40CA95F100303F2BF5AE142 (void);
extern void Smoothen__cctor_mE7B9458D88A04E0EDD3D990F32ECFA42A105847E (void);
extern void Tessellator_FindSplit_m7EEFF84B65CCEAC234D6BD19180AA65DCB9F1FDE (void);
extern void Tessellator_SetAllocator_mD0233B1C112FFE7BFB90F4A13722AAAD49EF3C46 (void);
extern void Tessellator_AddPoint_mBDF7196470641275050A71A48B5CF45404F370B8 (void);
extern void Tessellator_InsertHull_m67DF1406D733E2AE81A169BBAE3A80FFBBD3E8CB (void);
extern void Tessellator_EraseHull_m55761C9B5B7B9432851CA29CD8722FA40E3B1DDA (void);
extern void Tessellator_SplitHulls_m41280B60D1379FF22538AE33BF6BECF5FD78FD2E (void);
extern void Tessellator_MergeHulls_mC03CD5E5E3CCF9C79960C2A7F528824CA3EC9352 (void);
extern void Tessellator_InsertUniqueEdge_mFCA7027EFF547D248252183B4AE5FF3394342C7D (void);
extern void Tessellator_PrepareDelaunay_mEAADB0F4F68B596340D857E668110C2A613CA4E8 (void);
extern void Tessellator_OppositeOf_mB3604C8586A1358F603C27E05D24168F06257103 (void);
extern void Tessellator_FindConstraint_mC0DED9A33B6967DBC980E841462CE7956833D599 (void);
extern void Tessellator_AddTriangle_mDA63B6E6783AB998FABA70DC5014E49A74D6CD0B (void);
extern void Tessellator_RemovePair_m8BA0B1208FA0DB43FC7EC02062C2D58784A656A5 (void);
extern void Tessellator_RemoveTriangle_mF1C2F14B224CDEF0E6389CF24790EEC9D48DA2A8 (void);
extern void Tessellator_EdgeFlip_m2EFC2678083E4647B8993B6AD464544BD9C89BE4 (void);
extern void Tessellator_Flip_m8891166DD0D6693315A882BA942FDEFA7463CAE2 (void);
extern void Tessellator_GetCells_m9A270E7F88ACA2B9A82DB96D416507C6F6306A5D (void);
extern void Tessellator_ApplyDelaunay_m22E8AFD34B62640DE1668419D66DDE47E9CB1F4E (void);
extern void Tessellator_FindNeighbor_mBB5B6E1CFB6C816CB2EE3F45AC1E29B15DBD8207 (void);
extern void Tessellator_Constrain_mB83988169B046690C3D80CA111718DC809A3CCB0 (void);
extern void Tessellator_RemoveExterior_mC35CA28B5C5C1A6457C34715EA3649E566A14B00 (void);
extern void Tessellator_RemoveInterior_m8C9F53C6E83C695B2774C0083D633A8A2D5778B7 (void);
extern void Tessellator_Triangulate_mEDF1BD94E8DCCD0D95B061B6FAC8C118D580FA32 (void);
extern void Tessellator_Tessellate_m3EB11B589A3E3E8563756605E2C94CEBBC7F2F31 (void);
extern void Tessellator_Cleanup_mD62652EF03E447E90D6374B21895AB59072A3B75 (void);
extern void TestHullPointL_Test_mFFB799A0A608010AA7A00AECB121CA485508D3FC (void);
extern void TestHullPointU_Test_mFB4072C78BD14C4254E4466A6910EC43B48F806F (void);
extern void TestHullEventLe_Test_mB26988EC179628C07833DB1923209744350C7852 (void);
extern void TestHullEventE_Test_m3F5754510484A9A9775553F649BF5D1576A13689 (void);
extern void TestEdgePointE_Test_m0A78F3EABC68CD0992FB01DD956897B480542A77 (void);
extern void TestCellE_Test_mD7712C00B9AFEF847F2C2DD7599CD79F18470A4E (void);
extern void XCompare_Compare_m2C8C02429EAA63BC352FAB454EFC619735DD0FD4 (void);
extern void IntersectionCompare_Compare_mE4DE2982303B88DBD5D08E01BDEBBCA7AB2EB949 (void);
extern void TessEventCompare_Compare_mC9C917FCD344EC3C154098A7C3DA095493F2BE64 (void);
extern void TessEdgeCompare_Compare_mD9295D4C8DFA530C2A527AF00B76E90D71F92DB2 (void);
extern void TessCellCompare_Compare_mBAE987CDAB366826FCD0DF8D442AA9A4FB688351 (void);
extern void TessJunctionCompare_Compare_m01205F9F920EF4FA358BC4D6DDF1566F197FDCA7 (void);
extern void DelaEdgeCompare_Compare_m12BDA8FF5BB8871BF1A0A2994584DD3CB4776815 (void);
extern void TessLink_CreateLink_mDE5AB7EC269D0FA73DC352FD5D2B2C863BB4DF30 (void);
extern void TessLink_DestroyLink_mD74097587877DEC31033410C4A2159A64BBD206C (void);
extern void TessLink_Find_mFAFEEFC0DE81DE074103B5B1E11B898D532FDF05 (void);
extern void TessLink_Link_m74A93722E653788463D9BAF7176D259A210150E7 (void);
extern void ModuleHandle_OrientFast_m437EC497545DA10AEA60FF6D19D98367878448F9 (void);
extern void ModuleHandle_OrientFastDouble_mD284B0A55AF9518B1D1CE25F7F7F266CA2439232 (void);
extern void ModuleHandle_CircumCircle_m**************************************** (void);
extern void ModuleHandle_IsInsideCircle_m**************************************** (void);
extern void ModuleHandle_TriangleArea_m33666F4D829A98D16C70EE4E4C8861290C84B475 (void);
extern void ModuleHandle_Sign_m5AB02A9A8D3D3EAA37F330F89071FCC3EB5CF93A (void);
extern void ModuleHandle_IsInsideTriangle_m99A27741B6F4ED2BED5895E1447C7159DC19D418 (void);
extern void ModuleHandle_IsInsideTriangleApproximate_m60CC3CDF6F9B62AB6616A6432AA38AD1E1D4F7DC (void);
extern void ModuleHandle_IsInsideCircle_m**************************************** (void);
extern void ModuleHandle_BuildTriangles_mCE64F77A35EA332A2E0D234A74202B88DA142082 (void);
extern void ModuleHandle_BuildTriangles_mBC0D2742F3949422FB5CE7261A722A4EDED0CFFC (void);
extern void ModuleHandle_BuildTriangles_mF067D95190D8FDD2ACC8AE717EB313E95799E5DC (void);
extern void ModuleHandle_BuildTrianglesAndEdges_mCF04E3E12DA8E5B16B5DBD03206D8029B94614F2 (void);
extern void ModuleHandle_CopyGraph_m7524ECBD453A100BB700B073E6D833C297D8DBB1 (void);
extern void ModuleHandle_CopyGeometry_m48670245A1D3128CCD9300CAC9BB9330EE142905 (void);
extern void ModuleHandle_TransferOutput_mCA93E15FF7A62178863E3B3FFE2F361CB5EBEE8E (void);
extern void ModuleHandle_GraphConditioner_m7CCB383D420BDC3AAD6D9AF9824757BA3348E8E8 (void);
extern void ModuleHandle_Reorder_m0D8DB6E4777AF650332512ECEFF6E5D181B1A233 (void);
extern void ModuleHandle_VertexCleanupConditioner_mD6086E9DA27D79B1C91C34FC25B1C6C0BBD94AB7 (void);
extern void ModuleHandle_ConvexQuad_m4A21CDD915DA41A18538FC95EB915DCE7B2E70D0 (void);
extern void ModuleHandle_Tessellate_mDCFCA96AD66F268C584B76699ECF5632D0C0E352 (void);
extern void ModuleHandle_Subdivide_m344F7B51ECF90710B82DAC6C1A756C8CB20FD6F1 (void);
extern void ModuleHandle__cctor_mC0B6A682D8EE791793279C92C678083089B93BDC (void);
extern void kMeans_CalculateDistance_mB22BCF5FA8FDCD0E0811A4D4DA8183B7CE863FEA (void);
extern void kMeans_CalculateClustering_m3B105366B46A9C6393729435F477E1C362D87154 (void);
extern void kMeans_AssignClustering_mD9AC65DDCBF9A8DE38BF02325C1C0EDEAB9543FC (void);
extern void kMeans_ClusterInternal_m5B6363E85EFDABAFB6684ED7E309901BD684C615 (void);
extern void kMeans_Cluster3_mE2B12EC7FC744F298D3E728A64408EAC2F739DB7 (void);
static Il2CppMethodPointer s_methodPointers[147] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBB4FDEABAA002AD98D9A2B28FB8605502C990502,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m9DEDF85C8BFEA23248A12BD5B40978400DF1007C,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PlanarGraph_RemoveDuplicateEdges_m4BD4BCB21678A1ACF9CAA2D7C1CCEA846DEA607E,
	PlanarGraph_CheckCollinear_m2C20CF3CBD2F6C1598261C0D2F07A77B52DACF26,
	PlanarGraph_LineLineIntersection_m6A19D4B59781E13C978432AFAD7D233879D3EEBC,
	PlanarGraph_LineLineIntersection_m4A8A7E8A3A7F6783ED000E66F573B4F6FD700AFB,
	PlanarGraph_CalculateEdgeIntersections_m1C01C196ED8AE622F14AC3F6380650D5DDC35B06,
	PlanarGraph_CalculateTJunctions_m1644F4F05010E0F99BF4E212729750B4E2EB2EA4,
	PlanarGraph_CutEdges_m97A6AB066AE8E9FD110209F2370306C34CE5740D,
	PlanarGraph_RemoveDuplicatePoints_mAF205F732573BA4C5AB76533AE3526CBD0115972,
	PlanarGraph_Validate_mB9C1CC7BF7AA0DB3A5AA6358C99B6CF539EA44BB,
	PlanarGraph__cctor_m237102EAFF388BCF5B853FB5A810FF295CA02E7F,
	Refinery_RequiresRefining_m4EA65D4D6DC9952D1929E4137B766D381D50AAEC,
	Refinery_FetchEncroachedSegments_m121F47268DE44EC2B1CD13283FABF82B4EA85842,
	Refinery_InsertVertex_mCA9503EA509CE393850604C957C00C51AFE48F6E,
	Refinery_SplitSegments_m0757F012E06D755761CEF82647C757CB26A735CC,
	Refinery_Condition_m6C3A1C309B279D858D172E5E0593673A5912D98E,
	Refinery__cctor_m8CC81D621E197C54AFDE164722945ACE75F06624,
	Smoothen_RefineEdges_m914F0B0D1F7193F66F410668153ECBC3E7BA6BBB,
	Smoothen_GetAffectingEdges_mE425C25391BA739301127B8403F38CE0985040FA,
	Smoothen_CentroidByPoints_mD181064D3B258FDBC8BB36711C67869DFF719FD8,
	Smoothen_CentroidByPolygon_mBFA60C01A996E080A9C37EF3B4D6C07B9F815004,
	Smoothen_ConnectTriangles_m356CE5E58898A0C0018CD8A45997AA97817981D1,
	Smoothen_Condition_m6DA3DC27448AE26EC40CA95F100303F2BF5AE142,
	Smoothen__cctor_mE7B9458D88A04E0EDD3D990F32ECFA42A105847E,
	Tessellator_FindSplit_m7EEFF84B65CCEAC234D6BD19180AA65DCB9F1FDE,
	Tessellator_SetAllocator_mD0233B1C112FFE7BFB90F4A13722AAAD49EF3C46,
	Tessellator_AddPoint_mBDF7196470641275050A71A48B5CF45404F370B8,
	Tessellator_InsertHull_m67DF1406D733E2AE81A169BBAE3A80FFBBD3E8CB,
	Tessellator_EraseHull_m55761C9B5B7B9432851CA29CD8722FA40E3B1DDA,
	Tessellator_SplitHulls_m41280B60D1379FF22538AE33BF6BECF5FD78FD2E,
	Tessellator_MergeHulls_mC03CD5E5E3CCF9C79960C2A7F528824CA3EC9352,
	Tessellator_InsertUniqueEdge_mFCA7027EFF547D248252183B4AE5FF3394342C7D,
	Tessellator_PrepareDelaunay_mEAADB0F4F68B596340D857E668110C2A613CA4E8,
	Tessellator_OppositeOf_mB3604C8586A1358F603C27E05D24168F06257103,
	Tessellator_FindConstraint_mC0DED9A33B6967DBC980E841462CE7956833D599,
	Tessellator_AddTriangle_mDA63B6E6783AB998FABA70DC5014E49A74D6CD0B,
	Tessellator_RemovePair_m8BA0B1208FA0DB43FC7EC02062C2D58784A656A5,
	Tessellator_RemoveTriangle_mF1C2F14B224CDEF0E6389CF24790EEC9D48DA2A8,
	Tessellator_EdgeFlip_m2EFC2678083E4647B8993B6AD464544BD9C89BE4,
	Tessellator_Flip_m8891166DD0D6693315A882BA942FDEFA7463CAE2,
	Tessellator_GetCells_m9A270E7F88ACA2B9A82DB96D416507C6F6306A5D,
	Tessellator_ApplyDelaunay_m22E8AFD34B62640DE1668419D66DDE47E9CB1F4E,
	Tessellator_FindNeighbor_mBB5B6E1CFB6C816CB2EE3F45AC1E29B15DBD8207,
	Tessellator_Constrain_mB83988169B046690C3D80CA111718DC809A3CCB0,
	Tessellator_RemoveExterior_mC35CA28B5C5C1A6457C34715EA3649E566A14B00,
	Tessellator_RemoveInterior_m8C9F53C6E83C695B2774C0083D633A8A2D5778B7,
	Tessellator_Triangulate_mEDF1BD94E8DCCD0D95B061B6FAC8C118D580FA32,
	Tessellator_Tessellate_m3EB11B589A3E3E8563756605E2C94CEBBC7F2F31,
	Tessellator_Cleanup_mD62652EF03E447E90D6374B21895AB59072A3B75,
	TestHullPointL_Test_mFFB799A0A608010AA7A00AECB121CA485508D3FC,
	TestHullPointU_Test_mFB4072C78BD14C4254E4466A6910EC43B48F806F,
	TestHullEventLe_Test_mB26988EC179628C07833DB1923209744350C7852,
	TestHullEventE_Test_m3F5754510484A9A9775553F649BF5D1576A13689,
	TestEdgePointE_Test_m0A78F3EABC68CD0992FB01DD956897B480542A77,
	TestCellE_Test_mD7712C00B9AFEF847F2C2DD7599CD79F18470A4E,
	NULL,
	XCompare_Compare_m2C8C02429EAA63BC352FAB454EFC619735DD0FD4,
	IntersectionCompare_Compare_mE4DE2982303B88DBD5D08E01BDEBBCA7AB2EB949,
	TessEventCompare_Compare_mC9C917FCD344EC3C154098A7C3DA095493F2BE64,
	TessEdgeCompare_Compare_mD9295D4C8DFA530C2A527AF00B76E90D71F92DB2,
	TessCellCompare_Compare_mBAE987CDAB366826FCD0DF8D442AA9A4FB688351,
	TessJunctionCompare_Compare_m01205F9F920EF4FA358BC4D6DDF1566F197FDCA7,
	DelaEdgeCompare_Compare_m12BDA8FF5BB8871BF1A0A2994584DD3CB4776815,
	TessLink_CreateLink_mDE5AB7EC269D0FA73DC352FD5D2B2C863BB4DF30,
	TessLink_DestroyLink_mD74097587877DEC31033410C4A2159A64BBD206C,
	TessLink_Find_mFAFEEFC0DE81DE074103B5B1E11B898D532FDF05,
	TessLink_Link_m74A93722E653788463D9BAF7176D259A210150E7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ModuleHandle_OrientFast_m437EC497545DA10AEA60FF6D19D98367878448F9,
	ModuleHandle_OrientFastDouble_mD284B0A55AF9518B1D1CE25F7F7F266CA2439232,
	ModuleHandle_CircumCircle_m****************************************,
	ModuleHandle_IsInsideCircle_m****************************************,
	ModuleHandle_TriangleArea_m33666F4D829A98D16C70EE4E4C8861290C84B475,
	ModuleHandle_Sign_m5AB02A9A8D3D3EAA37F330F89071FCC3EB5CF93A,
	ModuleHandle_IsInsideTriangle_m99A27741B6F4ED2BED5895E1447C7159DC19D418,
	ModuleHandle_IsInsideTriangleApproximate_m60CC3CDF6F9B62AB6616A6432AA38AD1E1D4F7DC,
	ModuleHandle_IsInsideCircle_m****************************************,
	ModuleHandle_BuildTriangles_mCE64F77A35EA332A2E0D234A74202B88DA142082,
	ModuleHandle_BuildTriangles_mBC0D2742F3949422FB5CE7261A722A4EDED0CFFC,
	ModuleHandle_BuildTriangles_mF067D95190D8FDD2ACC8AE717EB313E95799E5DC,
	ModuleHandle_BuildTrianglesAndEdges_mCF04E3E12DA8E5B16B5DBD03206D8029B94614F2,
	ModuleHandle_CopyGraph_m7524ECBD453A100BB700B073E6D833C297D8DBB1,
	ModuleHandle_CopyGeometry_m48670245A1D3128CCD9300CAC9BB9330EE142905,
	ModuleHandle_TransferOutput_mCA93E15FF7A62178863E3B3FFE2F361CB5EBEE8E,
	ModuleHandle_GraphConditioner_m7CCB383D420BDC3AAD6D9AF9824757BA3348E8E8,
	ModuleHandle_Reorder_m0D8DB6E4777AF650332512ECEFF6E5D181B1A233,
	ModuleHandle_VertexCleanupConditioner_mD6086E9DA27D79B1C91C34FC25B1C6C0BBD94AB7,
	ModuleHandle_ConvexQuad_m4A21CDD915DA41A18538FC95EB915DCE7B2E70D0,
	ModuleHandle_Tessellate_mDCFCA96AD66F268C584B76699ECF5632D0C0E352,
	ModuleHandle_Subdivide_m344F7B51ECF90710B82DAC6C1A756C8CB20FD6F1,
	ModuleHandle__cctor_mC0B6A682D8EE791793279C92C678083089B93BDC,
	kMeans_CalculateDistance_mB22BCF5FA8FDCD0E0811A4D4DA8183B7CE863FEA,
	kMeans_CalculateClustering_m3B105366B46A9C6393729435F477E1C362D87154,
	kMeans_AssignClustering_mD9AC65DDCBF9A8DE38BF02325C1C0EDEAB9543FC,
	kMeans_ClusterInternal_m5B6363E85EFDABAFB6684ED7E309901BD684C615,
	kMeans_Cluster3_mE2B12EC7FC744F298D3E728A64408EAC2F739DB7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
extern void Tessellator_SetAllocator_mD0233B1C112FFE7BFB90F4A13722AAAD49EF3C46_AdjustorThunk (void);
extern void Tessellator_AddPoint_mBDF7196470641275050A71A48B5CF45404F370B8_AdjustorThunk (void);
extern void Tessellator_SplitHulls_m41280B60D1379FF22538AE33BF6BECF5FD78FD2E_AdjustorThunk (void);
extern void Tessellator_MergeHulls_mC03CD5E5E3CCF9C79960C2A7F528824CA3EC9352_AdjustorThunk (void);
extern void Tessellator_PrepareDelaunay_mEAADB0F4F68B596340D857E668110C2A613CA4E8_AdjustorThunk (void);
extern void Tessellator_OppositeOf_mB3604C8586A1358F603C27E05D24168F06257103_AdjustorThunk (void);
extern void Tessellator_FindConstraint_mC0DED9A33B6967DBC980E841462CE7956833D599_AdjustorThunk (void);
extern void Tessellator_AddTriangle_mDA63B6E6783AB998FABA70DC5014E49A74D6CD0B_AdjustorThunk (void);
extern void Tessellator_RemovePair_m8BA0B1208FA0DB43FC7EC02062C2D58784A656A5_AdjustorThunk (void);
extern void Tessellator_RemoveTriangle_mF1C2F14B224CDEF0E6389CF24790EEC9D48DA2A8_AdjustorThunk (void);
extern void Tessellator_EdgeFlip_m2EFC2678083E4647B8993B6AD464544BD9C89BE4_AdjustorThunk (void);
extern void Tessellator_Flip_m8891166DD0D6693315A882BA942FDEFA7463CAE2_AdjustorThunk (void);
extern void Tessellator_GetCells_m9A270E7F88ACA2B9A82DB96D416507C6F6306A5D_AdjustorThunk (void);
extern void Tessellator_ApplyDelaunay_m22E8AFD34B62640DE1668419D66DDE47E9CB1F4E_AdjustorThunk (void);
extern void Tessellator_FindNeighbor_mBB5B6E1CFB6C816CB2EE3F45AC1E29B15DBD8207_AdjustorThunk (void);
extern void Tessellator_Constrain_mB83988169B046690C3D80CA111718DC809A3CCB0_AdjustorThunk (void);
extern void Tessellator_RemoveExterior_mC35CA28B5C5C1A6457C34715EA3649E566A14B00_AdjustorThunk (void);
extern void Tessellator_RemoveInterior_m8C9F53C6E83C695B2774C0083D633A8A2D5778B7_AdjustorThunk (void);
extern void Tessellator_Triangulate_mEDF1BD94E8DCCD0D95B061B6FAC8C118D580FA32_AdjustorThunk (void);
extern void Tessellator_Cleanup_mD62652EF03E447E90D6374B21895AB59072A3B75_AdjustorThunk (void);
extern void TestHullPointL_Test_mFFB799A0A608010AA7A00AECB121CA485508D3FC_AdjustorThunk (void);
extern void TestHullPointU_Test_mFB4072C78BD14C4254E4466A6910EC43B48F806F_AdjustorThunk (void);
extern void TestHullEventLe_Test_mB26988EC179628C07833DB1923209744350C7852_AdjustorThunk (void);
extern void TestHullEventE_Test_m3F5754510484A9A9775553F649BF5D1576A13689_AdjustorThunk (void);
extern void TestEdgePointE_Test_m0A78F3EABC68CD0992FB01DD956897B480542A77_AdjustorThunk (void);
extern void TestCellE_Test_mD7712C00B9AFEF847F2C2DD7599CD79F18470A4E_AdjustorThunk (void);
extern void XCompare_Compare_m2C8C02429EAA63BC352FAB454EFC619735DD0FD4_AdjustorThunk (void);
extern void IntersectionCompare_Compare_mE4DE2982303B88DBD5D08E01BDEBBCA7AB2EB949_AdjustorThunk (void);
extern void TessEventCompare_Compare_mC9C917FCD344EC3C154098A7C3DA095493F2BE64_AdjustorThunk (void);
extern void TessEdgeCompare_Compare_mD9295D4C8DFA530C2A527AF00B76E90D71F92DB2_AdjustorThunk (void);
extern void TessCellCompare_Compare_mBAE987CDAB366826FCD0DF8D442AA9A4FB688351_AdjustorThunk (void);
extern void TessJunctionCompare_Compare_m01205F9F920EF4FA358BC4D6DDF1566F197FDCA7_AdjustorThunk (void);
extern void DelaEdgeCompare_Compare_m12BDA8FF5BB8871BF1A0A2994584DD3CB4776815_AdjustorThunk (void);
extern void TessLink_Find_mFAFEEFC0DE81DE074103B5B1E11B898D532FDF05_AdjustorThunk (void);
extern void TessLink_Link_m74A93722E653788463D9BAF7176D259A210150E7_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[35] = 
{
	{ 0x06000039, Tessellator_SetAllocator_mD0233B1C112FFE7BFB90F4A13722AAAD49EF3C46_AdjustorThunk },
	{ 0x0600003A, Tessellator_AddPoint_mBDF7196470641275050A71A48B5CF45404F370B8_AdjustorThunk },
	{ 0x0600003D, Tessellator_SplitHulls_m41280B60D1379FF22538AE33BF6BECF5FD78FD2E_AdjustorThunk },
	{ 0x0600003E, Tessellator_MergeHulls_mC03CD5E5E3CCF9C79960C2A7F528824CA3EC9352_AdjustorThunk },
	{ 0x06000040, Tessellator_PrepareDelaunay_mEAADB0F4F68B596340D857E668110C2A613CA4E8_AdjustorThunk },
	{ 0x06000041, Tessellator_OppositeOf_mB3604C8586A1358F603C27E05D24168F06257103_AdjustorThunk },
	{ 0x06000042, Tessellator_FindConstraint_mC0DED9A33B6967DBC980E841462CE7956833D599_AdjustorThunk },
	{ 0x06000043, Tessellator_AddTriangle_mDA63B6E6783AB998FABA70DC5014E49A74D6CD0B_AdjustorThunk },
	{ 0x06000044, Tessellator_RemovePair_m8BA0B1208FA0DB43FC7EC02062C2D58784A656A5_AdjustorThunk },
	{ 0x06000045, Tessellator_RemoveTriangle_mF1C2F14B224CDEF0E6389CF24790EEC9D48DA2A8_AdjustorThunk },
	{ 0x06000046, Tessellator_EdgeFlip_m2EFC2678083E4647B8993B6AD464544BD9C89BE4_AdjustorThunk },
	{ 0x06000047, Tessellator_Flip_m8891166DD0D6693315A882BA942FDEFA7463CAE2_AdjustorThunk },
	{ 0x06000048, Tessellator_GetCells_m9A270E7F88ACA2B9A82DB96D416507C6F6306A5D_AdjustorThunk },
	{ 0x06000049, Tessellator_ApplyDelaunay_m22E8AFD34B62640DE1668419D66DDE47E9CB1F4E_AdjustorThunk },
	{ 0x0600004A, Tessellator_FindNeighbor_mBB5B6E1CFB6C816CB2EE3F45AC1E29B15DBD8207_AdjustorThunk },
	{ 0x0600004B, Tessellator_Constrain_mB83988169B046690C3D80CA111718DC809A3CCB0_AdjustorThunk },
	{ 0x0600004C, Tessellator_RemoveExterior_mC35CA28B5C5C1A6457C34715EA3649E566A14B00_AdjustorThunk },
	{ 0x0600004D, Tessellator_RemoveInterior_m8C9F53C6E83C695B2774C0083D633A8A2D5778B7_AdjustorThunk },
	{ 0x0600004E, Tessellator_Triangulate_mEDF1BD94E8DCCD0D95B061B6FAC8C118D580FA32_AdjustorThunk },
	{ 0x06000050, Tessellator_Cleanup_mD62652EF03E447E90D6374B21895AB59072A3B75_AdjustorThunk },
	{ 0x06000051, TestHullPointL_Test_mFFB799A0A608010AA7A00AECB121CA485508D3FC_AdjustorThunk },
	{ 0x06000052, TestHullPointU_Test_mFB4072C78BD14C4254E4466A6910EC43B48F806F_AdjustorThunk },
	{ 0x06000053, TestHullEventLe_Test_mB26988EC179628C07833DB1923209744350C7852_AdjustorThunk },
	{ 0x06000054, TestHullEventE_Test_m3F5754510484A9A9775553F649BF5D1576A13689_AdjustorThunk },
	{ 0x06000055, TestEdgePointE_Test_m0A78F3EABC68CD0992FB01DD956897B480542A77_AdjustorThunk },
	{ 0x06000056, TestCellE_Test_mD7712C00B9AFEF847F2C2DD7599CD79F18470A4E_AdjustorThunk },
	{ 0x06000058, XCompare_Compare_m2C8C02429EAA63BC352FAB454EFC619735DD0FD4_AdjustorThunk },
	{ 0x06000059, IntersectionCompare_Compare_mE4DE2982303B88DBD5D08E01BDEBBCA7AB2EB949_AdjustorThunk },
	{ 0x0600005A, TessEventCompare_Compare_mC9C917FCD344EC3C154098A7C3DA095493F2BE64_AdjustorThunk },
	{ 0x0600005B, TessEdgeCompare_Compare_mD9295D4C8DFA530C2A527AF00B76E90D71F92DB2_AdjustorThunk },
	{ 0x0600005C, TessCellCompare_Compare_mBAE987CDAB366826FCD0DF8D442AA9A4FB688351_AdjustorThunk },
	{ 0x0600005D, TessJunctionCompare_Compare_m01205F9F920EF4FA358BC4D6DDF1566F197FDCA7_AdjustorThunk },
	{ 0x0600005E, DelaEdgeCompare_Compare_m12BDA8FF5BB8871BF1A0A2994584DD3CB4776815_AdjustorThunk },
	{ 0x06000061, TessLink_Find_mFAFEEFC0DE81DE074103B5B1E11B898D532FDF05_AdjustorThunk },
	{ 0x06000062, TessLink_Link_m74A93722E653788463D9BAF7176D259A210150E7_AdjustorThunk },
};
static const int32_t s_InvokerIndices[147] = 
{
	34299,
	21016,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	24455,
	23785,
	23785,
	22273,
	21780,
	21922,
	21582,
	23242,
	21590,
	34252,
	27614,
	21853,
	24472,
	23243,
	21423,
	34252,
	24458,
	22082,
	22081,
	23561,
	22194,
	21589,
	34252,
	28259,
	15903,
	819,
	24448,
	26105,
	1359,
	1359,
	26159,
	6409,
	5649,
	5649,
	3622,
	3622,
	3622,
	7405,
	500,
	8336,
	4647,
	853,
	8336,
	8956,
	8957,
	1360,
	21588,
	21016,
	3135,
	3135,
	3134,
	3134,
	3180,
	3181,
	-1,
	5616,
	5841,
	5792,
	5841,
	5842,
	5841,
	5843,
	28319,
	32779,
	13157,
	7405,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	25770,
	25397,
	32408,
	27601,
	25770,
	25770,
	23786,
	23786,
	23786,
	21633,
	21633,
	21431,
	21494,
	21719,
	21718,
	21432,
	22050,
	22085,
	23285,
	21661,
	21580,
	21421,
	34252,
	24375,
	22033,
	23636,
	21850,
	24285,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
};
static const Il2CppTokenRangePair s_rgctxIndices[13] = 
{
	{ 0x02000004, { 0, 18 } },
	{ 0x02000005, { 18, 7 } },
	{ 0x02000006, { 25, 18 } },
	{ 0x02000007, { 43, 5 } },
	{ 0x02000027, { 91, 11 } },
	{ 0x02000028, { 102, 7 } },
	{ 0x06000063, { 48, 3 } },
	{ 0x06000064, { 51, 2 } },
	{ 0x06000065, { 53, 6 } },
	{ 0x06000066, { 59, 8 } },
	{ 0x06000067, { 67, 8 } },
	{ 0x06000068, { 75, 8 } },
	{ 0x06000069, { 83, 8 } },
};
extern const uint32_t g_rgctx_NativeArray_1_tFD678138A6B723B60C86ABB403EF534E0CA81B38;
extern const uint32_t g_rgctx_NativeArray_1__ctor_mBEDEBA7AF89677A027D08D4E77AF9D9334C40F75;
extern const uint32_t g_rgctx_Array_1_tCD2E34BCB8A3A0CF94A776B6F91DAA9F2F4620AD;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_mCF9544AC4CA6EAB11424703656673034210F5DA1;
extern const uint32_t g_rgctx_NativeArray_1_tFD678138A6B723B60C86ABB403EF534E0CA81B38;
extern const uint32_t g_rgctx_Array_1_get_Length_mA336ACD040D1D91B9C7E1991907F54A8B7D7C022;
extern const uint32_t g_rgctx_Array_1_tCD2E34BCB8A3A0CF94A776B6F91DAA9F2F4620AD;
extern const uint32_t g_rgctx_NativeArray_1_Copy_m7CB08B8FDBABCE04D084BAF0F3A2F82BA629FDA9;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_m9D55F9C56715C58110FEB743F33216E132AF1FBA;
extern const uint32_t g_rgctx_NativeArray_1_get_Item_m407B8006EE7B150B6D2832C818B860A6FD58449A;
extern const uint32_t g_rgctx_T_t463E1064E02EA3FBF49BA4C5516C92C2E84641FD;
extern const uint32_t g_rgctx_Array_1_ResizeIfRequired_m3DC8C998E6D02A774069C46D28150FF86762149F;
extern const uint32_t g_rgctx_NativeArray_1_set_Item_m70E78A4695FFA506AF2DB16EA24701BF667EE278;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_mF35BAE3DFEA2B4BABD342F8BBCE855DFB4FA010F;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_t463E1064E02EA3FBF49BA4C5516C92C2E84641FD_m58FEB474F5C02FBC353C02386D727BD98A6B9EAF;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafeReadOnlyPtr_TisT_t463E1064E02EA3FBF49BA4C5516C92C2E84641FD_m7948B16AC3C40304257E529C90669E0D05C0BF6C;
extern const uint32_t g_rgctx_TU5BU5D_t611D89302D6D717108AF0C4C4C0D0D99B804741B;
extern const uint32_t g_rgctx_NativeArray_1_CopyTo_mB26AE074E3D879FC0157C122BBF9A0F19254234A;
extern const uint32_t g_rgctx_Array_1_t93F099D799A2B23161C285BF46CB69E97A800D8F;
extern const uint32_t g_rgctx_ArrayDebugView_1_t1208AD5581E70DCBA5637A2600F849BE81A0CD4F;
extern const uint32_t g_rgctx_Array_1_get_Length_m903F7A744DD3870500B3E9F7934EFE54F7862EDA;
extern const uint32_t g_rgctx_Array_1_t93F099D799A2B23161C285BF46CB69E97A800D8F;
extern const uint32_t g_rgctx_TU5BU5D_t1D0CBC1334FF9A1EFFC2FD43C8D2E99F2A47C793;
extern const uint32_t g_rgctx_Array_1_CopyTo_m608EDD3AA69C18197ACF226D4A8EBD8050B8F0C8;
extern const uint32_t g_rgctx_TU5BU5D_t1D0CBC1334FF9A1EFFC2FD43C8D2E99F2A47C793;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tD346C48AC90F7B012511060202822443437302BC_m1E401090D942E6D2540B23EC6D8C8A35FBD38846;
extern const uint32_t g_rgctx_ArraySlice_1_t5891947A7D7F900C0BB164308C3248E51E273816;
extern const uint32_t g_rgctx_NativeArray_1_t0030584CB4212D38A5A81B7D0CB272A0643B0406;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_tD346C48AC90F7B012511060202822443437302BC_mBB8E829C33ADF9091863897C1B6CBDE90EC3B926;
extern const uint32_t g_rgctx_Array_1_t7A31793E2B4418BA7454F997765EA336CB97490B;
extern const uint32_t g_rgctx_Array_1_get_UnsafePtr_m86F2BF119CC9FE01A5C3B28337864C178F0007AB;
extern const uint32_t g_rgctx_Array_1_t7A31793E2B4418BA7454F997765EA336CB97490B;
extern const uint32_t g_rgctx_ArraySlice_1_Equals_mE656D098DCE425C86425BB7003E4589F15C68455;
extern const uint32_t g_rgctx_ArraySlice_1_t5891947A7D7F900C0BB164308C3248E51E273816;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElementWithStride_TisT_tD346C48AC90F7B012511060202822443437302BC_m8B928503BEE3416F1A10AC4B13BB9846BF832009;
extern const uint32_t g_rgctx_T_tD346C48AC90F7B012511060202822443437302BC;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElementWithStride_TisT_tD346C48AC90F7B012511060202822443437302BC_m76FF44721EA02C2FA9BDC84074E4BF0302B78FEA;
extern const uint32_t g_rgctx_TU5BU5D_tECB8B70702B299C39258C457BA5AD03C965F6F23;
extern const uint32_t g_rgctx_ArraySlice_1_GetUnsafeReadOnlyPtr_m72B57A6A417B88F4B6BCA53958256ACB45055DF7;
extern const uint32_t g_rgctx_ArraySlice_1_get_Stride_mD9CB2020F671B29E251EC4B8B5B45C3F6002C38B;
extern const uint32_t g_rgctx_ArraySlice_1_get_Length_mFB89DDC8F750768AEE3E852941C15C0896073524;
extern const uint32_t g_rgctx_TU5BU5D_tECB8B70702B299C39258C457BA5AD03C965F6F23;
extern const uint32_t g_rgctx_ArraySlice_1_CopyTo_mBD020A2FD6CC577FA1F86A1A36DD8D78B35F00D5;
extern const uint32_t g_rgctx_ArraySlice_1_t43EEEFBA3FF72ACFC1F3E17E8FBF636E6B8E26E4;
extern const uint32_t g_rgctx_ArraySliceDebugView_1_tBB0234E25997837B08A50E93334D773C5F7A3BFB;
extern const uint32_t g_rgctx_ArraySlice_1_ToArray_m361DC5DC480D1CAA292DB14D69909CB9EFB098DE;
extern const uint32_t g_rgctx_ArraySlice_1_t43EEEFBA3FF72ACFC1F3E17E8FBF636E6B8E26E4;
extern const uint32_t g_rgctx_TU5BU5D_t9CA872DF77D3E98E3AD05C68685622A4461B7551;
extern const uint32_t g_rgctx_NativeArray_1_t62216077CFCEE40D462C487ED754FE50B6A499D5;
extern const uint32_t g_rgctx_NativeArray_1_Copy_m75FA338BF8EECCDE0B4FDA5E6317076F89D92949;
extern const uint32_t g_rgctx_NativeArray_1_t62216077CFCEE40D462C487ED754FE50B6A499D5;
extern const uint32_t g_rgctx_NativeArray_1_t32D4BB89BCB8E0BDDA50F449E21DEF9F5C58E8D7;
extern const uint32_t g_rgctx_ModuleHandle_Copy_TisT_t1E76A25FD8A6B4F9096D9CEBE7E18055F295AA45_mAF82E0AA87FDCEB638396DFE2F704691625B1112;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tBF87611215034719F22E4011A6C7698E03198A33_m2C8CC7350E21221882AF80EFBDA7301377CC6BB0;
extern const uint32_t g_rgctx_T_tBF87611215034719F22E4011A6C7698E03198A33;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElement_TisT_tBF87611215034719F22E4011A6C7698E03198A33_mA4F5F7062702C782FCB6C590C7AC7C35C20B8F31;
extern const uint32_t g_rgctx_U_tBB4F8F8C81DAF4F3DF951D4EA51641AFB4917EAA;
extern const uint32_t g_rgctx_IComparer_1_t7BE3CE75029A377962854CA484BD3CC034962BF2;
extern const Il2CppRGCTXConstrainedData g_rgctx_U_tBB4F8F8C81DAF4F3DF951D4EA51641AFB4917EAA_IComparer_1_Compare_m31FA2DC466E60DBE86A2490657794DA50AAE4830;
extern const uint32_t g_rgctx_X_t1D3D9412D0C3C8D6C0CC5A012672696DE76BDE6B;
extern const uint32_t g_rgctx_NativeArray_1_tBB4726485E8E74739FF28776808474058802CE78;
extern const uint32_t g_rgctx_NativeArray_1_get_Item_m85892A890B5AE3D3E040A6D29409852CA12D82FC;
extern const uint32_t g_rgctx_NativeArray_1_tBB4726485E8E74739FF28776808474058802CE78;
extern const uint32_t g_rgctx_T_t3ADF1E2B4330C3669F6188A92B74D663E2CC24AE;
extern const uint32_t g_rgctx_U_t597FE175164EAA1E1243E65C38A3DFE6CA054D50;
extern const uint32_t g_rgctx_ICondition2_2_t64D84EDCF3CA3C57B37E1D97DEC6430C8E4F1D68;
extern const Il2CppRGCTXConstrainedData g_rgctx_X_t1D3D9412D0C3C8D6C0CC5A012672696DE76BDE6B_ICondition2_2_Test_mCC34CA90C50E831FF02D962D295D447F230719AE;
extern const uint32_t g_rgctx_X_tD2AC491EBE5C9CC43A6BC2EEBBD0461BCF95A98A;
extern const uint32_t g_rgctx_NativeArray_1_t2FF446C9C9931252191CB7BB47B060E69D52C61C;
extern const uint32_t g_rgctx_NativeArray_1_get_Item_m64EA01CBD95CE8D06AB06CB16BA8E3EBB7FA0DAE;
extern const uint32_t g_rgctx_NativeArray_1_t2FF446C9C9931252191CB7BB47B060E69D52C61C;
extern const uint32_t g_rgctx_T_tB42A37CEC8DFB947E2F6757BA1E0D9E1F317352E;
extern const uint32_t g_rgctx_U_tBFD60B805E7DE212EFBB8FBDB8422D06BEA6B9CC;
extern const uint32_t g_rgctx_ICondition2_2_tB2DA8FB2ADC49A0DB23D72E1555AAB25F85AE254;
extern const Il2CppRGCTXConstrainedData g_rgctx_X_tD2AC491EBE5C9CC43A6BC2EEBBD0461BCF95A98A_ICondition2_2_Test_m67CE85BCC50B202C1BDCEC63ECAF8AFBE7CD003A;
extern const uint32_t g_rgctx_X_tDC9A2714056A0DE615BFF1CBE41EAEBCCEE3B55C;
extern const uint32_t g_rgctx_Array_1_tEEF93D16AECBBA08275011EFD9B9046269424FC5;
extern const uint32_t g_rgctx_Array_1_get_Item_m26456A0CD3F1216D83930C148F6D2FEAE08CBF01;
extern const uint32_t g_rgctx_Array_1_tEEF93D16AECBBA08275011EFD9B9046269424FC5;
extern const uint32_t g_rgctx_T_tA5C02707C38A3FC7AFFEEE02D5AD9B6C21FA4692;
extern const uint32_t g_rgctx_U_tD25FB345044D3C134155EC690DA7DC459925EF3A;
extern const uint32_t g_rgctx_ICondition2_2_t8F5DA8702AE25A059EA4F4766D17D9193D1BE275;
extern const Il2CppRGCTXConstrainedData g_rgctx_X_tDC9A2714056A0DE615BFF1CBE41EAEBCCEE3B55C_ICondition2_2_Test_mF3C3308CB1C7867D93D2D74E17B0D1B0BEFC115B;
extern const uint32_t g_rgctx_X_t275A14E0DB3474F2E73A70FD1BB99DB3FAD3AD72;
extern const uint32_t g_rgctx_NativeArray_1_t044DDF29500FDCFA713014DA06581466DE3250EF;
extern const uint32_t g_rgctx_NativeArray_1_get_Item_mCB04929D823975CE32E27FEE6BB192D77B972D3C;
extern const uint32_t g_rgctx_NativeArray_1_t044DDF29500FDCFA713014DA06581466DE3250EF;
extern const uint32_t g_rgctx_T_t8F71DFFB681815CAF23766214B6C948493D46548;
extern const uint32_t g_rgctx_U_t5367D96CCB13B6D8932AE1D78DE77609AD13984B;
extern const uint32_t g_rgctx_ICondition2_2_tBC201C4976CA5A900DC43FA82740A8BADF2E7FB9;
extern const Il2CppRGCTXConstrainedData g_rgctx_X_t275A14E0DB3474F2E73A70FD1BB99DB3FAD3AD72_ICondition2_2_Test_m00C4C426394ADD7987B6535DC0631D38A641AE9F;
extern const uint32_t g_rgctx_MatrixMxN_1_t7F4CE80357750D025F3ABD9FC65D852321968DB3;
extern const uint32_t g_rgctx_NativeArray_1_t2DDFCB51662FAA57DA966A83985A1DED2E2BB775;
extern const uint32_t g_rgctx_NativeArray_1__ctor_mEE0CD62C163DDA311D4CC26874EA4E54E0A2B970;
extern const uint32_t g_rgctx_NativeArray_1_get_Item_mCD9AEF1D4B1090DBE4844F2638E26D5685646968;
extern const uint32_t g_rgctx_NativeArray_1_t2DDFCB51662FAA57DA966A83985A1DED2E2BB775;
extern const uint32_t g_rgctx_T_t66F7E20077C28ADFCA962FA197D479353432DF9D;
extern const uint32_t g_rgctx_NativeArray_1_set_Item_m2548B4F66A779E888D70A36E4B0207255EA684A1;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_m39B1D8D1C00EBF1D2ECF5B08BBF8A6AF4D50FD61;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_m34DD452AF271CAEB0BB68E8D7B4103DD2D79D4DE;
extern const uint32_t g_rgctx_TU5BU5D_tD376A0CA284839428076A755C3CBB82673D1104D;
extern const uint32_t g_rgctx_NativeArray_1_CopyTo_m10078F8DE962FB542EAFF0FAE4E0D6696C114B18;
extern const uint32_t g_rgctx_MatrixMxN_1_tCA07DD352D5D174D4E7E956DE1052692046C1D58;
extern const uint32_t g_rgctx_MatrixMxNDebugView_1_t4D7F31F94CD9E65B1A23838F54582BA313B337E1;
extern const uint32_t g_rgctx_MatrixMxN_1_get_Length_m833F3CCD8699FD27A1DD19A22825A669E95B71D1;
extern const uint32_t g_rgctx_MatrixMxN_1_tCA07DD352D5D174D4E7E956DE1052692046C1D58;
extern const uint32_t g_rgctx_TU5BU5D_t33962E548A4DEA398EBD4A06374DC1C9FD189315;
extern const uint32_t g_rgctx_MatrixMxN_1_CopyTo_m8AADFF7D5372B675DF4A0C458B1082DCC593335D;
extern const uint32_t g_rgctx_TU5BU5D_t33962E548A4DEA398EBD4A06374DC1C9FD189315;
static const Il2CppRGCTXDefinition s_rgctxValues[109] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tFD678138A6B723B60C86ABB403EF534E0CA81B38 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_mBEDEBA7AF89677A027D08D4E77AF9D9334C40F75 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array_1_tCD2E34BCB8A3A0CF94A776B6F91DAA9F2F4620AD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_mCF9544AC4CA6EAB11424703656673034210F5DA1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tFD678138A6B723B60C86ABB403EF534E0CA81B38 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_1_get_Length_mA336ACD040D1D91B9C7E1991907F54A8B7D7C022 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array_1_tCD2E34BCB8A3A0CF94A776B6F91DAA9F2F4620AD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Copy_m7CB08B8FDBABCE04D084BAF0F3A2F82BA629FDA9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_m9D55F9C56715C58110FEB743F33216E132AF1FBA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Item_m407B8006EE7B150B6D2832C818B860A6FD58449A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t463E1064E02EA3FBF49BA4C5516C92C2E84641FD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_1_ResizeIfRequired_m3DC8C998E6D02A774069C46D28150FF86762149F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_set_Item_m70E78A4695FFA506AF2DB16EA24701BF667EE278 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_mF35BAE3DFEA2B4BABD342F8BBCE855DFB4FA010F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_t463E1064E02EA3FBF49BA4C5516C92C2E84641FD_m58FEB474F5C02FBC353C02386D727BD98A6B9EAF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafeReadOnlyPtr_TisT_t463E1064E02EA3FBF49BA4C5516C92C2E84641FD_m7948B16AC3C40304257E529C90669E0D05C0BF6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t611D89302D6D717108AF0C4C4C0D0D99B804741B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_CopyTo_mB26AE074E3D879FC0157C122BBF9A0F19254234A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array_1_t93F099D799A2B23161C285BF46CB69E97A800D8F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayDebugView_1_t1208AD5581E70DCBA5637A2600F849BE81A0CD4F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_1_get_Length_m903F7A744DD3870500B3E9F7934EFE54F7862EDA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array_1_t93F099D799A2B23161C285BF46CB69E97A800D8F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t1D0CBC1334FF9A1EFFC2FD43C8D2E99F2A47C793 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_1_CopyTo_m608EDD3AA69C18197ACF226D4A8EBD8050B8F0C8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t1D0CBC1334FF9A1EFFC2FD43C8D2E99F2A47C793 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tD346C48AC90F7B012511060202822443437302BC_m1E401090D942E6D2540B23EC6D8C8A35FBD38846 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySlice_1_t5891947A7D7F900C0BB164308C3248E51E273816 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t0030584CB4212D38A5A81B7D0CB272A0643B0406 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_tD346C48AC90F7B012511060202822443437302BC_mBB8E829C33ADF9091863897C1B6CBDE90EC3B926 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array_1_t7A31793E2B4418BA7454F997765EA336CB97490B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_1_get_UnsafePtr_m86F2BF119CC9FE01A5C3B28337864C178F0007AB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array_1_t7A31793E2B4418BA7454F997765EA336CB97490B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySlice_1_Equals_mE656D098DCE425C86425BB7003E4589F15C68455 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySlice_1_t5891947A7D7F900C0BB164308C3248E51E273816 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElementWithStride_TisT_tD346C48AC90F7B012511060202822443437302BC_m8B928503BEE3416F1A10AC4B13BB9846BF832009 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD346C48AC90F7B012511060202822443437302BC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElementWithStride_TisT_tD346C48AC90F7B012511060202822443437302BC_m76FF44721EA02C2FA9BDC84074E4BF0302B78FEA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tECB8B70702B299C39258C457BA5AD03C965F6F23 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySlice_1_GetUnsafeReadOnlyPtr_m72B57A6A417B88F4B6BCA53958256ACB45055DF7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySlice_1_get_Stride_mD9CB2020F671B29E251EC4B8B5B45C3F6002C38B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySlice_1_get_Length_mFB89DDC8F750768AEE3E852941C15C0896073524 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tECB8B70702B299C39258C457BA5AD03C965F6F23 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySlice_1_CopyTo_mBD020A2FD6CC577FA1F86A1A36DD8D78B35F00D5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySlice_1_t43EEEFBA3FF72ACFC1F3E17E8FBF636E6B8E26E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySliceDebugView_1_tBB0234E25997837B08A50E93334D773C5F7A3BFB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySlice_1_ToArray_m361DC5DC480D1CAA292DB14D69909CB9EFB098DE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySlice_1_t43EEEFBA3FF72ACFC1F3E17E8FBF636E6B8E26E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t9CA872DF77D3E98E3AD05C68685622A4461B7551 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t62216077CFCEE40D462C487ED754FE50B6A499D5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Copy_m75FA338BF8EECCDE0B4FDA5E6317076F89D92949 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t62216077CFCEE40D462C487ED754FE50B6A499D5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t32D4BB89BCB8E0BDDA50F449E21DEF9F5C58E8D7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ModuleHandle_Copy_TisT_t1E76A25FD8A6B4F9096D9CEBE7E18055F295AA45_mAF82E0AA87FDCEB638396DFE2F704691625B1112 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tBF87611215034719F22E4011A6C7698E03198A33_m2C8CC7350E21221882AF80EFBDA7301377CC6BB0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tBF87611215034719F22E4011A6C7698E03198A33 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElement_TisT_tBF87611215034719F22E4011A6C7698E03198A33_mA4F5F7062702C782FCB6C590C7AC7C35C20B8F31 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_tBB4F8F8C81DAF4F3DF951D4EA51641AFB4917EAA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IComparer_1_t7BE3CE75029A377962854CA484BD3CC034962BF2 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_U_tBB4F8F8C81DAF4F3DF951D4EA51641AFB4917EAA_IComparer_1_Compare_m31FA2DC466E60DBE86A2490657794DA50AAE4830 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_X_t1D3D9412D0C3C8D6C0CC5A012672696DE76BDE6B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tBB4726485E8E74739FF28776808474058802CE78 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Item_m85892A890B5AE3D3E040A6D29409852CA12D82FC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tBB4726485E8E74739FF28776808474058802CE78 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3ADF1E2B4330C3669F6188A92B74D663E2CC24AE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t597FE175164EAA1E1243E65C38A3DFE6CA054D50 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICondition2_2_t64D84EDCF3CA3C57B37E1D97DEC6430C8E4F1D68 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_X_t1D3D9412D0C3C8D6C0CC5A012672696DE76BDE6B_ICondition2_2_Test_mCC34CA90C50E831FF02D962D295D447F230719AE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_X_tD2AC491EBE5C9CC43A6BC2EEBBD0461BCF95A98A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t2FF446C9C9931252191CB7BB47B060E69D52C61C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Item_m64EA01CBD95CE8D06AB06CB16BA8E3EBB7FA0DAE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t2FF446C9C9931252191CB7BB47B060E69D52C61C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB42A37CEC8DFB947E2F6757BA1E0D9E1F317352E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_tBFD60B805E7DE212EFBB8FBDB8422D06BEA6B9CC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICondition2_2_tB2DA8FB2ADC49A0DB23D72E1555AAB25F85AE254 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_X_tD2AC491EBE5C9CC43A6BC2EEBBD0461BCF95A98A_ICondition2_2_Test_m67CE85BCC50B202C1BDCEC63ECAF8AFBE7CD003A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_X_tDC9A2714056A0DE615BFF1CBE41EAEBCCEE3B55C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array_1_tEEF93D16AECBBA08275011EFD9B9046269424FC5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_1_get_Item_m26456A0CD3F1216D83930C148F6D2FEAE08CBF01 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array_1_tEEF93D16AECBBA08275011EFD9B9046269424FC5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA5C02707C38A3FC7AFFEEE02D5AD9B6C21FA4692 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_tD25FB345044D3C134155EC690DA7DC459925EF3A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICondition2_2_t8F5DA8702AE25A059EA4F4766D17D9193D1BE275 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_X_tDC9A2714056A0DE615BFF1CBE41EAEBCCEE3B55C_ICondition2_2_Test_mF3C3308CB1C7867D93D2D74E17B0D1B0BEFC115B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_X_t275A14E0DB3474F2E73A70FD1BB99DB3FAD3AD72 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t044DDF29500FDCFA713014DA06581466DE3250EF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Item_mCB04929D823975CE32E27FEE6BB192D77B972D3C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t044DDF29500FDCFA713014DA06581466DE3250EF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8F71DFFB681815CAF23766214B6C948493D46548 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t5367D96CCB13B6D8932AE1D78DE77609AD13984B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICondition2_2_tBC201C4976CA5A900DC43FA82740A8BADF2E7FB9 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_X_t275A14E0DB3474F2E73A70FD1BB99DB3FAD3AD72_ICondition2_2_Test_m00C4C426394ADD7987B6535DC0631D38A641AE9F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MatrixMxN_1_t7F4CE80357750D025F3ABD9FC65D852321968DB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t2DDFCB51662FAA57DA966A83985A1DED2E2BB775 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_mEE0CD62C163DDA311D4CC26874EA4E54E0A2B970 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Item_mCD9AEF1D4B1090DBE4844F2638E26D5685646968 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t2DDFCB51662FAA57DA966A83985A1DED2E2BB775 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t66F7E20077C28ADFCA962FA197D479353432DF9D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_set_Item_m2548B4F66A779E888D70A36E4B0207255EA684A1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_m39B1D8D1C00EBF1D2ECF5B08BBF8A6AF4D50FD61 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_m34DD452AF271CAEB0BB68E8D7B4103DD2D79D4DE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tD376A0CA284839428076A755C3CBB82673D1104D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_CopyTo_m10078F8DE962FB542EAFF0FAE4E0D6696C114B18 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MatrixMxN_1_tCA07DD352D5D174D4E7E956DE1052692046C1D58 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MatrixMxNDebugView_1_t4D7F31F94CD9E65B1A23838F54582BA313B337E1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MatrixMxN_1_get_Length_m833F3CCD8699FD27A1DD19A22825A669E95B71D1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MatrixMxN_1_tCA07DD352D5D174D4E7E956DE1052692046C1D58 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t33962E548A4DEA398EBD4A06374DC1C9FD189315 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MatrixMxN_1_CopyTo_m8AADFF7D5372B675DF4A0C458B1082DCC593335D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t33962E548A4DEA398EBD4A06374DC1C9FD189315 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_2D_Common_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_Unity_2D_Common_Runtime_CodeGenModule = 
{
	"Unity.2D.Common.Runtime.dll",
	147,
	s_methodPointers,
	35,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	13,
	s_rgctxIndices,
	109,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
