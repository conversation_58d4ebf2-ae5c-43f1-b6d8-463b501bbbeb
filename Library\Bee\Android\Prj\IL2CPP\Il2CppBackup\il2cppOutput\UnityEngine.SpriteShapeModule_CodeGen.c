﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25 (void);
extern void SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A (void);
extern void SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1 (void);
extern void SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381 (void);
extern void SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3 (void);
extern void SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13 (void);
extern void SpriteShapeRenderer_get_color_mB5F692D74527187556CAE5F02D93EA792B03D45F (void);
extern void SpriteShapeRenderer_set_color_mD5C6A8ADF2BA925EBA287105C5450251004E0E51 (void);
extern void SpriteShapeRenderer_Prepare_m3EB1D60213EC54306CEC059519D22E2754072950 (void);
extern void SpriteShapeRenderer_SetSegmentCount_m649276042B95E37EE26246F371675D7123480EC5 (void);
extern void SpriteShapeRenderer_SetMeshDataCount_m7F8F41487DABF81F7DF3B4334452EE0C8A7095F6 (void);
extern void SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF (void);
extern void SpriteShapeRenderer_GetDataInfo_m8BEE8EE1332F043EA4FF0CFDF2F0C2882BDF800D (void);
extern void SpriteShapeRenderer_GetChannelInfo_m6B622671C214A2920BAC1DF0190DEC62ABA7BADA (void);
extern void SpriteShapeRenderer_SetLocalAABB_m50672DFB7F3EF0AB13FF725A86EA3DA718C8F080 (void);
extern void SpriteShapeRenderer_GetSplineMeshCount_mB0789A1FCA7CCC40FA3083EF173146088719A62A (void);
extern void SpriteShapeRenderer_GetBounds_mB1109C67BE9B7A2376B92299C07B89E25026E42A (void);
extern void SpriteShapeRenderer_GetSegments_m20EAF8C9AA9B74C31053A0F69B60B15D7967AA20 (void);
extern void SpriteShapeRenderer_GetChannels_mBFF908DA30D2D2A9650F917211D83F6A5795D755 (void);
extern void SpriteShapeRenderer_GetChannels_m08BD580C754823A2578D887BEEF70AA43019DB8F (void);
extern void SpriteShapeRenderer__ctor_m44BF2C8BE32D32910B2E162D0830BFC36694D6A6 (void);
extern void SpriteShapeRenderer_get_color_Injected_m7296028C209DA05BE4B62862161D7F70540DCDAB (void);
extern void SpriteShapeRenderer_set_color_Injected_m18CB0FE6B1F567AA8C40356E6EC5841920AF7A75 (void);
extern void SpriteShapeRenderer_Prepare_Injected_mDA45CC3C11063151FB393BA8E8B44867BE975376 (void);
extern void SpriteShapeRenderer_SetSegmentCount_Injected_mBF100934A747787A951947B49F38787982470AB5 (void);
extern void SpriteShapeRenderer_SetMeshDataCount_Injected_mDC7106ABC3B49CE5B5D354A61241F980D5A4128B (void);
extern void SpriteShapeRenderer_SetMeshChannelInfo_Injected_mB82C67188169523B1CBFC4F532D246CA9F910480 (void);
extern void SpriteShapeRenderer_GetDataInfo_Injected_mC6251133ED4834D29B479EF13B211A73817A487F (void);
extern void SpriteShapeRenderer_GetChannelInfo_Injected_m027D34C874A1A16F2BB36E548C887AF5B62BDCF2 (void);
extern void SpriteShapeRenderer_SetLocalAABB_Injected_m4D381B8A76DF76FB7AA8A52D44C8EB2802B19124 (void);
extern void SpriteShapeRenderer_GetSplineMeshCount_Injected_m8EB1F051BF5101331D1E585EBE5CF54B71D439CA (void);
static Il2CppMethodPointer s_methodPointers[33] = 
{
	SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25,
	SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A,
	SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1,
	SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381,
	SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3,
	SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13,
	SpriteShapeRenderer_get_color_mB5F692D74527187556CAE5F02D93EA792B03D45F,
	SpriteShapeRenderer_set_color_mD5C6A8ADF2BA925EBA287105C5450251004E0E51,
	SpriteShapeRenderer_Prepare_m3EB1D60213EC54306CEC059519D22E2754072950,
	NULL,
	NULL,
	SpriteShapeRenderer_SetSegmentCount_m649276042B95E37EE26246F371675D7123480EC5,
	SpriteShapeRenderer_SetMeshDataCount_m7F8F41487DABF81F7DF3B4334452EE0C8A7095F6,
	SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF,
	SpriteShapeRenderer_GetDataInfo_m8BEE8EE1332F043EA4FF0CFDF2F0C2882BDF800D,
	SpriteShapeRenderer_GetChannelInfo_m6B622671C214A2920BAC1DF0190DEC62ABA7BADA,
	SpriteShapeRenderer_SetLocalAABB_m50672DFB7F3EF0AB13FF725A86EA3DA718C8F080,
	SpriteShapeRenderer_GetSplineMeshCount_mB0789A1FCA7CCC40FA3083EF173146088719A62A,
	SpriteShapeRenderer_GetBounds_mB1109C67BE9B7A2376B92299C07B89E25026E42A,
	SpriteShapeRenderer_GetSegments_m20EAF8C9AA9B74C31053A0F69B60B15D7967AA20,
	SpriteShapeRenderer_GetChannels_mBFF908DA30D2D2A9650F917211D83F6A5795D755,
	SpriteShapeRenderer_GetChannels_m08BD580C754823A2578D887BEEF70AA43019DB8F,
	SpriteShapeRenderer__ctor_m44BF2C8BE32D32910B2E162D0830BFC36694D6A6,
	SpriteShapeRenderer_get_color_Injected_m7296028C209DA05BE4B62862161D7F70540DCDAB,
	SpriteShapeRenderer_set_color_Injected_m18CB0FE6B1F567AA8C40356E6EC5841920AF7A75,
	SpriteShapeRenderer_Prepare_Injected_mDA45CC3C11063151FB393BA8E8B44867BE975376,
	SpriteShapeRenderer_SetSegmentCount_Injected_mBF100934A747787A951947B49F38787982470AB5,
	SpriteShapeRenderer_SetMeshDataCount_Injected_mDC7106ABC3B49CE5B5D354A61241F980D5A4128B,
	SpriteShapeRenderer_SetMeshChannelInfo_Injected_mB82C67188169523B1CBFC4F532D246CA9F910480,
	SpriteShapeRenderer_GetDataInfo_Injected_mC6251133ED4834D29B479EF13B211A73817A487F,
	SpriteShapeRenderer_GetChannelInfo_Injected_m027D34C874A1A16F2BB36E548C887AF5B62BDCF2,
	SpriteShapeRenderer_SetLocalAABB_Injected_m4D381B8A76DF76FB7AA8A52D44C8EB2802B19124,
	SpriteShapeRenderer_GetSplineMeshCount_Injected_m8EB1F051BF5101331D1E585EBE5CF54B71D439CA,
};
extern void SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25_AdjustorThunk (void);
extern void SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A_AdjustorThunk (void);
extern void SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1_AdjustorThunk (void);
extern void SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381_AdjustorThunk (void);
extern void SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3_AdjustorThunk (void);
extern void SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[6] = 
{
	{ 0x06000001, SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25_AdjustorThunk },
	{ 0x06000002, SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A_AdjustorThunk },
	{ 0x06000003, SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1_AdjustorThunk },
	{ 0x06000004, SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381_AdjustorThunk },
	{ 0x06000005, SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3_AdjustorThunk },
	{ 0x06000006, SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13_AdjustorThunk },
};
static const int32_t s_InvokerIndices[33] = 
{
	15903,
	20694,
	15903,
	20694,
	15903,
	15903,
	20556,
	15762,
	3677,
	-1,
	-1,
	15903,
	7405,
	3622,
	14049,
	14049,
	15754,
	20694,
	18826,
	8944,
	2552,
	1178,
	21016,
	28958,
	28958,
	24578,
	28962,
	26493,
	24590,
	26489,
	26489,
	28958,
	31784,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x0600000A, { 0, 2 } },
	{ 0x0600000B, { 2, 2 } },
};
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t5BC1F8928A1B35AC768A2C4B0239C04CEDA1FD56_mE6BDF655A67788463021A6D09555A3B92E856D7B;
extern const uint32_t g_rgctx_NativeArray_1_t669037BF55DFB05FFC88EED828421A071607779E;
extern const uint32_t g_rgctx_NativeSliceUnsafeUtility_ConvertExistingDataToNativeSlice_TisT_t987F30B32AF875C4AE5FF41A2CF0541B43BA72C8_mFBF5CBDC8F822FB2B34B6722DC374C229061FC2E;
extern const uint32_t g_rgctx_NativeSlice_1_t548FE7BFD18D3AE82BF650C973E1A93318CA56B8;
static const Il2CppRGCTXDefinition s_rgctxValues[4] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t5BC1F8928A1B35AC768A2C4B0239C04CEDA1FD56_mE6BDF655A67788463021A6D09555A3B92E856D7B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t669037BF55DFB05FFC88EED828421A071607779E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSliceUnsafeUtility_ConvertExistingDataToNativeSlice_TisT_t987F30B32AF875C4AE5FF41A2CF0541B43BA72C8_mFBF5CBDC8F822FB2B34B6722DC374C229061FC2E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_t548FE7BFD18D3AE82BF650C973E1A93318CA56B8 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_SpriteShapeModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_SpriteShapeModule_CodeGenModule = 
{
	"UnityEngine.SpriteShapeModule.dll",
	33,
	s_methodPointers,
	6,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	4,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
