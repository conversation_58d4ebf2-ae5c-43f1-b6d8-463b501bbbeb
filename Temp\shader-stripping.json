{"totalVariantsIn": 4273, "totalVariantsOut": 2481, "shaders": [{"inputVariants": 10, "outputVariants": 0, "name": "Cartoon FX/Remaster/Particle Ubershader", "pipelines": [{"inputVariants": 10, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 10, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06760000000000001}]}]}, {"inputVariants": 8, "outputVariants": 0, "name": "Particles/Standard Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0763}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Legacy Shaders/VertexLit", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0509}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Cartoon FX/Remaster/Particle Procedural Ring", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049800000000000004}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Universal Render Pipeline/Sampling", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "BoxDownsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.055400000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/CanvasBackground", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0516}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VoxelizeShader", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0402}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.04}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Universal Render Pipeline/Stop NaN", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Stop NaN (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.038200000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CoreSRP/CoreCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.044000000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "CopyMS (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0632}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Shadow2DShadowGeometry", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Draw Geometry Shadow (R) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10010000000000001}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Universal Render Pipeline/CameraMotionBlur", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Camera Motion Blur - Low Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0438}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera Motion Blur - Medium Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0419}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera Motion Blur - High Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0415}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera And Object Motion Blur - Low Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.040100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera And Object Motion Blur - Medium Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0369}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera And Object Motion Blur - High Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0674}]}]}, {"inputVariants": 6, "outputVariants": 4, "name": "Hidden/TerrainEngine/Details/UniversalPipeline/BillboardWavingDoublePass", "pipelines": [{"inputVariants": 6, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "ForwardLit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0575}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthNormals (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0456}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/Universal Render Pipeline/BokehDepthOfField", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.038200000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.038200000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.054400000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.040400000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.043500000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.038900000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0356}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.058100000000000006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.042}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/VFX/Empty", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0434}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Core/FallbackError", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.038}]}]}, {"inputVariants": 512, "outputVariants": 512, "name": "Hidden/Light2D", "pipelines": [{"inputVariants": 512, "outputVariants": 512, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 512, "outputVariants": 512, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.3899000000000001}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/XR/XROcclusionMesh", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0521}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Shadow2DShadowSprite", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Draw Sprite Shadow (R) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.040400000000000005}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Universal Render Pipeline/Edge Adaptive Spatial Upsampling", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "EASU (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0398}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal/HDRDebugView", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.038900000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0551}]}]}, {"inputVariants": 7, "outputVariants": 7, "name": "Hidden/Universal Render Pipeline/GaussianDepthOfField", "pipelines": [{"inputVariants": 7, "outputVariants": 7, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06430000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057800000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0449}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field Blur Vertical (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042300000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0408}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Universal Render Pipeline/Bloom", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Bloom Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06380000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bloom Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0572}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bloom Blur Vertical (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0388}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bloom Upsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "Hidden/Universal Render Pipeline/SubpixelMorphologicalAntialiasing", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0488}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046400000000000004}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0449}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Universal Render Pipeline/LutBuilderLdr", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "LutBuilderLdr (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0444}]}]}, {"inputVariants": 33, "outputVariants": 33, "name": "Hidden/Universal Render Pipeline/LensFlareDataDriven", "pipelines": [{"inputVariants": 33, "outputVariants": 33, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "LensFlareAdditive (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.060700000000000004}, {"inputVariants": 8, "outputVariants": 8, "variantName": "LensFlareScreen (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0592}, {"inputVariants": 8, "outputVariants": 8, "variantName": "LensFlarePremultiply (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06330000000000001}, {"inputVariants": 8, "outputVariants": 8, "variantName": "LensFlareLerp (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1268}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0519}]}]}, {"inputVariants": 5, "outputVariants": 5, "name": "Hidden/Universal Render Pipeline/LensFlareScreenSpace", "pipelines": [{"inputVariants": 5, "outputVariants": 5, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpac Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0534}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Downsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Upsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0539}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Composition (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0407}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Write to BloomTexture (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0466}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shadow2DUnshadowGeometry", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Geometry Unshadow (0) - Stencil: Ref 1, <PERSON><PERSON> <PERSON>, <PERSON> Replace (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0392}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Geometry Unshadow (B) - Stencil: Ref 0, <PERSON><PERSON> <PERSON>, <PERSON> Replace (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0381}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/CircularProgress", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.056400000000000006}]}]}, {"inputVariants": 29, "outputVariants": 29, "name": "Hidden/Universal/CoreBlit", "pipelines": [{"inputVariants": 29, "outputVariants": 29, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Nearest (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0414}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bilinear (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.047400000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "NearestQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0422}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "NearestQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.040400000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0381}, {"inputVariants": 1, "outputVariants": 1, "variantName": "NearestQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.05}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0439}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadPaddingOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0376}, {"inputVariants": 1, "outputVariants": 1, "variantName": "NearestQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.037200000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.037000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "NearestQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.038200000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0506}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadPaddingAlphaBlendOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.055900000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CubeToOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0446}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CubeToOctahedralLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0391}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CubeToOctahedralAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CubeToOctahedralRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0422}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041600000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0446}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0409}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NearestCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041100000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0429}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0424}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NearestDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0439}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/App UI/SVSquare", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0397}]}]}, {"inputVariants": 7, "outputVariants": 7, "name": "Hidden/Universal Render Pipeline/TemporalAA", "pipelines": [{"inputVariants": 7, "outputVariants": 7, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "TemporalAA - Accumulate - Quality Very Low (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0473}, {"inputVariants": 1, "outputVariants": 1, "variantName": "TemporalAA - Accumulate - Quality Low (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041100000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "TemporalAA - Accumulate - Quality Medium (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0378}, {"inputVariants": 1, "outputVariants": 1, "variantName": "TemporalAA - Accumulate - Quality High (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.037700000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Very High (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.039900000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "TemporalAA - Copy History (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.037200000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/PaniniProjection", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Panini Projection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0514}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/Universal/BlitHDROverlay", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "BilinearDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.037200000000000004}, {"inputVariants": 1, "outputVariants": 0, "variantName": "NearestDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0313}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/ColorSwatch", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.038200000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0396}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/LinearProgress", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0438}]}]}, {"inputVariants": 256, "outputVariants": 96, "name": "Hidden/Universal Render Pipeline/FinalPost", "pipelines": [{"inputVariants": 256, "outputVariants": 96, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 64, "outputVariants": 48, "variantName": "FinalPost (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.22160000000000002}, {"inputVariants": 64, "outputVariants": 0, "variantName": "FinalPostXR (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10070000000000001}, {"inputVariants": 64, "outputVariants": 48, "variantName": "FinalPost (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.1864}, {"inputVariants": 64, "outputVariants": 0, "variantName": "FinalPostXR (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.09730000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/App UI/ColorWheel", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.052500000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ShadowProjected2D", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Projected Shadow (R) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.05}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Projected Unshadow (G) - Stencil: Ref 1, <PERSON><PERSON> Eq, <PERSON> Keep (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0437}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Universal Render Pipeline/Debug/DebugReplacement", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Vertex Attributes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.040100000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal/CoreBlitColorAndDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ColorOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0604}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ColorAndDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0424}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/XR/XRMirrorView", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0461}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/Universal Render Pipeline/CopyDepth", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0603}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shadow2DUnshadowSprite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Sprite Unshadow (B) - Stencil: Ref 1, <PERSON><PERSON> <PERSON>, <PERSON> Replace (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0412}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Sprite Unshadow (B) - Stencil: Ref 0, <PERSON><PERSON> <PERSON>, <PERSON> Replace (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042800000000000005}]}]}, {"inputVariants": 11, "outputVariants": 11, "name": "Hidden/App UI/Box", "pipelines": [{"inputVariants": 11, "outputVariants": 11, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Clear (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0424}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BoxShadows (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06760000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BackgroundColor (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0625}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BackgroundImage (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0698}, {"inputVariants": 2, "outputVariants": 2, "variantName": "InsetBoxShadows (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.055}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Border (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0519}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Outline (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046700000000000005}]}]}, {"inputVariants": 26, "outputVariants": 6, "name": "Hidden/TerrainEngine/Details/UniversalPipeline/WavingDoublePass", "pipelines": [{"inputVariants": 26, "outputVariants": 6, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 24, "outputVariants": 4, "variantName": "ForwardLit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09480000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0472}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthNormalsOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042300000000000004}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Universal Render Pipeline/FallbackError", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.050100000000000006}]}]}, {"inputVariants": 1920, "outputVariants": 720, "name": "Hidden/Universal Render Pipeline/UberPost", "pipelines": [{"inputVariants": 1920, "outputVariants": 720, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 960, "outputVariants": 720, "variantName": "UberPost (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 2.5507}, {"inputVariants": 960, "outputVariants": 0, "variantName": "UberPostXR (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.9224}]}]}, {"inputVariants": 6, "outputVariants": 4, "name": "Hidden/Universal Render Pipeline/Scaling Setup", "pipelines": [{"inputVariants": 6, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 6, "outputVariants": 4, "variantName": "ScalingSetup (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06770000000000001}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Universal Render Pipeline/LutBuilderHdr", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "LutBuilderHdr (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.055200000000000006}]}]}, {"inputVariants": 18, "outputVariants": 8, "name": "Hidden/TerrainEngine/Details/UniversalPipeline/Vertexlit", "pipelines": [{"inputVariants": 18, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 8, "outputVariants": 4, "variantName": "TerrainDetailVertex (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0748}, {"inputVariants": 8, "outputVariants": 2, "variantName": "TerrainDetailVertex - GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.067}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0489}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthNormals (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06470000000000001}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/XR/XRMotionVector", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "XR Camera MotionVectors (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.054900000000000004}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/App UI/Mask", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0441}]}]}, {"inputVariants": 65, "outputVariants": 65, "name": "Shader Graphs/TileShader", "pipelines": [{"inputVariants": 65, "outputVariants": 65, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 32, "outputVariants": 32, "variantName": "Sprite Lit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.138}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Sprite Normal (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2288}, {"inputVariants": 32, "outputVariants": 32, "variantName": "Sprite Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1328}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/TextCore/Sprite", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0476}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/ReduceExpBias", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0408}]}]}, {"inputVariants": 43, "outputVariants": 43, "name": "Hidden/InferenceEngine/Activation", "pipelines": [{"inputVariants": 43, "outputVariants": 43, "pipeline": "", "variants": [{"inputVariants": 43, "outputVariants": 43, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.16110000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Dense", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09280000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Pad", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0734}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/Mat<PERSON>ul", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0734}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/RadialWrap/RadialWrap/Output Particle Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0584}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0654}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Transpose", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0692}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/InferenceEngine/ActivationInt", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0711}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Universal Render Pipeline/2D/Sprite-Unlit-Default", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0476}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0635}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Trilu", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0635}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/InstanceNormalizationTail", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.052000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Tile", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0478}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/TextureConversion/TensorToTexture", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0748}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/InferenceEngine/TextureTensorDataDownload", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0545}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/CumSum", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06720000000000001}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombRed/Muzzle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.062400000000000004}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0594}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/LayerNormalizationTail", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.04}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/IsInfNaN", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.056600000000000004}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/Up/DistoArrow", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.064}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0567}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/GatherElements", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.055400000000000005}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Win/Bubbles/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0592}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0601}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/TextureConversion/TextureToTensor", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1168}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/DepthwiseConv", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1153}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/ConvTranspose", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0855}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/DepthToSpace", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0704}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/SpaceToDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0512}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/InferenceEngine/ScatterND", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 20, "outputVariants": 20, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0984}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Reshape", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0722}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/Conv", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08170000000000001}]}]}, {"inputVariants": 36, "outputVariants": 36, "name": "Hidden/InferenceEngine/GridSample", "pipelines": [{"inputVariants": 36, "outputVariants": 36, "pipeline": "", "variants": [{"inputVariants": 36, "outputVariants": 36, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.16670000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/TextureConversion/ComputeBufferToTexture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0613}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Gather", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0747}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Cast", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049800000000000004}]}]}, {"inputVariants": 33, "outputVariants": 33, "name": "Hidden/InferenceEngine/Broadcast", "pipelines": [{"inputVariants": 33, "outputVariants": 33, "pipeline": "", "variants": [{"inputVariants": 33, "outputVariants": 33, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1351}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/BatchNormalization", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0884}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rainbow/Strip/Output ParticleStrip Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0717}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0648}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Gemm", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0609}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/TextureTensorDataUpload", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046200000000000005}]}]}, {"inputVariants": 18, "outputVariants": 18, "name": "Hidden/InferenceEngine/Reduce", "pipelines": [{"inputVariants": 18, "outputVariants": 18, "pipeline": "", "variants": [{"inputVariants": 18, "outputVariants": 18, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0932}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Softmax", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.048}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/RoiAlign", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.051800000000000006}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombBlue/Muzzle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0728}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06760000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Random", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0574}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/HardmaxEnd", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0528}]}]}, {"inputVariants": 40, "outputVariants": 40, "name": "Hidden/InferenceEngine/ScatterElements", "pipelines": [{"inputVariants": 40, "outputVariants": 40, "pipeline": "", "variants": [{"inputVariants": 40, "outputVariants": 40, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.16090000000000002}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/GatherND", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09040000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/ConstantOfShape", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0663}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Coin/Coin/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.067}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0604}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Where", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.047}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Copy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0492}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/RMSNormalizationTail", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0417}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "Hidden/InferenceEngine/Upsample", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "", "variants": [{"inputVariants": 9, "outputVariants": 9, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/SliceSet", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0659}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Expand", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0661}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/InferenceEngine/GlobalPool", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058800000000000005}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/GroupedConv", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0794}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Loose/Bubbles/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09630000000000001}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07050000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Split", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0649}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Shader Graph/FallbackError", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0514}]}]}, {"inputVariants": 36, "outputVariants": 36, "name": "Universal Render Pipeline/2D/Sprite-Lit-Default", "pipelines": [{"inputVariants": 36, "outputVariants": 36, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 32, "outputVariants": 32, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1306}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0488}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 2 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0478}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Universal Render Pipeline/2D/Sprite-Mask", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057800000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0472}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0405}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Range", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.044700000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/OneHot", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0432}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/InferenceEngine/Resize1D", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046400000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/LocalPool", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.052000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/ScalarMad", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0436}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/ReduceIndices", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06280000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Slice", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059500000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/ScaleBias", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0604}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/Up/Output Particle Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0553}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0558}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/LayoutSwitchBlockedAxis", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.050300000000000004}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/TopP", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0526}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombRed/Glow/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0604}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0582}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/Up/DistoTrail", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08120000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057600000000000005}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Win/Fishes/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0644}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0599}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rainbow/Bubble/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058100000000000006}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059800000000000006}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombBlue/Glow/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0863}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07490000000000001}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Coin/CoinGlow/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0626}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0629}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Loose/Fishes/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.055200000000000006}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0614}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/Up/Output Particle Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0524}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0512}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombRed/Circle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0758}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0678}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/Down/DistorArrow", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053500000000000006}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053500000000000006}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Win/Trail Bodies/Output ParticleStrip Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0531}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0835}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rainbow/Cicle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0664}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06520000000000001}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombBlue/Circle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0621}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061900000000000004}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Coin/StaticGlow/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0594}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0575}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Loose/Trail Bodies/Output ParticleStrip Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0664}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09240000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/Down/Output Particle Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058600000000000006}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.052000000000000005}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/Down/DistoTrail", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0692}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058600000000000006}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Win/Stars/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0897}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.068}]}]}, {"inputVariants": 70, "outputVariants": 70, "name": "Hidden/VFX/VFX_Rainbow/Gem/Output Particle Shader Graph Quad - Sprite Lit", "pipelines": [{"inputVariants": 70, "outputVariants": 70, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 64, "outputVariants": 64, "variantName": "Sprite Lit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2257}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Sprite Normal (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0606}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0558}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombBlue/Circle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0644}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.060700000000000004}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Coin/Circle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0663}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.14880000000000002}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Loose/Gems/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06330000000000001}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061700000000000005}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/Down/Output Particle Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0506}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049600000000000005}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/BubbleUp/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0563}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053700000000000005}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Win/Gems/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.056}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0528}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/System (5)/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06330000000000001}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0591}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/BubbleDown/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061200000000000004}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058800000000000005}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/System (6)/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057600000000000005}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0585}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/Trail Bodies/TrailUp", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.055600000000000004}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/Trail Bodies/Output ParticleStrip Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0575}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/Trail Bodies (1)/TrailDown", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0551}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.054700000000000006}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/Trail Bodies (1)/Output ParticleStrip Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0608}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058600000000000006}]}]}, {"inputVariants": 6, "outputVariants": 4, "name": "Cartoon FX/Remaster/Particle Procedural Ring", "pipelines": [{"inputVariants": 6, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "BASE (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.060200000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BASE (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057}, {"inputVariants": 2, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0442}]}]}, {"inputVariants": 18, "outputVariants": 8, "name": "Cartoon FX/Remaster/Particle Ubershader", "pipelines": [{"inputVariants": 18, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "BASE_URP (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.051500000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BASE_URP (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0529}, {"inputVariants": 10, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0429}]}]}, {"inputVariants": 20, "outputVariants": 12, "name": "Particles/Standard Unlit", "pipelines": [{"inputVariants": 20, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0521}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 4 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0654}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Mobile/Particles/Additive", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0726}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Mobile/Particles/Alpha Blended", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.050800000000000005}]}]}, {"inputVariants": 6, "outputVariants": 4, "name": "Legacy Shaders/VertexLit", "pipelines": [{"inputVariants": 6, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06860000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0623}, {"inputVariants": 2, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0408}]}]}, {"inputVariants": 17, "outputVariants": 17, "name": "Legacy Shaders/Diffuse", "pipelines": [{"inputVariants": 17, "outputVariants": 17, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1529}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10200000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08700000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-StencilWrite", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.24080000000000001}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/Internal-DepthNormalsTexture", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0772}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.06380000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.051800000000000006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0692}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.1009}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.055200000000000006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.062}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.0883}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.1019}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0722}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/Internal-ScreenSpaceShadows", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08940000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0776}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.08650000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.13490000000000002}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-CombineDepthNormals", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06760000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/BlitCopy", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0748}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/BlitCopyDepth", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08800000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/ConvertTexture", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06430000000000001}]}]}, {"inputVariants": 27, "outputVariants": 27, "name": "Hidden/Internal-DeferredShading", "pipelines": [{"inputVariants": 27, "outputVariants": 27, "pipeline": "", "variants": [{"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3309}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11860000000000001}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Internal-DeferredReflections", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07060000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0994}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Internal-MotionVectors", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1351}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06380000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0745}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-Flare", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.079}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-Halo", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0983}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/BlitCopyWithDepth", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09040000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/BlitToDepth", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0829}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/BlitToDepth_MSAA", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0853}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/BlitCopyHDRTonemap", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0782}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Internal-DebugPattern", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08120000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08030000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0834}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-GUITextureClip", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0771}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.07640000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-GUITextureClipText", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0898}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0782}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-GUITexture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.12010000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.076}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-GUITextureBlit", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06720000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0568}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-GUIRoundedRect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.055}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.13290000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-GUIRoundedRectWithColorPerBorder", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06420000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.064}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRDefault", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09730000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-UIRAtlasBlitCopy", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0747}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-UIRDefaultWorld", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0492}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Sprites/Default", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0592}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Sprites/Mask", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10490000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "UI/Default", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0646}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "UI/DefaultETC1", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0604}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/CubeBlur", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0444}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0448}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/CubeCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0426}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.043000000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/CubeBlend", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0471}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0417}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/VR/BlitTexArraySlice", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0656}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/Internal-ODSWorldTexture", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10740000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.048}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0424}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0373}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0455}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0388}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0403}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.0393}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0386}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.039400000000000004}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-CubemapToEquirect", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0692}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VR/BlitFromTex2DToTexArraySlice", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0502}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0436}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/TextCore/Distance Field SSD", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0451}]}]}]}