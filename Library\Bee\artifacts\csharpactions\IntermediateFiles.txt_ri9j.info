{"AndroidPlayerBuildProgram.Actions.ActionGenerateProjectFiles+Arguments": {"ProjectPath": "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle", "Architectures": "ARM64", "BuildSystem": "<PERSON><PERSON><PERSON>", "GradleProjectCreateInfo": {"ArtifactsPath": "Library/Bee/artifacts/Android", "EnvironmentVariableInputs": ["UNITY_THISISABUILDMACHINE:"], "HostPlatform": "Windows", "ApplicationType": "AppBundle", "BuildType": "Release", "AndroidSDKPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK", "AndroidNDKPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/NDK", "AndroidJavaPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\OpenJDK", "PreferredHeapSizeForJVM": 4096, "GradleVersion": "8.9", "ProjectFiles": {"UnityLibraryBuildGradle": {"SourcePath": "Assets/Plugins/Android\\mainTemplate.gradle", "RelativeDestinationPath": "unityLibrary/build.gradle", "CanBeModifiedByUser": true}, "LauncherBuildGradle": {"RelativeDestinationPath": "launcher/build.gradle", "CanBeModifiedByUser": true}, "LauncherSetupUnitySymbolsGradle": {"SourcePath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\GradleTemplates\\launcher/setupSymbols.gradle", "RelativeDestinationPath": "launcher/setupSymbols.gradle", "CanBeModifiedByUser": false}, "SharedKeepUnitySymbolsGradle": {"SourcePath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\GradleTemplates\\shared/keepUnitySymbols.gradle", "RelativeDestinationPath": "shared/keepUnitySymbols.gradle", "CanBeModifiedByUser": false}, "SharedCommonGradle": {"SourcePath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\GradleTemplates\\shared/common.gradle", "RelativeDestinationPath": "shared/common.gradle", "CanBeModifiedByUser": false}, "ProjectLevelBuildGradle": {"RelativeDestinationPath": "build.gradle", "CanBeModifiedByUser": true}, "GradleProperties": {"SourcePath": "Assets/Plugins/Android\\gradleTemplate.properties", "RelativeDestinationPath": "gradle.properties", "CanBeModifiedByUser": true}, "UnityProguard": {"SourcePath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\UnityProGuardTemplate.txt", "RelativeDestinationPath": "unityLibrary/proguard-unity.txt", "CanBeModifiedByUser": true}, "ProguardUser": {"SourcePath": "Assets/Plugins/Android\\proguard-user.txt", "RelativeDestinationPath": "unityLibrary/proguard-user.txt", "CanBeModifiedByUser": true}, "GradleSettings": {"SourcePath": "Assets/Plugins/Android\\settingsTemplate.gradle", "RelativeDestinationPath": "settings.gradle", "CanBeModifiedByUser": true}, "LocalProperties": {"RelativeDestinationPath": "local.properties", "CanBeModifiedByUser": true}}, "AdditionalLibrariesRelativePaths": ["unityLibrary/IronSource.androidlib/build.gradle", "unityLibrary/GooglePlayGamesManifest.androidlib/build.gradle"], "AdditionalUserInputs": ["F:\\Match2D\\Library\\ScriptAssemblies\\Unity.AppUI.Editor.dll"], "AdditionalUserOutputs": {"AdditionalManifests": [], "AdditionalBuildGradleFiles": [], "AdditionalGradleSettings": [], "AdditionalGradleProperties": [], "AdditionalFilesWithContents": []}, "UserCopyData": {"FilesToCopy": [{"SourceFile": "F:\\Match2D\\Library\\PackageCache\\com.unity.dt.app-ui@5d6dc8bfd8d9\\Runtime/Core/Platform/Android/Plugins/Android/AppUIGameActivity.java", "DestinationFile": "unityLibrary/src/main/java/com/unity3d/player/appui/AppUIGameActivity.java"}], "DirectoriesToCopy": []}, "AdditionalUserData": [], "BuildTools": "34.0.0", "TargetSDKVersion": 36, "MinSDKVersion": 23, "PackageName": "com.PhantomTeam.PufflandAdventure", "Architectures": "ARM64", "BuildApkPerCpuArchitecture": false, "DebugSymbols": {"Level": "None", "Format": "5"}, "VersionCode": 7, "VersionName": "0.2.01", "Minify": 3, "NoCompressOptions": {"RelativeFilePaths": [], "FileExtensions": [".json"]}, "UseCustomKeystore": true, "KeystorePath": "F:/Match2D/match3-release-key.keystore", "KeystoreName": "match3-release-key.keystore", "KeystorePassword": "kapusta12", "KeystoreAliasName": "match3_key", "KeystoreAliasPassword": "kapusta12", "ScriptingImplementation": "IL2CPP", "AndroidLibraries": [{"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/IronSource.androidlib", "Dependee": "UnityLibrary", "IsAffectedBySymlinkSources": false}, {"Path": "Assets/Plugins/Android/GooglePlayGamesManifest.androidlib", "Dependee": "UnityLibrary", "IsAffectedBySymlinkSources": false}], "AARFiles": [], "BuiltinJavaSourcePaths": ["com/unity3d/player/UnityPlayerGameActivity.java"], "JavaSourcePaths": [{"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/UnityLevelPlayBannerListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/UnityLevelPlayRewardedVideoListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/RewardedAd.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/IUnityBannerAdListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/UnitySegmentListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/InterstitialAd.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/UnityImpressionDataListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/AndroidBridgeConstants.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/BannerAd.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/IUnityRewardedAdListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.dt.app-ui@5d6dc8bfd8d9/Runtime/Core/Platform/Android/Plugins/Android/HapticFeedback.java", "PackageName": "com.unity3d.player.appui"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/LevelPlayRewardedVideoWrapper.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/AndroidBridge.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/IUnityLevelPlayInitListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/UnityInitializationListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/UnityLevelPlayInterstitialListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/BannerUtils.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/IUnityInterstitialAdListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/LevelPlayUtils.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/LevelPlayBridge.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/AndroidBridgeUtilities.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/UnityLevelPlayRewardedVideoManualListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e/Runtime/Plugins/Android/LevelPlayInterstitialWrapper.java", "PackageName": "com.ironsource.unity.androidbridge"}], "KotlinSourcePaths": [], "PlayerPackage": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer", "PlayerPackageTools": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools", "SymlinkSources": false, "InstallIntoBuildsFolder": false, "UnityPath": "", "UnityProjectPath": "F:/Match2D", "OverrideCMakeIntermdiateDirectory": true, "AssetPacks": [{"Name": "UnityDataAssetPack", "CreateGradleFile": true, "DeliveryType": {"Name": "install-time"}}], "Dependencies": ["com.google.android.play:asset-delivery:2.1.0"], "ApplicationEntry": "GameActivity", "JarFiles": ["classes.jar"], "UseOptimizedFramePacing": false, "ReportGooglePlayAppDependencies": true, "UnityVersion": "6000.2.0b9"}}, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "AndroidPlayerBuildProgram.Actions.ActionGenerateProjectFiles", "methodName": "Run", "assemblyLocation": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\AndroidPlayerBuildProgram.exe", "targets": ["Library/Bee/artifacts/Android/IntermediateFiles.txt", "Library/Bee/artifacts/Android/Gradle/build.gradle", "Library/Bee/artifacts/Android/Gradle/unityLibrary/build.gradle", "Library/Bee/artifacts/Android/Gradle/launcher/build.gradle", "Library/Bee/artifacts/Android/Gradle/gradle.properties", "Library/Bee/artifacts/Android/Gradle/local.properties", "Library/Bee/artifacts/Android/Gradle/settings.gradle", "Library/Bee/artifacts/Android/Gradle/build.gradle.xml", "Library/Bee/artifacts/Android/Gradle/launcher/build.gradle.xml", "Library/Bee/artifacts/Android/Gradle/local.properties.xml", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/setupSymbols.gradle", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/shared/keepUnitySymbols.gradle", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/shared/common.gradle", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/CMakeLists.txt", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/GAToUnityCallbacks.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/MacroEnd.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/MacroHeaderBegin.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/MacroSourceBegin.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAApplication.cpp", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAApplication.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAConfiguration.cpp", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAConfiguration.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGADebug.cpp", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGADebug.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAEntry.cpp", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAEvents.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInput.cpp", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInput.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInputKeyEvent.cpp", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInputMotionEvent.cpp", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboard.cpp", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboard.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboardCallbacks.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGATypes.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAVersion.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGACallbacks.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGAConfigurationCallbacks.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGAKeyEventCallbacks.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGAMotionEventCallbacks.h", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing/CMakeLists.txt", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt", "Library/Bee/artifacts/Android/Gradle/unityLibrary/IronSource.androidlib/build.gradle", "Library/Bee/artifacts/Android/Gradle/unityLibrary/IronSource.androidlib/build.gradle.xml", "Library/Bee/artifacts/Android/Gradle/unityLibrary/GooglePlayGamesManifest.androidlib/build.gradle", "Library/Bee/artifacts/Android/Gradle/unityLibrary/GooglePlayGamesManifest.androidlib/build.gradle.xml", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/UnityDataAssetPack/build.gradle", "Library/Bee/artifacts/Android/Gradle/unityLibrary/proguard-unity.txt", "F:/Match2D/Library/Bee/Android/Prj/IL2CPP/Gradle/gradle/wrapper/gradle-wrapper.properties"], "inputs": ["Assets/Plugins/Android/mainTemplate.gradle", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/launcher/setupSymbols.gradle", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/keepUnitySymbols.gradle", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/common.gradle", "Assets/Plugins/Android/gradleTemplate.properties", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/UnityProGuardTemplate.txt", "Assets/Plugins/Android/proguard-user.txt", "Assets/Plugins/Android/settingsTemplate.gradle", "Library/Bee/artifacts/Android/AssetPacks/assets_info.json", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/CMakeLists.txt", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/GAToUnityCallbacks.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/MacroEnd.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/MacroHeaderBegin.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/MacroSourceBegin.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/ReadMe.txt", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAApplication.cpp", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAApplication.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAConfiguration.cpp", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAConfiguration.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGADebug.cpp", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGADebug.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAEntry.cpp", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAEvents.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInput.cpp", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInput.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInputKeyEvent.cpp", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInputMotionEvent.cpp", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGASoftKeyboard.cpp", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGASoftKeyboard.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGASoftKeyboardCallbacks.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGATypes.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAVersion.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGACallbacks.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGAConfigurationCallbacks.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGAKeyEventCallbacks.h", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGAMotionEventCallbacks.h"], "targetDirectories": []}}