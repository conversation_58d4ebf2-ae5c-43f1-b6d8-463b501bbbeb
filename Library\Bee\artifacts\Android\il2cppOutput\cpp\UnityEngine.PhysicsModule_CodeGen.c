﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208 (void);
extern void ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6 (void);
extern void Collision_set_Flipped_m5AF63260E99357BB87655DDAD6316568BE65F3A1 (void);
extern void Collision_get_contacts_m2E8E27E0399230DFA4303A4F4D81C1BD55CBC473 (void);
extern void Collision__ctor_mC3F14BC1026130B6B0E6BB83D7431753C3484912 (void);
extern void Collision__ctor_m6A02AD9B6F96A755B3A3A3A280CC7D2533228DA7 (void);
extern void Collision_Reuse_mC2E21A6480EE1DCEAF71F2EAF3E0CAEFD42EA90C (void);
extern void BoxCollider_get_center_mC370C79F9FC9398D0DD080500FA2EE14FC6E36C7 (void);
extern void BoxCollider_set_center_m0AB0482699735FEE8306A7FCAAE66A76C479F0F0 (void);
extern void BoxCollider_get_size_mC1A2DD270B04DFF5961F9F90DC147C271F72258E (void);
extern void BoxCollider_set_size_m8374267FDE5DD628973E0E5E1331E781552B855A (void);
extern void BoxCollider__ctor_m7E069AACEC2B76129335D222FC8B8A63FD50656F (void);
extern void BoxCollider_get_center_Injected_m8DB81B1CFC1FB8FB4040BBACAD9EAA7AB5094855 (void);
extern void BoxCollider_set_center_Injected_mF45B8F9D3C124C4AD7BA370715F542B6AA24BF08 (void);
extern void BoxCollider_get_size_Injected_m7DCE0DAAB04E18055D954D944A07B16B840C88B6 (void);
extern void BoxCollider_set_size_Injected_m7F9B55933C41D7733F88A47AD0D728A6ED98DCCE (void);
extern void CapsuleCollider_set_height_m5DAE3DC5AD851E30C5A29AC7A22F36BE1E205BBB (void);
extern void CapsuleCollider_set_height_Injected_m600D99DBF9E6314735E7FB060F344774AD554CD0 (void);
extern void CharacterController_Move_mE3F7AC1B4A2D6955980811C088B68ED3A31D2DA4 (void);
extern void CharacterController_Move_Injected_mBEBD5E60205BE4989D4BA963EBE81C06157CE598 (void);
extern void Collider_get_enabled_mDBFB488088ADB14C8016A83EF445653AC5A4A12B (void);
extern void Collider_get_attachedRigidbody_m060304DB909A1FACD260EBB619D64D39129739AD (void);
extern void Collider_set_isTrigger_mFCD22F3EB5E28C97863956AB725D53F7F4B7CA78 (void);
extern void Collider_ClosestPoint_mFFF9B6F6CF9F18B22B325835A3E2E78A1C03BFCB (void);
extern void Collider_Raycast_mBFA55E4B9BD7EE4E8D4107ADF24D2FA0F165FA2C (void);
extern void Collider_Raycast_mD7683E94051173B3FFC0862F4A17847E94AEB938 (void);
extern void Collider__ctor_m8975C6CCFC0E5740C523DB4A52ACC7F4A021F8FA (void);
extern void Collider_get_enabled_Injected_mE8FB3B18306335574123BDAC8CD64B85252FC410 (void);
extern void Collider_get_attachedRigidbody_Injected_mFDBCE8D40EF92923428F133D7D2B38A6793EE317 (void);
extern void Collider_set_isTrigger_Injected_m55B9E744938E11F684A13DCE07B85691B6F07DD3 (void);
extern void Collider_ClosestPoint_Injected_m95F62FF6C5A90AAF6163B1EA7F48F353E077D3FB (void);
extern void Collider_Raycast_Injected_m165289A4749E303FF6D97066FD4831D83BF4C225 (void);
extern void Physics_OnSceneContactModify_m52106C18952BF4768B05F67FAF2B7F6F6F7D0C9D (void);
extern void Physics_PhysXOnSceneContactModify_m249A4E5A6A2967EA3CE7FD32ADD5851890D77AEE (void);
extern void Physics_get_invokeCollisionCallbacks_m73AE9C988EC57467D9B8699B376759D8E44133C8 (void);
extern void Physics_get_defaultPhysicsScene_mC5D2BC20734D32FB421163F066BD5FB4118C633A (void);
extern void Physics_Raycast_m453681A406AADE0A30227D955279F5E7050B790D (void);
extern void Physics_Raycast_m0679FB03C9AFC1E803B8F8AE6CAB409670D31377 (void);
extern void Physics_Raycast_mCFF84927BE3EC1780DBA34CCED374E7FF12ABCBE (void);
extern void Physics_Raycast_mCAA46C95211C7BB95697A347B036C012D26EB028 (void);
extern void Physics_Raycast_mA782767AD4F149FBEA32C71460DFF061B7563688 (void);
extern void Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12 (void);
extern void Physics_Raycast_m011EA7022C33B2C499EF744E5AF3E01EEB8FBD33 (void);
extern void Physics_Raycast_m1B27F500505FFB57D78548B9F5A540A2AD092903 (void);
extern void Physics_Raycast_m9879C28DFF6CD3048F2365BC01C855565EE141F8 (void);
extern void Physics_Raycast_m5CAA0AEDB2A6FB26E5F42A8EA560A61CAAF12E50 (void);
extern void Physics_Raycast_m7A0FEA813B93A82713C06D8466F0A21325743488 (void);
extern void Physics_Raycast_mDB89EB287ED040E534F6A933683A070D29DC14D3 (void);
extern void Physics_Raycast_mCCD2542138D11E665A5D4F413C1547EE7D794DEB (void);
extern void Physics_Raycast_m34AC1210E893A9EF969BD2C7104B10BE5B580025 (void);
extern void Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685 (void);
extern void Physics_Raycast_mCAC9F02A1AAB49E16B384EBC8318E2DF30F4B0E5 (void);
extern void Physics_SphereCast_mDB2140FE8561D0CE870037527DACC44AB18A346D (void);
extern void Physics_SphereCast_mE7656F9355B33AED9095D8A0301734611EC95B05 (void);
extern void Physics_SphereCast_mF6538C6C4E3A9BBD81B686437CC91F3A93C1F3E7 (void);
extern void Physics_Internal_RaycastAll_mC128593FD48E6F237BE59CFCDC7DDE7A4E8CB074 (void);
extern void Physics_RaycastAll_m8B7FB8419A65BEE78927D0EE84916E8DBE7ECD34 (void);
extern void Physics_RaycastAll_m69ED0FF0B70ADBC45B907783C87B308E786F6D51 (void);
extern void Physics_RaycastAll_mDCBE530EF2ACD21EAADEA829259291D7327BC80E (void);
extern void Physics_RaycastAll_mE56962F670046BE618FFE8D9B19595A896922789 (void);
extern void Physics_RaycastAll_mD1643DB52C4E415083E215B154FEB9DFA3AD6D74 (void);
extern void Physics_RaycastAll_m4055619E0F7EFA04620EAA0517F8393C4EBCFE87 (void);
extern void Physics_RaycastAll_m1BBD4E474814BEC9B52B015081A256AE2FE00468 (void);
extern void Physics_RaycastAll_mE94864EF8243F7D3A26C8666CEB02166C3742CB2 (void);
extern void Physics_RaycastNonAlloc_mB37DE98E8C9407C3DB2FB488BAB1CF3A7C6FFFCE (void);
extern void Physics_RaycastNonAlloc_m2BFEE9072E390ED6ACD500FD0AE4E714DE9549BC (void);
extern void Physics_RaycastNonAlloc_m1908CB5E0D0570E9C88B6C259041520DD4D3169C (void);
extern void Physics_RaycastNonAlloc_m1961CFCDB7631C7FF4D12F88904CF1BEB24A6C3E (void);
extern void Physics_RaycastNonAlloc_mB8FE279E06CE87D77387AA9A10562B8052DC8836 (void);
extern void Physics_RaycastNonAlloc_m4CFAA8CA088502DA71D748D276BDAAEF234B12B0 (void);
extern void Physics_RaycastNonAlloc_m3EEB10539C49FEAD9533142FEE6578148A48FFA9 (void);
extern void Physics_RaycastNonAlloc_mBDC9E19F4E3C82DCE03D799FDD41FB3314209460 (void);
extern void Physics_get_reuseCollisionCallbacks_mB23A11C02B893238B5631A38F2FBB4C63A3B7541 (void);
extern void Physics_GetColliderByInstanceID_m5EF4B0EA9F8B66F23105FDD89BA528F5BC7EA179 (void);
extern void Physics_GetBodyByInstanceID_m63D67631140FF644B6860598E4D0433FF93A72BA (void);
extern void Physics_SendOnCollisionEnter_mA48BA0630EE3D28320C602A15B5BDD887FA24144 (void);
extern void Physics_SendOnCollisionStay_m2CC0293E4757CF89183A125270047A5054515590 (void);
extern void Physics_SendOnCollisionExit_m6E96913C80E49A77FD367FBA5EF63755A81AA7D7 (void);
extern void Physics_OnSceneContact_mFE8673EF13DD22B5C727BF5E2EFC4CF5E410C73A (void);
extern void Physics_ReportContacts_m9E7B0F448F534C8DEBBA6E33D86506C2500C919D (void);
extern void Physics_GetCollisionToReport_m7D22E5DD29678C65830A05A249650C49947A5D4E (void);
extern void Physics__cctor_m1E2D4816C77050D34F6D01F43E63D7969410AE2A (void);
extern void Physics_get_defaultPhysicsScene_Injected_mE86AE6A398435C1754A824B2B35DF13126A6C5D6 (void);
extern void Physics_Internal_RaycastAll_Injected_m0100C5BF359DDE45F69B973D243D5B3CC0132754 (void);
extern void Physics_GetColliderByInstanceID_Injected_mD465052413F8AE5D815CF2409F71C0F6FA37DEAD (void);
extern void Physics_GetBodyByInstanceID_Injected_mBB2ACC563B87F52D7C14FB54CD5BC0A9EAF0A695 (void);
extern void Physics_SendOnCollisionEnter_Injected_m6E860F99570F9228DE3CD5D9AA7D8A87E189E960 (void);
extern void Physics_SendOnCollisionStay_Injected_m4300A15E1F6881C0191E3A407A9296662661951C (void);
extern void Physics_SendOnCollisionExit_Injected_m935DC6B1CA45455BE752CEB6F4C6C42FC9B72E71 (void);
extern void ContactEventDelegate__ctor_mF0844AC2AA36D48C199DDDBFA55627E43980CEEE (void);
extern void ContactEventDelegate_Invoke_m84BF3B9092BD4F6D43A870421F8389BC7B0E0769 (void);
extern void ContactPairHeader_get_body_m8B8DC4B023CB7078B425E3CE764CFF3088D37FC8 (void);
extern void ContactPairHeader_get_otherBody_m76BD1BDDE46A87839F214F741ECB0E0857EFAE0A (void);
extern void ContactPairHeader_get_hasRemovedBody_mE79EF42C5CB9FCDB476AC9437861F71CF7FC442A (void);
extern void ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09 (void);
extern void ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27 (void);
extern void ContactPair_get_collider_mC74190F4919E0DAC5E8994C975BE36244900BD6C (void);
extern void ContactPair_get_otherCollider_mA99232AB090D27DCC1561094DC20621F3DA764A8 (void);
extern void ContactPair_get_isCollisionEnter_m901546CD819E7891F6594A72AAEAE72FCB5515B1 (void);
extern void ContactPair_get_isCollisionExit_mD54D019EABA4B209633B41803D6EE0CA1B978DF1 (void);
extern void ContactPair_get_isCollisionStay_m62946484C4BD866D9AD91B23FE1C18336048E5F8 (void);
extern void ContactPair_get_hasRemovedCollider_m3DFB259C3645FA874C7AD304494222472B5184AA (void);
extern void ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC (void);
extern void ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454 (void);
extern void ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565 (void);
extern void ContactPairPoint_get_position_mD7D5690B579C6DF348EBBAD5523DAB1B90253C10 (void);
extern void ContactPairPoint_get_separation_m5DF7D504E1EC95B2E44D37EAF78C1DCEC49DA12E (void);
extern void ContactPairPoint_get_normal_m019987EB1D8883A212596E8BB418EB787050FECF (void);
extern void ContactPairPoint_get_impulse_m982A7672DBBC6C7D8636D0FC9D1A8D75CA313D1A (void);
extern void PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7 (void);
extern void PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814 (void);
extern void PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3 (void);
extern void PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1 (void);
extern void PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730 (void);
extern void PhysicsScene_Internal_RaycastTest_m729F4A577F5DD911131C5321EC28E44F98A60BA0 (void);
extern void PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE (void);
extern void PhysicsScene_Internal_Raycast_m0211A7BDE011181718838F063296D51F88D92E74 (void);
extern void PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3 (void);
extern void PhysicsScene_Internal_RaycastNonAlloc_mC339255AAFC484588C813D7BE2BDAE03797D26DB (void);
extern void PhysicsScene_Query_SphereCast_m8E6770FE64FB74157199217381AA1A99B3CF580B (void);
extern void PhysicsScene_Internal_SphereCast_mE4B0FBE790E2A7309F7807F5F1EFB909D21E07BF (void);
extern void PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4 (void);
extern void PhysicsScene_Internal_RaycastTest_Injected_m7633DAED691C6CFE296418FDBCE2E5E630456C62 (void);
extern void PhysicsScene_Internal_Raycast_Injected_m09A18038A5A35901A6825B805600525583FD404D (void);
extern void PhysicsScene_Internal_RaycastNonAlloc_Injected_m5C535BFE6635BE6E66150B9560A14A5922580934 (void);
extern void PhysicsScene_Query_SphereCast_Injected_m660DCB273A7D7AC02A4CACC69BBC38DF397E0D9A (void);
extern void RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D (void);
extern void RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39 (void);
extern void RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5 (void);
extern void RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78 (void);
extern void RaycastHit_CalculateRaycastTexCoord_mC614773633D8C01BC9143536C0AAE22F8D575806 (void);
extern void RaycastHit_get_textureCoord_m71F12781E6A806033B42B2D6D1D42DDA2069FE6D (void);
extern void RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155 (void);
extern void RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005 (void);
extern void RaycastHit_CalculateRaycastTexCoord_Injected_m5BE3F0E10ADADB27EE315FFDDD4A99493B70AE4C (void);
extern void Rigidbody_set_linearVelocity_m29AE03D5FC079EAD4202FCF72E2AEBDC19363985 (void);
extern void Rigidbody_set_angularVelocity_m23266B4E52BF0D2E65CC984AC73CC40B8D4A27E0 (void);
extern void Rigidbody_set_useGravity_m1B1B22E093F9DC92D7BEEBBE6B02642B3B6C4389 (void);
extern void Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691 (void);
extern void Rigidbody_get_rotation_m07882A7024FB3F96BA13EC577A96163BBB621AA1 (void);
extern void Rigidbody_set_rotation_mF2FC85A4A26AD9FED7DE0061889DF5A408461A5D (void);
extern void Rigidbody_MovePosition_mB2CD29ABC8F59AC338C0A3A5A6B75C38FDA92CA9 (void);
extern void Rigidbody_MoveRotation_m85825C7206E770E39DED9EE6D792702F577A891D (void);
extern void Rigidbody_AddForce_mBDBC288D0E266BC1B62E3649B4FCE46E7EA9CCBC (void);
extern void Rigidbody_AddForce_m7A3EEEED21F986917107CBA6CC0106DCBC212198 (void);
extern void Rigidbody__ctor_mB4E21922228AED3B52D8696D54F5B514F922CB07 (void);
extern void Rigidbody_set_linearVelocity_Injected_mCAC97A87C8F0FAFA7007C394AF7974DD37073467 (void);
extern void Rigidbody_set_angularVelocity_Injected_m214B4B9E3C8DCA28990EE9D4BD3402474C32E939 (void);
extern void Rigidbody_set_useGravity_Injected_mE1C763DD42795192ABDEF9C9CD758ACA6B91EBBC (void);
extern void Rigidbody_get_position_Injected_mD3AC6CDFE76252AAE5F4862BC7D18E0C479DDC8E (void);
extern void Rigidbody_get_rotation_Injected_m185864060C2B33EDDEDE270E78A6C45F1AAF1FC1 (void);
extern void Rigidbody_set_rotation_Injected_m7E13C37597294859ACA960465BA6EC37177D1F5C (void);
extern void Rigidbody_MovePosition_Injected_m1FBFE36CBE00546E08E32E304FEB6EEA5EE34E3E (void);
extern void Rigidbody_MoveRotation_Injected_m45E52DDE97339CE0552EBF4169DFBBC87D3F02D5 (void);
extern void Rigidbody_AddForce_Injected_m0063728ED4AA2811701ED30CDFC9765816402152 (void);
extern void SphereCollider_get_center_m122A197607CD350873539A0EEE3BA10E8BE1759E (void);
extern void SphereCollider_get_radius_m1BB513491906E76A4F71929E3DB72A1542309697 (void);
extern void SphereCollider__ctor_mA8570CBE8C0E74C607669DC4E0CCA6CB1E4CB200 (void);
extern void SphereCollider_get_center_Injected_m092FEA1F41E394E8C2E25921DFD2A1425F8874E9 (void);
extern void SphereCollider_get_radius_Injected_mF96901A391F7DBD16C5895D85C4853B65CD033A0 (void);
static Il2CppMethodPointer s_methodPointers[160] = 
{
	ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208,
	ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6,
	Collision_set_Flipped_m5AF63260E99357BB87655DDAD6316568BE65F3A1,
	Collision_get_contacts_m2E8E27E0399230DFA4303A4F4D81C1BD55CBC473,
	Collision__ctor_mC3F14BC1026130B6B0E6BB83D7431753C3484912,
	Collision__ctor_m6A02AD9B6F96A755B3A3A3A280CC7D2533228DA7,
	Collision_Reuse_mC2E21A6480EE1DCEAF71F2EAF3E0CAEFD42EA90C,
	BoxCollider_get_center_mC370C79F9FC9398D0DD080500FA2EE14FC6E36C7,
	BoxCollider_set_center_m0AB0482699735FEE8306A7FCAAE66A76C479F0F0,
	BoxCollider_get_size_mC1A2DD270B04DFF5961F9F90DC147C271F72258E,
	BoxCollider_set_size_m8374267FDE5DD628973E0E5E1331E781552B855A,
	BoxCollider__ctor_m7E069AACEC2B76129335D222FC8B8A63FD50656F,
	BoxCollider_get_center_Injected_m8DB81B1CFC1FB8FB4040BBACAD9EAA7AB5094855,
	BoxCollider_set_center_Injected_mF45B8F9D3C124C4AD7BA370715F542B6AA24BF08,
	BoxCollider_get_size_Injected_m7DCE0DAAB04E18055D954D944A07B16B840C88B6,
	BoxCollider_set_size_Injected_m7F9B55933C41D7733F88A47AD0D728A6ED98DCCE,
	CapsuleCollider_set_height_m5DAE3DC5AD851E30C5A29AC7A22F36BE1E205BBB,
	CapsuleCollider_set_height_Injected_m600D99DBF9E6314735E7FB060F344774AD554CD0,
	CharacterController_Move_mE3F7AC1B4A2D6955980811C088B68ED3A31D2DA4,
	CharacterController_Move_Injected_mBEBD5E60205BE4989D4BA963EBE81C06157CE598,
	Collider_get_enabled_mDBFB488088ADB14C8016A83EF445653AC5A4A12B,
	Collider_get_attachedRigidbody_m060304DB909A1FACD260EBB619D64D39129739AD,
	Collider_set_isTrigger_mFCD22F3EB5E28C97863956AB725D53F7F4B7CA78,
	Collider_ClosestPoint_mFFF9B6F6CF9F18B22B325835A3E2E78A1C03BFCB,
	Collider_Raycast_mBFA55E4B9BD7EE4E8D4107ADF24D2FA0F165FA2C,
	Collider_Raycast_mD7683E94051173B3FFC0862F4A17847E94AEB938,
	Collider__ctor_m8975C6CCFC0E5740C523DB4A52ACC7F4A021F8FA,
	Collider_get_enabled_Injected_mE8FB3B18306335574123BDAC8CD64B85252FC410,
	Collider_get_attachedRigidbody_Injected_mFDBCE8D40EF92923428F133D7D2B38A6793EE317,
	Collider_set_isTrigger_Injected_m55B9E744938E11F684A13DCE07B85691B6F07DD3,
	Collider_ClosestPoint_Injected_m95F62FF6C5A90AAF6163B1EA7F48F353E077D3FB,
	Collider_Raycast_Injected_m165289A4749E303FF6D97066FD4831D83BF4C225,
	Physics_OnSceneContactModify_m52106C18952BF4768B05F67FAF2B7F6F6F7D0C9D,
	Physics_PhysXOnSceneContactModify_m249A4E5A6A2967EA3CE7FD32ADD5851890D77AEE,
	Physics_get_invokeCollisionCallbacks_m73AE9C988EC57467D9B8699B376759D8E44133C8,
	Physics_get_defaultPhysicsScene_mC5D2BC20734D32FB421163F066BD5FB4118C633A,
	Physics_Raycast_m453681A406AADE0A30227D955279F5E7050B790D,
	Physics_Raycast_m0679FB03C9AFC1E803B8F8AE6CAB409670D31377,
	Physics_Raycast_mCFF84927BE3EC1780DBA34CCED374E7FF12ABCBE,
	Physics_Raycast_mCAA46C95211C7BB95697A347B036C012D26EB028,
	Physics_Raycast_mA782767AD4F149FBEA32C71460DFF061B7563688,
	Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12,
	Physics_Raycast_m011EA7022C33B2C499EF744E5AF3E01EEB8FBD33,
	Physics_Raycast_m1B27F500505FFB57D78548B9F5A540A2AD092903,
	Physics_Raycast_m9879C28DFF6CD3048F2365BC01C855565EE141F8,
	Physics_Raycast_m5CAA0AEDB2A6FB26E5F42A8EA560A61CAAF12E50,
	Physics_Raycast_m7A0FEA813B93A82713C06D8466F0A21325743488,
	Physics_Raycast_mDB89EB287ED040E534F6A933683A070D29DC14D3,
	Physics_Raycast_mCCD2542138D11E665A5D4F413C1547EE7D794DEB,
	Physics_Raycast_m34AC1210E893A9EF969BD2C7104B10BE5B580025,
	Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685,
	Physics_Raycast_mCAC9F02A1AAB49E16B384EBC8318E2DF30F4B0E5,
	Physics_SphereCast_mDB2140FE8561D0CE870037527DACC44AB18A346D,
	Physics_SphereCast_mE7656F9355B33AED9095D8A0301734611EC95B05,
	Physics_SphereCast_mF6538C6C4E3A9BBD81B686437CC91F3A93C1F3E7,
	Physics_Internal_RaycastAll_mC128593FD48E6F237BE59CFCDC7DDE7A4E8CB074,
	Physics_RaycastAll_m8B7FB8419A65BEE78927D0EE84916E8DBE7ECD34,
	Physics_RaycastAll_m69ED0FF0B70ADBC45B907783C87B308E786F6D51,
	Physics_RaycastAll_mDCBE530EF2ACD21EAADEA829259291D7327BC80E,
	Physics_RaycastAll_mE56962F670046BE618FFE8D9B19595A896922789,
	Physics_RaycastAll_mD1643DB52C4E415083E215B154FEB9DFA3AD6D74,
	Physics_RaycastAll_m4055619E0F7EFA04620EAA0517F8393C4EBCFE87,
	Physics_RaycastAll_m1BBD4E474814BEC9B52B015081A256AE2FE00468,
	Physics_RaycastAll_mE94864EF8243F7D3A26C8666CEB02166C3742CB2,
	Physics_RaycastNonAlloc_mB37DE98E8C9407C3DB2FB488BAB1CF3A7C6FFFCE,
	Physics_RaycastNonAlloc_m2BFEE9072E390ED6ACD500FD0AE4E714DE9549BC,
	Physics_RaycastNonAlloc_m1908CB5E0D0570E9C88B6C259041520DD4D3169C,
	Physics_RaycastNonAlloc_m1961CFCDB7631C7FF4D12F88904CF1BEB24A6C3E,
	Physics_RaycastNonAlloc_mB8FE279E06CE87D77387AA9A10562B8052DC8836,
	Physics_RaycastNonAlloc_m4CFAA8CA088502DA71D748D276BDAAEF234B12B0,
	Physics_RaycastNonAlloc_m3EEB10539C49FEAD9533142FEE6578148A48FFA9,
	Physics_RaycastNonAlloc_mBDC9E19F4E3C82DCE03D799FDD41FB3314209460,
	Physics_get_reuseCollisionCallbacks_mB23A11C02B893238B5631A38F2FBB4C63A3B7541,
	Physics_GetColliderByInstanceID_m5EF4B0EA9F8B66F23105FDD89BA528F5BC7EA179,
	Physics_GetBodyByInstanceID_m63D67631140FF644B6860598E4D0433FF93A72BA,
	Physics_SendOnCollisionEnter_mA48BA0630EE3D28320C602A15B5BDD887FA24144,
	Physics_SendOnCollisionStay_m2CC0293E4757CF89183A125270047A5054515590,
	Physics_SendOnCollisionExit_m6E96913C80E49A77FD367FBA5EF63755A81AA7D7,
	Physics_OnSceneContact_mFE8673EF13DD22B5C727BF5E2EFC4CF5E410C73A,
	Physics_ReportContacts_m9E7B0F448F534C8DEBBA6E33D86506C2500C919D,
	Physics_GetCollisionToReport_m7D22E5DD29678C65830A05A249650C49947A5D4E,
	Physics__cctor_m1E2D4816C77050D34F6D01F43E63D7969410AE2A,
	Physics_get_defaultPhysicsScene_Injected_mE86AE6A398435C1754A824B2B35DF13126A6C5D6,
	Physics_Internal_RaycastAll_Injected_m0100C5BF359DDE45F69B973D243D5B3CC0132754,
	Physics_GetColliderByInstanceID_Injected_mD465052413F8AE5D815CF2409F71C0F6FA37DEAD,
	Physics_GetBodyByInstanceID_Injected_mBB2ACC563B87F52D7C14FB54CD5BC0A9EAF0A695,
	Physics_SendOnCollisionEnter_Injected_m6E860F99570F9228DE3CD5D9AA7D8A87E189E960,
	Physics_SendOnCollisionStay_Injected_m4300A15E1F6881C0191E3A407A9296662661951C,
	Physics_SendOnCollisionExit_Injected_m935DC6B1CA45455BE752CEB6F4C6C42FC9B72E71,
	ContactEventDelegate__ctor_mF0844AC2AA36D48C199DDDBFA55627E43980CEEE,
	ContactEventDelegate_Invoke_m84BF3B9092BD4F6D43A870421F8389BC7B0E0769,
	ContactPairHeader_get_body_m8B8DC4B023CB7078B425E3CE764CFF3088D37FC8,
	ContactPairHeader_get_otherBody_m76BD1BDDE46A87839F214F741ECB0E0857EFAE0A,
	ContactPairHeader_get_hasRemovedBody_mE79EF42C5CB9FCDB476AC9437861F71CF7FC442A,
	ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09,
	ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27,
	ContactPair_get_collider_mC74190F4919E0DAC5E8994C975BE36244900BD6C,
	ContactPair_get_otherCollider_mA99232AB090D27DCC1561094DC20621F3DA764A8,
	ContactPair_get_isCollisionEnter_m901546CD819E7891F6594A72AAEAE72FCB5515B1,
	ContactPair_get_isCollisionExit_mD54D019EABA4B209633B41803D6EE0CA1B978DF1,
	ContactPair_get_isCollisionStay_m62946484C4BD866D9AD91B23FE1C18336048E5F8,
	ContactPair_get_hasRemovedCollider_m3DFB259C3645FA874C7AD304494222472B5184AA,
	ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC,
	ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454,
	ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565,
	ContactPairPoint_get_position_mD7D5690B579C6DF348EBBAD5523DAB1B90253C10,
	ContactPairPoint_get_separation_m5DF7D504E1EC95B2E44D37EAF78C1DCEC49DA12E,
	ContactPairPoint_get_normal_m019987EB1D8883A212596E8BB418EB787050FECF,
	ContactPairPoint_get_impulse_m982A7672DBBC6C7D8636D0FC9D1A8D75CA313D1A,
	PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7,
	PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814,
	PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3,
	PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1,
	PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730,
	PhysicsScene_Internal_RaycastTest_m729F4A577F5DD911131C5321EC28E44F98A60BA0,
	PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE,
	PhysicsScene_Internal_Raycast_m0211A7BDE011181718838F063296D51F88D92E74,
	PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3,
	PhysicsScene_Internal_RaycastNonAlloc_mC339255AAFC484588C813D7BE2BDAE03797D26DB,
	PhysicsScene_Query_SphereCast_m8E6770FE64FB74157199217381AA1A99B3CF580B,
	PhysicsScene_Internal_SphereCast_mE4B0FBE790E2A7309F7807F5F1EFB909D21E07BF,
	PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4,
	PhysicsScene_Internal_RaycastTest_Injected_m7633DAED691C6CFE296418FDBCE2E5E630456C62,
	PhysicsScene_Internal_Raycast_Injected_m09A18038A5A35901A6825B805600525583FD404D,
	PhysicsScene_Internal_RaycastNonAlloc_Injected_m5C535BFE6635BE6E66150B9560A14A5922580934,
	PhysicsScene_Query_SphereCast_Injected_m660DCB273A7D7AC02A4CACC69BBC38DF397E0D9A,
	RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D,
	RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39,
	RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5,
	RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78,
	RaycastHit_CalculateRaycastTexCoord_mC614773633D8C01BC9143536C0AAE22F8D575806,
	RaycastHit_get_textureCoord_m71F12781E6A806033B42B2D6D1D42DDA2069FE6D,
	RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155,
	RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005,
	RaycastHit_CalculateRaycastTexCoord_Injected_m5BE3F0E10ADADB27EE315FFDDD4A99493B70AE4C,
	Rigidbody_set_linearVelocity_m29AE03D5FC079EAD4202FCF72E2AEBDC19363985,
	Rigidbody_set_angularVelocity_m23266B4E52BF0D2E65CC984AC73CC40B8D4A27E0,
	Rigidbody_set_useGravity_m1B1B22E093F9DC92D7BEEBBE6B02642B3B6C4389,
	Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691,
	Rigidbody_get_rotation_m07882A7024FB3F96BA13EC577A96163BBB621AA1,
	Rigidbody_set_rotation_mF2FC85A4A26AD9FED7DE0061889DF5A408461A5D,
	Rigidbody_MovePosition_mB2CD29ABC8F59AC338C0A3A5A6B75C38FDA92CA9,
	Rigidbody_MoveRotation_m85825C7206E770E39DED9EE6D792702F577A891D,
	Rigidbody_AddForce_mBDBC288D0E266BC1B62E3649B4FCE46E7EA9CCBC,
	Rigidbody_AddForce_m7A3EEEED21F986917107CBA6CC0106DCBC212198,
	Rigidbody__ctor_mB4E21922228AED3B52D8696D54F5B514F922CB07,
	Rigidbody_set_linearVelocity_Injected_mCAC97A87C8F0FAFA7007C394AF7974DD37073467,
	Rigidbody_set_angularVelocity_Injected_m214B4B9E3C8DCA28990EE9D4BD3402474C32E939,
	Rigidbody_set_useGravity_Injected_mE1C763DD42795192ABDEF9C9CD758ACA6B91EBBC,
	Rigidbody_get_position_Injected_mD3AC6CDFE76252AAE5F4862BC7D18E0C479DDC8E,
	Rigidbody_get_rotation_Injected_m185864060C2B33EDDEDE270E78A6C45F1AAF1FC1,
	Rigidbody_set_rotation_Injected_m7E13C37597294859ACA960465BA6EC37177D1F5C,
	Rigidbody_MovePosition_Injected_m1FBFE36CBE00546E08E32E304FEB6EEA5EE34E3E,
	Rigidbody_MoveRotation_Injected_m45E52DDE97339CE0552EBF4169DFBBC87D3F02D5,
	Rigidbody_AddForce_Injected_m0063728ED4AA2811701ED30CDFC9765816402152,
	SphereCollider_get_center_m122A197607CD350873539A0EEE3BA10E8BE1759E,
	SphereCollider_get_radius_m1BB513491906E76A4F71929E3DB72A1542309697,
	SphereCollider__ctor_mA8570CBE8C0E74C607669DC4E0CCA6CB1E4CB200,
	SphereCollider_get_center_Injected_m092FEA1F41E394E8C2E25921DFD2A1425F8874E9,
	SphereCollider_get_radius_Injected_mF96901A391F7DBD16C5895D85C4853B65CD033A0,
};
extern void ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208_AdjustorThunk (void);
extern void ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6_AdjustorThunk (void);
extern void ContactPairHeader_get_body_m8B8DC4B023CB7078B425E3CE764CFF3088D37FC8_AdjustorThunk (void);
extern void ContactPairHeader_get_otherBody_m76BD1BDDE46A87839F214F741ECB0E0857EFAE0A_AdjustorThunk (void);
extern void ContactPairHeader_get_hasRemovedBody_mE79EF42C5CB9FCDB476AC9437861F71CF7FC442A_AdjustorThunk (void);
extern void ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09_AdjustorThunk (void);
extern void ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27_AdjustorThunk (void);
extern void ContactPair_get_collider_mC74190F4919E0DAC5E8994C975BE36244900BD6C_AdjustorThunk (void);
extern void ContactPair_get_otherCollider_mA99232AB090D27DCC1561094DC20621F3DA764A8_AdjustorThunk (void);
extern void ContactPair_get_isCollisionEnter_m901546CD819E7891F6594A72AAEAE72FCB5515B1_AdjustorThunk (void);
extern void ContactPair_get_isCollisionExit_mD54D019EABA4B209633B41803D6EE0CA1B978DF1_AdjustorThunk (void);
extern void ContactPair_get_isCollisionStay_m62946484C4BD866D9AD91B23FE1C18336048E5F8_AdjustorThunk (void);
extern void ContactPair_get_hasRemovedCollider_m3DFB259C3645FA874C7AD304494222472B5184AA_AdjustorThunk (void);
extern void ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC_AdjustorThunk (void);
extern void ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454_AdjustorThunk (void);
extern void ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565_AdjustorThunk (void);
extern void ContactPairPoint_get_position_mD7D5690B579C6DF348EBBAD5523DAB1B90253C10_AdjustorThunk (void);
extern void ContactPairPoint_get_separation_m5DF7D504E1EC95B2E44D37EAF78C1DCEC49DA12E_AdjustorThunk (void);
extern void ContactPairPoint_get_normal_m019987EB1D8883A212596E8BB418EB787050FECF_AdjustorThunk (void);
extern void ContactPairPoint_get_impulse_m982A7672DBBC6C7D8636D0FC9D1A8D75CA313D1A_AdjustorThunk (void);
extern void PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7_AdjustorThunk (void);
extern void PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814_AdjustorThunk (void);
extern void PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3_AdjustorThunk (void);
extern void PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3_AdjustorThunk (void);
extern void PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4_AdjustorThunk (void);
extern void RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D_AdjustorThunk (void);
extern void RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39_AdjustorThunk (void);
extern void RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5_AdjustorThunk (void);
extern void RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78_AdjustorThunk (void);
extern void RaycastHit_get_textureCoord_m71F12781E6A806033B42B2D6D1D42DDA2069FE6D_AdjustorThunk (void);
extern void RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155_AdjustorThunk (void);
extern void RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[35] = 
{
	{ 0x06000001, ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208_AdjustorThunk },
	{ 0x06000002, ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6_AdjustorThunk },
	{ 0x0600005C, ContactPairHeader_get_body_m8B8DC4B023CB7078B425E3CE764CFF3088D37FC8_AdjustorThunk },
	{ 0x0600005D, ContactPairHeader_get_otherBody_m76BD1BDDE46A87839F214F741ECB0E0857EFAE0A_AdjustorThunk },
	{ 0x0600005E, ContactPairHeader_get_hasRemovedBody_mE79EF42C5CB9FCDB476AC9437861F71CF7FC442A_AdjustorThunk },
	{ 0x0600005F, ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09_AdjustorThunk },
	{ 0x06000060, ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27_AdjustorThunk },
	{ 0x06000061, ContactPair_get_collider_mC74190F4919E0DAC5E8994C975BE36244900BD6C_AdjustorThunk },
	{ 0x06000062, ContactPair_get_otherCollider_mA99232AB090D27DCC1561094DC20621F3DA764A8_AdjustorThunk },
	{ 0x06000063, ContactPair_get_isCollisionEnter_m901546CD819E7891F6594A72AAEAE72FCB5515B1_AdjustorThunk },
	{ 0x06000064, ContactPair_get_isCollisionExit_mD54D019EABA4B209633B41803D6EE0CA1B978DF1_AdjustorThunk },
	{ 0x06000065, ContactPair_get_isCollisionStay_m62946484C4BD866D9AD91B23FE1C18336048E5F8_AdjustorThunk },
	{ 0x06000066, ContactPair_get_hasRemovedCollider_m3DFB259C3645FA874C7AD304494222472B5184AA_AdjustorThunk },
	{ 0x06000067, ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC_AdjustorThunk },
	{ 0x06000068, ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454_AdjustorThunk },
	{ 0x06000069, ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565_AdjustorThunk },
	{ 0x0600006A, ContactPairPoint_get_position_mD7D5690B579C6DF348EBBAD5523DAB1B90253C10_AdjustorThunk },
	{ 0x0600006B, ContactPairPoint_get_separation_m5DF7D504E1EC95B2E44D37EAF78C1DCEC49DA12E_AdjustorThunk },
	{ 0x0600006C, ContactPairPoint_get_normal_m019987EB1D8883A212596E8BB418EB787050FECF_AdjustorThunk },
	{ 0x0600006D, ContactPairPoint_get_impulse_m982A7672DBBC6C7D8636D0FC9D1A8D75CA313D1A_AdjustorThunk },
	{ 0x0600006E, PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7_AdjustorThunk },
	{ 0x0600006F, PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814_AdjustorThunk },
	{ 0x06000070, PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3_AdjustorThunk },
	{ 0x06000071, PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1_AdjustorThunk },
	{ 0x06000072, PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730_AdjustorThunk },
	{ 0x06000074, PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE_AdjustorThunk },
	{ 0x06000076, PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3_AdjustorThunk },
	{ 0x0600007A, PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4_AdjustorThunk },
	{ 0x0600007F, RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D_AdjustorThunk },
	{ 0x06000080, RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39_AdjustorThunk },
	{ 0x06000081, RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5_AdjustorThunk },
	{ 0x06000082, RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78_AdjustorThunk },
	{ 0x06000084, RaycastHit_get_textureCoord_m71F12781E6A806033B42B2D6D1D42DDA2069FE6D_AdjustorThunk },
	{ 0x06000085, RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155_AdjustorThunk },
	{ 0x06000086, RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005_AdjustorThunk },
};
static const int32_t s_InvokerIndices[160] = 
{
	21004,
	21004,
	15757,
	20761,
	21016,
	3514,
	6503,
	21004,
	16199,
	21004,
	16199,
	21016,
	28958,
	28958,
	28958,
	28958,
	16071,
	28965,
	13403,
	27844,
	20550,
	20761,
	15757,
	14279,
	3366,
	3121,
	21016,
	31560,
	31847,
	28959,
	26481,
	23301,
	24691,
	24691,
	34103,
	34160,
	22271,
	23781,
	25358,
	27620,
	21953,
	22270,
	23780,
	25356,
	23749,
	25342,
	27534,
	31565,
	22254,
	23748,
	25340,
	27533,
	21802,
	21948,
	22255,
	22833,
	22839,
	24364,
	25711,
	28116,
	24354,
	25700,
	28085,
	32095,
	22771,
	24183,
	25545,
	27864,
	21984,
	22773,
	24190,
	25549,
	34103,
	32077,
	32077,
	29176,
	29176,
	29176,
	26593,
	32743,
	25622,
	34252,
	32748,
	22069,
	31840,
	31840,
	28964,
	28964,
	28964,
	7988,
	8070,
	20761,
	20761,
	20550,
	10204,
	10204,
	20761,
	20761,
	20550,
	20550,
	20550,
	20550,
	5701,
	10204,
	10204,
	21004,
	20873,
	21004,
	21004,
	20761,
	20694,
	11681,
	11701,
	849,
	22253,
	532,
	21947,
	540,
	21981,
	21675,
	21674,
	369,
	22199,
	21932,
	21961,
	21666,
	20761,
	21004,
	21004,
	20873,
	22881,
	21002,
	20761,
	20761,
	22083,
	16199,
	16199,
	15757,
	21004,
	20807,
	16013,
	16199,
	16013,
	8196,
	16199,
	21016,
	28958,
	28958,
	28959,
	28958,
	28958,
	28958,
	28958,
	28958,
	26483,
	21004,
	20873,
	21016,
	28958,
	32260,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule = 
{
	"UnityEngine.PhysicsModule.dll",
	160,
	s_methodPointers,
	35,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
