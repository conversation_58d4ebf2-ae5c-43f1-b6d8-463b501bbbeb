using System;
using UnityEngine;
using Unity.Services.LevelPlay;
using Cysharp.Threading.Tasks;
using OwnMatch3.Utils;

namespace OwnMatch3.Monetization
{
    /// <summary>
    /// Comprehensive ads manager for Unity LevelPlay mediation
    /// Handles VideoReward, Interstitial, and Banner ads
    /// </summary>
    public class AdsManager : MonoBehaviour
    {
        [Header("Configuration")]
        [SerializeField] private AdConfig adConfig;
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private bool autoInitialize = true;
        [SerializeField] private bool showBannerOnStart = false;
        
        // Singleton instance
        public static AdsManager Instance { get; private set; }
        
        // Ad instances
        private LevelPlayBannerAd bannerAd;
        private LevelPlayInterstitialAd interstitialAd;
        private LevelPlayRewardedAd rewardedVideoAd;
        
        // State tracking
        public bool IsInitialized { get; private set; }
        public bool IsBannerLoaded { get; private set; }
        public bool IsInterstitialLoaded { get; private set; }
        public bool IsRewardedVideoLoaded { get; private set; }
        public bool IsBannerVisible { get; private set; }
        
        // Events
        public static event Action OnAdsInitialized;
        public static event Action<AdReward> OnRewardedVideoCompleted;
        public static event Action OnRewardedVideoFailed;
        public static event Action OnInterstitialShown;
        public static event Action OnInterstitialFailed;
        public static event Action OnBannerLoaded;
        public static event Action OnBannerFailed;
        public static event Action OnBannerShown;
        
        private void Awake()
        {
            // Singleton pattern
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            
            Instance = this;
            DontDestroyOnLoad(gameObject);
            
            if (autoInitialize)
            {
                InitializeAds();
            }
        }
        
        /// <summary>
        /// Initialize LevelPlay SDK and setup ad instances
        /// </summary>
        public async void InitializeAds()
        {
            if (IsInitialized)
            {
                LogDebug("Ads already initialized");
                return;
            }
            
            if (adConfig == null)
            {
                LogError("AdConfig is null! Please assign it in the inspector.");
                return;
            }
            
            LogDebug("Initializing LevelPlay SDK...");
            
            // Register initialization callbacks
            LevelPlay.OnInitSuccess += OnInitSuccess;
            LevelPlay.OnInitFailed += OnInitFailed;
            
            // Register impression data callback
            LevelPlay.OnImpressionDataReady += OnImpressionDataReady;
            
            // Initialize SDK
            LevelPlay.Init(adConfig.AppKey);
            
            // Wait for initialization with timeout
            await WaitForInitialization();
        }
        
        private async UniTask WaitForInitialization()
        {
            float timeout = 10f;
            float elapsed = 0f;
            
            while (!IsInitialized && elapsed < timeout)
            {
                await UniTask.Delay(100);
                elapsed += 0.1f;
            }
            
            if (!IsInitialized)
            {
                LogError("Ads initialization timed out");
            }
        }
        
        private void OnInitSuccess(LevelPlayConfiguration config)
        {
            LogDebug($"LevelPlay initialized successfully: {config}");
            IsInitialized = true;
            
            SetupAdInstances();
            OnAdsInitialized?.Invoke();
            
            if (showBannerOnStart)
            {
                LoadAndShowBanner();
            }
        }
        
        private void OnInitFailed(LevelPlayInitError error)
        {
            LogError($"LevelPlay initialization failed: {error}");
        }
        
        private void SetupAdInstances()
        {
            SetupRewardedVideo();
            SetupInterstitial();
            SetupBanner();
        }
        
        #region Rewarded Video
        
        private void SetupRewardedVideo()
        {
            if (string.IsNullOrEmpty(adConfig.RewardedVideoAdUnitId))
            {
                LogError("Rewarded Video Ad Unit ID is empty");
                return;
            }
            
            rewardedVideoAd = new LevelPlayRewardedAd(adConfig.RewardedVideoAdUnitId);
            
            // Register events
            rewardedVideoAd.OnAdLoaded += OnRewardedVideoLoaded;
            rewardedVideoAd.OnAdLoadFailed += OnRewardedVideoLoadFailed;
            rewardedVideoAd.OnAdDisplayed += OnRewardedVideoDisplayed;
            rewardedVideoAd.OnAdDisplayFailed += OnRewardedVideoDisplayFailed;
            rewardedVideoAd.OnAdRewarded += OnRewardedVideoRewarded;
            rewardedVideoAd.OnAdClosed += OnRewardedVideoClosed;
            
            // Load first ad
            LoadRewardedVideo();
        }
        
        public void LoadRewardedVideo()
        {
            if (rewardedVideoAd == null)
            {
                LogError("Rewarded video ad not initialized");
                return;
            }
            
            LogDebug("Loading rewarded video ad...");
            rewardedVideoAd.LoadAd();
        }
        
        public void ShowRewardedVideo(AdReward reward = null)
        {
            if (rewardedVideoAd == null)
            {
                LogError("Rewarded video ad not initialized");
                OnRewardedVideoFailed?.Invoke();
                return;
            }
            
            if (!IsRewardedVideoLoaded)
            {
                LogError("Rewarded video ad not loaded");
                OnRewardedVideoFailed?.Invoke();
                return;
            }
            
            LogDebug("Showing rewarded video ad...");
            currentReward = reward;
            rewardedVideoAd.ShowAd();
        }
        
        private AdReward currentReward;
        
        private void OnRewardedVideoLoaded(LevelPlayAdInfo adInfo)
        {
            LogDebug("Rewarded video loaded");
            IsRewardedVideoLoaded = true;
        }
        
        private void OnRewardedVideoLoadFailed(LevelPlayAdError error)
        {
            LogError($"Rewarded video load failed: {error}");
            IsRewardedVideoLoaded = false;
        }
        
        private void OnRewardedVideoDisplayed(LevelPlayAdInfo adInfo)
        {
            LogDebug("Rewarded video displayed");
        }
        
        private void OnRewardedVideoDisplayFailed(LevelPlayAdDisplayInfoError error)
        {
            LogError($"Rewarded video display failed: {error}");
            OnRewardedVideoFailed?.Invoke();
            LoadRewardedVideo(); // Load next ad
        }
        
        private void OnRewardedVideoRewarded(LevelPlayAdInfo adInfo, LevelPlayReward reward)
        {
            LogDebug($"Rewarded video completed: {reward.Name} - {reward.Amount}");
            
            // Give reward to player
            if (currentReward != null)
            {
                GiveRewardToPlayer(currentReward);
                OnRewardedVideoCompleted?.Invoke(currentReward);
            }
            else
            {
                // Default reward if none specified
                var defaultReward = new AdReward(AdRewardType.Gold, 100);
                GiveRewardToPlayer(defaultReward);
                OnRewardedVideoCompleted?.Invoke(defaultReward);
            }
        }
        
        private void OnRewardedVideoClosed(LevelPlayAdInfo adInfo)
        {
            LogDebug("Rewarded video closed");
            IsRewardedVideoLoaded = false;
            currentReward = null;
            LoadRewardedVideo(); // Load next ad
        }
        
        #endregion
        
        #region Interstitial
        
        private void SetupInterstitial()
        {
            if (string.IsNullOrEmpty(adConfig.InterstitialAdUnitId))
            {
                LogError("Interstitial Ad Unit ID is empty");
                return;
            }
            
            interstitialAd = new LevelPlayInterstitialAd(adConfig.InterstitialAdUnitId);
            
            // Register events
            interstitialAd.OnAdLoaded += OnInterstitialLoaded;
            interstitialAd.OnAdLoadFailed += OnInterstitialLoadFailed;
            interstitialAd.OnAdDisplayed += OnInterstitialDisplayed;
            interstitialAd.OnAdDisplayFailed += OnInterstitialDisplayFailed;
            interstitialAd.OnAdClosed += OnInterstitialClosed;
            
            // Load first ad
            LoadInterstitial();
        }
        
        public void LoadInterstitial()
        {
            if (interstitialAd == null)
            {
                LogError("Interstitial ad not initialized");
                return;
            }
            
            LogDebug("Loading interstitial ad...");
            interstitialAd.LoadAd();
        }
        
        public void ShowInterstitial()
        {
            if (interstitialAd == null)
            {
                LogError("Interstitial ad not initialized");
                OnInterstitialFailed?.Invoke();
                return;
            }
            
            if (!IsInterstitialLoaded)
            {
                LogError("Interstitial ad not loaded");
                OnInterstitialFailed?.Invoke();
                return;
            }
            
            LogDebug("Showing interstitial ad...");
            interstitialAd.ShowAd();
        }
        
        private void OnInterstitialLoaded(LevelPlayAdInfo adInfo)
        {
            LogDebug("Interstitial loaded");
            IsInterstitialLoaded = true;
        }
        
        private void OnInterstitialLoadFailed(LevelPlayAdError error)
        {
            LogError($"Interstitial load failed: {error}");
            IsInterstitialLoaded = false;
        }
        
        private void OnInterstitialDisplayed(LevelPlayAdInfo adInfo)
        {
            LogDebug("Interstitial displayed");
            OnInterstitialShown?.Invoke();
        }
        
        private void OnInterstitialDisplayFailed(LevelPlayAdDisplayInfoError error)
        {
            LogError($"Interstitial display failed: {error}");
            OnInterstitialFailed?.Invoke();
            LoadInterstitial(); // Load next ad
        }
        
        private void OnInterstitialClosed(LevelPlayAdInfo adInfo)
        {
            LogDebug("Interstitial closed");
            IsInterstitialLoaded = false;
            LoadInterstitial(); // Load next ad
        }
        
        #endregion

        #region Banner

        private void SetupBanner()
        {
            if (string.IsNullOrEmpty(adConfig.BannerAdUnitId))
            {
                LogError("Banner Ad Unit ID is empty");
                return;
            }

            bannerAd = new LevelPlayBannerAd(adConfig.BannerAdUnitId);

            // Register events
            bannerAd.OnAdLoaded += OnBannerAdLoaded;
            bannerAd.OnAdLoadFailed += OnBannerLoadFailed;
            bannerAd.OnAdDisplayed += OnBannerDisplayed;
            bannerAd.OnAdDisplayFailed += OnBannerDisplayFailed;
            bannerAd.OnAdClicked += OnBannerClicked;
        }
        public void LoadAndShowBanner()
        {
            if (bannerAd == null)
            {
                LogError("Banner ad not initialized");
                return;
            }

            LogDebug("Loading banner ad...");
            bannerAd.LoadAd();
            // Banner will auto-show after loading via OnBannerAdLoaded callback
        }

        public void ShowBanner()
        {
            if (bannerAd == null)
            {
                LogError("Banner ad not initialized");
                return;
            }

            if (!IsBannerLoaded)
            {
                LogDebug("Banner not loaded yet, loading first...");
                LoadAndShowBanner();
                return;
            }

            LogDebug("Showing banner ad...");
            bannerAd.ShowAd();
        }

        public void HideBanner()
        {
            if (bannerAd == null)
            {
                LogError("Banner ad not initialized");
                return;
            }

            LogDebug("Hiding banner ad...");
            bannerAd.HideAd();
            IsBannerVisible = false;
        }

        public void DestroyBanner()
        {
            if (bannerAd == null)
            {
                LogDebug("Banner ad already destroyed or not initialized");
                return;
            }

            LogDebug("Destroying banner ad...");
            bannerAd.DestroyAd();
            IsBannerLoaded = false;
            IsBannerVisible = false;
        }



        private void OnBannerAdLoaded(LevelPlayAdInfo adInfo)
        {
            LogDebug($"Banner loaded: {adInfo.AdUnitId}");
            IsBannerLoaded = true;
            OnBannerLoaded?.Invoke();

            // Auto-show banner after loading (modern approach)
            bannerAd.ShowAd();
        }

        private void OnBannerLoadFailed(LevelPlayAdError error)
        {
            LogError($"Banner load failed: {error}");
            IsBannerLoaded = false;
            OnBannerFailed?.Invoke();
        }

        private void OnBannerDisplayed(LevelPlayAdInfo adInfo)
        {
            LogDebug($"Banner displayed: {adInfo.AdUnitId}");
            IsBannerVisible = true;
            OnBannerShown?.Invoke();
        }

        private void OnBannerDisplayFailed(LevelPlayAdDisplayInfoError error)
        {
            LogError($"Banner display failed: {error}");
            IsBannerVisible = false;
        }

        private void OnBannerClicked(LevelPlayAdInfo adInfo)
        {
            LogDebug($"Banner clicked: {adInfo.AdUnitId}");
        }

        #endregion

        #region Reward System Integration

        private void GiveRewardToPlayer(AdReward reward)
        {
            if (GameProgressManager.Instance == null)
            {
                LogError("GameProgressManager not found");
                return;
            }

            switch (reward.Type)
            {
                case AdRewardType.Gold:
                    GameProgressManager.Instance.AddGold(reward.Amount);
                    LogDebug($"Gave {reward.Amount} gold to player");
                    break;

                case AdRewardType.Stars:
                    GameProgressManager.Instance.AddStars(reward.Amount);
                    LogDebug($"Gave {reward.Amount} stars to player");
                    break;

                case AdRewardType.ExtraMoves:
                    // This would need to be handled by the game board
                    LogDebug($"Gave {reward.Amount} extra moves to player");
                    break;

                case AdRewardType.Lives:
                    // This would need to be handled by a lives system
                    LogDebug($"Gave {reward.Amount} lives to player");
                    break;

                case AdRewardType.AvatarProgress:
                    HandleAvatarProgressReward(reward);
                    break;

                default:
                    LogError($"Unknown reward type: {reward.Type}");
                    break;
            }
        }

        /// <summary>
        /// Handle avatar progress reward
        /// </summary>
        private void HandleAvatarProgressReward(AdReward reward)
        {
            if (string.IsNullOrEmpty(reward.AvatarId))
            {
                LogError("Avatar progress reward missing avatar ID");
                return;
            }

            // Use AvatarManager to handle the ad watching
            if (OwnMatch3.Avatars.AvatarManager.Instance != null)
            {
                bool success = OwnMatch3.Avatars.AvatarManager.Instance.WatchAdForAvatar(reward.AvatarId);
                if (success)
                {
                    LogDebug($"Avatar progress updated for {reward.AvatarId}");
                }
                else
                {
                    LogError($"Failed to update avatar progress for {reward.AvatarId}");
                }
            }
            else
            {
                LogError("AvatarManager not found for avatar progress reward");
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Check if rewarded video is available to show
        /// </summary>
        public bool IsRewardedVideoAvailable()
        {
            return IsInitialized && IsRewardedVideoLoaded;
        }

        /// <summary>
        /// Check if interstitial is available to show
        /// </summary>
        public bool IsInterstitialAvailable()
        {
            return IsInitialized && IsInterstitialLoaded;
        }

        /// <summary>
        /// Show rewarded video with specific reward
        /// </summary>
        public void ShowRewardedVideoForGold(int amount)
        {
            var reward = new AdReward(AdRewardType.Gold, amount);
            ShowRewardedVideo(reward);
        }

        /// <summary>
        /// Show rewarded video for extra moves
        /// </summary>
        public void ShowRewardedVideoForExtraMoves(int moves)
        {
            var reward = new AdReward(AdRewardType.ExtraMoves, moves);
            ShowRewardedVideo(reward);
        }

        /// <summary>
        /// Show rewarded video for stars
        /// </summary>
        public void ShowRewardedVideoForStars(int stars)
        {
            var reward = new AdReward(AdRewardType.Stars, stars);
            ShowRewardedVideo(reward);
        }

        /// <summary>
        /// Show rewarded video for avatar progress
        /// </summary>
        public void ShowRewardedVideoForAvatar(string avatarId, string avatarName = "")
        {
            var reward = CommonAdRewards.CreateAvatarProgressReward(avatarId, avatarName);
            ShowRewardedVideo(reward);
        }

        private void OnImpressionDataReady(LevelPlayImpressionData impressionData)
        {
            LogDebug($"Impression data ready: {impressionData}");
        }

        private void LogDebug(string message)
        {
            if (enableDebugLogs)
            {
                DebugManager.Log($"[AdsManager] {message}");
            }
        }

        private void LogError(string message)
        {
            DebugManager.LogError($"[AdsManager] {message}");
        }

        #endregion

        private void OnDestroy()
        {
            // Cleanup events
            LevelPlay.OnInitSuccess -= OnInitSuccess;
            LevelPlay.OnInitFailed -= OnInitFailed;
            LevelPlay.OnImpressionDataReady -= OnImpressionDataReady;

            // Cleanup ad instances
            bannerAd?.DestroyAd();
            interstitialAd?.DestroyAd();
        }
    }
}
