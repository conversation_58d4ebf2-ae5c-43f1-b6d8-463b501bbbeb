using UnityEngine;

using OwnMatch3.Utils;
namespace OwnMatch3.Monetization
{
    /// <summary>
    /// Configuration for Unity LevelPlay ads
    /// Contains platform-specific ad unit IDs and app keys
    /// </summary>
    [CreateAssetMenu(fileName = "AdConfig", menuName = "OwnMatch3/Monetization/Ad Config")]
    public class AdConfig : ScriptableObject
    {
        [Header("App Keys")]
        [SerializeField] private string androidAppKey = "85460dcd";  // Unity LevelPlay test key
        [SerializeField] private string iosAppKey = "8545d445";     // Unity LevelPlay test key

        [Header("Banner Ad Unit IDs")]
        [SerializeField] private string androidBannerAdUnitId = "";
        [SerializeField] private string iosBannerAdUnitId = "";

        [Header("Interstitial Ad Unit IDs")]
        [SerializeField] private string androidInterstitialAdUnitId = "";
        [SerializeField] private string iosInterstitialAdUnitId = "";

        [Header("Rewarded Video Ad Unit IDs")]
        [SerializeField] private string androidRewardedVideoAdUnitId = "";
        [SerializeField] private string iosRewardedVideoAdUnitId = "";

        [Header("Test Mode")]
        [SerializeField] private bool useTestIds = true;  // Enable test mode by default
        [SerializeField] private string testBannerAdUnitId = "TestBanner";
        [SerializeField] private string testInterstitialAdUnitId = "TestInterstitial";
        [SerializeField] private string testRewardedVideoAdUnitId = "TestRewardedVideo";
        
        /// <summary>
        /// Get the appropriate app key for the current platform
        /// </summary>
        public string AppKey
        {
            get
            {
#if UNITY_ANDROID
                return androidAppKey;
#elif UNITY_IOS
                return iosAppKey;
#else
                return "unexpected_platform";
#endif
            }
        }
        
        /// <summary>
        /// Get the appropriate banner ad unit ID for the current platform
        /// </summary>
        public string BannerAdUnitId
        {
            get
            {
                if (useTestIds)
                    return testBannerAdUnitId;
                    
#if UNITY_ANDROID
                return androidBannerAdUnitId;
#elif UNITY_IOS
                return iosBannerAdUnitId;
#else
                return "unexpected_platform";
#endif
            }
        }
        
        /// <summary>
        /// Get the appropriate interstitial ad unit ID for the current platform
        /// </summary>
        public string InterstitialAdUnitId
        {
            get
            {
                if (useTestIds)
                    return testInterstitialAdUnitId;
                    
#if UNITY_ANDROID
                return androidInterstitialAdUnitId;
#elif UNITY_IOS
                return iosInterstitialAdUnitId;
#else
                return "unexpected_platform";
#endif
            }
        }
        
        /// <summary>
        /// Get the appropriate rewarded video ad unit ID for the current platform
        /// </summary>
        public string RewardedVideoAdUnitId
        {
            get
            {
                if (useTestIds)
                    return testRewardedVideoAdUnitId;
                    
#if UNITY_ANDROID
                return androidRewardedVideoAdUnitId;
#elif UNITY_IOS
                return iosRewardedVideoAdUnitId;
#else
                return "unexpected_platform";
#endif
            }
        }
        
        /// <summary>
        /// Validate that all required ad unit IDs are set
        /// </summary>
        public bool IsValid()
        {
            if (useTestIds)
                return true;
                
            bool hasAppKey = !string.IsNullOrEmpty(AppKey) && AppKey != "unexpected_platform";
            bool hasBanner = !string.IsNullOrEmpty(BannerAdUnitId) && BannerAdUnitId != "unexpected_platform";
            bool hasInterstitial = !string.IsNullOrEmpty(InterstitialAdUnitId) && InterstitialAdUnitId != "unexpected_platform";
            bool hasRewardedVideo = !string.IsNullOrEmpty(RewardedVideoAdUnitId) && RewardedVideoAdUnitId != "unexpected_platform";
            
            return hasAppKey && hasBanner && hasInterstitial && hasRewardedVideo;
        }
        
        /// <summary>
        /// Get validation errors for debugging
        /// </summary>
        public string GetValidationErrors()
        {
            if (useTestIds)
                return "Using test IDs - validation skipped";
                
            var errors = new System.Text.StringBuilder();
            
            if (string.IsNullOrEmpty(AppKey) || AppKey == "unexpected_platform")
                errors.AppendLine("App Key is not set for current platform");
                
            if (string.IsNullOrEmpty(BannerAdUnitId) || BannerAdUnitId == "unexpected_platform")
                errors.AppendLine("Banner Ad Unit ID is not set for current platform");
                
            if (string.IsNullOrEmpty(InterstitialAdUnitId) || InterstitialAdUnitId == "unexpected_platform")
                errors.AppendLine("Interstitial Ad Unit ID is not set for current platform");
                
            if (string.IsNullOrEmpty(RewardedVideoAdUnitId) || RewardedVideoAdUnitId == "unexpected_platform")
                errors.AppendLine("Rewarded Video Ad Unit ID is not set for current platform");
                
            return errors.ToString();
        }
        
#if UNITY_EDITOR
        [Header("Editor Tools")]
        [SerializeField] private bool showValidationInInspector = true;
        
        private void OnValidate()
        {
            if (showValidationInInspector && !IsValid())
            {
                DebugManager.LogWarning($"[AdConfig] Validation failed:\n{GetValidationErrors()}", this);
            }
        }
#endif
    }
}
