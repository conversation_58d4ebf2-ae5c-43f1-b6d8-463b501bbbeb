﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m3A7DCD782C10D2045D64E762F42F065618A737C0 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m6DC13BAF03B589E28A9570C6B1AA2E9D55889E0A (void);
extern void CFXR_Demo_NextEffect_m94EF66A3322CB9B857A8217260CFCEDD06FC56D2 (void);
extern void CFXR_Demo_PreviousEffect_m3CE1D987622FFB1BF325D8278D4A480764E4317C (void);
extern void CFXR_Demo_ToggleSlowMo_mE657E27920989DF0E13DB44CCC026A7AAF1A0083 (void);
extern void CFXR_Demo_ToggleCamera_mDF2E6E2EA9F515F46A3EB6E4D0B0CB4C98A6E09B (void);
extern void CFXR_Demo_ToggleGround_m62681F628E827CFDE43E262B6FB8E3A12B310F10 (void);
extern void CFXR_Demo_ToggleCameraShake_mDC6F268EB4FB616BEF0250B802883EEB74EFDEE1 (void);
extern void CFXR_Demo_ToggleEffectsLights_mF9BB908B8FE3402630FB19DEBC1C954E9BC195F2 (void);
extern void CFXR_Demo_ToggleBloom_m5AA98EF1D19188D31FC1132501037E5AC5A47194 (void);
extern void CFXR_Demo_ResetCam_m7968B3A10B9F4D3FF40B670580884C1DA2CEAA0B (void);
extern void CFXR_Demo_Awake_m269164F22777A36B8368C605A3C7EFDCAB9BD0DF (void);
extern void CFXR_Demo_Update_m9A55C3BA95AAE759CC901A345356ADCC54E71DCD (void);
extern void CFXR_Demo_PlayAtIndex_m9015D10CB7D122067A50B951740F04A5B9BC1112 (void);
extern void CFXR_Demo_WrapIndex_m82B44BE735AEBAE58FC762F9638EDCD5F86CD648 (void);
extern void CFXR_Demo_UpdateLabels_mE424EDA24B9F0ACE70AA011E9966DDD6AC5C34D7 (void);
extern void CFXR_Demo__ctor_mEF21647C89A2F37F19A4260FE182AF7CF93210F8 (void);
extern void ButtonsPressed_get_PlayEffect_m2DBB32789B36DF08EBE5FB0893474845B27C5B2B (void);
extern void ButtonsPressed_get_RestartEffect_m83CB3BC19712BC3B6C32D55C67B01F874E41DCA6 (void);
extern void ButtonsPressed_get_Left_m00F75CC4EDE8FA9F4ABCCB298B3C3203ACAB0B4E (void);
extern void ButtonsPressed_get_Right_m51A0D3EAC25EB68CB4B83B86C115F4D99331356C (void);
extern void ButtonsPressed_get_Mouse0_m4D9D354847B477579DFC07C5C63120248B6B0E93 (void);
extern void ButtonsPressed_get_Mouse1_m867184D58D447EA53DFCECEEA05A7A7AE54C5EB3 (void);
extern void ButtonsPressed_get_Mouse2_m3D33DB5F0F08375D31E141349FB699B02066324F (void);
extern void ButtonsPressed_get_MousePosition_mAE7508CBEDFE93C4AD441CF1DCA70167140F8928 (void);
extern void ButtonsPressed_get_MouseScrollY_m0960F523618201C6FC5EE57700E8F86C0DE49DEC (void);
extern void CFXR_Demo_Rotate_Update_m75A2742B1AA849A4A8A23A116B47D7BFAAF42346 (void);
extern void CFXR_Demo_Rotate__ctor_mB669EB6BEC9244C858AFE4D8739184771161DCDD (void);
extern void CFXR_Demo_Translate_Awake_m2E13757C3B4901D3D03E45F0EC35D92B2262E23F (void);
extern void CFXR_Demo_Translate_OnEnable_mC821E4225DE439D7668DEB5F1A32AA67933E5829 (void);
extern void CFXR_Demo_Translate_Update_m15580A236CE7D056694B5A61452170CDEF4D559E (void);
extern void CFXR_Demo_Translate__ctor_m584FD684223379377628B35DF5320D0C137DEF8F (void);
static Il2CppMethodPointer s_methodPointers[32] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m3A7DCD782C10D2045D64E762F42F065618A737C0,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m6DC13BAF03B589E28A9570C6B1AA2E9D55889E0A,
	CFXR_Demo_NextEffect_m94EF66A3322CB9B857A8217260CFCEDD06FC56D2,
	CFXR_Demo_PreviousEffect_m3CE1D987622FFB1BF325D8278D4A480764E4317C,
	CFXR_Demo_ToggleSlowMo_mE657E27920989DF0E13DB44CCC026A7AAF1A0083,
	CFXR_Demo_ToggleCamera_mDF2E6E2EA9F515F46A3EB6E4D0B0CB4C98A6E09B,
	CFXR_Demo_ToggleGround_m62681F628E827CFDE43E262B6FB8E3A12B310F10,
	CFXR_Demo_ToggleCameraShake_mDC6F268EB4FB616BEF0250B802883EEB74EFDEE1,
	CFXR_Demo_ToggleEffectsLights_mF9BB908B8FE3402630FB19DEBC1C954E9BC195F2,
	CFXR_Demo_ToggleBloom_m5AA98EF1D19188D31FC1132501037E5AC5A47194,
	CFXR_Demo_ResetCam_m7968B3A10B9F4D3FF40B670580884C1DA2CEAA0B,
	CFXR_Demo_Awake_m269164F22777A36B8368C605A3C7EFDCAB9BD0DF,
	CFXR_Demo_Update_m9A55C3BA95AAE759CC901A345356ADCC54E71DCD,
	CFXR_Demo_PlayAtIndex_m9015D10CB7D122067A50B951740F04A5B9BC1112,
	CFXR_Demo_WrapIndex_m82B44BE735AEBAE58FC762F9638EDCD5F86CD648,
	CFXR_Demo_UpdateLabels_mE424EDA24B9F0ACE70AA011E9966DDD6AC5C34D7,
	CFXR_Demo__ctor_mEF21647C89A2F37F19A4260FE182AF7CF93210F8,
	ButtonsPressed_get_PlayEffect_m2DBB32789B36DF08EBE5FB0893474845B27C5B2B,
	ButtonsPressed_get_RestartEffect_m83CB3BC19712BC3B6C32D55C67B01F874E41DCA6,
	ButtonsPressed_get_Left_m00F75CC4EDE8FA9F4ABCCB298B3C3203ACAB0B4E,
	ButtonsPressed_get_Right_m51A0D3EAC25EB68CB4B83B86C115F4D99331356C,
	ButtonsPressed_get_Mouse0_m4D9D354847B477579DFC07C5C63120248B6B0E93,
	ButtonsPressed_get_Mouse1_m867184D58D447EA53DFCECEEA05A7A7AE54C5EB3,
	ButtonsPressed_get_Mouse2_m3D33DB5F0F08375D31E141349FB699B02066324F,
	ButtonsPressed_get_MousePosition_mAE7508CBEDFE93C4AD441CF1DCA70167140F8928,
	ButtonsPressed_get_MouseScrollY_m0960F523618201C6FC5EE57700E8F86C0DE49DEC,
	CFXR_Demo_Rotate_Update_m75A2742B1AA849A4A8A23A116B47D7BFAAF42346,
	CFXR_Demo_Rotate__ctor_mB669EB6BEC9244C858AFE4D8739184771161DCDD,
	CFXR_Demo_Translate_Awake_m2E13757C3B4901D3D03E45F0EC35D92B2262E23F,
	CFXR_Demo_Translate_OnEnable_mC821E4225DE439D7668DEB5F1A32AA67933E5829,
	CFXR_Demo_Translate_Update_m15580A236CE7D056694B5A61452170CDEF4D559E,
	CFXR_Demo_Translate__ctor_m584FD684223379377628B35DF5320D0C137DEF8F,
};
static const int32_t s_InvokerIndices[32] = 
{
	34282,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
	34103,
	34103,
	34103,
	34103,
	34103,
	34103,
	34103,
	34247,
	34200,
	21016,
	21016,
	21016,
	21016,
	21016,
	21016,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_CFXRDemo_CodeGenModule;
const Il2CppCodeGenModule g_CFXRDemo_CodeGenModule = 
{
	"CFXRDemo.dll",
	32,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
